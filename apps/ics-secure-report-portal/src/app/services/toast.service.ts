import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface Toast {
  id: number;
  message: string;
  classname: string;
  delay?: number;
  count?: number;
  header?: string;
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private toasts: Toast[] = [];

  private toastsSubject = new BehaviorSubject<Toast[]>([]);

  toasts$ = this.toastsSubject.asObservable();

  private idCounter = 0;

  show(message: string, classname = 'error', delay = 2000) {
    const existing = this.toasts.find(
      t => t.message === message && t.classname === classname
    );
    if (existing) {
      existing.count = (existing.count || 1) + 1;
      this.toastsSubject.next(this.toasts);
      return;
    }

    const toast: Toast = {
      id: ++this.idCounter,
      message,
      classname,
      delay,
      count: 1,
    };
    this.toasts.push(toast);
    this.toastsSubject.next(this.toasts);

    if (delay > 0) {
      setTimeout(() => this.remove(toast.id), delay);
    }
  }

  remove(id: number) {
    this.toasts = this.toasts.filter(t => t.id !== id);
    this.toastsSubject.next(this.toasts);
  }

  clear() {
    this.toasts = [];
    this.toastsSubject.next(this.toasts);
  }
}
