$white: #ffffff;
$light-gray: #f5f5f5;
$gray: #e0e0e0;
$dark-gray: #333333;
$red: red;
$border-radius: 4px;
$padding-small: 0.5em;
$padding-medium: 20px;
$margin-bottom-small: 20px;
$margin-bottom-medium: 40px;

.card-content {
  padding-left: 30vh;
}
.container {
  margin: 0 auto;
  background-color: $white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: $padding-medium;
}

.step-content {
  margin-top: 5px;
  padding-left: 36px;
  margin-bottom: $margin-bottom-medium;
}

.mdc-radio__inner-circle {
  border-color: $red !important;
}

.full-width {
  width: 100%;
  border-radius: $border-radius $border-radius 0 0;
  margin-bottom: 0;
  background-color: $light-gray;
  display: flex;
  align-items: center;
}

.half-width {
  width: 50%;
  border-radius: $border-radius $border-radius 0 0;
  margin-bottom: 0;
  align-items: center;
}

.hidden-file-input {
  visibility: hidden;
}

.card-container {
  border: 1px solid $gray;
  border-radius: $border-radius;
  overflow: hidden;
  margin-bottom: $margin-bottom-small;
  background-color: $white;
}

.list-container {
  margin-bottom: $margin-bottom-small;
}

.list-header {
  display: flex;
  padding: 8px 0;
  background-color: $light-gray;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 5px solid #eee;
}

.list-header .name-column {
  padding-left: 56px;
}

.list-header .date-column {
  padding-right: 16px;
}

.name-column {
  flex: 1;
  text-align: left;
}

.date-column {
  width: 120px;
  text-align: left;
}
.date-data {
  margin-right: -18px;
}

.sort-header {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  user-select: none;

  &:hover {
    color: $dark-gray;
  }
}

.sort-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.list-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.radio-column {
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.report-list {
  width: 100%;

  mat-list {
    padding: 0;
  }

  mat-list-item {
    height: 40px;
    border-bottom: 1px solid $gray;

    &:last-child {
      border-bottom: none;
    }
  }
}

.actions {
  margin-top: 20px;
}

.hidden-file-input {
  display: none;
}

.file-upload-field {
  position: relative;
  margin-bottom: 1.5rem;
  width: 66%;
  height: 56px;
  background-color: white;

  .file-upload-label-container {
    position: absolute;
    top: -8px;
    left: 12px;
    background-color: white;
    padding: 0 4px;
    font-size: 12px;
    color: var(--mdc-outlined-text-field-label-text-color);
    font-weight: 400;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .file-upload-label {
    margin: 0;
  }

  .info-icon-container {
    position: relative;
    display: inline-flex;
    align-items: center;
  }
  .tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(-2px);
  }

  .info-icon {
    width: 14px;
    height: 14px;
    color: #757575;
    transition: color 0.2s ease;

    &:hover {
      color: #3f51b5;
    }

    &:hover + .tooltip {
      opacity: 1;
      visibility: visible;
    }
  }

  .tooltip {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%) translateY(-4px);
    background-color: #3f51b5;
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
    margin-bottom: 5px;

    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: #333;
    }
  }

  &:focus-within {
    border-color: #3f51b5;
    border-width: 2px;

    .file-upload-label {
      color: #3f51b5;
    }
  }
}

.file-upload-container {
  display: flex;
  flex-direction: row;
  gap: 0;
  align-items: stretch;
  font-size: 1.2rem;
  height: 56px;
  overflow: hidden;
  width: 100%;
}

.file-upload-button {
  padding: 12px 16px;
  border: none;
  border-left: 1px solid #9e9e9e;
  border-radius: 0;
  background-color: #f5f5f5;
  font-family: var(--mdc-outlined-text-field-label-text-font);
  font-size: 15px;
  font-weight: 400;
  letter-spacing: var(--mdc-outlined-text-field-label-text-tracking);
  color: var(--mdc-outlined-text-field-label-text-color);
  cursor: pointer;
  outline: none;
  height: 100%;
  width: 25%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  flex-shrink: 0;
  border: 1px solid #9e9e9e;
  border-radius: 5px;

  &:focus {
    outline: none;
    box-shadow: none;
  }

  &:active {
    outline: none;
    box-shadow: none;
  }

  &:hover {
    background-color: #eeeeee;
  }
}

.file-upload-info {
  display: flex;
  align-items: center;
  color: var(--mdc-outlined-text-field-label-text-color);
  line-height: 1.6rem;
  font-weight: 400;
  font-size: 15px;
  padding: 12px 16px;
  flex: 1;
  background-color: white;
  height: 100%;
  margin: 0;
  border: 1px solid #9e9e9e;
  border-radius: 5px;
}
