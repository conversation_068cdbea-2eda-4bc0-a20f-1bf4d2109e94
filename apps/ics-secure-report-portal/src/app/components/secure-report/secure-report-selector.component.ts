import {
  Component,
  ElementRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormGroup,
  FormControl,
  Validators,
  ReactiveFormsModule,
  FormsModule,
} from '@angular/forms';
import { StepperSelectionEvent } from '@angular/cdk/stepper';

import {
  MatPaginator,
  MatPaginatorModule,
  PageEvent,
} from '@angular/material/paginator';
import { MatStepperModule, MatStepper } from '@angular/material/stepper';

import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatListModule } from '@angular/material/list';
import { CommonModule } from '@angular/common';
import { debounceTime } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { FileService } from '../../services/file.service';
import { SecureReportApi, SecureReport } from '../../types/secureReport.type';
import { IcsLoaderComponent } from '../ics-loader/ics-loader.component';
import { DecryptionService } from 'src/app/services/decryption.service';
import { ZipService } from 'src/app/services/zip.service';
import {
  ALLOWED_EXTENSIONS,
  ENCRYPTED_PRIVATE_KEY_TEXT,
  NO_FILE_CHOSEN,
  PAGINATION,
  STEPPER_LABELS,
  ZIP_FILE_NAME,
} from 'src/app/constants/secure-report';
import {
  REQUIRED_PASSPHRASE_ERROR,
  UNKNOWN_ERROR,
} from 'src/app/constants/error-message';
import { isBankUser } from 'src/app/services/jwt-service';
import { ToastService } from 'src/app/services/toast.service';

@Component({
  selector: 'app-sales-report-selector',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatStepperModule,
    MatInputModule,
    MatButtonModule,
    MatListModule,
    MatRadioModule,
    MatIconModule,
    MatCardModule,
    MatPaginatorModule,
    MatFormFieldModule,
    IcsLoaderComponent,
  ],
  templateUrl: './secure-report-selector.component.html',
  styleUrls: ['./secure-report-selector.component.scss'],
})
export class SecureReportComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  @ViewChild('stepper', { static: false }) stepper!: MatStepper;

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  // Form controls for each step
  stepOneForm: FormGroup;

  stepTwoForm: FormGroup;

  stepThreeForm: FormGroup;

  searchInput: string = '';

  isLoading: Boolean = false;

  labels = STEPPER_LABELS;

  sortOrder: 'asc' | 'desc' = 'desc';

  secureReports: SecureReport[] = [];

  pagination = PAGINATION;

  // Selected report
  selectedReport: SecureReport | null = null;

  selectedFileName: String = NO_FILE_CHOSEN;

  encryptedZipBlob: Blob | null = null;

  decryptedZipBlob: Blob | null = null;

  tempPrivateKey: string = '';

  toastService = inject(ToastService);

  constructor(
    private fileService: FileService,
    private decryptionService: DecryptionService,
    private zipService: ZipService
  ) {
    this.stepOneForm = new FormGroup({
      searchControl: new FormControl(''),
      selectedReport: new FormControl(null, Validators.required),
    });

    this.stepTwoForm = new FormGroup({
      passPhrase: new FormControl(''),
      privateKey: new FormControl('', Validators.required),
      done: new FormControl('', Validators.required),
    });

    this.stepThreeForm = new FormGroup({
      completed: new FormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.checkIfAuthorized();
    this.handleLoadFiles();

    this.stepOneForm
      .get('searchControl')
      ?.valueChanges.pipe(debounceTime(300))
      .subscribe((searchText: string) => {
        this.pagination.pageIndex = 1;
        this.searchInput = searchText.trim();
        this.handleLoadFiles();
      });
  }

  private checkIfAuthorized() {
    !isBankUser() && window.location.replace('/dashboard');
  }

  private loadFiles(search: string = ''): Observable<SecureReportApi> {
    const payload = {
      search,
      pageIndex: this.pagination.pageIndex,
      pageSize: this.pagination.pageSize,
      sortOrder: this.sortOrder,
    };

    return this.fileService.getFiles(payload);
  }

  private handleLoadFiles(): void {
    this.loadFiles(this.searchInput).subscribe({
      next: (data: SecureReportApi) => {
        const { results, metadata } = data;
        this.secureReports = results;
        this.pagination.totalItems = metadata.totalResults;
      },
      error: err => {
        this.showToast(err.message, err.type);
        console.error('Error loading files:', err);
      },
    });
  }

  toggleSort(): void {
    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    this.handleLoadFiles();
  }

  generatePreSignedUrl() {
    this.getPresignedUrl(true);
  }

  showToast(message: string, errorType?: string) {
    this.isLoading = false;
    this.toastService.show(message, errorType);
  }

  private getPresignedUrl = (shouldNavigate?: boolean) => {
    this.isLoading = true;
    this.fileService.getPresignedUrl(this.selectedReport?.id ?? '').subscribe({
      next: data => {
        this.fileService
          .downloadZip(data.s3Url)
          .then(value => {
            this.encryptedZipBlob = value;
            this.isLoading = false;

            if (this.stepTwoForm.get('privateKey')?.value) {
              this.stepTwoForm.patchValue({ done: true });
            }
            if (shouldNavigate) this.stepper.next();
          })
          .catch(error => {
            console.error('Error getting file from s3', error);
            this.showToast(UNKNOWN_ERROR);
          });
      },
      error: err => {
        this.showToast(err.message ?? UNKNOWN_ERROR, err.type);
        console.error('Error loading files:', err);
      },
    });
  };

  onPageChange(event: PageEvent): void {
    this.pagination.pageIndex = event.pageIndex + 1;
    this.handleLoadFiles();
  }

  onReportSelect(report: SecureReport): void {
    this.selectedReport = report;
    this.stepOneForm.patchValue({ selectedReport: report });
    this.stepTwoForm.patchValue({ done: '' });
  }

  triggerFileInput(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.fileInput.nativeElement.click();
  }

  private changePassPhraseValidation(required: boolean) {
    if (required) {
      this.stepTwoForm.get('passPhrase')?.setValidators([Validators.required]);
    } else this.stepTwoForm.get('passPhrase')?.clearValidators();

    this.stepTwoForm.get('passPhrase')?.updateValueAndValidity();
  }

  private readFile(file: File) {
    const reader = new FileReader();

    reader.onload = e => {
      const text = e.target?.result as string;
      try {
        if (text.includes(ENCRYPTED_PRIVATE_KEY_TEXT)) {
          this.tempPrivateKey = text;
          this.changePassPhraseValidation(true);

          this.showToast(REQUIRED_PASSPHRASE_ERROR);
        } else {
          this.changePassPhraseValidation(false);
          this.tempPrivateKey = '';
          this.decryptionService.loadPrivateKey(text);
        }
        this.selectedFileName = file.name;
        this.stepTwoForm.patchValue({ privateKey: text });
        this.stepTwoForm.patchValue({ done: true });
      } catch (err: unknown) {
        const error = err as Error;
        this.showToast(error.message ?? UNKNOWN_ERROR);
        this.stepTwoForm.patchValue({ privateKey: null });
        this.selectedFileName = NO_FILE_CHOSEN;
        console.error('Failed to load private key:', e);
      }
    };

    reader.readAsText(file);
  }

  handleFileChange(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
      const file = target.files[0];
      const fileExtension = file.name.split('.').pop();
      if (!ALLOWED_EXTENSIONS.includes(fileExtension ?? '')) {
        return;
      }
      this.readFile(file);
    }
  }

  async unzipAndProcess(zipBlob: Blob) {
    try {
      this.isLoading = true;
      this.tempPrivateKey &&
        this.decryptionService.loadPrivateKey(
          this.tempPrivateKey,
          this.stepTwoForm.get('passPhrase')?.value
        );

      const zip = await this.zipService.unzip(zipBlob);
      const files = Object.values(zip.files).filter(file => !file.dir);

      const decryptedEntries = await Promise.all(
        files.map(async file => {
          const content = await file.async('text');
          const decryptedContent = this.decryptionService.decryptPkcs7(content);
          return [file.name, decryptedContent] as [string, string];
        })
      );

      const decryptedFiles = Object.fromEntries(decryptedEntries);

      this.decryptedZipBlob = await this.zipService.zip(decryptedFiles);
      this.isLoading = false;

      this.stepper.next();
    } catch (e: unknown) {
      const error = e as Error;
      this.showToast(error.message);
      console.error('Error unzipping or processing files:', error);
    }
  }

  decryptFile() {
    this.encryptedZipBlob && this.unzipAndProcess(this.encryptedZipBlob);
  }

  downloadDecryptedFile() {
    if (this.decryptedZipBlob) {
      const url = URL.createObjectURL(this.decryptedZipBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = ZIP_FILE_NAME;
      a.click();
      this.stepThreeForm.patchValue({ completed: true });
      URL.revokeObjectURL(url);
    }
  }

  getRequiredDataForStepperTwoData() {
    if (!this.stepTwoForm.get('done')?.value) this.getPresignedUrl();
  }

  // if someone directly clicks on stepper without gettig the data then it this load data
  onStepChange(event: StepperSelectionEvent): void {
    if (event.selectedIndex === 0) return;

    const stepCalls = {
      1: () => this.getRequiredDataForStepperTwoData(),
      2: () => this.decryptFile(),
    };

    const handler = stepCalls[event.selectedIndex as keyof typeof stepCalls];
    if (handler) {
      handler();
    }
  }
}
