:host ::ng-deep ngb-toast {
  position: fixed;
  padding: 15px;
  border-radius: 5px;
  z-index: 1000;
  left: 3%;
  opacity: 0;
  color: white;
}

:host ::ng-deep ngb-toast.error {
  background-color: #f44336;
}

:host ::ng-deep ngb-toast.warning {
  background-color: #2d2d2e;
}

:host ::ng-deep ngb-toast.show {
  animation: bounceIn 1s forwards;
  opacity: 1;
}

:host ::ng-deep ngb-toast.hide {
  animation: fadeOut 1.5s forwards;
  opacity: 0;
}

.toast-counter {
  margin-right: 1rem;
  padding: 0.3rem 0.7rem;
  font-size: 75%;
  font-weight: bold;
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.2rem;
  vertical-align: middle;
}

/* Animations */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
