import { Component } from '@angular/core';
import { ToastService, Toast } from '../../services/toast.service';

@Component({
  selector: 'app-toast-container',
  templateUrl: './toast-container.component.html',
  styleUrls: ['./toast-container.component.scss'],
})
export class ToastContainerComponent {
  toasts: Toast[] = [];

  showToasts = new Set<number>();

  constructor(private toastService: ToastService) {
    this.toastService.toasts$.subscribe(toast => {
      this.toasts = toast;
      this.toasts.forEach(toastMsg => {
        if (!this.showToasts.has(toastMsg.id)) {
          this.showToasts.add(toastMsg.id);
          setTimeout(() => this.startHide(toastMsg.id), toastMsg.delay || 5000);
        }
      });
    });
  }

  startHide(id: number) {
    this.showToasts.delete(id);
  }

  onAnimationEnd(toast: Toast) {
    if (!this.showToasts.has(toast.id)) {
      this.remove(toast.id);
    }
  }

  remove(id: number) {
    this.toasts = this.toasts.filter(t => t.id !== id);
  }
}
