<ngb-toast
  *ngFor="let toast of toasts"
  [class.error]="toast.classname === 'error'"
  [class.warning]="toast.classname === 'warning'"
  [class.show]="showToasts.has(toast.id)"
  [class.hide]="!showToasts.has(toast.id)"
  [autohide]="false"
  (animationend)="onAnimationEnd(toast)"
  [delay]="toast.delay || 5000"
  (hidden)="remove(toast.id)"
  [header]="toast.header || ''"
>
  <span *ngIf="toast.count && toast.count > 1" class="toast-counter">
    {{ toast.count }}
  </span>
  {{ toast.message }}
</ngb-toast>
