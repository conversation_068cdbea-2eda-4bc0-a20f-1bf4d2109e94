import qs from 'qs';

import errors from '../constants/errors';
import {
  Assignments,
  CreateInstancePayload,
  CreateInstanceResponse,
  DeviceConfig,
  InstancesResponse,
  RevisionPayload,
  RevisionResultItem,
  UpdateInstancePayload,
  RevisionsResponse,
  InstanceDetailsResponse,
  ConfigAssignmentStats,
  InstancesRequest,
  InstanceItemStats,
  ConfigInstanceItemsStats,
  GetUsersResponse,
  GetUsersRequest,
  RevisionPublishDeploymentType,
  DeviceAssetResponse,
  SiteAssetResponse,
  SiteTagAssetResponse,
  GetMaintenanceWindow,
  GetPanicMode,
  AuditEventDetailsRequest,
  ApprovalRejectPayload,
  EntityPendingPayload,
  NewEntitySettingPayload,
  UpdateEntitySettingsPayload,
  IcsUserDetails,
  GetEntityPendingSettingsParams,
  PanicEventPayload,
  AutomaticDeployPayload,
} from '../constants/types';
import {
  ScheduleAlertRequest,
  ScheduleAlertResponse,
} from '../components/ScheduleDeploymentAlert/types';
import {
  SchedulesRequest,
  ScheduleResponse,
} from '../components/ScheduleList/types';
import { ScheduleRevisionPayload } from '../components/ScheduleDeploymentModal/types';
import { DeploymentFilterRequest } from '../components/DeploymentHistory/Schemas/deploymentSchema';
import request from './request';

export const getInstances = (
  instancesRequest: InstancesRequest,
  signal?: AbortSignal
): Promise<InstancesResponse> =>
  request()
    .get('/config-management/user/instances', {
      params: instancesRequest,
      paramsSerializer: params =>
        qs.stringify(params, {
          encode: true,
        }),
      signal,
    })
    .then(resp => resp.data);

export const getSchedules = (
  schedulesRequest: SchedulesRequest,
  signal?: AbortSignal
): Promise<ScheduleResponse> =>
  request()
    .get('/schedule-ms/schedules', {
      params: schedulesRequest,
      paramsSerializer: params =>
        qs.stringify(params, {
          encode: true,
        }),
      signal,
    })
    .then(resp => resp.data);

export const putUpdateSchedule = payload =>
  request()
    .put(`/schedule-ms/schedules`, payload)
    .then(resp => resp.data)
    .catch(error => {
      throw error;
    });

export const postEntityPending = (
  payload: EntityPendingPayload[]
): Promise<CreateInstanceResponse> =>
  request()
    .post('/approval/data', payload)
    .then(resp => resp.data)
    .catch(error => {
      throw error;
    });
export const getUserConfig = (signal?: AbortSignal): Promise<DeviceConfig[]> =>
  request()
    .get('/config-management/user/config/applications', { signal })
    .then(resp => resp.data);

export const postCreateInstance = (
  payload: CreateInstancePayload
): Promise<CreateInstanceResponse> =>
  request()
    .post('/config-management/user/instances', payload)
    .then(resp => resp.data)
    .catch(error => {
      if (error?.response?.status === 409) {
        throw new Error(errors.DUPLICATION);
      }
      throw new Error(error?.response?.data ?? errors.CREATE_INSTANCE);
    });

// Save draft, description and instance name.
export const putUpdateInstance = ({
  // add the error handling here
  instanceId,
  payload,
}: {
  instanceId: string;
  payload: UpdateInstancePayload;
}) =>
  request()
    .put(`/config-management/user/instances/${instanceId}`, payload)
    .then(resp => resp.data)
    .catch(error => {
      throw new Error(error?.response?.data ?? errors.CREATE_INSTANCE);
    });

export const getInstanceItemStats = (
  instanceId: string,
  signal?: AbortSignal
): Promise<InstanceItemStats> =>
  request()
    .get(`/config-management/user/instances/${instanceId}/stats`, { signal })
    .then(resp => resp.data);

export const getConfigInstanceItemStats = (
  { instanceIds, tenantId }: { instanceIds: string[]; tenantId: string },
  signal?: AbortSignal
): Promise<ConfigInstanceItemsStats> =>
  request()
    .get(`/renditions/config-instances`, {
      params: {
        filters: {
          desiredConfigFileInstanceId: {
            $in: instanceIds,
          },
          tenantId: {
            $in: tenantId,
          },
        },
      },
      paramsSerializer: params =>
        qs.stringify(params, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);

export const getInstanceDetails = (
  instanceId: string
): Promise<InstanceDetailsResponse> =>
  request()
    .get(`/config-management/user/instances/${instanceId}/details`)
    .then(resp => resp.data)
    .catch(error => {
      throw error?.response;
    });

export const getScheduleCsvUrl = (schedulesRequest: SchedulesRequest) =>
  request()
    .get('/schedule-ms/schedules', {
      params: schedulesRequest,
      paramsSerializer: params =>
        qs.stringify(params, {
          encode: true,
        }),
    })
    .then(resp => {
      const { data } = resp;
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `schedules.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    });

export const getInstanceCsvUrl = (instanceId: string, name: string) =>
  request()
    .get(`/config-management/user/instances/${instanceId}/exportcsv`)
    .then(resp => {
      const { data } = resp;

      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${name}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    });

export const getMultipleInstancesCSV = ({
  configFileName,
  configFileDottedString,
}: {
  configFileName: string;
  configFileDottedString: string;
}) =>
  request()
    .get(
      `/config-management/user/config/${configFileDottedString}/${configFileName}/exportcsv`
    )
    .then(response => {
      const { data } = response;

      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${configFileName}-Instances-Report.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      return true;
    })
    .catch(() => false);

export const getConfigInstanceStat = (instanceId: string) =>
  request()
    .get(`/config-management/user/instances/${instanceId}/stats`)
    .then(resp => resp.data);

export const getEntityHierarchyStats = (
  instanceId: string,
  hierarchyType: string,
  hierarchyValue: string,
  signal: AbortSignal
): Promise<ConfigAssignmentStats> =>
  request()
    .get(
      `/config-management/user/instances/${instanceId}/stats?hierarchyType=${hierarchyType}&hierarchyValue=${hierarchyValue}`,
      { signal }
    )
    .then(resp => resp.data);

export const getInstanceSummary = (instanceId: string) =>
  request()
    .get(`/config-management/user/instances/${instanceId}/summary`)
    .then(resp => resp.data)
    .catch(() => {
      throw new Error(errors.INSTANCE);
    });

export const getRevisions = (instanceId: string): Promise<RevisionsResponse> =>
  request()
    .get(`/config-management/user/instances/${instanceId}/revisions`)
    .then(resp => resp.data)
    .catch(() => {
      throw new Error(errors.REVISION);
    });

export const postPublishRevisions = ({
  instanceId,
  payload,
}: {
  instanceId: string;
  payload: RevisionPayload;
}) =>
  request()
    .post(`/config-management/user/instances/${instanceId}/revisions`, payload)
    .then(resp => resp.data);

export const postScheduledRevisions = ({
  instanceId,
  payload,
}: {
  instanceId: string;
  payload: ScheduleRevisionPayload;
}) =>
  request()
    .post(`/config-management/user/instances/${instanceId}/schedule`, payload)
    .then(resp => resp.data);

export const getOneRevision = ({
  instanceId,
  revisionId,
  signal,
}: {
  instanceId: string;
  revisionId: string;
  signal: AbortSignal;
}): Promise<RevisionResultItem> =>
  request()
    .get(
      `/config-management/user/instances/${instanceId}/revisions/${revisionId}`,
      { signal }
    )
    .then(resp => resp.data);

export const putOneRevision = (
  {
    instanceId,
    revisionId,
    displayName,
  }: {
    instanceId: string;
    revisionId: string;
    displayName: string;
  },
  signal?: AbortSignal
): Promise<RevisionResultItem> =>
  request()
    .put(
      `/config-management/user/instances/${instanceId}/revisions/${revisionId}`,
      { displayName },
      { signal }
    )
    .then(resp => resp.data);

export const postDeployment = (instanceId: string) =>
  request()
    .post('/config-management/user/instances/deployments', { instanceId })
    .then(resp => resp.data);

export const getDeployments = (instanceId: string) =>
  request()
    .get(`/config-management/user/instances/${instanceId}/deployments`)
    .then(resp => resp.data)
    .catch(() => {
      throw new Error(errors.DEPLOYMENT);
    });

export const getDeploymentStats = (instanceId: string, deploymentId: string) =>
  request()
    .get(
      `/config-management/user/instances/${instanceId}/deployments/${deploymentId}/stats`
    )
    .then(resp => resp.data)
    .catch(() => {
      throw new Error(errors.DEPLOYMENT);
    });

export const getConfigInstanceAssignmentStats = (
  instanceId: string,
  assignmentId: string
) =>
  request()
    .get(
      `/config-management/user/instances/${instanceId}/assignments/${assignmentId}/stats`
    )
    .then(resp => resp.data)
    .catch(() => {
      throw new Error(errors.ASSIGNMENT_STATS);
    });

export const getDeploymentDetails = (
  instanceId: string,
  deploymentId: string
) =>
  request()
    .get(
      `/config-management/user/instances/${instanceId}/deployments/${deploymentId}`
    )
    .then(resp => resp.data);

export const postDeploymentCancel = (
  instanceId: string,
  deploymentId: string
) =>
  request()
    .post(
      `/config-management/user/instances/${instanceId}/deployments/${deploymentId}/cancel`
    )
    .then(resp => resp.data);

export const getAssignments = (instanceId: string) =>
  request()
    .get(`/config-management/user/instances/${instanceId}/assignments`)
    .then(resp => resp.data);

export const getEntitySettingsStatus = (
  signal: AbortSignal,
  entityType: string
) =>
  request()
    .get(`/approval/settings`, {
      signal,
      params: {
        filters: {
          entityType: {
            $eq: entityType,
          },
        },
      },
    })
    .then(resp => resp.data.results);

export const postEntitySetting = (
  newEntities: NewEntitySettingPayload[]
): Promise<any> =>
  request()
    .post('/approval/settings', newEntities)
    .then(resp => resp)
    .catch(error => {
      throw error.response.data;
    });

export const putApprovalReject = (payload: ApprovalRejectPayload[]) =>
  request()
    .put(`/approval/data`, payload)
    .then(resp => resp.data)
    .catch(error => {
      throw error;
    });

export const putEntitySettings = (payload: UpdateEntitySettingsPayload[]) =>
  request()
    .put(`/approval/settings`, payload)
    .then(resp => resp.data)
    .catch(error => {
      throw error;
    });

export const getEntitySettings = (
  configFileIds,
  signal?: AbortSignal
): Promise<any> =>
  request()
    .get(`/approval/settings`, {
      params: {
        filters: {
          entityType: { $eq: 'config-instance-draft' },
        },
      },
      paramsSerializer: params =>
        qs.stringify(params, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);

export const getEntityPendingData = ({
  configInstanceId,
  status,
  entityType,
}: GetEntityPendingSettingsParams) =>
  request()
    .get(`/approval/data`, {
      params: {
        filters: {
          'entityApprovalSettings.entityType': {
            $eq: entityType,
          },
          'condition.configInstanceId': {
            $in: configInstanceId,
          },
          status: {
            $in: status,
          },
        },
      },
    })
    .then(resp => resp.data);

export const postAssignments = (
  instanceId: string,
  payload: {
    configAssignments: Assignments[];
    deploymentType: RevisionPublishDeploymentType;
    mfaCode: string;
    configFileId: number;
    appDescriptorId: number;
    appName: string;
  }
) =>
  request()
    .post(
      `/config-management/user/instances/${instanceId}/assignments`,
      payload
    )
    .then(resp => resp.data)
    .catch(error => {
      throw error.response.data;
    });

export const getAssignmentTags = (instanceId: string) =>
  request()
    .get(`/config-management/user/instances/${instanceId}/assignments/tags`)
    .then(resp => resp.data);

export const getDeviceCountByTenant = ({
  pageIndex,
  pageSize,
  fields = ['id', 'name', 'serialNumber', 'deviceType', 'site.id'],
}: {
  pageIndex: number;
  pageSize?: number;
  fields?: string[];
}): Promise<DeviceAssetResponse> =>
  request()
    .get('/devices', {
      params: { pageIndex, pageSize, fields },
    })
    .then(resp => resp.data);

export const getDeviceList = ({
  signal,
  pageIndex,
  pageSize,
  fields = ['id', 'name', 'serialNumber', 'deviceType', 'site.id'],
  filters,
}: {
  signal: AbortSignal;
  pageIndex: number;
  pageSize?: number;
  fields?: string[];
  filters?: {
    name: {
      $containsi: string;
    };
  };
}): Promise<DeviceAssetResponse> =>
  request()
    .get('/entities/devices', {
      params: { pageIndex, pageSize, fields, filters },
      signal,
    })
    .then(resp => resp.data);

export const getSiteTagCount = ({
  pageIndex,
  pageSize = 20,
  fields = ['id', 'name', 'serialNumber', 'deviceType', 'site.id'],
  selecteddevices,
  selectedSiteTags,
}: {
  pageIndex: number;
  pageSize?: number;
  fields?: string[];
  selecteddevices: string[];
  selectedSiteTags: string[];
}): Promise<DeviceAssetResponse> => {
  const filters: Record<string, any> = {};

  if (selectedSiteTags.length > 0) {
    filters['site.tags.id'] = { $in: JSON.stringify(selectedSiteTags) };
    filters.id = { $notIn: JSON.stringify(selecteddevices) };
  }
  return request()
    .get('/entities/devices', {
      params: {
        pageIndex,
        pageSize,
        fields,
        filters, // Serialize filters object to JSON string
      },
      paramsSerializer: params => {
        const queryString = qs.stringify(params, {
          encode: false,
        });
        return queryString;
      },
    })
    .then(resp => resp.data)
    .catch(error => {
      console.error('Error occurred while fetching device list:', error);
      throw new Error(`Failed to fetch device list: ${error.message}`);
    });
};

export const getSiteCount = ({
  pageIndex,
  pageSize = 20,
  fields = ['id', 'name', 'serialNumber', 'deviceType', 'site.id'],
  selecteddevices,
  selectedSites,
}: {
  pageIndex: number;
  pageSize?: number;
  fields?: string[];
  selecteddevices: string[];
  selectedSites: string[];
}): Promise<DeviceAssetResponse> => {
  const filters: Record<string, any> = {};

  if (selectedSites.length > 0) {
    filters['site.id'] = { $in: JSON.stringify(selectedSites) };
    filters.id = { $notIn: JSON.stringify(selecteddevices) };
  }
  return request()
    .get('/entities/devices', {
      params: {
        pageIndex,
        pageSize,
        fields,
        filters, // Serialize filters object to JSON string
      },
      paramsSerializer: params => {
        const queryString = qs.stringify(params, {
          encode: false,
        });
        return queryString;
      },
    })
    .then(resp => resp.data)
    .catch(error => {
      console.error('Error occurred while fetching device list:', error);
      throw new Error(`Failed to fetch device list: ${error.message}`);
    });
};

export const getSiteList = ({
  signal,
  pageIndex,
  pageSize,
  fields = ['siteId', 'formattedAddress', 'siteName', 'siteTags'],
  filters,
}: {
  signal: AbortSignal;
  pageIndex: number;
  pageSize?: number;
  fields?: string[];
  filters?: {
    siteName: {
      $containsi: string;
    };
  };
}): Promise<SiteAssetResponse> =>
  request()
    .get('/entities/sites', {
      params: { pageIndex, pageSize, fields, filters },
      signal,
    })
    .then(resp => resp.data);

export const getSiteTagList = ({
  signal,
  pageIndex,
  pageSize,
  fields = ['tagId', 'tagName', 'siteCount'],
  filters,
}: {
  signal: AbortSignal;
  pageIndex: number;
  pageSize?: number;
  fields?: string[];
  filters?: {
    tagName: {
      $containsi: string;
    };
  };
}): Promise<SiteTagAssetResponse> =>
  request()
    .get('/entities/site-tags', {
      params: { pageIndex, pageSize, fields, filters },
      signal,
    })
    .then(resp => resp.data);

export const deleteAssignments = (
  instanceId: string,
  deploymentType: RevisionPublishDeploymentType
) =>
  request()
    .delete(
      `/config-management/user/instances/${instanceId}/assignments?deploymentType=${deploymentType}`
    )
    .then(resp => resp.data);

export const getInstanceSchema = (
  instanceId: string
): Promise<InstanceDetailsResponse> =>
  request()
    .get(`/config-management/user/instances/${instanceId}/schema`)
    .then(resp => resp.data);

/**
 * Delete config instance
 *
 * @param {{instanceId:string}} {instanceId}
 * @returns {Promise<number>}
 */
export const deleteConfigInstance = ({
  instanceId,
}: {
  instanceId: string;
}): Promise<number> =>
  request()
    .delete(`/config-management/user/instances/${instanceId}`)
    .then(resp => resp.status);

/**
 * Get users in company
 *
 * @param {GetUsersRequest} { fullName, pageIndex, pageSize, pending, roles }
 * @param {AbortSignal} signal
 * @returns {Promise<GetUsersResponse>}
 */
export const getUsers = (
  {
    fullName,
    pageIndex = 0,
    // eslint-disable-next-line @typescript-eslint/no-shadow
    pageSize = 10,
    pending = false,
    roles = [],
  }: GetUsersRequest,
  signal: AbortSignal
): Promise<GetUsersResponse> =>
  request()
    .get<GetUsersResponse>('/users', {
      params: {
        pageIndex,
        pageSize,
        pending,
        roles,
        ...(fullName ? { q: fullName } : {}),
      },
      paramsSerializer: params =>
        qs.stringify(params, {
          encode: true,
          strictNullHandling: true,
        }),
      signal,
    })
    .then(resp => resp.data);

/**
 * Get any schedules are present
 * @param data
 * @returns {Promise<ScheduleAlertResponse>}
 */
export const getConfigScheduled = (
  data: ScheduleAlertRequest
): Promise<ScheduleAlertResponse> =>
  request()
    .post(`/schedule-ms/schedules/source-type-scheduled`, data)
    .then(resp => resp.data);

export const getMaintenanceWindow = (): Promise<GetMaintenanceWindow> =>
  request()
    .get('/renditions/maintenance-window')
    .then(resp => resp.data);

export const getPanicMode = (): Promise<GetPanicMode> =>
  request()
    .get('/renditions/panic-mode')
    .then(resp => resp.data);

export const putPanicActivation = (payload: PanicEventPayload) =>
  request()
    .put(`/renditions/panic-mode`, payload)
    .then(resp => resp.data)
    .catch(error => {
      const errorMessage =
        error?.response?.data?.message ||
        error?.response?.data?.error ||
        error.message ||
        'Something went wrong.';
      throw new Error(errorMessage);
    });

export const postAutomaticDeployment = (
  panicAutomaticDeploymentPayload: AutomaticDeployPayload
) =>
  request()
    .post(`/renditions/deployments`, panicAutomaticDeploymentPayload)
    .then(resp => resp.data)
    .catch(error => {
      let errorMessage = 'Something went wrong.';

      if (error?.response?.data?.error) {
        const deploymentTypeErrors = error.response.data.error.deploymentType;
        if (deploymentTypeErrors && Array.isArray(deploymentTypeErrors)) {
          errorMessage = deploymentTypeErrors.join(', ');
        }
      } else {
        errorMessage =
          error?.response?.data?.message ||
          error?.response?.data?.error ||
          error.message ||
          errorMessage;
      }

      throw new Error(errorMessage);
    });

export const getAuditReport = (
  deploymentFilterRequest: DeploymentFilterRequest
): Promise<any> =>
  request()
    .get('/config-management/audit-report', {
      params: deploymentFilterRequest,
    })
    .then(resp => resp.data)
    .catch(error => {
      throw new Error(error?.response?.data ?? 'Error fetching audit report');
    });

export const getAuditFilters = (): Promise<any> =>
  request()
    .get('/config-management/audit-filters')
    .then(resp => resp.data)
    .catch(error => {
      throw new Error(`Failed to fetch filtered list: ${error.message}`);
    });

export const getAuditEventDetails = (
  auditId?: string,
  signal?: AbortSignal
): Promise<AuditEventDetailsRequest> =>
  request()
    .get(`/config-management/audit-event/${auditId}/config-files`, { signal })
    .then(resp => resp.data)
    .catch(error => {
      throw new Error(`Failed to fetch filtered list: ${error.message}`);
    });

export const getUserDetails = (userId: string): Promise<IcsUserDetails> =>
  request()
    .get(`/users/${userId}`)
    .then(resp => resp.data)
    .catch(error => {
      throw new Error(error.response?.data?.message || error.message);
    });
