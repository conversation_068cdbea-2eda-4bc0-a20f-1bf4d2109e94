import React, { FC, useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  DoNotDisturbOn as PanicIcon,
  RestartAlt as ResetIcon,
  WarningAmber,
} from '@mui/icons-material';
import Tooltip from '@mui/material/Tooltip';
import { useSnackbar } from 'notistack';
import { Box } from '@mui/system';
import useHasPermissions from '../hooks/useHasPermissions';
import UserRoles from '../constants/userRoles';

import {
  useAutomaticDeployment,
  useGetPanicMode,
  useInitiatePanic,
} from '../services/use-query';
import useToken from '../hooks/useToken';
import TwoFactorVerification from './TwoFactorAuthentication/TwoFactorAuthentication';

interface ActionButtonProps {
  icon?: React.ElementType;
  color?: string;
  tooltip: string;
  onClick?: () => void;
  label?: string;
  disabled?: boolean;
}

interface Action {
  name: string;
  [key: string]: any;
}
const ActionButton: FC<ActionButtonProps> = ({
  icon: Icon,
  color,
  tooltip,
  onClick,
  label,
  disabled,
}) => {
  const getTooltipText = () => {
    if (disabled) {
      if (tooltip === 'Panic') {
        return 'Panic ON button disabled';
      }
      if (tooltip === 'Reset') {
        return 'Panic OFF button disabled';
      }
      if (label === 'Deploy Now') {
        return 'Deploy-now button disabled';
      }
    }
    return tooltip;
  };

  return (
    <Tooltip title={getTooltipText()} arrow>
      <div
        style={{ display: 'flex', alignItems: 'center', marginRight: '8px' }}
      >
        {Icon && (
          <Icon
            style={{
              color: disabled ? 'grey' : color,
              fontSize: '32px',
              cursor: disabled ? 'not-allowed' : 'pointer',
            }}
            onClick={disabled ? null : onClick}
          />
        )}
        {label && (
          <Button
            onClick={disabled ? null : onClick}
            variant='contained'
            sx={{
              backgroundColor: disabled ? 'grey' : ' ',
              color: 'white',
              minWidth: '100px',
              marginLeft: '8px',
              cursor: disabled ? 'not-allowed' : 'pointer',
            }}
            disabled={disabled}
          >
            {label}
          </Button>
        )}
      </div>
    </Tooltip>
  );
};

const ResetDeployPanicButtons = () => {
  const [isDialogOpen, setDialogOpen] = useState({
    panic: false,
    reset: false,
    deploy: false,
  });
  const [isMfaOpen, setMfaOpen] = useState(false);
  const [isFinalConfirmOpen, setFinalConfirmOpen] = useState(false);
  const [mfaCode, setMfaCode] = useState('');
  const [currentAction, setCurrentAction] = useState({ name: '' });
  const { enqueueSnackbar } = useSnackbar();
  const [enableStop, setEnableStop] = useState(false);
  const [enableReset, setEnableReset] = useState(false);
  const [enableDeploy, setEnableDeploy] = useState(false);
  const [selectedWindow, setSelectedWindow] = useState('');
  const [selectedAllWindow, setSelectedAllWindow] = useState('');

  const userToken = useToken();

  const maintenanceAllWindows = ['All', 'Immediate', 'Maintenance-Window'];
  const maintenanceWindows = ['All', 'Disabled-Only', 'Enabled-Only'];
  const hasAdminAccess = useHasPermissions({
    userRoles: [UserRoles.COMPANY_ADMIN, UserRoles.CONFIG_MGMT_DEPLOY],
  });

  const {
    data: panicData,
    isLoading: isPanicLoading,
    refetch: refetchResetPanicStatus,
  } = useGetPanicMode();

  const updateButtonStates = () => {
    const panicModeEnabled = panicData?.panicModeEnabled || false;
    const blockedOutExist = panicData?.blockedOutExist || false;

    setEnableStop(!panicModeEnabled);
    setEnableReset(panicModeEnabled);
    setEnableDeploy(!panicModeEnabled && !blockedOutExist);
  };

  const handleInputValues = () => {
    setSelectedWindow('');
    setSelectedAllWindow('');
  };

  useEffect(() => {
    if (isPanicLoading) {
      return;
    }
    updateButtonStates();
  }, [panicData, isPanicLoading]);

  const { mutate } = useInitiatePanic(
    {
      panicMode: currentAction.name === 'panic',
      mfaCode,
    },
    {
      onSuccess: () => {
        enqueueSnackbar('panic action completed successfully', {
          variant: 'success',
        });
        refetchResetPanicStatus();
        updateButtonStates();
      },
      onError: (error: { message: string }) => {
        enqueueSnackbar(error.message, {
          variant: 'error',
          autoHideDuration: 2000,
        });
      },
    }
  );

  const { mutate: automaticDeployMutate } = useAutomaticDeployment(
    {
      deploymentType: selectedAllWindow.toLowerCase(),
      entityType: 'tenant',
      entityId: [userToken?.company?.id || ''],
      deployDisabledSites: selectedWindow.toLowerCase(),
      mfaCode,
    },
    {
      onSuccess: () => {
        enqueueSnackbar('deploy action completed successfully', {
          variant: 'success',
        });
        refetchResetPanicStatus();
        handleInputValues();
      },
      onError: (error: { message: string }) => {
        enqueueSnackbar(error.message, {
          variant: 'error',
          autoHideDuration: 2000,
        });
        handleInputValues();
      },
    }
  );

  const handleConfirmResetPanic = async () => {
    await mutate({
      panicMode: currentAction?.name === 'panic',
      mfaCode,
    });
  };

  const handleConfirmDeploy = async () => {
    await automaticDeployMutate({
      deploymentType: selectedAllWindow,
      entityType: 'tenant',
      deployDisabledSites: false,
      entityId: [userToken?.company?.id || ''],
      mfaCode,
    });
  };

  const handleActionClick = (action: Action) => {
    setCurrentAction(action);
    setDialogOpen(prev => ({ ...prev, [action.name]: true }));
  };

  const handleMfaSubmit = (code: string) => {
    setMfaCode(code);
    setMfaOpen(false);
    setFinalConfirmOpen(true);
    handleInputValues();
  };

  const handleFinalConfirm = () => {
    if (currentAction?.name === 'panic' || currentAction?.name === 'reset') {
      handleConfirmResetPanic();
    } else if (currentAction?.name === 'deploy') {
      handleConfirmDeploy();
    }
    setFinalConfirmOpen(false);
  };

  const dialogStyles = {
    borderRadius: '20px',
    width: 500,
    padding: 3,
    display: 'flex',
    justifyContent: 'space-between',
    alignitems: 'center',
    backgroundColor: 'common.modalBackground',
    boxSizing: 'unset',
  };

  const actionButtons = [
    {
      name: 'panic',
      icon: PanicIcon,
      color: 'red',
      tooltip: 'Panic',
      dialogTitle: 'Panic Action Confirmation',
      dialogContent:
        'Enabling the panic button will disable all configuration and deployment jobs and block new jobs, causing a major disruption. Do you want to continue?',
      disabled: isPanicLoading || !enableStop,
    },
    {
      name: 'reset',
      icon: ResetIcon,
      color: 'orange',
      tooltip: 'Reset',
      dialogTitle: 'Reset Action Confirmation',
      dialogContent:
        'All the new jobs will be created for maintenance window and immediate deployment',
      disabled: isPanicLoading || !enableReset,
    },
    {
      name: 'deploy',
      color: '#3D5BB8',
      tooltip: 'Deploy Now',
      dialogTitle: 'Deploy Action Confirmation',
      dialogContent: 'It will deploy all the pending jobs',
      label: 'Deploy Now',
      disabled: isPanicLoading || !enableDeploy,
    },
  ];

  const dialogFontStyles = {
    fontFamily: 'Roboto',
    fontWeight: 500,
    fontSize: '20px',
    lineHeight: '24px',
    letterSpacing: '0.1px',
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      {hasAdminAccess &&
        actionButtons.map(action => (
          <ActionButton
            key={action.name}
            icon={action.icon}
            color={action.color}
            tooltip={action.tooltip}
            onClick={() => {
              handleActionClick(action);
              handleInputValues();
            }}
            label={action.label}
            disabled={action.disabled}
          />
        ))}

      {actionButtons.map(action => (
        <Dialog
          key={action.name}
          open={isDialogOpen[action.name]}
          onClose={() =>
            setDialogOpen(prev => ({ ...prev, [action.name]: false }))
          }
          PaperProps={{ sx: dialogStyles }}
        >
          <Box display='flex' alignItems='center' gap={1}>
            <WarningAmber sx={{ color: 'orange', fontSize: '24px' }} />
            <Typography variant='h6' sx={dialogFontStyles}>
              {action.dialogTitle}
            </Typography>
          </Box>

          {action.name === 'deploy' && (
            <Box sx={{ padding: '10px 24px' }}>
              <FormControl id='selectDeployWindow' variant='filled' fullWidth>
                <InputLabel>Deployment Sites</InputLabel>
                <Select
                  value={selectedWindow}
                  onChange={event => {
                    setSelectedWindow(event.target.value);
                  }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        maxHeight: 300,
                      },
                    },
                  }}
                >
                  {maintenanceWindows.map(window => (
                    <MenuItem key={window} value={window}>
                      {window}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}
          {action.name === 'deploy' && (
            <Box sx={{ padding: '10px 24px' }}>
              <FormControl id='selectDeployType' variant='filled' fullWidth>
                <InputLabel>Deployment Type</InputLabel>
                <Select
                  value={selectedAllWindow}
                  onChange={event => {
                    setSelectedAllWindow(event.target.value);
                  }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        maxHeight: 300,
                      },
                    },
                  }}
                >
                  {maintenanceAllWindows.map(window => (
                    <MenuItem key={window} value={window}>
                      {window}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}
          <DialogContent>
            <Typography>{action.dialogContent}</Typography>
          </DialogContent>
          <DialogActions>
            <Box sx={{ display: 'flex', marginRight: '20px', gap: 40 }}>
              <Button
                onClick={() => {
                  setDialogOpen(prev => ({ ...prev, [action.name]: false }));
                }}
                sx={{ fontSize: '14px', width: '80px' }}
              >
                No
              </Button>
              <Button
                onClick={() => {
                  setMfaOpen(true);
                  setDialogOpen({
                    panic: false,
                    reset: false,
                    deploy: false,
                  });
                }}
                color='primary'
                variant='contained'
                sx={{ fontSize: '14px', width: '80px' }}
                disabled={
                  action.name === 'deploy' &&
                  (selectedWindow === '' || selectedAllWindow === '')
                }
              >
                Yes
              </Button>
            </Box>
          </DialogActions>
        </Dialog>
      ))}

      <TwoFactorVerification
        open={isMfaOpen}
        onClose={() => setMfaOpen(false)}
        onSubmit={handleMfaSubmit}
      />

      <Dialog
        open={isFinalConfirmOpen}
        onClose={() => setFinalConfirmOpen(false)}
        PaperProps={{ sx: dialogStyles }}
      >
        <Box display='flex' alignItems='center' gap={1}>
          <WarningAmber sx={{ color: 'orange', fontSize: '24px' }} />
          <Typography
            variant='h6'
            sx={{
              fontFamily: 'Roboto',
              fontWeight: 500,
              fontSize: '20px',
              lineHeight: '24px',
              letterSpacing: '0.1px',
            }}
          >
            Final Confirmation
          </Typography>
        </Box>
        <DialogContent>
          <Typography>
            Are you sure you want to proceed with the action?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Box sx={{ display: 'flex', marginRight: '20px', gap: 35 }}>
            <Button
              onClick={() => setFinalConfirmOpen(false)}
              sx={{ fontSize: '11px' }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleFinalConfirm}
              color='primary'
              variant='contained'
              sx={{ fontSize: '11px' }}
            >
              Confirm
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default ResetDeployPanicButtons;
