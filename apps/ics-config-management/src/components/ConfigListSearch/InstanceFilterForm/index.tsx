import { Buffer } from 'buffer';
import React, {
  ChangeEvent,
  KeyboardEvent,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  Box,
  Button,
  Grid,
  IconButton,
  InputAdornment,
  ListSubheader,
  MenuItem,
  TextField,
  Typography,
} from '@mui/material';
import {
  Close as CloseIcon,
  InsertDriveFile as FileIcon,
} from '@mui/icons-material';
import { Controller, ControllerRenderProps, useForm } from 'react-hook-form';
import isEqual from 'lodash/isEqual';
import removeEmpty from '../removeEmpty';
import {
  InstanceView,
  type InstanceFilterFormData,
  type InstanceFilterFormProps,
} from '../types';
import { UserAutocomplete } from './UserAutocomplete';

const InstanceFilterForm = memo(
  ({
    view,
    popupState,
    defaultValues,
    deviceConfigs,
    onFormSubmit,
    onClearFilters,
    approvalFlowStatus,
  }: InstanceFilterFormProps) => {
    const [deviceConfigFilter, setDeviceConfigFilter] = useState<string>('');
    const { control, reset, resetField, handleSubmit, watch } =
      useForm<InstanceFilterFormData>({
        defaultValues,
      });

    const {
      appDescriptorId,
      configFileId,
      createdBy,
      editedBy,
      approvalStatus,
    } = watch();

    // Reset form when defaultValues change (e.g., when filters are cleared from parent)
    useEffect(() => {
      reset(defaultValues);
    }, [defaultValues, reset]);

    const appDescriptorIds = useMemo(() => {
      const currentValue = appDescriptorId?.toString();
      const deviceConfigsWithApplicationName = deviceConfigs?.map(
        deviceConfig => {
          const newDeviceConfig = {
            ...deviceConfig,
            applicationConfigs: deviceConfig.applicationConfigs.map(
              applicationConfig => ({
                ...applicationConfig,
                applicationName: deviceConfig.applicationName,
              })
            ),
          };
          return newDeviceConfig;
        }
      );
      const result = currentValue
        ? deviceConfigsWithApplicationName?.find(
            device => device.appDescriptorId.toString() === currentValue
          )?.applicationConfigs ?? []
        : deviceConfigsWithApplicationName
            ?.map(device => device.applicationConfigs)
            ?.flat() ?? [];

      return result?.filter(deviceConfig =>
        deviceConfigFilter
          ? deviceConfig.configFileName
              ?.toLowerCase()
              .includes(deviceConfigFilter.toLowerCase())
          : true
      );
    }, [deviceConfigs, appDescriptorId, deviceConfigFilter]);

    const handleFilterSubmit = useCallback(
      handleSubmit((values: InstanceFilterFormData) => {
        const encodedCreatedBy = values.createdBy?.id
          ? Buffer.from(JSON.stringify(values.createdBy)).toString('base64')
          : '';
        const encodedEditedBy = values.editedBy?.id
          ? Buffer.from(JSON.stringify(values.editedBy)).toString('base64')
          : '';

        // Determine if "Pending Approval" is selected
        const isPendingApprovalSelected =
          values.approvalStatus === 'Pending Approval';

        // Extract configInstanceIds from approvalFlowStatus array when "Pending Approval" is selected
        const configInstanceIds = isPendingApprovalSelected
          ? approvalFlowStatus
              ?.map(item => item.condition?.configInstanceId)
              .filter(id => id !== undefined && id !== null)
              .join(',') || ''
          : '';

        const searchObject = {
          createdBy: encodedCreatedBy,
          editedBy: encodedEditedBy,
          ...(isPendingApprovalSelected && { configInstanceIds }),
          ...(view === InstanceView.ALL_INSTANCES && {
            appDescriptorId: values.appDescriptorId,
            configFileId: values.configFileId,
          }),
        };

        onFormSubmit(searchObject);
      }),
      [handleSubmit, view, onFormSubmit, approvalFlowStatus]
    );

    const handleResetFilters = useCallback(() => {
      const resetValues = {
        createdBy: null,
        editedBy: null,
        approvalStatus: '',
        ...(view === InstanceView.ALL_INSTANCES && {
          appDescriptorId: '',
          configFileId: '',
        }),
      };
      reset(resetValues);

      if (onClearFilters) {
        onClearFilters();
      } else {
        onFormSubmit(resetValues);
      }
    }, [reset, onClearFilters, onFormSubmit, view]);

    const formHasValues = useMemo(() => {
      const searchObject = removeEmpty({
        appDescriptorId,
        configFileId,
        createdBy,
        editedBy,
        approvalStatus,
      });
      return Boolean(Object.keys(searchObject).length);
    }, [appDescriptorId, configFileId, createdBy, editedBy, approvalStatus]);

    const handleConfigFileIdFilterChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        setDeviceConfigFilter(event.target?.value ?? '');
      },
      [setDeviceConfigFilter]
    );

    return (
      <Box
        component='form'
        method='dialog'
        onSubmit={handleFilterSubmit}
        sx={{
          width: 400,
          pt: 2,
          px: 2,
        }}
      >
        {view === InstanceView.ALL_INSTANCES && (
          <Controller
            name='appDescriptorId'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                data-testid='appConfig'
                label='Application'
                margin='dense'
                fullWidth
                InputProps={
                  appDescriptorId
                    ? {
                        endAdornment: (
                          <InputAdornment position='start'>
                            <Box pr={2}>
                              <IconButton
                                aria-label='Clear'
                                onClick={() => {
                                  reset({
                                    appDescriptorId: '',
                                    configFileId: '',
                                  });
                                }}
                                size='small'
                              >
                                <CloseIcon fontSize='small' />
                              </IconButton>
                            </Box>
                          </InputAdornment>
                        ),
                      }
                    : {}
                }
                onChange={event => {
                  field.onChange(event.target.value);
                  resetField('configFileId');
                }}
                select
                variant='standard'
              >
                {deviceConfigs?.map(deviceConfig => (
                  <MenuItem
                    key={deviceConfig.appDescriptorId}
                    value={deviceConfig.appDescriptorId}
                  >
                    {deviceConfig.applicationName}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />
        )}
        {view === InstanceView.ALL_INSTANCES && (
          <Controller
            name='configFileId'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                data-testid='fileConfig'
                label='File Name'
                disabled={!appDescriptorIds.length}
                margin='dense'
                fullWidth
                InputProps={
                  configFileId
                    ? {
                        endAdornment: (
                          <InputAdornment position='start'>
                            <Box pr={2}>
                              <IconButton
                                aria-label='Clear'
                                onClick={() => {
                                  reset({
                                    configFileId: '',
                                  });
                                }}
                                size='small'
                              >
                                <CloseIcon fontSize='small' />
                              </IconButton>
                            </Box>
                          </InputAdornment>
                        ),
                      }
                    : {}
                }
                select
                SelectProps={{
                  MenuProps: {
                    PaperProps: {
                      sx: {
                        maxHeight: 300,
                      },
                    },
                    MenuListProps: { sx: { pt: 0 } },
                  },
                  onClose: () => {
                    setDeviceConfigFilter('');
                  },
                }}
                variant='standard'
              >
                <ListSubheader
                  sx={{
                    pt: 1,
                  }}
                >
                  <TextField
                    size='small'
                    autoFocus
                    placeholder='Type to filter...'
                    fullWidth
                    onChange={handleConfigFileIdFilterChange}
                    onKeyDown={(keyboardEvent: KeyboardEvent) => {
                      /**
                       * Prevents the other menu items from being
                       * selected when the user presses any key
                       */
                      keyboardEvent.stopPropagation();
                    }}
                  />
                </ListSubheader>
                {appDescriptorIds.map(
                  ({
                    applicationName,
                    configFileName,
                    configFileId: configFileIdValue,
                    schemaType,
                    configFileType,
                  }) => (
                    <MenuItem key={configFileIdValue} value={configFileIdValue}>
                      <Grid container alignItems='center'>
                        <Grid item sx={{ display: 'flex', width: 44 }}>
                          <FileIcon sx={{ color: 'text.secondary' }} />
                        </Grid>
                        <Grid item sx={{ width: 'calc(100% - 44px)' }}>
                          <Typography variant='subtitle2' noWrap>
                            {configFileName ?? ''}
                          </Typography>
                          <Box display='flex' justifyContent='space-between'>
                            <Typography
                              variant='body2'
                              color='text.secondary'
                              noWrap
                            >
                              {applicationName ?? ''}
                            </Typography>
                            <Typography
                              variant='body2'
                              color='text.secondary'
                              noWrap
                            >
                              {schemaType ?? configFileType ?? ''}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </MenuItem>
                  )
                )}
              </TextField>
            )}
          />
        )}
        <Controller
          name='createdBy'
          control={control}
          render={({ field }) => (
            <UserAutocomplete
              field={
                field as ControllerRenderProps<
                  InstanceFilterFormData,
                  'createdBy' | 'editedBy'
                >
              }
              label='Created By'
            />
          )}
        />
        <Controller
          name='editedBy'
          control={control}
          render={({ field }) => (
            <UserAutocomplete
              field={
                field as ControllerRenderProps<
                  InstanceFilterFormData,
                  'createdBy' | 'editedBy'
                >
              }
              label='Edited By'
            />
          )}
        />
        <Controller
          name='approvalStatus'
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              data-testid='approvalStatus'
              label='Status'
              margin='dense'
              fullWidth
              InputProps={
                approvalStatus
                  ? {
                      endAdornment: (
                        <InputAdornment position='start'>
                          <Box pr={2}>
                            <IconButton
                              aria-label='Clear'
                              onClick={() => {
                                resetField('approvalStatus', {
                                  defaultValue: '',
                                });
                              }}
                              size='small'
                            >
                              <CloseIcon fontSize='small' />
                            </IconButton>
                          </Box>
                        </InputAdornment>
                      ),
                    }
                  : {}
              }
              onChange={event => {
                field.onChange(event.target.value);
              }}
              select
              SelectProps={{
                displayEmpty: true,
              }}
              variant='standard'
            >
              <MenuItem key='Pending Approval' value='Pending Approval'>
                Pending Approval
              </MenuItem>
            </TextField>
          )}
        />
        <Box display='flex' gap={1} justifyContent='end'>
          <Button
            disabled={!formHasValues}
            onClick={() => {
              handleResetFilters();
              popupState.close();
            }}
          >
            Clear Filters
          </Button>
          <Button
            onClick={() => {
              popupState.close();
            }}
            type='submit'
          >
            Apply Filters
          </Button>
        </Box>
      </Box>
    );
  },
  (prevProps, nextProps) =>
    isEqual(prevProps.defaultValues, nextProps.defaultValues) &&
    isEqual(prevProps.deviceConfigs, nextProps.deviceConfigs) &&
    prevProps.popupState.isOpen === nextProps.popupState.isOpen
);

export default InstanceFilterForm;

export { InstanceFilterForm };
