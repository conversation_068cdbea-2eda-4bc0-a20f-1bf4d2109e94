import ArticleIcon from '@mui/icons-material/Article';
import { Avatar, styled, Tab, Tabs, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { indigo } from '@mui/material/colors';
import React, { FC, useMemo, ReactNode } from 'react';
import { useLocation } from 'react-router';
import { Link } from 'react-router-dom';

import { buildVersion, branchName } from '../constants/buildInfo';
import { LinkTabProps } from '../constants/types';
import { CONFIG_INSTANCE_TAB_ROOT } from '../constants/routes';
import useHasPermissions from '../hooks/useHasPermissions';
import UserRoles from '../constants/userRoles';
import ResetDeployPanicButtons from './ResetPanicDeployButton';

interface HeaderProps {
  title: string;
  dynamicTitle?: ReactNode;
  eyebrow?: string;
  configVersion?: string;
  icon?: JSX.Element;
  links?: LinkTabProps[];
  action?: JSX.Element;
  activeTab?: string;
  customContent?: ReactNode;
}

interface TabsProps {
  children?: ReactNode;
  value: number;
}

const sharedStyles = {
  height: 32,
  minHeight: 32,
};

const LinkTab = styled((props: LinkTabProps) => {
  const { to, onClick, ...tabProps } = props;

  // If onClick is provided, render as button tab, otherwise as Link tab
  if (onClick) {
    return <Tab wrapped onClick={onClick} {...tabProps} />;
  }

  return <Tab wrapped component={Link} to={to} {...tabProps} />;
})({
  ...sharedStyles,
  padding: 0,
  marginRight: '48px',
  minWidth: 'unset',
  textTransform: 'unset',
  lineHeight: '20px',
  letterSpacing: '0.1px',
});

const LinkTabs = styled((props: TabsProps) => (
  <Tabs {...props} textColor='primary' indicatorColor='primary' />
))({
  ...sharedStyles,
  marginTop: '12px',
});

const Header: FC<HeaderProps> = ({
  title,
  eyebrow,
  configVersion,
  icon,
  links,
  action,
  dynamicTitle,
  activeTab,
  customContent,
}) => {
  const { pathname } = useLocation();

  const currentTab = useMemo(() => {
    if (activeTab) {
      const activeIndex = links?.findIndex(({ key }) => key === activeTab);
      return activeIndex !== -1 ? activeIndex : 0;
    }

    const location = links?.findIndex(({ to, key }) => {
      if (key === 'assignment') return false;
      return to === pathname;
    });
    return location !== -1 ? location : 0;
  }, [links, pathname, activeTab]);

  const isInstanceDeploymentTab = pathname.includes(
    '/remote/config-management/deployment'
  );

  const isSchedulePage = pathname.includes('schedule');

  const hasAdminAccess = useHasPermissions({
    userRoles: [UserRoles.COMPANY_ADMIN, UserRoles.CONFIG_MGMT_DEPLOY],
  });

  return (
    <Box
      alignItems='center'
      display='flex'
      px={2}
      borderBottom='solid 1px'
      borderColor='common.modalBackground'
      bgcolor='common.white'
      position='relative'
    >
      <Box
        sx={{
          position: 'absolute',
          right: '5px',
          top: '2px',
          fontSize: '10px',
          opacity:
            branchName.startsWith('master') || branchName.startsWith('release')
              ? '0'
              : '0.25',
          pointerEvents: 'none',
        }}
      >
        {buildVersion}
      </Box>
      <Box
        alignSelf='baseline'
        alignItems='center'
        sx={{
          width: '280px',
          marginRight: '16px',
        }}
        display='flex'
      >
        <Link to={CONFIG_INSTANCE_TAB_ROOT}>
          <Avatar sx={{ bgcolor: indigo[100], m: 2, ml: 0 }}>
            {icon || <ArticleIcon sx={{ color: 'common.icon' }} />}
          </Avatar>
        </Link>
        {!eyebrow ? (
          <Box>
            <Typography
              component='h1'
              variant='titleLarge'
              display='block'
              noWrap
              sx={{ lineHeight: '1.2', height: 'auto' }}
            >
              {title}
            </Typography>
          </Box>
        ) : (
          <Box padding='16px 0' display='flex' flexDirection='column'>
            <Box
              display='flex'
              gap={3}
              sx={{
                '.MuiTypography-root': {
                  whiteSpace: 'nowrap',
                },
              }}
            >
              {[eyebrow, configVersion].map(item => (
                <Typography
                  key={item}
                  color='primary.light'
                  variant='labelSmall'
                  display='block'
                  title={item}
                >
                  {item}
                </Typography>
              ))}
            </Box>
            {dynamicTitle}
          </Box>
        )}
      </Box>
      <Box display='flex' flexGrow={1} alignItems='center'>
        {links?.length && (
          <LinkTabs value={currentTab}>
            {links?.map((linkProp: LinkTabProps) => (
              <LinkTab key={linkProp.to + linkProp.label} {...linkProp} />
            ))}
          </LinkTabs>
        )}
        {customContent && (
          <Box display='flex' alignItems='center' ml={2} p={2}>
            {customContent}
          </Box>
        )}
      </Box>
      <Box>
        {isInstanceDeploymentTab && hasAdminAccess && (
          <ResetDeployPanicButtons />
        )}
      </Box>
      {!isInstanceDeploymentTab && !isSchedulePage && action}
    </Box>
  );
};

export default Header;
export type { HeaderProps };
