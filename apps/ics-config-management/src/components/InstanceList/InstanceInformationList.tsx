import React from 'react';
import { Typography, Box, CircularProgress } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import DescriptionIcon from '@mui/icons-material/Description';
import DevicesIcon from '@mui/icons-material/Devices';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Instance } from '../../constants/types';
import { AssetIcon, InProgressIcon, MatchedIcon } from '../Icons';

interface InstanceInformationListProps {
  instance: Partial<Instance>;
  statsToNumbers;
  isFetchingConfigInstanceItemStats: Boolean;
  instanceHasAnyAssignments: Boolean;
}

const GridItemStyle = {
  '&.MuiGrid-item': {
    paddingLeft: 0,
    paddingTop: 0,
    padding: '10px',
  },
};

const LoadingSkeleton = () => (
  <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
    <CircularProgress size={20} />
    <Typography
      variant='body2'
      ml={1}
      sx={{
        color: 'black',
      }}
    >
      Loading information...
    </Typography>
  </Box>
);

const InstanceInformationList = ({
  instance,
  statsToNumbers,
  instanceHasAnyAssignments,
  isFetchingConfigInstanceItemStats,
}: InstanceInformationListProps) => {
  const renderAssignmentInfo = () => {
    if (isFetchingConfigInstanceItemStats) {
      return <LoadingSkeleton />;
    }

    if (instanceHasAnyAssignments) {
      return (
        <>
          <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
            <AssetIcon sx={{ fontSize: 'large' }} />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`${statsToNumbers?.deviceCount} Applicable`}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
            <ErrorOutlineIcon color='warning' sx={{ fontSize: 'large' }} />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`${statsToNumbers?.notMatchingDeviceCount} Mismatch`}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
            <InProgressIcon sx={{ fontSize: 'large' }} />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`${statsToNumbers?.deployingDeviceCount} Active Asset`}
            </Typography>
          </Box>
          <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
            <MatchedIcon sx={{ fontSize: 'large' }} />
            <Typography
              variant='body2'
              ml={1}
              sx={{
                color: 'black',
              }}
            >
              {`${statsToNumbers?.matchingDeviceCount} Matching`}
            </Typography>
          </Box>
        </>
      );
    }

    return (
      <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
        <InfoOutlinedIcon sx={{ color: '#009ACE', fontSize: 'large' }} />
        <Typography
          variant='body2'
          ml={1}
          sx={{
            color: 'black',
          }}
        >
          No Information available
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        paddingTop: '20px',
        display: 'grid',
        gridTemplateColumns: {
          xs: 'repeat(auto-fit, minmax(200px, 1fr))',
          sm: 'repeat(auto-fit, minmax(220px, 1fr))',
          md: 'repeat(auto-fit, minmax(240px, 1fr))',
        },
        gap: 2,
        alignItems: 'center',
        '@media (max-width: 600px)': {
          gridTemplateColumns: '1fr',
        },
      }}
    >
      <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
        <CalendarTodayIcon
          sx={{
            fontSize: 'large',
          }}
        />
        <Typography
          variant='body2'
          ml={1}
          sx={{
            color: 'black',
          }}
        >
          {`Updated on ${instance?.stats?.lastUpdated}`}
        </Typography>
      </Box>
      <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
        <LocationOnIcon
          sx={{
            fontSize: 'large',
          }}
        />
        <Typography
          variant='body2'
          ml={1}
          sx={{
            color: 'black',
          }}
        >
          {`${statsToNumbers?.siteCount || 0} Sites`}
        </Typography>
      </Box>
      <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
        <DescriptionIcon
          sx={{
            fontSize: 'large',
          }}
        />
        <Typography
          variant='body2'
          ml={1}
          sx={{
            color: 'black',
          }}
        >
          {`${instance.totalRevision} Revisions`}
        </Typography>
      </Box>
      <Box display='flex' alignItems='center' sx={{ padding: '10px' }}>
        <DevicesIcon
          sx={{
            fontSize: 'large',
          }}
        />
        <Typography
          variant='body2'
          ml={1}
          sx={{
            color: 'black',
          }}
        >
          {/* {`${instance.stats.devices} Devices`} */}
          {`${statsToNumbers?.deviceCount} Devices`}
        </Typography>
      </Box>
      {renderAssignmentInfo()}
    </Box>
  );
};
export { InstanceInformationList };
