import React, {
  MouseEvent,
  useEffect,
  useMemo,
  useState,
  useRef,
  Suspense,
  ReactNode,
} from 'react';
import styled from '@emotion/styled';
import {
  Box,
  CircularProgress,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  Skeleton,
  Tooltip,
  Typography,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import pluralize from 'pluralize';
import { Link as ReactDomLink } from 'react-router-dom';

import { Download } from 'react-bootstrap-icons';
import EditIcon from '@mui/icons-material/Edit';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  useGetConfigInstanceItemStats,
  useGetUserDetails,
} from '../../services/use-query';
import {
  InstanceItemProps,
  ConfigInstanceItemStats,
} from '../../constants/types';
import { getInstanceUrl, hasApprovalRequirement } from '../../utils/helpers';
import {
  DeviceIconWhite,
  SiteIconWhite,
  SiteTagIconWhite,
  TenantIconWhite,
} from '../Icons';
import { getSchemaRendererType } from '../DynamicEditor/utils/getSchemaRendererType';
import useIntersectionObserver from '../../hooks/useIntersectionObserver';
import useToken from '../../hooks/useToken';
import lazyWithPreload from '../../utils/lazyWithPreload';
import { InstanceInformationList } from './InstanceInformationList';

const DeleteConfigInstanceDialog = lazyWithPreload(
  () => import('../DeleteConfigInstanceDialog')
);

DeleteConfigInstanceDialog.preload();

const Container = styled(Box)({
  display: 'flex',
  flexDirection: 'row',
  borderRadius: '16px',
  paddingRight: '16px',
  alignItems: 'center',
  gap: '16px',
});

const Link = styled(ReactDomLink)({
  textDecoration: 'none',
  ':hover': {
    textDecoration: 'none',
  },
});

const Tag = styled(Box)({
  borderRadius: '4px',
  padding: '2px 8px',
  display: 'flex',
  alignItems: 'center',
});

const TagText = styled(props => <Typography {...props} variant='labelSmall' />)(
  {
    lineHeight: 'unset',
    marginLeft: '4px',
    textTransform: 'capitalize',
  }
);

const CreatedByLoadingSkeleton = () => (
  <Box display='inline-flex' alignItems='center' ml={0.5}>
    <Skeleton width={80} height={16} />
  </Box>
);

const CreatedByDisplay = ({
  isLoading,
  isError,
  text,
  hasUserId,
}: {
  isLoading: boolean;
  isError: boolean;
  text: string;
  hasUserId: boolean;
}) => {
  if (isLoading && hasUserId) {
    return <CreatedByLoadingSkeleton />;
  }

  if (isError || !hasUserId) {
    return (
      <Typography
        variant='labelSmall'
        sx={{
          color: isError ? 'error.main' : 'text.secondary',
          fontStyle: 'italic',
          cursor: 'help',
        }}
      >
        {text}
      </Typography>
    );
  }

  return <Typography variant='labelSmall'>{text}</Typography>;
};

const AssignmentTag = ({
  count,
  name,
  icon,
}: {
  count: number;
  name: string;
  icon: ReactNode;
}) =>
  count > 0 && (
    <Tooltip title={`${count} ${pluralize(name, count)} is under assignment`}>
      <Tag
        bgcolor='#045529'
        sx={{
          borderRadius: '12px !important',
          color: 'white !important',
        }}
      >
        <span
          style={{
            color: 'white !important',
          }}
        >
          {icon}
        </span>
        <TagText color='white'>Deployed</TagText>
      </Tag>
    </Tooltip>
  );

const ApprovalTag = ({ configFile }: { configFile: string }) => {
  if (!hasApprovalRequirement(configFile)) return null;

  return (
    <Tooltip title='The configuration file requires approval before any changes are deployed'>
      <Tag
        bgcolor='#005E80'
        sx={{
          borderRadius: '12px !important',
          color: 'white !important',
          display: 'flex',
          alignItems: 'center',
          height: 24,
        }}
      >
        <span
          style={{
            color: 'white !important',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <InfoOutlinedIcon sx={{ fontSize: 14, color: 'white' }} />
        </span>
        <TagText
          color='white'
          sx={{ fontSize: 12, fontWeight: 500 }}
          width='115px'
        >
          Pending Approval
        </TagText>
      </Tag>
    </Tooltip>
  );
};

const InstanceItem = ({
  instance,
  fetchCsv,
  handleShowAssignment,
  approvalFlowStatus,
  refetchApprovalFlowStatus,
  isApprovalFlowDataLoading,
}: InstanceItemProps) => {
  const token = useToken();
  const ref = useRef<HTMLDivElement | null>(null);
  const entry = useIntersectionObserver(ref, { freezeOnceVisible: true });
  const isVisible = !!entry?.isIntersecting;
  const {
    $id: instanceId,
    configFile,
    appName,
    instanceName,
    totalRevision,
    configFileId,
    appDescriptorId,
  } = instance;

  const tenantId = token?.company?.id;

  const {
    data: dataConfigInstanceItemStats,
    isFetching: isFetchingConfigInstanceItemStats,
  } = useGetConfigInstanceItemStats(
    { instanceIds: [instanceId], tenantId },
    {
      cacheTime: 5_000,
      enabled: isVisible,
    }
  );

  const userId = instance?.createdBy;

  const {
    data: userDetails,
    isLoading: isUserDetailsLoading,
    isError: isErrorUserDetails,
    error: errorUserDetails,
  } = useGetUserDetails(userId, isVisible && !!userId);

  const pendingApprovalsByInstanceId = useMemo(() => {
    if (!approvalFlowStatus?.length) return {};
    const pendingMap: Record<string, boolean> = {};

    approvalFlowStatus.forEach(item => {
      const configInstanceId = item.condition?.configInstanceId;
      const isPending = item.status === 'pending';

      if (configInstanceId && isPending) {
        pendingMap[configInstanceId] = true;
      }
    });

    return pendingMap;
  }, [approvalFlowStatus]);

  const stats: ConfigInstanceItemStats = useMemo(() => {
    if (!dataConfigInstanceItemStats || isFetchingConfigInstanceItemStats)
      return null;
    return dataConfigInstanceItemStats.results.find(
      item => item.desiredConfigFileInstanceId === instanceId
    );
  }, [dataConfigInstanceItemStats, isFetchingConfigInstanceItemStats]);

  const statsToNumbers: {
    deviceCount?: number;
    matchingDeviceCount?: number;
    notMatchingDeviceCount?: number;
    deployingDeviceCount?: number;
  } = useMemo(() => {
    if (!stats)
      return {
        deviceCount: 0,
        matchingDeviceCount: 0,
        notMatchingDeviceCount: 0,
        deployingDeviceCount: 0,
      };
    return Object.keys(stats).reduce((acc, key) => {
      if (['desiredConfigFileInstanceId', 'tenantId'].includes(key)) {
        return acc;
      }
      acc[key] = Number(stats[key]);
      return acc;
    }, {});
  }, [stats]);

  const {
    deviceCount = 0,
    matchingDeviceCount = 0,
    notMatchingDeviceCount = 0,
    deployingDeviceCount = 0,
  } = statsToNumbers;

  const instanceHasAnyAssignments = useMemo(
    () =>
      deviceCount > 0 ||
      matchingDeviceCount > 0 ||
      notMatchingDeviceCount > 0 ||
      deployingDeviceCount > 0,
    [deviceCount, matchingDeviceCount, notMatchingDeviceCount]
  );

  const {
    tenants = 0,
    sites = 0,
    siteTags = 0,
    devices = 0,
    lastUpdated,
  } = instance?.stats ?? {};

  const createdOnFormatted = useMemo(() => {
    const createdOnDate = instance?.createdOn;
    const isCreatedDateValid =
      createdOnDate && !Number.isNaN(new Date(createdOnDate).getTime());

    return isCreatedDateValid
      ? new Intl.DateTimeFormat('en-UK', {
          day: 'numeric',
          month: 'short',
          year: '2-digit',
        }).format(new Date(createdOnDate))
      : '';
  }, [instance?.createdOn]);

  const [anchorEl, setAnchorEl] = useState(null);
  const [isSchemaTable, setIsSchemaTable] = useState(false);
  const instanceAssignedValue = [tenants, siteTags, sites, devices];

  useEffect(() => {
    setIsSchemaTable(
      getSchemaRendererType(
        instance.configSchemaRenderType,
        instance.schemaType
      ) === 'table'
    );
  }, [instance]);

  const getCreatedByText = useMemo(() => {
    if (!userId) {
      return 'Unknown User';
    }
    if (isUserDetailsLoading) {
      return 'Loading...';
    }
    if (isErrorUserDetails) {
      const errorMessage = errorUserDetails &&
        typeof errorUserDetails === 'object' &&
        errorUserDetails !== null &&
        'message' in errorUserDetails
        ? (errorUserDetails as { message: string }).message
        : 'Error loading user details';
      
      if (errorMessage === 'User not found' || errorMessage === 'Unknown User') {
        return 'Inactive User';
      }
      
      return errorMessage;
    }
    return userDetails.fullName;
  }, [userId, isUserDetailsLoading, isErrorUserDetails, userDetails?.fullName]);

  const isConfigAssigned = instanceAssignedValue.find(item => item !== 0);

  return (
    <Container bgcolor='common.white' data-testid='InstanceItem' ref={ref}>
      <Grid
        container
        sx={{ padding: '16px' }}
        display='flex'
        flexDirection='column'
      >
        <Grid item xs={4} display='flex' flexDirection='row'>
          <Typography // instance Name
            noWrap
            variant='titleMedium'
            data-testid='instance-name'
            sx={{ paddingRight: 2 }}
            component={Link}
            to={getInstanceUrl(instanceId)}
          >
            {instanceName}
          </Typography>
          <Grid item xs={4} gap={1} alignItems='center' display='flex'>
            {isApprovalFlowDataLoading && <CircularProgress size={20} />}

            {!isApprovalFlowDataLoading &&
              pendingApprovalsByInstanceId[instanceId] && (
                <ApprovalTag configFile={configFile} />
              )}

            {!isApprovalFlowDataLoading &&
              !pendingApprovalsByInstanceId[instanceId] &&
              isConfigAssigned && (
                <>
                  <AssignmentTag
                    icon={<TenantIconWhite fontSize='tag' />}
                    name='tenant'
                    count={tenants}
                  />
                  <AssignmentTag
                    icon={
                      <SiteIconWhite fontSize='tag' sx={{ fill: 'white' }} />
                    }
                    name='site'
                    count={sites}
                  />
                  <AssignmentTag
                    icon={<SiteTagIconWhite fontSize='tag' fill='#FFFFFF' />}
                    name='site tag'
                    count={siteTags}
                  />
                  <AssignmentTag
                    icon={
                      <DeviceIconWhite
                        fontSize='tag'
                        sx={{ '& svg path': { fill: 'white !important' } }}
                      />
                    }
                    name='device'
                    count={devices}
                  />
                </>
              )}

            {!isApprovalFlowDataLoading &&
              !pendingApprovalsByInstanceId[instanceId] &&
              !isConfigAssigned && (
                <Tooltip
                  title='Not Assigned'
                  sx={{
                    height: '20px',
                    borderRadius: '12px !important',
                  }}
                >
                  <Tag bgcolor='grey'>
                    <TagText color='white' width='75px'>
                      Not Assigned
                    </TagText>
                  </Tag>
                </Tooltip>
              )}
          </Grid>
        </Grid>

        <Grid>
          <Box display='flex' alignItems='center' sx={{ paddingTop: '10px' }}>
            <Typography // app name file name
              noWrap
              variant='labelSmall'
              data-testid='instance-title'
              sx={{ paddingRight: '5px' }}
            >
              {`${appName || ''}${configFile ? `, ${configFile}` : ''}${
                lastUpdated
                  ? ` Created on - ${createdOnFormatted}, Created By - `
                  : ''
              }`}
            </Typography>
            {lastUpdated && (
              <CreatedByDisplay
                isLoading={isUserDetailsLoading}
                isError={isErrorUserDetails}
                text={getCreatedByText}
                hasUserId={!!userId}
              />
            )}
          </Box>
        </Grid>

        <Grid>
          <InstanceInformationList
            instance={instance}
            statsToNumbers={statsToNumbers}
            isFetchingConfigInstanceItemStats={
              isFetchingConfigInstanceItemStats
            }
            instanceHasAnyAssignments={instanceHasAnyAssignments}
          />
        </Grid>
      </Grid>

      <Grid display='flex' justifyContent='end' item xs>
        <IconButton
          data-testid='instance-row-actions-button'
          aria-controls='simple-menu'
          aria-haspopup='true'
          onClick={event => {
            event.preventDefault();
            setAnchorEl(event.currentTarget);
          }}
          sx={{
            border: '1px solid lightgrey',
            borderRadius: '10px',
          }}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id='simple-menu'
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={(e: MouseEvent) => {
            e.preventDefault();
            setAnchorEl(null);
          }}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
          transformOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <MenuItem
            data-testid='edit-button'
            component={Link}
            to={getInstanceUrl(instanceId)}
            onClick={() => {
              setAnchorEl(null);
            }}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              textDecoration: 'none',
              color: 'inherit',
              outline: 'none',
            }}
          >
            <EditIcon
              style={{
                fontSize: '15px',
                verticalAlign: 'middle',
              }}
            />
            <span
              style={{
                fontSize: '16px',
                lineHeight: '0.2',
              }}
            >
              Edit
            </span>
          </MenuItem>

          {isSchemaTable && (
            <MenuItem
              data-testid='export-config-instance-button'
              onClick={e => {
                setAnchorEl(null);
                e.preventDefault();
                fetchCsv(instanceId, instanceName);
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
              }}
            >
              <Download
                style={{
                  fontSize: '15px',
                  verticalAlign: 'middle',
                }}
              />
              <span
                style={{
                  fontSize: '16px',
                  lineHeight: '0.2',
                }}
              >
                Export config instance (CSV)
              </span>
            </MenuItem>
          )}
          <Suspense
            fallback={
              <Box sx={{ width: '240px' }}>
                <Skeleton width='100%' height='24px' />
              </Box>
            }
          >
            <DeleteConfigInstanceDialog
              totalRevision={totalRevision}
              isFromConfigList
              instanceId={instanceId}
              onClickAway={() => {
                setAnchorEl(null);
              }}
              handleShowAssignment={handleShowAssignment}
              appDescriptorId={appDescriptorId}
              configFileId={configFileId}
              isApporvalPending={!!pendingApprovalsByInstanceId[instanceId]}
              approvalFlowStatus={approvalFlowStatus}
              refetchApprovalFlowStatus={refetchApprovalFlowStatus}
            />
          </Suspense>
        </Menu>
      </Grid>
    </Container>
  );
};

export default InstanceItem;
