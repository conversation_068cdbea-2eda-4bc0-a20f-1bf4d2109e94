import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  InstanceSortOrder,
  InstanceSortOrderBy,
  InstanceView,
} from '../components/ConfigListSearch/types';
import usePersistedState from './usePersistedState';

interface ConfigListState {
  // Search parameters
  appDescriptorId: string;
  configFileId: string;
  createdBy: string;
  editedBy: string;
  configInstanceIds: string;
  instanceName: string;
  order: string; // Changed from InstanceSortOrder to string for serialization
  orderBy: string; // Changed from InstanceSortOrderBy to string for serialization
  pageNumber: string;
  // View state
  view: string; // Changed from InstanceView to string for serialization
  // Index signature for SerializableObject constraint
  [key: string]: string;
}

interface UsePersistedConfigListStateOptions {
  /**
   * Whether to persist state across browser sessions
   * @default true
   */
  persistAcrossSessions?: boolean;
  /**
   * Whether to sync state across tabs
   * @default true
   */
  syncAcrossTabs?: boolean;
}

const DEFAULT_STATE: ConfigListState = {
  appDescriptorId: '',
  configFileId: '',
  createdBy: '',
  editedBy: '',
  configInstanceIds: '',
  instanceName: '',
  order: InstanceSortOrder.desc as string,
  orderBy: InstanceSortOrderBy.lastUpdated as string,
  pageNumber: '',
  view: InstanceView.ALL_INSTANCES as string,
};

/**
 * Custom hook that combines URL search parameters with persistent state
 * for the ConfigList component. This ensures that filter and view states
 * are preserved across navigation and browser sessions.
 */
function usePersistedConfigListState(
  options: UsePersistedConfigListStateOptions = {}
) {
  const { persistAcrossSessions = true, syncAcrossTabs = true } = options;

  const [urlParams, setUrlParams] = useSearchParams();

  const [persistedState, setPersistedState] =
    usePersistedState<ConfigListState>('configListState', DEFAULT_STATE, {
      storageType: persistAcrossSessions ? 'localStorage' : 'sessionStorage',
      syncAcrossTabs,
    });

  const currentState = useMemo(
    (): ConfigListState => ({
      appDescriptorId: urlParams.get('appDescriptorId') ?? '',
      configFileId: urlParams.get('configFileId') ?? '',
      createdBy: urlParams.get('createdBy') ?? '',
      editedBy: urlParams.get('editedBy') ?? '',
      configInstanceIds: urlParams.get('configInstanceIds') ?? '',
      instanceName: urlParams.get('instanceName') ?? '',
      order:
        (urlParams.get('order') as InstanceSortOrder) ?? InstanceSortOrder.desc,
      orderBy:
        (urlParams.get('orderBy') as InstanceSortOrderBy) ??
        InstanceSortOrderBy.lastUpdated,
      pageNumber: urlParams.get('pageNumber') ?? '',
      view: persistedState.view,
    }),
    [urlParams, persistedState.view]
  );

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      const currentOrder = urlParams.get('order');
      const currentOrderBy = urlParams.get('orderBy');

      if (!currentOrder || !currentOrderBy) {
        const paramsToSet: Record<string, string> = {};

        Array.from(urlParams.entries()).forEach(([key, value]) => {
          if (value !== '') {
            paramsToSet[key] = value;
          }
        });

        paramsToSet.order = persistedState?.order || DEFAULT_STATE.order;
        paramsToSet.orderBy = persistedState?.orderBy || DEFAULT_STATE.orderBy;

        const hasExistingParams = Array.from(urlParams.entries()).some(
          ([, value]) => value !== ''
        );
        if (!hasExistingParams && persistedState) {
          Object.entries(persistedState).forEach(([key, value]) => {
            if (
              key !== 'view' &&
              key !== 'order' &&
              key !== 'orderBy' &&
              value &&
              value !== DEFAULT_STATE[key as keyof ConfigListState]
            ) {
              paramsToSet[key] = String(value);
            }
          });
        }

        setUrlParams(paramsToSet, { replace: true });
      }

      setIsInitialized(true);
    }
  }, [urlParams, persistedState, setUrlParams, isInitialized]);

  useEffect(() => {
    setPersistedState(currentState);
  }, [currentState, setPersistedState]);

  const setParams = useCallback(
    (
      params:
        | Partial<ConfigListState>
        | ((prev: ConfigListState) => Partial<ConfigListState>),
      urlOptions?: { replace?: boolean }
    ) => {
      const paramsToUpdate =
        typeof params === 'function' ? params(currentState) : params;

      const { view, ...urlParamsToUpdate } = paramsToUpdate;

      if (Object.keys(urlParamsToUpdate).length > 0) {
        const currentUrlParams = Object.fromEntries(urlParams.entries());
        const mergedParams = { ...currentUrlParams, ...urlParamsToUpdate };

        const filteredParams = Object.entries(mergedParams).reduce(
          (acc, [key, value]) => {
            const isSortParam = key === 'order' || key === 'orderBy';

            const shouldInclude =
              value !== undefined &&
              value !== null &&
              (isSortParam ||
                (value !== '' &&
                  value !== DEFAULT_STATE[key as keyof ConfigListState]));

            if (shouldInclude) {
              acc[key] = String(value);
            }
            return acc;
          },
          {} as Record<string, string>
        );

        setUrlParams(filteredParams, urlOptions);
      }

      if (view !== undefined) {
        setPersistedState(prev => ({ ...prev, view }));
      }
    },
    [currentState, urlParams, setUrlParams, setPersistedState]
  );

  const setViewInternal = useCallback(
    (view: string) => {
      setPersistedState(prev => ({ ...prev, view }));
    },
    [setPersistedState]
  );

  const setView = useCallback(
    (view: InstanceView) => {
      setViewInternal(view as string);
    },
    [setViewInternal]
  );

  const clearAllState = useCallback(() => {
    setUrlParams({}, { replace: true });
    setPersistedState(DEFAULT_STATE);
  }, [setUrlParams, setPersistedState]);

  const clearFilters = useCallback(() => {
    const currentUrlParams = Object.fromEntries(urlParams.entries());
    const clearedParams = {
      ...currentUrlParams,
      appDescriptorId: '',
      configFileId: '',
      createdBy: '',
      editedBy: '',
      configInstanceIds: '',
      instanceName: '',
      pageNumber: '',
    };

    const filteredParams = Object.entries(clearedParams).reduce(
      (acc, [key, value]) => {
        const isSortParam = key === 'order' || key === 'orderBy';

        const shouldInclude =
          value !== null &&
          value !== undefined &&
          (isSortParam ||
            (value !== '' &&
              value !== DEFAULT_STATE[key as keyof ConfigListState]));

        if (shouldInclude) {
          acc[key] = String(value);
        }
        return acc;
      },
      {} as Record<string, string>
    );

    setUrlParams(filteredParams, { replace: true });
  }, [urlParams, setUrlParams]);

  return {
    appDescriptorId: currentState.appDescriptorId,
    configFileId: currentState.configFileId,
    createdBy: currentState.createdBy,
    editedBy: currentState.editedBy,
    configInstanceIds: currentState.configInstanceIds,
    instanceName: currentState.instanceName,
    pageNumber: currentState.pageNumber,
    order: currentState.order as InstanceSortOrder,
    orderBy: currentState.orderBy as InstanceSortOrderBy,
    view: currentState.view as InstanceView,

    // State setters
    setParams,
    setView,
    clearAllState,
    clearFilters,

    // Raw URL params for compatibility
    urlParams,
    setUrlParams,

    // Persisted state for advanced use cases
    persistedState,
    setPersistedState,
  };
}

export default usePersistedConfigListState;
