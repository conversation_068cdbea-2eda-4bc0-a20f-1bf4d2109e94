{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ics-tms-web-v2": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.single-spa.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": [], "customWebpackConfig": {"path": "extra-webpack.config.js", "libraryName": "ics-tms-web-v2", "libraryTarget": "umd"}, "vendorChunk": true, "allowedCommonJsDependencies": ["zone.js/dist/zone", "semver"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "5mb", "maximumError": "5mb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"production": {"browserTarget": "ics-tms-web-v2:build:production"}, "development": {"browserTarget": "ics-tms-web-v2:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ics-tms-web-v2:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "codeCoverage": true, "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": [], "karmaConfig": "karma.conf.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "af9d60ec-0f20-4a48-8385-72013b1fdeb3", "schematicCollections": ["@angular-eslint/schematics"]}}