module.exports = {
  extends: ['@invenco-cloud-systems-ics/eslint-config'],
  rules: {
    'no-useless-constructor': 0,
    'class-methods-use-this': 0,
    'dot-notation': 0,
    'import/order': ['error', { 'newlines-between': 'ignore' }],
    'no-console': 2,
    'no-debugger': 'error',
    'no-plusplus': 0,
    'import/no-unused-modules': [0],
    'react-hooks/exhaustive-deps': 0,
    'testing-library/await-async-query': 0,
    'testing-library/no-await-sync-query': 0,
    'import/no-extraneous-dependencies': [
      'error',
      {
        devDependencies: [
          '__tests__/**/*.{ts,tsx,js,jsx}',
          'test.{ts,tsx,js,jsx}',
          'test-*.{ts,tsx,js,jsx}',
          '**/*{.,_}{test,spec}.{ts,tsx,js,jsx}',
          '**/jest.config.{ts,js}',
          '**/jest.setup.{ts,js}',
          '**/*.stories.*',
          '**/.storybook/**/*.*',
        ],
      },
    ],
  },
  env: {
    browser: true,
    jest: true,
    jasmine: true,
  },
  globals: {
    describe: true,
    it: true,
  },
};
