:root {
  --bs-border-radius: 0.4rem;
}

.bootstrap-iso {
  .form-control {
    font-size: 1.4rem;
    padding: 0.6rem 1.2rem;
  }

  .form-group {
    margin-bottom: 1.6rem;
  }
  .btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: 500;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 0.1rem solid transparent;
    white-space: nowrap;
    padding: 0.6rem 1.2rem;
    font-size: 1.4rem;
    line-height: 1.42857143;
    border-radius: 0.3rem;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;

    &:hover,
    &:focus {
      color: var(----md-grey-1000);
      text-decoration: none;
    }

    &:active {
      outline: 0;
      background-image: none;
      box-shadow: none;
      border-color: transparent !important;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.65;
      filter: alpha(opacity=65);
      box-shadow: none;
      pointer-events: all;
    }
  }

  .btn-default {
    color: var(----md-grey-1000);
    background-color: var(--md-red-grey-100);
    border-color: var(--md-red-grey-800);
    .badge {
      color: var(--md-red-grey-100);
      background-color: var(----md-grey-1000);
    }
    &:focus {
      color: var(----md-grey-1000);
      background-color: var(--md-red-grey-400);
      border-color: var(--md-red-grey-1200);
    }
    &:hover,
    &:active {
      color: var(----md-grey-1000) !important;
      background-color: var(--md-red-grey-400) !important;
      border-color: var(--md-red-grey-1000) !important;
    }

    &:disabled {
      background-color: var(--md-red-grey-100) !important;
      border-color: var(--md-red-grey-800) !important;
    }
  }

  .btn-primary {
    color: var(--white);
    background-color: var(--md-indigo-600);
    border-color: var(--md-dark-blue-50);
    .badge {
      color: var(--md-indigo-600);
      background-color: var(--white);
    }
    &:focus {
      color: var(--white);
      background-color: var(--md-dark-blue-100);
      border-color: var(--md-dark-blue-400);
    }
    &:hover,
    &:active {
      color: var(--white) !important;
      background-color: var(--md-dark-blue-100) !important;
      border-color: var(--md-dark-blue-300) !important;
    }
    &:disabled {
      background-color: var(--md-indigo-600) !important;
      border-color: var(--md-dark-blue-50) !important;
    }
  }

  .btn-success {
    color: var(--white);
    background-color: var(--md-green-500);
    border-color: var(--md-green-550);
    .badge {
      color: var(--md-green-500);
      background-color: var(--white);
    }
    &:focus {
      color: var(--white);
      background-color: var(--md-green-750);
      border-color: var(--md-green-850);
    }
    &:hover,
    &:active {
      color: var(--white);
      background-color: var(--md-green-750);
      border-color: var(--md-green-720);
    }
    &:disabled {
      background-color: var(--md-green-500) !important;
      border-color: var(--md-green-550) !important;
    }
  }
  .btn-info {
    color: var(--white);
    background-color: var(--md-light-blue-500);
    border-color: var(--md-light-blue-550);
    .badge {
      color: var(--md-light-blue-500);
      background-color: var(--white);
    }
    &:focus {
      color: var(--white);
      background-color: var(--md-light-blue-750);
      border-color: var(--md-light-blue-950);
    }
    &:hover,
    &:active {
      color: var(--white) !important;
      background-color: var(--md-light-blue-750) !important;
      border-color: var(--md-light-blue-850) !important;
    }
    &:disabled {
      background-color: var(--md-light-blue-500) !important;
      border-color: var(--md-light-blue-550) !important;
    }
  }

  .btn-warning {
    color: var(--white);
    background-color: var(--md-orange-500);
    border-color: var(--md-orange-850);
    .badge {
      color: var(--md-orange-500);
      background-color: var(--white);
    }
    &:focus {
      color: var(--white);
      background-color: var(--md-orange-a500);
      border-color: var(--md-orange-a700);
    }
    &:hover,
    &:active {
      color: var(--white) !important;
      background-color: var(--md-orange-a500) !important;
      border-color: var(--md-orange-a600) !important;
    }

    &:disabled {
      background-color: var(--md-orange-500) !important;
      border-color: var(--md-orange-850) !important;
    }
  }

  .btn-danger {
    color: var(--white);
    background-color: var(--md-red-600);
    border-color: var(--md-red-650);
    .badge {
      color: var(--md-red-600);
      background-color: var(--white);
    }
    &:focus {
      color: var(--white);
      background-color: var(--md-red-750);
      border-color: var(--md-red-a500);
    }
    &:hover,
    &:active {
      color: var(--white) !important;
      background-color: var(--md-red-750) !important;
      border-color: var(--md-red-850) !important;
    }
    &:disabled {
      background-color: var(--md-red-600) !important;
      border-color: var(--md-red-650) !important;
    }
  }

  .btn-link {
    color: var(--md-indigo-600);
    border-radius: 0;
    background-color: transparent;
    box-shadow: none;
    border-color: transparent;
    text-decoration: none;
    &:hover,
    &:focus {
      color: var(--md-dark-blue-200);
    }
    &:active {
      color: var(--md-indigo-600) !important;
    }
    &:disabled {
      color: var(----md-grey-1000) !important;
    }
    &-default {
      color: var(----md-grey-1000) !important;
    }
    &-danger {
      color: var(--md-red-600) !important;
      &:hover,
      &:focus {
        background-color: var(--md-black-12);
      }
    }
  }

  .btn-lg,
  .btn-group-lg > .btn {
    padding: 1rem 1.6rem;
    font-size: 1.8rem;
    line-height: 1.3333333;
    border-radius: 0.4rem;
  }
  .btn-sm,
  .btn-group-sm > .btn {
    padding: 0.5rem 1rem;
    font-size: 1.2rem;
    line-height: 1.5;
    border-radius: 0.3rem;
  }
  .btn-xs,
  .btn-group-xs > .btn {
    padding: 0.1rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1.5;
    border-radius: 0.3rem;
  }
  .btn-block {
    display: block;
    width: 100%;
    margin-top: 0.5rem;
  }
  .btn-gray {
    background-color: var(--md-grey-300);
    border-color: var(--md-grey-300);
    color: var(----md-grey-1000);
    &:hover,
    &:focus,
    &:active {
      color: var(--black);
    }
    &:focus,
    &:active {
      background-color: var(--md-grey-350);
      border-color: var(--md-grey-350);
    }
    &:hover {
      background-color: var(--md-grey-370);
      border-color: var(--md-grey-370);
    }
    &.text-danger {
      background-color: var(--md-grey-200);
      border-color: var(--md-grey-200);
      color: var(--md-red-800);
      font-weight: bold;
      &:hover,
      &:focus,
      &:active {
        color: var(--white) !important;
        background-color: var(--md-red-800);
        border-color: var(--md-red-900);
      }
    }
  }
  .btn-dark {
    background-color: var(--md-grey-900);
    border-color: var(--md-grey-900);
    color: var(--white);
    &:hover,
    &:focus,
    &:active {
      color: var(--md-grey-300);
    }
    &:focus,
    &:active {
      background-color: var(--md-red-grey-1500);
      border-color: var(--md-red-grey-1500);
    }
    &:hover {
      background-color: var(--md-red-grey-1600);
      border-color: var(--md-red-grey-1600);
    }
    &.text-danger {
      background-color: var(--md-grey-400);
      border-color: var(--md-grey-400);
      color: var(--md-red-800);
      font-weight: bold;
      &:hover,
      &:focus,
      &:active {
        color: var(--white) !important;
        background-color: var(--md-red-800);
        border-color: var(--md-red-900);
      }
    }
  }
  .btn-box-shadow {
    &,
    &.active,
    &:active {
      box-shadow:
        0 0.1rem 0.2rem var(--md-black-30),
        0 -0.1rem 0.3rem -0.2rem var(--md-black-20);
    }

    &.disabled,
    &[disabled],
    fieldset[disabled] & {
      box-shadow: none;
    }

    &.btn-default {
      border-color: var(--md-red-grey-100);

      &:hover,
      &:focus {
        border-color: var(--md-red-grey-400);
      }
    }
    &.btn-primary {
      border-color: var(--md-indigo-600);
      &:hover,
      &:focus {
        border-color: var(--md-dark-blue-100);
      }
    }
    &.btn-success {
      border-color: var(--md-green-500);

      &:hover,
      &:focus {
        border-color: var(--md-green-750);
      }
    }
  }

  .btn-wide {
    padding-left: 1.25em !important;
    padding-right: 1.25em !important;
  }

  .btn-circle {
    padding: 6px 9px;
    background-color: transparent;
    z-index: 0;
    color: var(--md-grey-600);

    &,
    &:active,
    &.active {
      &:focus,
      &.focus {
        outline: none;
      }
    }

    &:before {
      border-radius: 50%;
      top: -4px;
      left: -4px;
      bottom: -4px;
      right: -4px;
      margin: auto;
      position: absolute;
      content: '';
      width: 30px;
      height: 30px;
      z-index: -1;
    }

    &:focus,
    &:active {
      color: var(--md-grey-900);
      &:before {
        background-color: var(--md-black-12);
      }
    }

    &.btn-lg {
      padding: 10px 13px;
      &:before {
        width: 38px;
        height: 38px;
      }
    }
  }

  .modal-header {
    padding: 1rem 1.5rem;

    .modal-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 500;
    }
  }
  .modal-body {
    font-size: 1.4rem;
    padding: 1rem 1.5rem;
  }
  .modal-footer {
    padding: 1rem 1.5rem;

    .btn {
      margin: 0;
      margin-left: 0.5rem;
    }
  }

  .help-block {
    font-size: 1.4rem;
    .text-normal {
      color: var(--md-grey-800);
      margin-bottom: 0;
    }
    .invalid-feedback {
      font-size: 1.4rem;
      display: block;
      color: var(--md-red-600);
      text-decoration: none;
      transition: none;
    }
  }
  .label-blue {
    background-color: var(--md-blue-500);
  }

  .pointer {
    cursor: pointer;
  }

  .m-auto {
    margin: auto;
  }

  .mb-6 {
    margin-bottom: 0.6rem;
  }

  .mx-15 {
    margin-bottom: 1.5rem;
  }

  .mb-0 {
    margin-bottom: 0;
  }

  .mb-10 {
    margin-bottom: 1rem;
  }

  .mb-12 {
    margin-bottom: 1.2rem;
  }

  .mb-14 {
    margin-bottom: 1.4rem;
  }

  .ms-18 {
    margin-left: 1.8rem;
  }

  .mb-18 {
    margin-bottom: 1.8rem;
  }

  .mt-18 {
    margin-top: 1.8rem;
  }

  .mb-20 {
    margin-bottom: 2rem !important;
  }

  .me-14 {
    margin-right: 1.4rem;
  }

  .mt-0 {
    margin-top: 0;
  }

  .mt-10 {
    margin-top: 1rem;
  }

  .mt-14 {
    margin-top: 1.4rem !important;
  }

  .mt-20 {
    margin-top: 2rem !important;
  }

  .ms-20 {
    margin-left: 2rem !important;
  }

  .ms-50 {
    margin-left: 50% !important;
  }

  .me-20 {
    margin-right: 2rem !important;
  }

  .me-24 {
    margin-right: 2.4rem !important;
  }

  .mt-24 {
    margin-top: 2.4rem !important;
  }

  .me-30 {
    margin-right: 3rem !important;
  }

  .mb-26 {
    margin-bottom: 2.6rem !important;
  }

  .mb-30 {
    margin-bottom: 3rem !important;
  }

  .mb-36 {
    margin-bottom: 3.6rem !important;
  }

  .mt-40 {
    margin-top: 2.5rem !important;
  }

  .mb-40 {
    margin-bottom: 2.5rem !important;
  }

  .ml-15 {
    margin-left: 1.5rem;
  }

  .ml-40 {
    margin-left: 4rem;
  }

  .py-6 {
    padding-top: 0.6rem;
    padding-bottom: 0.6rem;
  }

  .px-7 {
    padding-left: 0.7rem;
    padding-right: 0.7rem;
  }

  .pb-20 {
    padding-bottom: 2rem !important;
  }

  .py-10 {
    padding-top: 1.6rem;
    padding-bottom: 1.6rem;
  }

  .px-10 {
    padding-left: 1.6rem;
    padding-right: 1.6rem;
  }

  .py-12 {
    padding-top: 1.2rem;
    padding-bottom: 1.2rem;
  }

  .pe-14 {
    padding-right: 1.4rem;
  }

  .p-15 {
    padding: 1.5rem;
  }

  .px-15 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .py-18 {
    padding-top: 1.8rem;
    padding-bottom: 1.8rem;
  }

  .px-20 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .py-22 {
    padding-top: 2.2rem;
    padding-bottom: 2.2rem;
  }

  .py-30 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .pe-22 {
    padding-right: 2.2rem;
  }

  .p-30 {
    padding: 3rem;
  }

  .pl-36 {
    padding-left: 3.6rem;
  }

  .pr-36 {
    padding-right: 3.6rem;
  }

  .pl-38 {
    padding-left: 3.8rem;
  }

  .pl-20 {
    padding-left: 2rem;
  }

  .pl-40 {
    padding-left: 4rem;
  }

  .pl-68 {
    padding-left: 6.8rem;
  }

  .modal {
    --bs-modal-width: 100%;
  }

  .ics-input {
    width: 100%;
    height: 3.4rem;
    padding: 0.6rem 1.2rem;
    transition:
      border-color ease-in-out 0.15s,
      box-shadow ease-in-out 0.15s;
    border-radius: 0.3rem;
    background-color: var(--white);
    background-image: none;
    border: 0.1rem solid var(--md-red-grey-600);
    font-size: 1.4rem;

    &:-webkit-autofill {
      -webkit-box-shadow: inset 0 0 0 624.938rem var(--white);
      border-radius: 0.3rem !important;
    }

    &::placeholder {
      opacity: 1;
      color: var(--md-grey-500);
    }

    &:focus {
      box-shadow: inset 0 0 0 0.1rem var(--md-blue-75);
      border-color: var(--md-blue-75);
      outline: 0;
    }
  }

  .cell-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;

    .cell-text {
      width: 100%;

      .cell-heading {
        display: flex;
        align-items: center;
        gap: 1rem;

        .bold {
          font-weight: 600;
        }

        .new-label-tag {
          padding: 0.2rem 0.6rem;
          color: var(--md-indigo-600);
          border-radius: 0.4rem;
          background-color: var(--md-indigo-100);
          font-size: 1.19rem;
          font-weight: 500;
        }
      }
      a {
        color: var(--md-grey-1000);
        &:hover {
          text-decoration: underline !important;
        }
      }

      p {
        margin: 0;
      }

      .address {
        font-size: 1.2rem;
      }
      tr {
        border-bottom: 0.1rem solid var(--md-blue-grey-600);
        &:last-child {
          border-bottom: none;
        }
        td {
          padding: 0.5rem;

          .icon-calendar {
            color: var(--md-grey-500);
          }
          .tooltip {
            .tooltip-arrow {
              transform: translate(0.2rem, -0.3rem) !important;
            }

            .tooltip-inner {
              font-size: 1.2rem;
              transform: translate(0rem, -0.3rem);
            }
          }
        }
        &:first-child {
          td {
            padding-top: 0;
          }
        }
      }
    }

    .cell-image {
      width: 4rem;
      height: 4rem;
      background-color: var(--md-black-60);
      border-radius: 0.4rem;
    }
  }

  .ics-notes-title {
    font-size: 2rem;
    font-weight: 400;
    margin: 0.7rem 0 2rem 0;
    padding-bottom: 1.6rem;
    border-bottom: 0.1rem solid var(--md-red-grey-600);
    line-height: 1;
  }

  .ics-licenses-content {
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      background-color: transparent;
      border: none;
      padding: 0;
      font-size: 1.3rem;
      line-height: 1.42857143;
      table {
        th,
        td {
          padding-right: 1rem;
        }
      }
      .nested {
        padding-left: 2.5rem;
      }
      h1 {
        font-size: 3.6rem;
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-family: inherit;
        font-weight: 500;
        line-height: 1.1;
        color: inherit;
      }
      h2 {
        font-size: 3rem;
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-family: inherit;
        font-weight: 500;
        line-height: 1.1;
        color: inherit;
      }
      a {
        touch-action: manipulation;
        text-decoration: none;
        color: var(--md-indigo-600);
        background-color: transparent;
      }
    }
  }

  .disabled-btn {
    cursor: not-allowed;
    > button {
      pointer-events: none;
      opacity: 0.5;
    }
  }

  .text-muted {
    color: var(--md-grey-700) !important;
  }

  .text-primary {
    color: var(--md-indigo-600) !important;
  }

  .text-danger {
    color: var(--md-red-600) !important;
  }

  .font-bold {
    font-weight: bold;
  }

  .label-success {
    background-color: #4caf50;
  }
  .label {
    display: inline;
    padding: 0.2em 0.6em 0.3em;
    font-size: 75%;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25em;
  }
}
