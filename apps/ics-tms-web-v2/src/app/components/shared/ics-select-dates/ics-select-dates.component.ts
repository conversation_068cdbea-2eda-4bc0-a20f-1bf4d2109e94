import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { Ngb<PERSON>alendar, NgbDate, NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import { JOBS } from '../../../modules/module-site/constants/appConstants';
import { MONTH_NAMES } from 'src/app/constants/datepickerConstant';

@Component({
  selector: 'app-ics-select-dates',
  templateUrl: './ics-select-dates.component.html',
  styleUrls: ['./ics-select-dates.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class IcsSelectDatesComponent implements OnInit {
  @ViewChild('selectDatesDropdown') selectDatesDropdown!: NgbDropdown;

  @ViewChild('selectDatesDropdown', { read: ElementRef })
  clickedOutside!: ElementRef;

  @Input() dateRanges!: string[];

  @Input() useMaxDate!: boolean;

  @Output() selectedEndDate: EventEmitter<string> = new EventEmitter<string>();

  @Output() selectedStartDate: EventEmitter<string> =
    new EventEmitter<string>();

  @Output() clickUpdate: EventEmitter<string> = new EventEmitter<string>();

  @Input() type!: string;

  @HostListener('document:click', ['$event'])
  onClick(event: Event) {
    if (!this.selectDatesDropdown?.isOpen()) {
      return;
    }

    const clickedInside = this.clickedOutside.nativeElement.contains(
      event.target
    );
    if (!clickedInside) {
      this.cancel();
    }
  }

  startDate!: NgbDate;

  hoveredDate: NgbDate | null = null;

  fromDate: NgbDate | null = null;

  toDate: NgbDate | null = null;

  prevFromDate: NgbDate | null = null;

  prevToDate: NgbDate | null = null;

  currSelectedDate = 'Select Dates';

  prevSelectedDate = 'Select Dates';

  selectedDateRange = 'Custom';

  lastDateRangeSelected = 'Custom';

  currentUpdatedDate = 'Select Dates';

  lastUpdateDate = 'Select Dates';

  lastSelectedDate!: NgbDate;

  updatedDate!: NgbDate;

  commonDateRange = '';

  calendar = inject(NgbCalendar);

  ngOnInit() {
    const today = this.calendar.getToday();
    this.visibleMonths(today);

    if (this.type === 'JOBS') {
      this.fromDate = this.calendar.getPrev(today, 'd', 7);
      this.toDate = this.calendar.getNext(today, 'd', 7);

      this.selectedDateRange = 'Custom';
      this.formatSelectedDate();
      this.update();
    }
  }

  get maxDate(): NgbDate {
    return this.type === 'media-reports'
      ? this.calendar.getPrev(this.calendar.getToday())
      : this.calendar.getToday();
  }

  visibleMonths(today: NgbDate) {
    this.startDate = new NgbDate(
      today.month - 1 === 0 ? today.year - 1 : today.year,
      today.month - 1 === 0 ? 12 : today.month - 1,
      1
    );
  }

  onDateSelection(date: NgbDate) {
    if (!this.fromDate && !this.toDate) {
      this.fromDate = date;
    } else if (this.fromDate && !this.toDate && date.after(this.fromDate)) {
      this.toDate = date;
    } else {
      this.toDate = null;
      this.fromDate = date;
    }
    this.selectedDateRange = 'Custom';
    this.formatSelectedDate();
    this.commonDateRange = this.currSelectedDate;
    this.updatedDate = date;
  }

  formatSelectedDate() {
    const format = (date: NgbDate) =>
      `${MONTH_NAMES[date.month - 1]} ${date.day}, ${date.year}`;

    if (this.fromDate && this.toDate) {
      this.currSelectedDate = `${format(this.fromDate)} - ${format(
        this.toDate
      )}`;
    } else if (this.fromDate) {
      this.currSelectedDate = format(this.fromDate);
    } else {
      this.currSelectedDate = 'Select Dates';
    }
    const today = this.calendar.getToday();
    const diffInDays = (date1: NgbDate, date2: NgbDate) =>
      (new Date(date1.year, date1.month - 1, date1.day).getTime() -
        new Date(date2.year, date2.month - 1, date2.day).getTime()) /
      (1000 * 3600 * 24);

    const lastMonth = this.calendar.getPrev(today, 'm');
    const nextMonth = this.calendar.getNext(today, 'm');

    if (this.fromDate && this.toDate) {
      const diff = diffInDays(this.toDate, this.fromDate) + 1;

      if (diff === 1 && this.fromDate.equals(today)) {
        this.commonDateRange = 'Today';
      } else if (
        diff === 1 &&
        this.fromDate.equals(this.calendar.getPrev(today, 'd'))
      ) {
        this.commonDateRange = 'Yesterday';
      } else if (
        diff === 7 &&
        this.fromDate.equals(this.calendar.getPrev(today, 'd', 6))
      ) {
        this.commonDateRange = 'Last 7 Days';
      } else if (
        diff === 14 &&
        this.fromDate.equals(this.calendar.getPrev(today, 'd', 13))
      ) {
        this.commonDateRange = 'Last 14 Days';
      } else if (
        diff === 30 &&
        this.fromDate.equals(this.calendar.getPrev(today, 'd', 29))
      ) {
        this.commonDateRange = 'Last 30 Days';
      } else if (
        diff === 365 &&
        this.fromDate.equals(this.calendar.getPrev(today, 'd', 364))
      ) {
        this.commonDateRange = 'Last 365 Days';
      } else if (
        this.fromDate.equals(new NgbDate(lastMonth.year, lastMonth.month, 1)) &&
        this.toDate.equals(
          new NgbDate(
            lastMonth.year,
            lastMonth.month,
            new Date(lastMonth.year, lastMonth.month, 0).getDate()
          )
        )
      ) {
        this.commonDateRange = 'Last Month';
      } else if (
        this.fromDate.equals(new NgbDate(today.year, today.month, 1)) &&
        this.toDate.equals(
          new NgbDate(
            today.year,
            today.month,
            new Date(today.year, today.month + 1, 0).getDate()
          )
        )
      ) {
        this.commonDateRange = 'This Month';
      } else if (
        this.fromDate.equals(new NgbDate(nextMonth.year, nextMonth.month, 1)) &&
        this.toDate.equals(
          new NgbDate(
            nextMonth.year,
            nextMonth.month,
            new Date(nextMonth.year, nextMonth.month + 2, 0).getDate()
          )
        )
      ) {
        this.commonDateRange = 'Next Month';
      } else if (
        this.fromDate.equals(
          this.calendar.getNext(
            today,
            'd',
            (7 - new Date(today.year, today.month - 1, today.day).getDay()) % 7
          )
        ) &&
        this.toDate.equals(this.calendar.getNext(this.fromDate, 'd', 6))
      ) {
        this.commonDateRange = 'Next Week';
      } else {
        this.commonDateRange = 'Custom';
        if (this.type === JOBS) {
          this.commonDateRange = `${format(this.fromDate)} - ${format(
            this.toDate
          )}`;
        }
      }
      this.selectedDateRange = this.commonDateRange;
    }
  }

  isHovered(date: NgbDate) {
    return (
      this.fromDate &&
      !this.toDate &&
      this.hoveredDate &&
      date.after(this.fromDate) &&
      date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.fromDate) ||
      (this.toDate && date.equals(this.toDate)) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  isSelected(dateRange: string) {
    return this.selectedDateRange === dateRange;
  }

  cancel() {
    this.selectDatesDropdown.close();
    this.currSelectedDate = this.prevSelectedDate;
    this.toDate = this.prevToDate;
    this.fromDate = this.prevFromDate;
    this.selectedDateRange = this.lastDateRangeSelected;
  }

  update() {
    this.getCurrentSelectedDate();
    if (this.fromDate && !this.toDate) {
      this.toDate = this.fromDate;
    }
    this.selectedStartDate.emit(this.formatDateToString(this.fromDate, true));
    this.selectedEndDate.emit(this.formatDateToString(this.toDate, false));
    this.clickUpdate.emit('clicked');
    this.currentUpdatedDate =
      this.selectedDateRange === 'Custom'
        ? this.commonDateRange
        : this.selectedDateRange;
    this.prevFromDate = this.fromDate;
    this.prevToDate = this.toDate;
    this.prevSelectedDate = this.currSelectedDate;
    this.lastDateRangeSelected = this.selectedDateRange;
    this.selectDatesDropdown.close();
  }

  isAnyDateRangeSelected() {
    return this.currSelectedDate === 'Select Dates';
  }

  onDateRangeClick(dateRange: string) {
    let startDate: NgbDate | null = null;
    let endDate: NgbDate = this.calendar.getToday();

    switch (dateRange) {
      case 'Today':
        startDate = this.calendar.getToday();
        this.commonDateRange = 'Today';
        break;
      case 'Yesterday':
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'd');
        endDate = this.calendar.getPrev(this.calendar.getToday(), 'd');
        this.commonDateRange = 'Yesterday';
        this.visibleMonths(endDate);
        break;
      case 'Tomorrow':
        startDate = this.calendar.getNext(this.calendar.getToday(), 'd');
        endDate = this.calendar.getNext(this.calendar.getToday(), 'd');
        this.commonDateRange = 'Tomorrow';
        this.visibleMonths(startDate);
        break;
      case 'Last 7 Days':
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'd', 6);
        this.commonDateRange = 'Last 7 Days';
        this.visibleMonths(endDate);
        break;
      case 'Last 14 Days':
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'd', 13);
        this.commonDateRange = 'Last 14 Days';
        this.visibleMonths(endDate);
        break;
      case 'Last 30 Days':
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'd', 29);
        this.commonDateRange = 'Last 30 Days';
        this.visibleMonths(endDate);
        break;
      case 'Last 90 Days':
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'd', 89);
        this.commonDateRange = 'Last 90 Days';
        this.visibleMonths(endDate);
        break;
      case 'Last Month': {
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'm', 1);
        startDate.day = 1;
        const lastDayOfPreviousMonth = new Date(
          startDate.year,
          startDate.month,
          0
        ).getDate();
        endDate = new NgbDate(
          startDate.year,
          startDate.month,
          lastDayOfPreviousMonth
        );
        this.visibleMonths(endDate);
        this.commonDateRange = 'Last Month';
        break;
      }
      case 'This Month': {
        startDate = new NgbDate(
          this.calendar.getToday().year,
          this.calendar.getToday().month,
          1
        );
        if (!this.useMaxDate) {
          const lastDayOfMonth = new Date(
            this.calendar.getToday().year,
            this.calendar.getToday().month,
            0
          ).getDate();
          endDate = new NgbDate(
            this.calendar.getToday().year,
            this.calendar.getToday().month,
            lastDayOfMonth
          );
        } else {
          endDate = new NgbDate(
            this.calendar.getToday().year,
            this.calendar.getToday().month,
            this.calendar.getToday().day
          );
        }
        this.commonDateRange = 'This Month';
        this.visibleMonths(startDate);
        break;
      }
      case 'This Year': {
        startDate = new NgbDate(this.calendar.getToday().year, 1, 1);
        if (!this.useMaxDate) {
          endDate = new NgbDate(this.calendar.getToday().year, 12, 31);
        } else {
          endDate = new NgbDate(
            this.calendar.getToday().year,
            this.calendar.getToday().month,
            this.calendar.getToday().day
          );
        }
        this.commonDateRange = 'This Year';
        this.visibleMonths(endDate);
        break;
      }
      case 'Last 365 Days':
        startDate = this.calendar.getPrev(this.calendar.getToday(), 'd', 365);
        this.commonDateRange = 'Last 365 Days';
        this.visibleMonths(endDate);
        break;
      case 'Next Week': {
        const today = this.calendar.getToday();
        const currentDayOfWeek = new Date(
          today.year,
          today.month - 1,
          today.day
        ).getDay();
        const daysUntilNextSunday = (7 - currentDayOfWeek) % 7;
        startDate = this.calendar.getNext(today, 'd', daysUntilNextSunday);
        endDate = this.calendar.getNext(startDate, 'd', 6);
        this.commonDateRange = 'Next Week';
        this.visibleMonths(startDate);
        break;
      }
      case 'Next Month': {
        startDate = this.calendar.getNext(this.calendar.getToday(), 'm', 1);
        startDate.day = 1;
        const lastDayOfMonth = new Date(
          startDate.year,
          startDate.month,
          0
        ).getDate();
        endDate = new NgbDate(startDate.year, startDate.month, lastDayOfMonth);
        this.commonDateRange = 'Next Month';
        this.visibleMonths(startDate);
        break;
      }
      case 'Custom':
        this.commonDateRange = 'Select Dates';
        this.fromDate = null;
        this.toDate = null;
        break;

      default:
        startDate = this.calendar.getToday();
    }

    if (dateRange !== 'Custom') {
      this.fromDate = startDate;
      this.toDate = endDate;
      this.formatSelectedDate();
    }
    if (
      dateRange !== 'Last Month' &&
      dateRange !== 'Next Month' &&
      dateRange !== 'This Year'
    ) {
      this.visibleMonths(this.calendar.getToday());
    }
    this.selectedDateRange = dateRange;
  }

  getCurrentSelectedDate() {
    this.currentUpdatedDate = this.commonDateRange;
  }

  formatDateToString(date: NgbDate | null, isStartDate: boolean): string {
    if (date) {
      const { year } = date;
      const month = date.month < 10 ? `0${date.month}` : date.month;
      const day = date.day < 10 ? `0${date.day}` : date.day;
      if (isStartDate) {
        return `${year}-${month}-${day}T00:00:00Z`;
      }
      return `${year}-${month}-${day}T23:59:59Z`;
    }

    return '';
  }
}
