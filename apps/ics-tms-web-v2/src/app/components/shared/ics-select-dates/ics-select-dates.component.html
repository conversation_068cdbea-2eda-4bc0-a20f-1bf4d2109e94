<div class="select-dates-filter">
  <div
    ngbDropdown
    class="d-inline-block reports-dropdown"
    [autoClose]="'outside'"
    #selectDatesDropdown="ngbDropdown"
  >
    <button
      class="btn btn-outline-primary"
      id="dropdownBasic1"
      ngbDropdownToggle
    >
      {{ currentUpdatedDate }}
    </button>
    <div ngbDropdownMenu aria-labelledby="dropdownBasic1" class="back">
      <div class="date-picker">
        <div class="date-picker-main d-flex">
          <div class="date-picker-side-panel">
            <ul class="list-group">
              <li
                *ngFor="let dateRange of dateRanges"
                class="list-group-item d-flex align-items-center"
                (click)="onDateRangeClick(dateRange)"
                (keyup)="onDateRangeClick(dateRange)"
                tabindex="0"
                [ngClass]="{ selected: isSelected(dateRange) }"
              >
                {{ dateRange }}
              </li>
            </ul>
          </div>
          <div class="date-picker-body">
            <ng-container *ngIf="useMaxDate; else noMaxDate">
              <ngb-datepicker
                #dp
                (dateSelect)="onDateSelection($event)"
                [displayMonths]="2"
                [dayTemplate]="t"
                [startDate]="startDate"
                [maxDate]="maxDate"
                outsideDays="hidden"
                class="datepicker"
                [firstDayOfWeek]="7"
              ></ngb-datepicker>
            </ng-container>

            <ng-template #noMaxDate>
              <ngb-datepicker
                #dp
                (dateSelect)="onDateSelection($event)"
                [displayMonths]="2"
                [dayTemplate]="t"
                [startDate]="startDate"
                outsideDays="hidden"
                class="datepicker"
                [firstDayOfWeek]="7"
              ></ngb-datepicker>
            </ng-template>

            <ng-template #t let-date let-focused="focused">
              <span
                class="custom-day"
                [class.focused]="focused"
                [class.range]="isRange(date)"
                [class.faded]="isHovered(date) || isInside(date)"
                (mouseenter)="hoveredDate = date"
                (mouseleave)="hoveredDate = null"
              >
                {{ date.day }}
              </span>
            </ng-template>
          </div>
        </div>
        <div class="date-picker-footer d-flex justify-content-end gap-1">
          <span class="selectedDate">{{ currSelectedDate }}</span>
          <button class="btn cancel" (click)="cancel()">Cancel</button>
          <button
            class="btn update"
            (click)="update()"
            [disabled]="isAnyDateRangeSelected()"
          >
            Update
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
