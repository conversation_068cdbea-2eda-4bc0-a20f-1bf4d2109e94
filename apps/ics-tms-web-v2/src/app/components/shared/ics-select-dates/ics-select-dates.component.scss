.select-dates-filter {
  .reports-dropdown {
    overflow: none;

    &:hover {
      background-color: var(--color-bg-default);
    }

    .btn-outline-primary {
      border-color: var(--color-border);

      &:focus {
        border-color: var(--dropdown-selected-border-hover);
      }

      &:hover {
        border-color: var(--dropdown-border-hover);
      }

      --bs-btn-color: var(--color-black);
      --bs-btn-bg: var(--dropdown-by-default);
      --bs-btn-hover-color: var(--color-black);
      --bs-btn-hover-bg: var(--bs-dropdown-link-hover-bg);
      --bs-btn-active-color: var(--color-black);
      --bs-btn-active-bg: var(--bs-dropdown-link-active-bg);
    }

    .dropdown-toggle {
      &:after {
        vertical-align: middle;
        width: 0;
        height: 0;
        border-top: 0.4rem dashed;
        border-top: 0.4rem solid;
        border-right: 0.4rem solid transparent;
        border-left: 0.4rem solid transparent;
      }

      background-color: var(--dropdown-by-default);
      color: var(--color-black);
      font-size: 1.2rem !important;
      font-weight: 500 !important;
      padding: 0.5rem 1rem !important;

      &:focus {
        background-color: var(--dropdown-bg-selected);
        color: var(--color-black);
      }

      &:hover {
        background-color: var(--dropdown-hover);
        color: var(--color-black);
      }
    }

    .dropdown-menu {
      font-size: 1.4rem;
      overflow-y: auto;
      padding: 0;
      --bs-dropdown-link-hover-bg: var(--color-bg-default);
      --bs-dropdown-link-hover-color: var(--color-black);
      --bs-dropdown-link-active-bg: var(--color-primary);
      --bs-dropdown-link-active-color: var(--color-white);
      box-shadow:
        0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
        0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
        0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
      border: 0.1rem;
    }

    .selected {
      background-color: var(--color-primary);
      color: var(--color-white);
      font-weight: 500;
      border-radius: 0;
    }

    .hr {
      margin: 0;
    }

    .back {
      background-color: var(--dropdown-bg-filter-sites);
    }
  }
}

.date-picker {
  background-color: var(--color-white);

  .date-picker-main {
    .date-picker-side-panel {
      min-width: 13rem;
      padding-top: 1.5rem;
      padding-bottom: 1rem;
      border-right: 0.1rem solid var(--color-border);

      .list-group {
        cursor: pointer;

        .list-group-item {
          font-size: 1.2rem;
          font-weight: 400;
          padding: 0.4rem 1.2rem;
          word-wrap: break-word;
          border: none !important;
          border-radius: 0;
        }
      }
    }

    .date-picker-body {
      padding: 1.5rem;
      padding-top: 2rem;
      overflow: hidden !important;
      width: 100%;

      .datepicker {
        border: none !important;
        margin-left: -0.6rem;
        height: 100%;
        width: 100%;

        .ngb-dp-month-name {
          background-color: var(--color-white);
        }

        .ngb-dp-weekdays {
          background-color: var(--color-white);
        }

        .ngb-dp-header {
          background-color: var(--color-white);
          padding: 0;
        }

        .ngb-dp-content {
          height: 100%;
          width: 100%;
        }

        .ngb-dp-month {
          width: 50%;
          height: 100%;
          transform: translate(0.6rem, -2rem);

          .ngb-dp-month-name {
            font-size: 1.2rem;
            color: var(--color-black);
            height: 3.84rem;
          }

          .ngb-dp-weekday,
          .ngb-dp-week-number {
            text-align: center;
            font-style: normal;
            color: var(--text-color-gray);
          }

          small,
          .small {
            font-size: 1rem;
          }
        }

        .ngb-dp-weekdays {
          margin: 0 0 0 0.6rem;
        }

        .custom-day {
          text-align: center;
          display: inline-block;
          font-size: 1.2rem;
          height: 2.4rem;
          padding: 0.3rem 1rem;
          width: 100%;

          &:hover {
            background-color: var(--color-bg-default);
            color: var(--color-black);
          }

          &.range {
            background-color: var(--color-primary);
            color: var(--color-white);

            &:first-child {
              border-radius: 0.4rem;
            }

            &:last-child {
              border-radius: 0.4rem;
            }
          }

          &:focus {
            background-color: var(--color-primary);
            color: var(--color-white);
          }

          &.faded {
            background-color: var(--color-bg-default);
            color: var(--color-black);
            border-radius: 0 !important;
          }
        }

        ngb-datepicker-navigation-select {
          .form-select {
            display: none;
          }
        }

        .ngb-dp-day {
          height: 2.4rem !important;
          margin: 0.3rem 0rem;
          border-radius: 0.3rem;
          width: 100%;

          :hover {
            border-radius: 0.3rem;
          }

          &.disabled {
            cursor: not-allowed !important;
            pointer-events: inherit !important;
            color: var(--color-btn-disabled);

            .custom-day {
              &:hover {
                color: var(--color-btn-disabled) !important;
              }
            }
          }
          &.hidden {
            display: block !important;
          }
        }

        $days: 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat';

        @for $i from 1 through length($days) {
          .ngb-dp-weekday:nth-child(#{$i}) {
            visibility: hidden;
            width: 100%;
            font-size: 1rem;

            &::before {
              visibility: visible;
              content: nth($days, $i);
            }
          }
        }
      }
    }
  }

  .date-picker-footer {
    padding: 1rem 1.5rem;
    border-top: 0.1rem solid var(--color-border);
    align-items: center;

    .selectedDate {
      font-size: 1.2rem;
      margin: 0 0.7rem 0 0;
    }

    .cancel {
      height: 3rem;
      padding: 0.5rem 1rem;
      border: 0.1rem solid var(--dropdown-border-hover);
      font-size: 1.2rem;
      background-color: var(--dropdown-by-default);
      color: var(--color-black);

      &:hover {
        background-color: var(--dropdown-hover);
        border: 0.1rem solid var(--dropdown-border-hover);
      }
    }

    .update {
      height: 3rem;
      padding: 0.5rem 1rem;
      background-color: var(--color-primary);
      color: var(--color-white);
      border: 0.1rem solid var(--btn-border-color);

      &:hover {
        background-color: var(--active-btn-bg-color);
        border: 0.1rem solid var(--active-btn-border-color);
      }

      &:focus {
        background-color: var(--active-btn-border-color);
        border: 0.1rem solid var(--btn-border-focus);
      }

      font-size: 1.2rem;
    }
  }
}

.ngb-dp-arrow-prev {
  .ngb-dp-arrow-btn {
    .ngb-dp-navigation-chevron {
      width: 0 !important;
      height: 0 !important;
      border-style: inherit;

      transform: rotate(-90deg);
      border-left: 0.6rem solid transparent;
      border-right: 0.6rem solid transparent;
      border-bottom: 0.6rem solid var(--color-black-shade-two);

      &:hover {
        border-bottom: 0.6rem solid var(--color-black);
      }
    }
  }
}

.ngb-dp-arrow-next {
  .ngb-dp-arrow-btn {
    margin: 0;
    padding: 0;

    .ngb-dp-navigation-chevron {
      width: 0 !important;
      height: 0 !important;
      border-style: inherit;

      transform: rotate(90deg);
      border-left: 0.6rem solid transparent;
      border-right: 0.6rem solid transparent;
      border-bottom: 0.6rem solid var(--color-black-shade-two);

      &:hover {
        border-bottom: 0.6rem solid var(--color-black);
      }
    }
  }
}
