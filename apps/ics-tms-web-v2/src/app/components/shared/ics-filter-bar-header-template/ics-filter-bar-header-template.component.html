<div class="ics-filter-bar-header">
  <h1>Devices</h1>
  <div class="icon-list">
    <ng-container>
      <button
        class="btn btn-link link-underline-light link-color import-attributes"
        [routerLink]="'/asset-management/sites/attributes/import-devices'"
        *ngIf="canImportDevices()"
      >
        Import Devices
      </button>

      <div class="button-tooltip">
        <button
          [ngbTooltip]="isSites ? sitesDownloadTooltip : deviceDownloadTooltip"
          [placement]="'bottom-left'"
          class="download-button"
          (click)="onCSVDownload()"
          [openDelay]="100"
          #downloadtooltip="ngbTooltip"
        >
          <i class="material-icons">download</i>
        </button>
      </div>

      <div class="button-tooltip">
        <button
          class="visible"
          [ngbTooltip]="getVisibilityTooltip()"
          [placement]="'bottom-left'"
          (click)="visibilityClicked()"
          [openDelay]="100"
          #visibleTooltip="ngbTooltip"
          triggers="mouseenter:mouseleave"
          [closeDelay]="0"
        >
          <i
            class="fa fa-eye-slash icon visible-icon eye"
            aria-hidden="true"
            [ngClass]="{
              active: !isVisibilityChecked,
              inactive: isVisibilityChecked,
            }"
          ></i>
        </button>
      </div>

      <!--    Filter -->
      <div
        class="dropdown-custom mat-check device-tooltip"
        #filterDropdown
        [ngClass]="{ 'is-filter-disabled': isLoading }"
      >
        <button
          [ngbTooltip]="'Filter ' + headerType + ' by:'"
          [placement]="'bottom'"
          class="btn icon filter-button"
          (click)="toggleMenu()"
          [disableTooltip]="isMenuOpened"
          triggers="hover"
          #filterButton
        >
          <mat-icon>sort</mat-icon>
        </button>

        <div class="dropdown-menu-items" *ngIf="isMenuOpened">
          <p class="filter-sites">Filter {{ headerType }} by:</p>

          <p class="p-header filter-sites">DEVICE STATUS</p>
          <hr class="hr" />

          <div class="innerListItem">
            <a>
              <div class="div-header">
                <mat-icon class="green-icon">check_circle</mat-icon>
                <p class="p-device-status">Operational</p>
              </div>
            </a>
            <div class="div-checkbox">
              <p class="count">
                {{
                  healthData.deviceHealth['OPERATIONAL']
                    ? healthData.deviceHealth['OPERATIONAL']
                    : 0
                }}
              </p>

              <mat-checkbox
                [(ngModel)]="checkboxes['OPERATIONAL DEVICES']"
                (change)="
                  onCheckboxChange('devices', 'Operational', $event.checked)
                "
                [disableRipple]="true"
              >
              </mat-checkbox>
            </div>
          </div>
          <hr class="hr" />

          <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
            <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
              <button
                class="tree-btn"
                disabled
                aria-label="Tree Button"
              ></button>
              <div class="innerListItem">
                <div class="div-header">
                  <p class="p-device-status">{{ node.name }}</p>
                </div>
                <div class="div-checkbox">
                  <p class="count">
                    {{ healthData.oosCondition[node.name] || 0 }}
                  </p>
                  <mat-checkbox
                    [checked]="node.completed"
                    (change)="setAll(node, $event.checked)"
                  >
                  </mat-checkbox>
                </div>
              </div>
            </mat-tree-node>
            <mat-tree-node
              *matTreeNodeDef="let node; when: hasChild"
              matTreeNodePadding
            >
              <button mat-icon-button matTreeNodeToggle class="toggle-btn">
                <mat-icon class="mat-icon-rtl-mirror">
                  {{
                    treeControl.isExpanded(node)
                      ? 'expand_more'
                      : 'chevron_right'
                  }}
                </mat-icon>
              </button>
              <div class="innerListItem no-pl">
                <div class="div-header oos-header">
                  <mat-icon class="oos-icon">{{
                    getValue(node.name)
                  }}</mat-icon>
                  <p class="p-device-status">{{ node.name }}</p>
                </div>
                <div class="div-checkbox">
                  <p class="count">
                    {{
                      healthData.deviceHealth[nameToKey(node.name)] ||
                        healthData.oosCategory[node.name] ||
                        0
                    }}
                  </p>
                  <mat-checkbox
                    [checked]="node.completed"
                    [indeterminate]="someComplete(node)"
                    (change)="setAll(node, $event.checked)"
                    [disableRipple]="true"
                  >
                  </mat-checkbox>
                </div>
              </div>
            </mat-tree-node>
          </mat-tree>

          <hr class="hr" />
          <div *ngFor="let key of ['Unknown', 'Inactive']">
            <div class="innerListItem">
              <div class="div-header">
                <mat-icon [ngClass]="key">{{
                  key === 'Unknown' ? 'cloud_off' : 'remove_circle'
                }}</mat-icon>
                <p class="p-device-status">{{ key }}</p>
              </div>
              <div class="div-checkbox">
                <p class="count">
                  {{
                    healthData.deviceHealth[key.toUpperCase()]
                      ? healthData.deviceHealth[key.toUpperCase()]
                      : 0
                  }}
                </p>
                <mat-checkbox
                  [(ngModel)]="checkboxes[key.toUpperCase() + ' DEVICES']"
                  (change)="onCheckboxChange('devices', key, $event.checked)"
                  [disableRipple]="true"
                >
                </mat-checkbox>
              </div>
            </div>
            <hr class="hr" *ngIf="key !== 'Inactive'" />
          </div>
        </div>
      </div>
    </ng-container>
    <button
      class="btn btn-primary move-device-button"
      (click)="openMoveDevices()"
      *ngIf="canMoveDevice()"
    >
      Move Devices
    </button>
  </div>
</div>

<div class="ics-filter-bar-result">
  <h2>{{ totalResults }} Results found</h2>
</div>
