import { FlatTreeControl } from '@angular/cdk/tree';
import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import {
  MatTreeFlatDataSource,
  MatTreeFlattener,
} from '@angular/material/tree';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import {
  DEVICE_DOWNLOAD_TOOLTIP,
  DEVICE_NOT_VISIBLE_TOOLTIP,
  DEVICE_VISIBLE_TOOLTIP,
  SITE_DOWNLOAD_TOOLTIP,
  SITE_NOT_VISIBLE_TOOLTIP,
  SITE_VISIBLE_TOOLTIP,
} from 'src/app/constants/appConstants';
import { HealthStatus } from 'src/app/constants/health-status';
import { MoveDevicesComponent } from 'src/app/modules/module-devices/components/dialogs/move-devices/move-devices.component';
import { DEVICE } from 'src/app/modules/module-devices/constants/apiConstants';
import { SITE } from 'src/app/modules/module-site/constants/api';
import {
  FiltersArray,
  PARENT_HAVE_CHILD,
  PARENT_HAVE_CHILD_INITIAL,
  SiteStatus,
} from 'src/app/modules/module-site/constants/filter-bar-data';
import {
  ExampleFlatNode,
  OOSNode,
  Task,
} from 'src/app/modules/module-site/models/sites-list.model';
import { SitesListDataService } from 'src/app/modules/module-site/services/sites-list.service';
import { AuthService } from 'src/app/services/auth.service';
import { ToastService } from 'src/app/services/toast.service';
import { loadHealthStatusData } from 'src/app/store/actions/health-status.actions';
import {
  healthStatusLoading,
  healthStatusData,
} from 'src/app/store/selectors/health-status.selectors';

@Component({
  selector: 'app-ics-filter-bar-header-template',
  templateUrl: './ics-filter-bar-header-template.component.html',
  styleUrls: ['./ics-filter-bar-header-template.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class IcsFilterBarHeaderTemplateComponent implements OnInit {
  @ViewChild('visibleTooltip') visibletooltip!: NgbTooltip;

  @ViewChild('downloadtooltip') downloadtooltip!: NgbTooltip;

  @ViewChild('filterButton') filterBtn!: ElementRef;

  @ViewChild('filterDropdown') filterDropdown!: ElementRef<HTMLElement>;

  @Output() getVisibility = new EventEmitter<boolean>();

  @Output() getFiltersArray = new EventEmitter<FiltersArray>();

  @Output() getCSVDownload = new EventEmitter<boolean>();

  @Input() headerType!: string;

  @Input() totalResults!: number | undefined;

  toastService = inject(ToastService);

  isVisibilityChecked = false;

  isMenuOpened = false;

  treeData: OOSNode[] = [];

  selectedSiteEvents: string[] = [];

  selectedStatuses: string[] = [];

  selectedDeviceStatus: string[] = [];

  selectedoutOfService: string[] = [];

  statuses = SiteStatus;

  selectedFilters: FiltersArray = {};

  sitesDownloadTooltip = SITE_DOWNLOAD_TOOLTIP;

  sitesVisibleTooltip = SITE_VISIBLE_TOOLTIP;

  sitesNotVisibleTooltip = SITE_NOT_VISIBLE_TOOLTIP;

  deviceDownloadTooltip = DEVICE_DOWNLOAD_TOOLTIP;

  deviceVisibleTooltip = DEVICE_NOT_VISIBLE_TOOLTIP;

  deviceNotVisibleTooltip = DEVICE_VISIBLE_TOOLTIP;

  isSitesEventParam = false;

  mp = new Map<string, string>();

  healthData!: HealthStatus;

  dataSubscription!: Subscription;

  checkboxes: { [key: string]: boolean } = {};

  isDevices = false;

  isSites = false;

  isRKI = false;

  isLoading = true;

  private modalService = inject(NgbModal);

  treeControl = new FlatTreeControl<ExampleFlatNode>(
    node => node.level,
    node => node.expandable
  );

  // Permissions
  hasSiteAttributesFeature = false;

  hasShellSupportFeature = false;

  hasSitDetailsV2Feature = false;

  hasConfigManagementFeature = false;

  hasCMPublishPermission = false;

  hasCMAssignPermission = false;

  hasCMDeployPermission = false;

  hasAnyConfigPermission = false;

  store = inject(Store);

  route = inject(ActivatedRoute);

  router = inject(Router);

  sitesListDataService = inject(SitesListDataService);

  authService = inject(AuthService);

  constructor() {
    this.mp = this.sitesListDataService.getMap();
    this.dataSource.data = this.sitesListDataService.getTreeData();
    const company = AuthService.getCompany();
    const roles = AuthService.getRole();
    this.hasSiteAttributesFeature = company?.featureFlags?.includes(
      'SITE_ATTRIBUTES'
    ) as boolean;
    this.hasShellSupportFeature = company?.featureFlags?.includes(
      'SHELL_SUPPORT'
    ) as boolean;
    this.hasSitDetailsV2Feature = company?.featureFlags?.includes(
      'SITE_DETAIL_V2'
    ) as boolean;
    this.hasConfigManagementFeature = company?.featureFlags?.includes(
      'CONFIG_MGMT'
    ) as boolean;
    this.hasCMDeployPermission = roles.includes('CONFIG_MGMT_DEPLOY');
    this.hasCMPublishPermission = roles.includes('CONFIG_MGMT_PUBLISH');
    this.hasCMAssignPermission = roles.includes('CONFIG_MGMT_ASSIGN');
    this.hasAnyConfigPermission =
      this.hasCMAssignPermission &&
      this.hasCMDeployPermission &&
      this.hasCMPublishPermission;
  }

  ngOnInit(): void {
    this.isDevices = this.headerType === 'devices';
    this.isSites = this.headerType === 'sites';
    this.isRKI = this.headerType === 'rki';
    if (!this.isRKI) {
      this.store.dispatch(
        loadHealthStatusData({ param: this.isSites ? SITE : DEVICE })
      );
      this.store.select(healthStatusLoading).subscribe(loading => {
        this.isLoading = Boolean(loading);
      });

      this.dataSubscription = this.store
        .pipe(select(healthStatusData))
        .subscribe(data => {
          if (data) this.healthData = data;
        });
    }

    this.route.queryParams.subscribe(params => {
      if (params['showHiddenSites']) {
        this.isVisibilityChecked = params['showHiddenSites'] === 'true';
      }
      if (params['siteStatus']) {
        const siteStatusArray = this.convertToArray(params['siteStatus']);
        siteStatusArray.forEach((key: string) => {
          this.checkboxes[key] = true;
        });
        this.selectedStatuses = siteStatusArray;
        this.selectedFilters.siteStatus = siteStatusArray;
      }
      if (params['deviceStatus']) {
        const deviceStatus = this.convertToArray(params['deviceStatus']);
        deviceStatus.forEach((key: string) => {
          this.checkboxes[`${key} DEVICES`] = true;
        });
        this.markCompletedTrue(deviceStatus);
        this.selectedDeviceStatus = deviceStatus;
        this.selectedFilters.deviceStatus = deviceStatus;
      }
      if (params['siteEvents']) {
        const siteEvents = this.convertToArray(params['siteEvents']);
        siteEvents.forEach((key: string) => {
          this.checkboxes[key] = true;
        });
        this.selectedSiteEvents = siteEvents;
        this.selectedFilters.siteEvents = siteEvents;
      }
      if (params['deviceOOS']) {
        const oosFilter = this.convertToArray(params['deviceOOS']);
        this.markCompletedTrue(oosFilter);
        this.selectedoutOfService = oosFilter;
        this.selectedFilters.outOfService = oosFilter;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['totalResults']) {
      this.totalResults = changes['totalResults'].currentValue;
    }
  }

  markCompletedTrue(filterArray: string[]) {
    if (filterArray.includes('OUT_OF_SERVICE')) {
      this.treeControl.dataNodes.forEach(node => {
        this.setNodeCompleted(node, true);
      });
      return;
    }

    filterArray.forEach(item => {
      const [parentName, childName] = item.split('|');
      PARENT_HAVE_CHILD_INITIAL[parentName] += 1;
      if (
        PARENT_HAVE_CHILD[parentName] === PARENT_HAVE_CHILD_INITIAL[parentName]
      ) {
        const matchingItem = this.treeControl.dataNodes.find(
          entry => entry.name === parentName
        );
        if (matchingItem) {
          this.setNodeCompleted(matchingItem, true);
        }
      }
      const matchingItem = this.treeControl.dataNodes.find(
        entry => entry.name === childName
      );
      if (matchingItem) {
        this.setNodeCompleted(matchingItem, true);
      }
    });
  }

  private setNodeCompleted(node: ExampleFlatNode, completed: boolean) {
    Object.assign(node, { completed });
  }

  nameToKey(name: string): string {
    return name.toUpperCase().replace(/ /g, '_');
  }

  visibilityClicked() {
    this.isVisibilityChecked = !this.isVisibilityChecked;
    this.getVisibility.emit(this.isVisibilityChecked);
    this.visibletooltip.close();

    setTimeout(() => {
      this.visibletooltip.open();
    }, 500);
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: Event): void {
    if (
      this.isMenuOpened &&
      !this.filterDropdown.nativeElement.contains(event.target as Node)
    ) {
      this.closeDropdown();
    }
  }

  onCheckboxChange(type: string, key: string, isChecked: boolean) {
    if (type === 'sites') {
      this.checkboxes[key.toUpperCase()] = isChecked;
      this.insertKey(this.transformFilterName(key), isChecked, type);
    } else if (type === 'devices') {
      this.checkboxes[`${key.toUpperCase()} DEVICES`] = isChecked;
      this.insertKey(this.transformFilterName(key), isChecked, type);
    } else if (type === 'sitesEvents') {
      this.checkboxes[key.replace(' ', '_').toUpperCase()] = isChecked;
      this.insertKey(this.transformFilterName(key), isChecked, type);
    }
  }

  insertKey(filterName: string, isChecked: boolean, type: string) {
    switch (type) {
      case 'sites':
        this.selectedStatuses.includes(filterName)
          ? (this.selectedStatuses = this.selectedStatuses.filter(
              item => item !== filterName
            ))
          : (this.selectedStatuses = [...this.selectedStatuses, filterName]);
        break;
      case 'devices':
        this.selectedDeviceStatus.includes(filterName)
          ? (this.selectedDeviceStatus = this.selectedDeviceStatus.filter(
              item => item !== filterName
            ))
          : (this.selectedDeviceStatus = [
              ...this.selectedDeviceStatus,
              filterName,
            ]);
        break;
      case 'sitesEvents':
        this.selectedSiteEvents.includes(filterName)
          ? (this.selectedSiteEvents = this.selectedSiteEvents.filter(
              item => item !== filterName
            ))
          : (this.selectedSiteEvents = [
              ...this.selectedSiteEvents,
              filterName,
            ]);
        break;
      case 'outOfService':
        this.selectedoutOfService.includes(filterName)
          ? (this.selectedoutOfService = this.selectedoutOfService.filter(
              item => item !== filterName
            ))
          : (this.selectedoutOfService = [
              ...this.selectedoutOfService,
              filterName,
            ]);
        break;
      default:
        break;
    }
  }

  // Tree
  openAddSiteForm() {
    this.router.navigate(['sites', 'add']);
  }

  transformer = (node: Task, level: number) => ({
    expandable: !!node.subtasks && node.subtasks.length > 0,
    name: node.name,
    level,
    completed: node.completed,
    color: node.color,
  });

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    node => node.level,
    node => node.expandable,
    node => node.subtasks
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  hasChild = (_: number, node: ExampleFlatNode) => node.expandable;

  someComplete(node: ExampleFlatNode): boolean {
    const descendants = this.treeControl.getDescendants(node);
    const result = descendants.some(n => n.completed);
    return result && !this.allComplete(node);
  }

  allComplete(node: ExampleFlatNode): boolean {
    const descendants = this.treeControl.getDescendants(node);
    return descendants.every(child => child.completed);
  }

  updateAllComplete(node: ExampleFlatNode) {
    const descendants = this.treeControl.getDescendants(node);
    this.setNodeCompleted(
      node,
      descendants.every(child => child.completed)
    );
    this.updateParentNodes(node);
  }

  updateParentNodes(node: ExampleFlatNode) {
    let parent = this.getParentNode(node);
    while (parent) {
      parent.completed = this.treeControl
        .getDescendants(parent)
        .every(child => child.completed);
      parent = this.getParentNode(parent);
    }
  }

  getParentNode(node: ExampleFlatNode): ExampleFlatNode | null {
    const currentLevel = this.treeControl.getLevel(node);

    if (currentLevel < 1) {
      return null;
    }

    const startIndex = this.treeControl.dataNodes.indexOf(node) - 1;

    for (let i = startIndex; i >= 0; i--) {
      const currentNode = this.treeControl.dataNodes[i];

      if (this.treeControl.getLevel(currentNode) < currentLevel) {
        return currentNode;
      }
    }

    return null;
  }

  getValue(key: string): string {
    const value = this.mp.get(key);
    if (value === undefined) {
      return '';
    }
    return value;
  }

  transformFilterName(name: string): string {
    return name.toUpperCase().replace(/\s+/g, '_');
  }

  setAll(node: ExampleFlatNode, completed: boolean) {
    this.setNodeCompleted(node, completed);

    if (node.expandable) {
      this.treeControl.getDescendants(node).forEach(descendant => {
        this.setNodeCompleted(descendant, completed);
      });
    }

    this.updateParentNodes(node);

    if (node.name === 'Out of Service') {
      this.handleOutOfServiceNode(node, completed);
    } else {
      this.handleNonOutOfServiceNode(node, completed);
    }
  }

  private handleOutOfServiceNode(node: ExampleFlatNode, completed: boolean) {
    this.insertKey(
      this.transformFilterName(node.name),
      node.completed,
      'devices'
    );

    if (completed) {
      this.selectedoutOfService = [];
      this.treeControl.getDescendants(node).forEach(descendant => {
        this.handleDescendantOfOutOfServiceNode(descendant);
      });
    } else {
      this.selectedoutOfService = [];
    }
  }

  private handleDescendantOfOutOfServiceNode(descendant: ExampleFlatNode) {
    if (descendant.expandable) {
      this.treeControl.getDescendants(descendant).forEach(child => {
        const key = `${descendant.name}|${child.name}`;
        this.insertKey(key, true, 'outOfService');
      });
    }
  }

  private handleNonOutOfServiceNode(node: ExampleFlatNode, completed: boolean) {
    this.insertKey(
      this.transformFilterName('Out of Service'),
      false,
      'devices'
    );

    if (completed) {
      this.handleCompletedNonOutOfServiceNode(node);
    } else {
      this.handleDeselectedNonOutOfServiceNode(node);
    }
  }

  private handleCompletedNonOutOfServiceNode(node: ExampleFlatNode) {
    if (node.expandable) {
      this.treeControl.getDescendants(node).forEach(descendant => {
        const key = `${node.name}|${descendant.name}`;
        this.insertKey(key, true, 'outOfService');
      });
    } else {
      const parentName = this.getParentNode(node)?.name ?? '';
      this.insertKey(`${parentName}|${node.name}`, true, 'outOfService');
    }
  }

  private handleDeselectedNonOutOfServiceNode(node: ExampleFlatNode) {
    this.selectedoutOfService = this.selectedoutOfService.filter(
      item => !item.startsWith(node.name) && !item.includes(node.name)
    );
  }

  toggleMenu(): void {
    this.isMenuOpened ? this.closeDropdown() : this.openDropdown();
    if (!this.isMenuOpened) {
      this.filterBtn.nativeElement.blur();
    }
  }

  openDropdown(): void {
    this.isMenuOpened = true;
  }

  closeDropdown(): void {
    this.isMenuOpened = false;
    this.selectedFilters = {};

    if (this.selectedSiteEvents && this.selectedSiteEvents.length > 0) {
      this.selectedFilters.siteEvents = this.selectedSiteEvents;
    }

    if (this.selectedStatuses && this.selectedStatuses.length > 0) {
      this.selectedFilters.siteStatus = this.selectedStatuses;
    }

    if (
      this.selectedoutOfService &&
      this.selectedoutOfService.length > 0 &&
      this.selectedoutOfService.length < 13
    ) {
      this.selectedFilters.outOfService = this.selectedoutOfService;
      this.selectedDeviceStatus = this.selectedDeviceStatus.filter(
        item => item !== 'OUT_OF_SERVICE'
      );
    }

    if (this.selectedDeviceStatus && this.selectedDeviceStatus.length > 0) {
      this.selectedFilters.deviceStatus = this.selectedDeviceStatus;
    }

    this.getFiltersArray.emit(this.selectedFilters);
  }

  onCSVDownload() {
    this.getCSVDownload.emit(true);
    this.downloadtooltip.close();
  }

  convertToArray(item: string | string[]): string[] {
    return Array.isArray(item) ? item : [item];
  }

  openMoveDevices() {
    const modalRef = this.modalService.open(MoveDevicesComponent, {
      windowClass: 'common-menu-popup-modal in move-device-popup',
      container: '#ng-modal-container',
      size: 'lg',
      animation: true,
    });
    modalRef.result.then(res => {
      if (res.success) {
        this.toastService.show({ message: 'Devices Moved' });
        if (res.siteId) {
          this.router.navigate(['/asset-management/sites', res.siteId]);
        }
      }
    });
  }

  getVisibilityTooltip(): string {
    if (this.isSites) {
      return this.isVisibilityChecked
        ? this.sitesVisibleTooltip
        : this.sitesNotVisibleTooltip;
    }
    return this.isVisibilityChecked
      ? this.deviceNotVisibleTooltip
      : this.deviceVisibleTooltip;
  }

  canImportAttributes(): boolean {
    return (
      this.isSites &&
      this.hasSiteAttributesFeature &&
      this.hasSitDetailsV2Feature &&
      this.hasShellSupportFeature &&
      this.hasCMDeployPermission
    );
  }

  canImportBulkEditAttributes(): boolean {
    return (
      this.isSites &&
      this.hasCMDeployPermission &&
      this.hasSiteAttributesFeature &&
      this.hasSitDetailsV2Feature
    );
  }

  canAddSite(): boolean {
    return this.isSites && AuthService.isAllowedAccess('WRITE_SITES');
  }

  canImportDevices(): boolean {
    return (
      this.isDevices &&
      this.hasCMDeployPermission &&
      this.hasSiteAttributesFeature &&
      this.hasSitDetailsV2Feature &&
      this.hasShellSupportFeature
    );
  }

  canMoveDevice(): boolean {
    return this.isDevices && AuthService.isAllowedAccess('WRITE_DEVICES');
  }
}
