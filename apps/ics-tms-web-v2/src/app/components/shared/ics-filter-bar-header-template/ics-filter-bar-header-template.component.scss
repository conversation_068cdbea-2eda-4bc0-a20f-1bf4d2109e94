$classes: (
  normal: var(--label-success),
  warning: var(--label-warning),
  critical: var(--label-danger),
  unknown: var(--label-unknown),
  inactive: var(--label-inactive),
);

.ics-filter-bar-header {
  background: var(--color-white);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 2vh 1vw;
  height: 8vh;
  position: relative;
  z-index: 1;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;

  .mat-icon {
    font-size: 2.1rem;
  }

  h1 {
    font-size: 2.1rem;
    font-weight: 400;
    margin: 0.8rem 0 !important;
    line-height: 1;
    display: inline-block;
  }

  p {
    margin: 0;
  }

  .count {
    margin: 0.3rem 0.8rem 0.1rem 0 !important;
    font-size: 1.2rem;
  }

  .icon-list {
    display: flex;
    align-items: center;
    width: auto;
    gap: 1.2rem;
    padding-top: 0.5vh;

    .device-tooltip {
      .tooltip {
        .tooltip-arrow {
          transform: translate(4.75rem, 1rem) !important;
        }

        .tooltip-inner {
          transform: translate(0rem, 1.1rem) !important;
        }
      }
    }

    .link-color {
      color: var(--color-primary);
      font-weight: 500;
      text-decoration: none;

      &:hover {
        color: var(--link-hover-color);
      }
    }

    .icon {
      cursor: pointer;
    }

    .visible-icon.active {
      color: var(--color-primary);
    }

    .visible-icon.inactive {
      color: var(--visible-icon-inactive-color);
    }

    .dropdown-custom {
      position: relative;

      .tooltip {
        .tooltip-inner {
          width: max-content;
          display: flex;
          font-size: 1.2rem;
          padding: 0.2rem 0.8rem;
        }
      }

      &:focus-within {
        .tooltip {
          opacity: 0;
        }
      }

      button {
        padding: 0;
        min-width: 0;

        &:active {
          background: transparent;
        }
      }

      .tree-btn {
        border: none;
      }

      input {
        margin: 0 0 0.2rem 0.3rem;
      }

      .hr {
        margin: 0;
      }

      b {
        margin: 0 1.6rem;
      }

      .p-header {
        color: var(--color-gray) !important;
        font-size: 1rem !important;
      }

      .custom-status {
        border-radius: 0.2rem !important;
        color: var(--color-white);
        font-size: 1.05rem;
        font-weight: 500;
        width: 7rem;
        height: 2.4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        right: -8rem;

        &.normal {
          background-color: map-get($classes, normal);
        }

        &.warning {
          background-color: map-get($classes, warning);
        }

        &.critical {
          background-color: map-get($classes, critical);
        }

        &.unknown {
          background-color: map-get($classes, unknown);
        }

        &.inactive {
          background-color: map-get($classes, inactive);
        }
      }

      .dropdown-menu-items {
        box-shadow:
          0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
          0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
          0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
        position: absolute;
        font-size: 1.4rem;
        z-index: 2;
        right: -9rem;
        min-width: 32.5rem;
        background-color: var(--color-white);
        border-radius: 0.4rem;
        border: 0.0625 solid var(--dropdown-border-color);
        margin: 0.938rem -4.688rem 0 -10rem;

        .filter-sites {
          font-weight: 500;
          font-size: 1.4rem;
          font-weight: 500;
          line-height: 2.4rem;
          display: flex;
          height: 4.8rem;
          text-indent: 1.6rem;
          letter-spacing: 0.01rem;
          -ms-flex-align: center;
          align-items: center;
        }
      }
    }
  }

  .toggle-btn {
    border: none;
    background-color: var(--color-white);
    margin: 0 1rem 0.3rem 0.8rem;
  }

  .mdc-checkbox__checkmark-path {
    stroke-width: 0.5rem;
  }
}

.move-device-button {
  font-size: 1.2rem;
  padding: 0.5rem 1rem;
}

.div-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.oos-header {
  margin-left: -0.8rem;
}

.mat-icon-rtl-mirror {
  color: var(--dropdown-arrow);
  font-size: 2.1rem;
  margin-top: 1rem;
}

.div-checkbox {
  display: flex;
  align-items: center;
}

.p-device-status {
  font-size: 1.2rem;
}

.tree-node {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tree-expanded {
  padding-left: 0.938rem;
}

.example-tree[_ngcontent-ng-c2607133081]
  .mat-nested-tree-node[_ngcontent-ng-c2607133081]
  div[role='group'][_ngcontent-ng-c2607133081] {
  padding-left: 1.188rem;
}

.example-tree[_ngcontent-ng-c2607133081]
  div[role='group'][_ngcontent-ng-c2607133081]
  > .mat-tree-node[_ngcontent-ng-c2607133081] {
  padding-left: 2rem;
}

.mdc-button {
  min-width: 0;
}

.innerListItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4.8rem;
  width: -webkit-fill-available;
  text-decoration: none;
  color: var(--color-black);
  font-size: 1.05rem;
  padding: 0 1.6rem 0 3.2rem;
}

.mat-mdc-menu-content {
  width: 17.5rem;
}

.mdc-list-item {
  padding: 1.6rem;
}

.green-icon {
  color: var(--label-success);

  svg {
    height: 0.8rem;
    fill: var(--label-success);
  }
}

.Inactive {
  color: var(--color-gray);

  svg {
    fill: var(--color-gray);
  }
}

.Unknown {
  color: var(--unknown-devices);

  svg {
    fill: var(--unknown-devices);
  }
}

.align-center-item {
  display: flex;
  align-content: center;
  align-items: center;
}

.mat-mdc-icon-button.mat-mdc-button-base {
  margin-left: 1.6rem;
  left: -0.6rem;
  --mdc-icon-button-state-layer-size: -2.5rem;
  width: var(--mdc-icon-button-state-layer-size);
  height: var(--mdc-icon-button-state-layer-size);
  padding: 0;
}

.oos-icon {
  color: var(--label-danger);
}

/* Tree */
.example-tree-invisible {
  display: none;
}

.example-tree ul,
.example-tree li {
  margin-top: -0;

  margin-bottom: -0;

  list-style-type: none;
}

/* This padding sets alignment of the nested nodes. */
.example-tree .mat-nested-tree-node div[role='group'] {
  padding-left: -2.5rem;
}

.example-tree div[role='group'] > .mat-tree-node {
  padding-left: -2.5rem;
}

.mat-tree-node {
  min-height: 0;
}

.visible {
  border: transparent;
  background: transparent;
  font-size: 1.4rem;

  .eye {
    transform: scaleX(-1);
  }
}

.download-button {
  background-color: transparent;
  border: transparent;
  color: var(--logo-color-font);
  transition: all ease-in 0.1s;
  border-radius: 0.3rem;
  display: flex;
  margin: 0.7rem 0;
  padding: 0.3rem 0.6rem;

  &:hover {
    color: var(--color-black);
    background-color: rgba(0, 0, 0, 0.26);
  }
}

.mdc-checkbox {
  cursor: default !important;
}

.mdc-checkbox
  .mdc-checkbox__native-control:enabled:checked
  ~ .mdc-checkbox__background,
.mdc-checkbox
  .mdc-checkbox__native-control[data-indeterminate='true']:enabled
  ~ .mdc-checkbox__background {
  border-color: var(--color-default-checkbox) !important;
  background-color: var(--color-default-checkbox) !important;

  &:hover {
    border-color: var(--color-default-checkbox) !important;
    background-color: var(--color-default-checkbox) !important;
  }
}

.mdc-checkbox
  .mdc-checkbox__native-control:enabled:indeterminate
  ~ .mdc-checkbox__background {
  border-color: var(--bs-primary) !important;
  background-color: var(--bs-primary) !important;

  &:hover {
    border-color: var(--color-black) !important;
  }
}

.mdc-checkbox
  .mdc-checkbox__native-control:enabled
  ~ .mdc-checkbox__background
  .mdc-checkbox__mixedmark {
  border-color: var(--color-white) !important;
  border-radius: 0.4rem;
  width: 75%;

  &:hover {
    border-color: var(--color-black);
  }
}

.mat-mdc-checkbox .mdc-checkbox__background {
  border-radius: 0.4rem;
  height: 2rem;
  width: 2rem;
}

:root {
  --mdc-checkbox-disabled-selected-checkmark-color: var(--color-white);
  --mdc-checkbox-selected-focus-state-layer-opacity: 0;
  --mdc-checkbox-selected-hover-state-layer-opacity: 0;
  --mdc-checkbox-selected-pressed-state-layer-opacity: 0;
  --mdc-checkbox-unselected-focus-state-layer-opacity: 0;
  --mdc-checkbox-unselected-hover-state-layer-opacity: 0;
  --mdc-checkbox-unselected-pressed-state-layer-opacity: 0;
}

.filter-button {
  position: relative !important;
  transition: all ease-in 0.1s !important;
  border-radius: 0.3rem !important;
  display: flex !important;
  padding: 0.3rem 0.6rem !important;
  border: none;

  &:focus,
  &:active {
    color: var(--color-black) !important;
    background-color: rgba(0, 0, 0, 0.26) !important;
  }

  &:hover {
    color: var(--color-black);
    background-color: rgba(0, 0, 0, 0.26);
  }
}

.btn-link {
  font-size: 1.2rem;
}

.button-tooltip {
  position: relative;
  .tooltip {
    .tooltip-arrow {
      display: none;
    }

    .tooltip-inner {
      width: max-content;
      display: flex;
      font-size: 1.2rem;
      padding: 0.2rem 0.8rem;
      text-align: start;
      transform: translate(0.2rem, 1.2rem);
      background-color: var(--color-black-shade-one);
    }
  }
}

.no-pl {
  padding-left: 0;
}

.mat-mdc-icon-button {
  --mdc-icon-button-icon-color: inherit;
  --mat-mdc-button-persistent-ripple-color: none !important;
  --mat-mdc-button-ripple-color: none !important;
}

.ics-filter-bar-result {
  background: var(--color-white);
  display: flex;

  h2 {
    font-size: 1.55rem;
    font-weight: 400;
    padding-left: 2rem;
    margin: 0.8rem 0 !important;
    line-height: 1;
    display: inline-block;
  }
}

.move-device-popup {
  .modal-content {
    padding: 0 !important;
  }
}

.is-filter-disabled {
  cursor: not-allowed !important;
  > button {
    pointer-events: none;
  }
}
