.sort-prompt-set-dropdown {
  .btn-default-icon {
    background-color: transparent;
    padding: 0 0.8rem;
    font-size: 2rem;

    &:focus,
    &:active {
      color: var(--md-grey-700) !important;
    }

    &:hover {
      background-color: var(--md-black-26) !important;
      color: var(--md-grey-900) !important;
    }
  }

  .dropdown-toggle {
    &::after {
      display: none;
    }
  }
  .tooltip {
    transform: translate(-1.5rem, 3.6rem);
    width: 8rem;
    .tooltip-arrow {
      transform: translate(3rem, 0rem) !important;
    }
  }
  &.show {
    .btn-default-icon {
      background-color: var(--md-black-26) !important;
      color: var(--md-grey-900) !important;
    }
    .tooltip {
      opacity: 0 !important;
      .tooptip-inner {
        opacity: 0 !important;
      }
    }
  }

  .dropdown-menu {
    font-size: 1.4rem;
    top: 100%;
    left: 0;
    min-width: 18rem;
    padding: 0.313rem 0 !important;
    border: 0.1rem solid transparent;
    border-radius: 0.3rem;
    background-color: var(--white);
    background-clip: padding-box;
    box-shadow:
      0 0.1rem 0.3rem 0 var(--md-black-20),
      0 0.1rem 0.8rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    cursor: pointer;
    list-style-type: none;
    padding: 0;
    margin: 0;

    .filter-heading {
      font-size: 1.2rem;
      color: var(--md-red-grey-a180);
      &:hover {
        background-color: transparent;
        cursor: auto;
      }
    }

    .selected {
      background-color: var(--md-indigo-600);
      color: var(--white);
      &:hover {
        background-color: var(--md-indigo-600);
      }
      &:active {
        background-color: var(--md-indigo-600);
      }
    }
    li {
      position: relative;
      padding: 0.3rem 2rem;
      &:hover {
        background-color: var(--md-blue-grey-500);
      }
      .fa-circle-check {
        margin-left: auto;
      }
    }
  }
}
