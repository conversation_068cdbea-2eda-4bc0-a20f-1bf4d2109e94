import { StepperSelectionEvent } from '@angular/cdk/stepper';
import {
  ChangeDetectorRef,
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store, select } from '@ngrx/store';
import {
  BehaviorSubject,
  Observable,
  Subscription,
  combineLatestWith,
} from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { MatStepper } from '@angular/material/stepper';
import { ModalConfirmationComponent } from './modal-confirmation/modal-confirmation.component';
import { ModalMfaComponent } from './modal-mfa/modal-mfa.component';
import {
  FeatureFlags,
  INSTALL_TYPE,
  PRESENT,
} from 'src/app/constants/appConstants';
import { FILE_DOWNLOADS } from 'src/app/constants/globalConstant';
import { Consumers } from 'src/app/models/consumers.model';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';
import {
  Device,
  DeviceSummary,
  SoftwareParams,
  Approver,
  EntitySetting,
} from 'src/app/models/ics-stepper-template.model';
import { MEDIA } from 'src/app/modules/module-media/constants/appConstants';
import { MediaDownload } from 'src/app/modules/module-media/models/media-download.modal';
import { MediaDownloadsService } from 'src/app/modules/module-media/services/media-downloads.service';
import { SOFTWARE } from 'src/app/modules/modules-remote/constants/appConstant';
import { FileDownloadItemDetail } from 'src/app/modules/modules-remote/models/file-downloads.model';
import { AuthService } from 'src/app/services/auth.service';
import { IcsStepperTemplateService } from 'src/app/services/ics-stepper-template.service';
import { ToastService } from 'src/app/services/toast.service';
import { loadConsumers } from 'src/app/store/actions/consumers.actions';
import { loadDeviceSummaryData } from 'src/app/store/actions/device-summary.actions';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';
import { loadSiteGroups } from 'src/app/store/actions/site-groups.actions';
import {
  setLoadingSoftwareData,
  loadSoftwareData,
} from 'src/app/store/actions/software.actions';
import { loadTags } from 'src/app/store/actions/tags.actions';
import { getConsumersData } from 'src/app/store/selectors/consumers.selector';
import {
  getDeviceSummaryData,
  getDeviceSummaryDataLoading,
} from 'src/app/store/selectors/device-summary.selectors';
import { selectSiteGroupsData } from 'src/app/store/selectors/site-groups.selectors';
import {
  selectSoftwareData,
  selectSoftwareIsLoading,
} from 'src/app/store/selectors/software.selector';
import { fetchEntitySettings } from 'src/app/store/actions/entity-settings.actions';
import { selectEntitySettings } from 'src/app/store/selectors/entity-settings.selectors';
import { getFileDownloadsLimit } from 'src/app/store/selectors/globalStore.selectors';
import { FileDownloadsService } from 'src/app/modules/modules-remote/services/file-downloads.service';
import { SiteGroups } from 'src/app/models/sites-groups.model';

@Component({
  selector: 'app-ics-stepper-template',
  templateUrl: './ics-stepper-template.component.html',
  styleUrls: ['./ics-stepper-template.component.scss'],
})
export class IcsStepperTemplateComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('stepper', { static: false }) stepper!: MatStepper;

  parentForm!: FormGroup;

  activeStepIndex = 0;

  isRetry = false;

  isDisabled = false;

  isInstall = false;

  isFailed = false;

  type!: string;

  isCopy!: boolean;

  mediaItemId!: number;

  consumers$!: Observable<Consumers[]>;

  summary$!: Observable<DeviceSummary[]>;

  softwares$!: Observable<CommonResponseData<Device>>;

  isSoftwaresLoading$!: Observable<boolean>;

  softwareParams!: SoftwareParams;

  siteGroups!: SiteGroups[];

  companySiteGroups!: SiteGroups[];

  installType = INSTALL_TYPE;

  deployments!: FileDownloadItemDetail[];

  siteGroupCompanyMapping: Map<string, string[]> = new Map<string, string[]>();

  fileDeletedShown = false;

  isSummaryDataLoading$!: Observable<boolean>;

  defaultSiteLimit: number = -1;

  defaultTargetLimit: number = -1;

  siteLimit: number = -1;

  targetLimit: number = -1;

  private isFileNotPresentSubject = new BehaviorSubject<boolean>(false);

  isFileNotPresent$ = this.isFileNotPresentSubject.asObservable();

  isFileNotPresent: boolean = false;

  private subGetMediaCopyDetails: Subscription | undefined;

  private subIcsStepperTemplateService: Subscription | undefined;

  private subSoftwares: Subscription | undefined;

  isCopyInitialized: boolean = false;

  downloadEndTime!: number;

  downloadStartTime: Date = new Date();

  availableActions: Array<string> = ['Download Only', 'Download and Install'];

  actionStepPresent: boolean = false;

  defaultActionValue: string = 'Download and Install';

  chosenAction!: string;

  releaseId!: number;

  tenantId: string | undefined;

  selectedCompanyId: string | undefined;

  companyApprovers: Approver[] = [];

  preventAutoNavigation: boolean = false;

  readonly STEP_IDS = {
    DESTINATION_COMPANY: 'destination-company',
    DOWNLOAD_NAME: 'download-name',
    DEVICE_MODE: 'device-mode',
    CHOOSE_FILE: 'choose-file',
    SELECT_DEVICES: 'select-devices',
    ACTION: 'action',
    SCHEDULE: 'schedule',
    APPROVER: 'approver',
  };

  private getDisplayedSteps(): string[] {
    const steps: string[] = [];
    if (this.parentForm && this.parentForm.get('destinationCompanyForm')) {
      steps.push(this.STEP_IDS.DESTINATION_COMPANY);
    }
    steps.push(this.STEP_IDS.DOWNLOAD_NAME);
    if (this.shouldShowDeviceModeSelectionStep()) {
      steps.push(this.STEP_IDS.DEVICE_MODE);
    }
    steps.push(this.STEP_IDS.CHOOSE_FILE, this.STEP_IDS.SELECT_DEVICES);
    if (this.actionStepPresent) {
      steps.push(this.STEP_IDS.ACTION);
    }
    steps.push(this.STEP_IDS.SCHEDULE);
    if (this.shouldShowApproverStep()) {
      steps.push(this.STEP_IDS.APPROVER);
    }
    return steps;
  }

  private getLastStep(): string {
    try {
      if (this.shouldShowApproverStep()) {
        return this.STEP_IDS.APPROVER;
      }
    } catch (error) {
      console.error(
        'Defaulting to schedule step due to unavailable approver data'
      );
    }
    return this.STEP_IDS.SCHEDULE;
  }

  public activeStep: string = '';

  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    private store: Store,
    private route: ActivatedRoute,
    private router: Router,
    private icsStepperTemplateService: IcsStepperTemplateService,
    private mediaDownloadsService: MediaDownloadsService,
    private toastService: ToastService,
    private fileDownloadService: FileDownloadsService,
    private cdr: ChangeDetectorRef
  ) {
    this.initilizeFormData();
  }

  ngOnInit() {
    const featureFlags = AuthService.getFeatureFlags();
    this.actionStepPresent = featureFlags.some(
      ({ key, active }: { key: string; active: boolean }) =>
        key === FeatureFlags.STAGED_SOFTWARE_ROLLOUT && active
    );
    const chosenActionControl = this.actionForm.get('chosenAction');
    if (this.actionStepPresent && !this.isInstall) {
      chosenActionControl?.setValidators([Validators.required]);
    } else {
      chosenActionControl?.clearValidators();
    }
    chosenActionControl?.updateValueAndValidity();
    // set stepper type
    this.route.data.subscribe(data => {
      this.type = data['type'];
      this.isCopy = data['isCopy'];
      const startStep = this.isCopy
        ? this.STEP_IDS.SCHEDULE
        : this.STEP_IDS.DOWNLOAD_NAME;
      this.goToStep(startStep);
    });

    this.isFileNotPresent$.subscribe(value => {
      this.isFileNotPresent = value;
      this.fileDeletedShown = this.isCopy && !this.isFileNotPresent;
    });

    this.store
      .select(getFileDownloadsLimit)
      .subscribe(({ siteLimit, targetLimit }) => {
        this.defaultSiteLimit = siteLimit;
        this.defaultTargetLimit = targetLimit;
      });
    // Check for query parameters
    this.route.queryParams.subscribe(params => {
      this.isFailed = !!params['failed'];

      if (this.actionStepPresent) {
        this.isRetry = !!params['retry'];
        this.isInstall = !!params['install'];
        if (this.isRetry || this.isInstall) {
          this.isDisabled = true;
          this.isCopy = false;
          this.goToStep(this.STEP_IDS.ACTION);
        }
      } else {
        this.isRetry = false;
        this.isInstall = false;
        this.isDisabled = false;
      }
    });

    const tenantId = AuthService.getCompany()?.id;
    this.tenantId = tenantId;
    this.selectedCompanyId = tenantId;
    if (tenantId) {
      this.store.dispatch(fetchEntitySettings({ tenantId }));

      this.store
        .pipe(select(selectEntitySettings))
        .subscribe((settings: EntitySetting[]) => {
          this.siteLimit = this.defaultSiteLimit;
          this.targetLimit = this.defaultTargetLimit;

          // Find site limit setting
          const siteSetting = settings.find(
            (s: EntitySetting) =>
              s.featureName === 'file_downloads' &&
              s.settingName === 'SiteLimit'
          );
          if (siteSetting) {
            const value = JSON.parse(siteSetting.entityValue)?.siteLimit;
            if (value > 0) {
              this.siteLimit = value;
            }
          }

          // Find target limit setting
          const targetSetting = settings.find(
            (s: EntitySetting) =>
              s.featureName === 'file_downloads' &&
              s.settingName === 'TargetLimit'
          );
          if (targetSetting) {
            const value = JSON.parse(targetSetting.entityValue)?.targetLimit;
            if (value > 0) {
              this.targetLimit = value;
            }
          }
        });
    }

    this.mediaItemId = this.route.snapshot.params['id'];

    // set software params
    this.softwareParams = {
      type: this.type === FILE_DOWNLOADS ? SOFTWARE : MEDIA,
    };

    // load consumers data
    this.store.dispatch(loadConsumers());
    this.consumers$ = this.store.pipe(select(getConsumersData));
    this.showDestinationCompanyForm();

    // load siteGroups, tags and deviceTypes data
    this.store.dispatch(loadTags());
    this.store.dispatch(loadSiteGroups());
    this.store.dispatch(loadDeviceTypes({}));

    this.createSiteGroupCompanyMapping();

    // load software data
    this.store.dispatch(loadSoftwareData({ params: this.softwareParams }));
    this.softwares$ = this.store.pipe(select(selectSoftwareData));
    this.isSoftwaresLoading$ = this.store.select(selectSoftwareIsLoading);

    this.store.select(selectSiteGroupsData).subscribe(siteGroups => {
      this.siteGroups = siteGroups;

      const { id } = this.getSelectedCompany();
      if (id) {
        this.companySiteGroups = this.siteGroups.filter(
          siteGroup => siteGroup?.owner.id === id
        );
      }
    });

    // if copy stepper opened
    if (this.mediaItemId) {
      this.subGetMediaCopyDetails = this.mediaDownloadsService
        .getMediaCopyDetails(this.mediaItemId)
        .subscribe({
          next: data => {
            this.isCopyInitialized = true;
            this.initilizeCopyDownloadData(data);
            if (this.isCopy) {
              setTimeout(() => {
                this.goToStep(this.getLastStep());
              }, 500);
            }
          },
          error: error => {
            console.error('Error fetching copy details:', error);
            this.isCopyInitialized = true; // Still show the form even if API fails
            this.toastService.show({
              message:
                'Failed to load copy details. You can still create a new download.',
              type: 'error',
            });
          },
        });
    }
    this.actionForm.get('chosenAction')?.valueChanges.subscribe(value => {
      this.chosenAction = value;
      this.cdr.markForCheck();
    });
    // Reset selected devices and approvers when device mode changes
    this.deviceModeForm.get('deviceMode')?.valueChanges.subscribe(() => {
      this.deviceSelectForm.patchValue({ sites: [], targets: [] });
      this.approvalForm.patchValue({ selectedApprovers: [] });
      // Reset total count when mode changes to ensure proper threshold checks
      this.deviceSelectForm.patchValue({ totalDevicesCount: 0 });
    });
    // Reset approvers when selected devices change
    this.deviceSelectForm.get('targets')?.valueChanges.subscribe(() => {
      this.approvalForm.patchValue({ selectedApprovers: [] });
    });

    this.store.select(getDeviceSummaryData).subscribe(summary => {
      const current =
        this.deviceSelectForm.get('totalDevicesCount')?.value || 0;
      if (!current) {
        const totalCount = (summary || []).reduce(
          (sum, s) => sum + (s.devices?.length || 0),
          0
        );
        this.deviceSelectForm.patchValue({ totalDevicesCount: totalCount });
      }
    });
  }

  ngAfterViewInit(): void {
    if (this.isCopy && !this.preventAutoNavigation) {
      setTimeout(() => {
        this.goToStep(this.getLastStep());
      }, 1200);
    }
  }

  ngOnDestroy(): void {
    if (this.subGetMediaCopyDetails) {
      this.subGetMediaCopyDetails.unsubscribe();
    }
    if (this.subIcsStepperTemplateService) {
      this.subIcsStepperTemplateService.unsubscribe();
    }

    if (this.subSoftwares) {
      this.subSoftwares.unsubscribe();
    }
    this.actionForm.get('chosenAction')?.valueChanges.subscribe(value => {
      this.chosenAction = value;
      this.cdr.markForCheck();
    });
  }

  showDestinationCompanyForm(): void {
    // if there is more than one company, then only show destination company
    this.consumers$.subscribe(companies => {
      if (companies?.length) {
        // add a step/form for destination company
        this.parentForm.addControl(
          'destinationCompanyForm',
          this.fb.group({
            destinationCompany: [null, Validators.required],
            userCompany: [AuthService.getCompany()],
            destinationCompanyApprovers: [[]],
          })
        );

        // subscribe to selected company changes to reset selections, update site groups, and fetch approvers
        const ctrlDestinationCompany =
          this.destinationCompanyForm.get('destinationCompany');
        if (ctrlDestinationCompany) {
          ctrlDestinationCompany.valueChanges.subscribe(company => {
            this.deviceSelectForm.patchValue({ targets: '', sites: '' });
            this.chooseFileForm.patchValue({ chooseFile: '' });
            const selectedId = company?.id;
            if (selectedId) {
              this.selectedCompanyId = selectedId;
              this.companySiteGroups = this.siteGroups.filter(
                sg => sg?.owner.id === selectedId
              );
              this.loadDestinationCompanyApprovers(selectedId);
            }
          });
        }

        if (this.isCopy) {
          this.goToStep(this.STEP_IDS.SCHEDULE);
        } else {
          this.goToStep(this.STEP_IDS.DESTINATION_COMPANY);
        }
      } else {
        // If consumers are null or empty, fetch company approvers
        this.fetchCompanyApprovers();

        if (this.isCopy) {
          this.goToStep(this.STEP_IDS.SCHEDULE);
        } else if (this.isInstall) {
          this.goToStep(this.STEP_IDS.SCHEDULE);
        } else {
          this.goToStep(this.STEP_IDS.DOWNLOAD_NAME);
        }
      }
    });
  }

  initilizeFormData() {
    this.parentForm = this.fb.group({
      // destinationCompanyForm: this.fb
      //   .group({
      //     destinationCompany: [null, Validators.required],
      //   }),
      downloadNameForm: this.fb.group({
        downloadName: ['', [Validators.required, this.noWhitespaceValidator]],
      }),
      deviceModeForm: this.fb.group({
        deviceMode: [null, Validators.required],
      }),
      chooseFileForm: this.fb.group(
        {
          chooseFile: ['', Validators.required],
        },
        { validators: Validators.minLength(1) }
      ),
      deviceSelectForm: this.fb.group({
        sites: [''],
        targets: ['', Validators.required],
        totalDevicesCount: [0],
      }),
      actionForm: this.fb.group({
        chosenAction: [''],
      }),
      scheduleForm: this.fb.group({
        downloadTimeForm: this.fb.group({
          downloadDate: [null, Validators.required],
          downloadTime: ['', Validators.required],
        }),
        installTimeForm: this.fb.group({
          installDate: [null, Validators.required],
          installTime: ['', Validators.required],
        }),
        installType: ['1', Validators.required],
        installWindow: ['6 hours', Validators.required],
        downloadWindow: ['6 hours'],
        installFrequency: ['15 minutes', Validators.required],
      }),
      approvalForm: this.fb.group({
        selectedApprovers: [null, Validators.required],
      }),
    });
  }

  initilizeCopyDownloadData(data: MediaDownload) {
    this.isFileNotPresentSubject.next(false);
    this.store.dispatch(setLoadingSoftwareData({ value: true }));
    this.releaseId = data.id;
    const date = new Date();

    this.destinationCompanyForm?.patchValue({
      destinationCompany: data.createdBy.company,
    });
    this.selectedCompanyId = data.createdBy.company.id;

    this.downloadNameForm.patchValue({
      downloadName: data.name,
    });
    if (this.isInstall) {
      this.downloadEndTime = data?.downloadEndTime ?? 0;
    }

    let selectedInstallDate =
      this.isInstall && this.actionStepPresent
        ? new Date(Math.floor(this.downloadEndTime))
        : new Date();

    const today = new Date();
    if (selectedInstallDate < today) {
      selectedInstallDate = today;
    }

    // Choose File:  select the choosen file from the list of files
    this.subSoftwares = this.softwares$.subscribe(software => {
      if (software?.results?.length) {
        const softwareDetails = software.results.find(
          device => device.id === data.software.id
        );

        const sanitizedSoftwareDetails = { ...softwareDetails };
        if (sanitizedSoftwareDetails.description === 'undefined') {
          delete sanitizedSoftwareDetails.description;
        }

        this.chooseFileForm
          .get('chooseFile')
          ?.setValue(sanitizedSoftwareDetails);

        // Select Devices: summary data, as well get the selected devices and sites
        if (softwareDetails?.deviceType.id) {
          this.store.dispatch(setLoadingSoftwareData({ value: true }));
          this.subIcsStepperTemplateService = this.icsStepperTemplateService
            .getDeployments(this.mediaItemId)
            .subscribe(d => {
              this.deployments = d;

              let status: number | null;

              if (this.isRetry || this.isFailed) {
                status = 4;
              } else if (this.isInstall) {
                status = 3;
              } else {
                status = null;
              }

              if (status !== null) {
                this.deployments = this.deployments.filter(
                  deployment => deployment.status === status
                );
              }

              if (this.isCopy) {
                const deviceCount = this.deployments.length;
                const deviceMode = deviceCount === 1 ? 'single' : 'multiple';
                this.deviceModeForm.get('deviceMode')?.setValue(deviceMode);
              }

              const { deviceType, seqVersion } = softwareDetails;
              if (deviceType.id) {
                this.loadDeviceSummaryData(deviceType.id, seqVersion);
              }

              const allSiteIds = d.map(obj => obj.site.id);
              const siteIds = Array.from(new Set(allSiteIds)).map(id => ({
                id,
              }));

              this.setCompany({ ...softwareDetails }, siteIds);

              this.store.dispatch(setLoadingSoftwareData({ value: false }));

              if (this.isCopy && !this.preventAutoNavigation) {
                setTimeout(() => {
                  this.goToStep(this.getLastStep());
                }, 200);
              }
            });
        } else {
          this.isFileNotPresentSubject.next(true);
          this.store.dispatch(setLoadingSoftwareData({ value: false }));
        }
      }
    });
    if (data.deployAction === 'download') {
      this.actionForm.patchValue({ chosenAction: 'Download Only' });
      this.chosenAction = 'Download Only';
      this.isDisabled = !!(this.isRetry || this.isInstall);
    } else if (data.deployAction === 'downloadAndInstall') {
      this.actionForm.patchValue({ chosenAction: 'Download and Install' });
      this.chosenAction = 'Download and Install';
      this.isDisabled = false;
    }

    this.scheduleForm.patchValue({
      installType: data.deploymentPolicy.id.toString(),
      downloadTimeForm: {
        downloadDate: {
          day: date.getDate(),
          month: date.getMonth() + 1,
          year: date.getFullYear(),
        },
        downloadTime: date,
      },
      installTimeForm: {
        installDate: {
          day: selectedInstallDate.getDate(),
          month: selectedInstallDate.getMonth() + 1,
          year: selectedInstallDate.getFullYear(),
        },
        installTime: selectedInstallDate,
      },
      installWindow: this.convertMilliSecondsToHours(data.installWindow),
      installFrequency: this.convertMilliSecondsToHours(data.installFrequency),
      downloadWindow: this.convertMilliSecondsToHours(data.downloadWindow),
    });
    this.scheduleForm.markAsTouched();
  }

  setCompany(software: Device, siteIds: { id: string }[]) {
    const sanitizedSoftwareDetails = { ...software };
    if (sanitizedSoftwareDetails.description === 'undefined') {
      sanitizedSoftwareDetails.description = '';
    }

    // select company
    if (this.isCopy || this.isRetry || this.isInstall || this.isFailed) {
      this.consumers$
        .pipe(combineLatestWith(this.store.select(getDeviceSummaryData)))
        .pipe(combineLatestWith(this.store.select(selectSiteGroupsData)))
        .subscribe(([[companies, sites], siteGroups]) => {
          this.store.dispatch(setLoadingSoftwareData({ value: true }));

          if (
            companies &&
            companies.length &&
            sites &&
            siteGroups &&
            sites.length &&
            siteGroups.length
          ) {
            const selectedSites = sites.filter(site =>
              siteIds.some(selectedSite => selectedSite.id === site.id)
            );

            if (selectedSites && selectedSites.length) {
              // get unique site group(s)
              const uniqueGroups = Array.from(
                new Map(
                  selectedSites
                    .flatMap(item => item.siteGroups)
                    .map(group => [group.id, group])
                ).values()
              );

              // site group must always be one
              if (uniqueGroups && uniqueGroups.length === 1) {
                const selectedSiteGroup = siteGroups.find(
                  sg => sg.id === uniqueGroups[0].id
                );

                if (selectedSiteGroup && selectedSiteGroup.owner) {
                  if (this.destinationCompanyForm) {
                    // reset destination company, device file and site(s)/device(s)
                    this.destinationCompanyForm.patchValue({
                      destinationCompany: selectedSiteGroup.owner,
                    });

                    if (sanitizedSoftwareDetails) {
                      this.chooseFileForm
                        .get('chooseFile')
                        ?.setValue(sanitizedSoftwareDetails);
                    } else {
                      this.isFileNotPresentSubject.next(true);
                    }

                    // update selected item(s) when there is a company
                    const items = this.getSelectedSitesAndDevices(sites);

                    if (items) {
                      this.deviceSelectForm.patchValue({
                        sites: items.selectedSites,
                        targets: items.selectedDevices,
                      });
                    }
                  }
                }
                this.store.dispatch(setLoadingSoftwareData({ value: false }));
              } else {
                this.store.dispatch(setLoadingSoftwareData({ value: false }));
              }
            } else {
              this.store.dispatch(setLoadingSoftwareData({ value: false }));
            }
          } else {
            this.store.dispatch(setLoadingSoftwareData({ value: false }));
          }

          // update selected item(s) when there is no company
          if (sites && sites.length) {
            const items = this.getSelectedSitesAndDevices(sites);

            if (items) {
              this.deviceSelectForm.patchValue({
                sites: items.selectedSites,
                targets: items.selectedDevices,
              });
            }
          }
        });
    }
  }

  noWhitespaceValidator(control: AbstractControl): ValidationErrors | null {
    const { value } = control;
    if (value && value.trim().length === 0) {
      return { whitespace: true };
    }
    return null;
  }

  get destinationCompanyForm() {
    return this.parentForm.get('destinationCompanyForm') as FormGroup;
  }

  get chooseFileForm() {
    return this.parentForm.get('chooseFileForm') as FormGroup;
  }

  get downloadNameForm() {
    return this.parentForm.get('downloadNameForm') as FormGroup;
  }

  get deviceSelectForm() {
    return this.parentForm.get('deviceSelectForm') as FormGroup;
  }

  get actionForm() {
    return this.parentForm.get('actionForm') as FormGroup;
  }

  get scheduleForm() {
    return this.parentForm.get('scheduleForm') as FormGroup;
  }

  get installTimeForm() {
    return this.scheduleForm.get('installTimeForm') as FormGroup;
  }

  get downloadTimeForm() {
    return this.scheduleForm.get('downloadTimeForm') as FormGroup;
  }

  get installTypeValue() {
    return this.scheduleForm.get('installType') as FormGroup;
  }

  get deviceModeForm() {
    return this.parentForm.get('deviceModeForm') as FormGroup;
  }

  get approvalForm() {
    return this.parentForm.get('approvalForm') as FormGroup;
  }

  getCurrentDateInformation() {
    const date = new Date();
    return {
      date: date.getDate(),
      month: date.getMonth(),
      year: date.getFullYear(),
    };
  }

  convertMilliSecondsToHours(milliseconds: number) {
    if (!milliseconds || milliseconds === null) {
      return '6 hours';
    }
    const hours = milliseconds / 3600000;

    if (hours < 1) {
      const minutes = milliseconds / 60000;
      return `${minutes} minutes`;
    }
    const hourText = hours > 1 ? 'hours' : 'hour';

    return `${hours} ${hourText}`;
  }

  onStepChange(event: StepperSelectionEvent) {
    this.activeStepIndex = event.selectedIndex;
    this.activeStep = this.getDisplayedSteps()[this.activeStepIndex];

    if (
      this.activeStep === this.STEP_IDS.DEVICE_MODE &&
      this.shouldShowDeviceModeSelectionStep()
    ) {
      this.deviceModeForm.markAsTouched();
    } else if (this.activeStep === this.STEP_IDS.CHOOSE_FILE) {
      if (this.chooseFileForm.get('chooseFile')?.value?.id) {
        this.isFileNotPresentSubject.next(false);
      }
      this.preventAutoNavigation = true;
    } else {
      this.preventAutoNavigation = false;
    }

    if (
      this.activeStep === this.STEP_IDS.APPROVER &&
      this.shouldShowApproverStep()
    ) {
      if (
        this.tenantId &&
        (!this.companyApprovers || this.companyApprovers.length === 0)
      ) {
        this.fetchCompanyApprovers();
      }
    }

    if (this.isCopy && !this.isFileNotPresent) {
      this.fileDeletedShown = true;
    }
  }

  handleRoute() {
    if (this.type === FILE_DOWNLOADS) {
      this.router.navigate(['remote', 'downloads']);
    } else {
      this.router.navigate(['media', 'downloads']);
    }
  }

  convertToEpochTime(type = '') {
    let dateInfo;
    let timeInfo;
    if (type !== 'download') {
      dateInfo = this.scheduleForm
        .get('installTimeForm')
        ?.get('installDate')?.value;
      timeInfo = this.scheduleForm
        .get('installTimeForm')
        ?.get('installTime')?.value;
    } else {
      dateInfo = this.scheduleForm
        .get('downloadTimeForm')
        ?.get('downloadDate')?.value;
      timeInfo = this.scheduleForm
        .get('downloadTimeForm')
        ?.get('downloadTime')?.value;
    }
    const dateTime = new Date(
      dateInfo.year,
      dateInfo.month - 1,
      dateInfo.day,
      timeInfo.getHours(),
      timeInfo.getMinutes()
    );

    const epochTime = dateTime.getTime();
    return epochTime;
  }

  getTimeInMilliSeconds(time: string) {
    const splitTime = time.split(' ');
    if (splitTime[1] === 'hours' || splitTime[1] === 'hour')
      return +splitTime[0] * 3600 * 1000;

    return +splitTime[0] * 60 * 1000;
  }

  getFormattedDate(type = '') {
    const selectedDate =
      type !== 'download'
        ? this.installTimeForm.get('installDate')?.value
        : this.downloadTimeForm.get('downloadDate')?.value;

    const date = new Date(
      selectedDate.year,
      selectedDate.month - 1,
      selectedDate.day
    );

    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }

  getActionDetails() {
    const chosenAction = this.actionForm.get('chosenAction')?.value;

    if (this.isInstall && chosenAction === 'Download Only') {
      return {
        showDownloadTime: false,
        showInstallDetails: true,
        showDownloadWindow: false,
      };
    }
    if (
      chosenAction === 'Download Only' ||
      this.chosenAction === 'Download Only'
    ) {
      return {
        showDownloadTime: true,
        showInstallDetails: false,
        showDownloadWindow: true,
      };
    }
    if (
      chosenAction === 'Download and Install' ||
      this.chosenAction === 'Download and Install'
    ) {
      return {
        showDownloadTime: true,
        showInstallDetails: true,
        showDownloadWindow: false,
      };
    }
    return {
      showDownloadTime: false,
      showInstallDetails: true,
      showDownloadWindow: false,
    };
  }

  getTime() {
    let hour = this.installTimeForm.get('installTime')?.value.getHours();
    const minute = this.installTimeForm.get('installTime')?.value.getMinutes();
    const dayNight = this.formatTime(hour);

    if (hour === 0) hour = 12;
    else if (hour > 12) hour %= 12;

    return `${hour}:${minute < 10 ? `0${minute}` : minute} ${dayNight} (UTC+05:30)`;
  }

  formatTime(hour: number): string {
    return hour > 12 ? 'pm' : 'am';
  }

  getPayload() {
    const featureFlags = AuthService.getFeatureFlags();
    const requiresApproval = featureFlags
      ? featureFlags.some(
          ({ key, active }: { key: string; active: boolean }) =>
            key === FeatureFlags.FILE_DOWNLOAD_APPROVAL_REQUIRED && active
        )
      : false;

    const payload: any = {
      name: this.downloadNameForm.get('downloadName')?.value,
      description: '',
      softwareId: this.chooseFileForm.get('chooseFile')?.value.id,
      targets: this.deviceSelectForm.get('targets')?.value,
      sites: this.deviceSelectForm.get('sites')?.value,
      deploymentPolicy: this.scheduleForm.get('installType')?.value,
      type: this.type === FILE_DOWNLOADS ? SOFTWARE : MEDIA,
    };

    if (requiresApproval) {
      payload.approvalRequired = false;
      payload.approvers = null;
      payload.serviceRecipientId = this.tenantId;
    }

    if (this.shouldShowApproverStep()) {
      const approverId = this.approvalForm?.get('selectedApprovers')?.value;
      if (approverId) {
        payload.approvalRequired = true;
        payload.approvers = Array.isArray(approverId)
          ? approverId.map((aid: number) => ({ id: aid }))
          : null;
        payload.serviceRecipientId = this.selectedCompanyId;
      }
    }

    const actionName = this.actionForm.get('chosenAction')?.value;

    if (
      actionName === 'Download Only' &&
      this.actionStepPresent &&
      !this.isInstall
    ) {
      payload.deploymentAction = 'download';
      payload.downloadWindow = this.getTimeInMilliSeconds(
        this.scheduleForm.get('downloadWindow')?.value
      );
      payload.downloadStartTime = this.convertToEpochTime('download');
    } else {
      payload.startTime = this.convertToEpochTime();
      payload.installWindow = this.getTimeInMilliSeconds(
        this.scheduleForm.get('installWindow')?.value
      );
      payload.installFrequency = this.getTimeInMilliSeconds(
        this.scheduleForm.get('installFrequency')?.value
      );
      if (
        actionName === 'Download and Install' &&
        this.actionStepPresent &&
        !this.isInstall
      ) {
        payload.deploymentAction = 'downloadAndInstall';
        payload.downloadStartTime = this.convertToEpochTime('download');
      }
      if (this.isInstall) {
        payload.deploymentAction = 'install';
        payload.releaseId = this.releaseId;
      }
    }

    return payload;
  }

  loadDeviceSummaryData(deviceTypeId: string, seqVersion?: string): void {
    const siteGroupsFilter = this.companySiteGroups
      ?.map(sg => sg.name)
      .join(',');
    const summaryParams: any = {
      isFileDownload: true,
      presence: PRESENT,
      type: deviceTypeId,
      ...(seqVersion ? { seqVersion } : {}),
      ...(siteGroupsFilter ? { siteGroups: siteGroupsFilter } : {}),
    };

    // load summary data
    this.store.dispatch(loadDeviceSummaryData({ params: summaryParams }));
    this.isSummaryDataLoading$ = this.store.select(getDeviceSummaryDataLoading);
  }

  handleCreatePackage(): void {
    const selectedSites: number =
      this.deviceSelectForm.get('sites')?.value.length;
    const selectedTargets: number =
      this.deviceSelectForm.get('targets')?.value.length;

    const refConfirmationModal = this.modalService.open(
      ModalConfirmationComponent,
      {
        container: '#ng-modal-container',
        windowClass: 'modal-confirm-create-copy-download modal-prompt in',
        centered: true,
        size: 'sm',
      }
    );
    refConfirmationModal.componentInstance.siteCount = selectedSites;
    refConfirmationModal.componentInstance.deviceCount = selectedTargets;
    refConfirmationModal.componentInstance.isCopy = this.isCopy;
    refConfirmationModal.componentInstance.textContent = 'Are you sure?';
    refConfirmationModal.componentInstance.buttons = [
      { label: 'Cancel', cancel: true, class: 'btn btn-secondary' },
      { label: 'Ok', primary: true, class: 'btn btn-primary' },
    ];

    refConfirmationModal.result.then((result: boolean): void => {
      if (result) {
        this.fileDownloadService.logConfirmation({
          siteCount: selectedSites ?? 0,
          targetCount: selectedTargets ?? 0,
        });

        const payload = this.getPayload();

        if (
          selectedSites >= this.siteLimit ||
          selectedTargets >= this.targetLimit
        ) {
          const refMfaModal = this.modalService.open(ModalMfaComponent, {
            container: '#ng-modal-container',
            windowClass: 'modal-multi-factor-authentication modal-prompt in',
            size: 'sm',
          });
          refMfaModal.componentInstance.payload = payload;

          refMfaModal.result.then((close: boolean) => {
            if (close) {
              this.router.navigate(['remote', 'downloads']);
              this.toastService.show({ message: 'Download Created' });
            }
          });
        } else {
          this.icsStepperTemplateService.createPackage(payload).subscribe({
            next: (): void => {
              this.router.navigate(['remote', 'downloads']);
              this.toastService.show({ message: 'Download Created' });
            },
            error: (e: HttpErrorResponse): void => {
              if (e.status === 403) {
                this.toastService.show({ message: e.message });
              }
            },
          });
        }
      }
    });
  }

  getSelectedCompany() {
    if (this.destinationCompanyForm) {
      return this.destinationCompanyForm.get('destinationCompany')?.value ?? {};
    }
    const { id, name } = AuthService.getCompany() ?? {};
    return { id, name };
  }

  createSiteGroupCompanyMapping() {
    this.store.select(selectSiteGroupsData).subscribe(data => {
      data.forEach(siteGroup => {
        const companyName = siteGroup.owner.name;
        const siteGroupName = siteGroup.name;
        if (this.siteGroupCompanyMapping.has(companyName)) {
          const siteGroups = this.siteGroupCompanyMapping.get(companyName);
          if (siteGroups) {
            siteGroups.push(siteGroupName);
            this.siteGroupCompanyMapping.set(companyName, siteGroups);
          }
        } else {
          this.siteGroupCompanyMapping.set(companyName, [siteGroupName]);
        }
      });
    });
  }

  getAriaLabelledByForDownloadName(): string {
    if (!this.destinationCompanyForm) {
      return 'cursor-pointer';
    }
    return this.destinationCompanyForm.valid
      ? 'cursor-pointer'
      : 'cursor-not-allowed';
  }

  getSelectedSitesAndDevices(sites: DeviceSummary[]) {
    if (this.deployments && sites?.length) {
      const sitesTargetMapping = new Map<string, number[]>();
      this.deployments.forEach(deployment => {
        const containsSite = sitesTargetMapping.has(deployment.site.id);
        if (containsSite) {
          let existingDevices =
            sitesTargetMapping.get(deployment.site.id) || [];
          existingDevices = [...existingDevices, deployment.target.id];
          sitesTargetMapping.set(deployment.site.id, existingDevices);
        } else {
          sitesTargetMapping.set(deployment.site.id, [deployment.target.id]);
        }
      });

      const selectedSites: { id: string }[] = [];
      const selectedDevices: { id: number }[] = [];
      sitesTargetMapping.forEach((targets, site) => {
        const siteExists = sites.find(data => data.id === site);
        if (siteExists?.devices.length === targets.length) {
          selectedSites.push({ id: site });
        }
        const targetObjects = targets.map(target => ({ id: target }));
        selectedDevices.push(...targetObjects);
      });

      return { selectedSites, selectedDevices };
    }
    return null;
  }

  fetchCompanyApprovers(): void {
    if (this.tenantId) {
      this.icsStepperTemplateService
        .getCompanyApprovers(this.tenantId)
        .subscribe(
          (approvers: Approver[]) => {
            this.companyApprovers = approvers;
          },
          (error: Error) => {
            console.error('Error fetching company approvers:', error);
          }
        );
    }
  }

  private loadDestinationCompanyApprovers(companyId: string): void {
    this.icsStepperTemplateService.getCompanyApprovers(companyId).subscribe(
      (approvers: Approver[]) => {
        this.companyApprovers = approvers;
        this.destinationCompanyForm
          .get('destinationCompanyApprovers')
          ?.setValue(approvers);
      },
      (error: Error) => {
        console.error('Error fetching destination company approvers:', error);
      }
    );
  }

  getApproverName(approverIds: string | string[]): string {
    if (!approverIds || !this.companyApprovers?.length) {
      return '';
    }
    if (Array.isArray(approverIds)) {
      const names = approverIds
        .map(id => this.companyApprovers.find(a => a.id === id)?.name)
        .filter((name): name is string => !!name);
      return names.join(', ');
    }
    const approver = this.companyApprovers.find(a => a.id === approverIds);
    return approver ? approver.name : '';
  }

  shouldShowApproverStep(): boolean {
    const featureFlags = AuthService.getFeatureFlags();
    const requiresApproval = featureFlags
      ? featureFlags.some(
          ({ key, active }: { key: string; active: boolean }) =>
            key === FeatureFlags.FILE_DOWNLOAD_APPROVAL_REQUIRED && active
        )
      : false;

    const totalCount: number =
      this.deviceSelectForm.get('totalDevicesCount')?.value || 0;
    const selectedCount: number =
      this.deviceSelectForm.get('targets')?.value?.length || 0;
    const percentage: number = totalCount > 0 ? selectedCount / totalCount : 0;
    const exceedDevicesLimit: boolean = percentage >= 0.9;

    return (
      requiresApproval &&
      this.companyApprovers?.length > 0 &&
      exceedDevicesLimit
    );
  }

  shouldShowDeviceModeSelectionStep(): boolean {
    const featureFlags = AuthService.getFeatureFlags();
    const showDeviceMode = featureFlags
      ? featureFlags.some(
          ({ key, active }: { key: string; active: boolean }) =>
            key === FeatureFlags.FILE_DOWNLOAD_APPROVAL_REQUIRED && active
        )
      : true;
    return showDeviceMode;
  }

  isFormValid(): boolean {
    const isBasicFormsValid =
      this.downloadNameForm.valid &&
      this.chooseFileForm.valid &&
      this.deviceSelectForm.valid &&
      this.scheduleForm.valid;

    const isDestinationValid = this.destinationCompanyForm
      ? this.destinationCompanyForm.valid
      : true;

    const isDeviceModeValid = this.shouldShowDeviceModeSelectionStep()
      ? this.deviceModeForm.valid
      : true;

    const isActionValid = this.actionStepPresent ? this.actionForm.valid : true;

    const isApprovalValid = this.shouldShowApproverStep()
      ? this.approvalForm.valid
      : true;

    return (
      isBasicFormsValid &&
      isDestinationValid &&
      isDeviceModeValid &&
      isActionValid &&
      isApprovalValid
    );
  }

  private goToStep(stepId: string): void {
    const steps = this.getDisplayedSteps();
    const idx = steps.indexOf(stepId);
    if (idx >= 0) {
      this.activeStepIndex = idx;
      this.activeStep = stepId;
      if (this.stepper) {
        this.stepper.selectedIndex = idx;
      }
    }
  }
}
