import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-select-device-mode',
  templateUrl: './select-device-mode.component.html',
  styleUrls: ['./select-device-mode.component.scss'],
})
export class SelectDeviceModeComponent {
  @Input() formGroup!: FormGroup;

  @Input() isDisabled!: boolean;

  @Input() isCopy!: boolean;

  @Input() activeStep!: string;

  @Input() stepId!: string;
}
