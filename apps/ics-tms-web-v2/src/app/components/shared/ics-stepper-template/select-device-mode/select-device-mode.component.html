<div class="row" *ngIf="activeStep === stepId">
  <div class="col-lg-6">
    <form [formGroup]="formGroup">
      <div class="device-mode-selection">
        <h4>Select Single or Multiple Devices</h4>
        <div class="radio-group" [class.copy-disabled]="isCopy">
          <div class="radio-option">
            <input
              type="radio"
              id="singleDevice"
              formControlName="deviceMode"
              value="single"
              [disabled]="isDisabled || isCopy"
              [readonly]="isCopy"
            />
            <label for="singleDevice">Single Device</label>
          </div>
          <div class="radio-option">
            <input
              type="radio"
              id="multipleDevices"
              formControlName="deviceMode"
              value="multiple"
              [disabled]="isDisabled || isCopy"
              [readonly]="isCopy"
            />
            <label for="multipleDevices">Multiple Devices</label>
          </div>
        </div>
      </div>

      <div class="step-btn-container">
        <button
          matStepperNext
          [disabled]="!formGroup.valid"
          class="btn btn-primary btn-box-shadow"
        >
          Continue
        </button>
      </div>
    </form>
  </div>
</div>
