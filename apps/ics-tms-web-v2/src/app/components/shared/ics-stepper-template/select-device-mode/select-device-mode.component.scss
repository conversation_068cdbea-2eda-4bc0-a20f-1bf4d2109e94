.device-mode-selection {
  margin-bottom: 2rem;

  h4 {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: var(--md-grey-1000);
  }

  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .radio-option {
      display: flex;
      align-items: center;
      gap: 0.8rem;

      input[type='radio'] {
        margin: 0;
        width: 1.6rem;
        height: 1.6rem;
        cursor: pointer;

        &:disabled {
          cursor: not-allowed;
          opacity: 0.65;
        }
      }

      label {
        font-size: 1.4rem;
        margin: 0;
        cursor: pointer;
        color: var(--md-grey-700);

        &.disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }
      }
    }

    &.copy-disabled {
      pointer-events: none;
      opacity: 0.6;

      input[type='radio'],
      label {
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}

.step-btn-container {
  margin-top: 2rem;
}
