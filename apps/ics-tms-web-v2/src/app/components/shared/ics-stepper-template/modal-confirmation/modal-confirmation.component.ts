import { Component, Input, OnInit, inject } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

export interface ModalButton {
  label: string;
  cancel?: boolean;
  primary?: boolean;
  class?: string;
}

@Component({
  selector: 'app-modal-confirmation',
  templateUrl: './modal-confirmation.component.html',
  styleUrls: ['./modal-confirmation.component.scss'],
})
export class ModalConfirmationComponent implements OnInit {
  @Input() title: string = 'Confirm';

  @Input() textContent: string = '';

  @Input() buttons: ModalButton[] = [
    { label: 'Cancel', cancel: true },
    { label: 'Ok', primary: true },
  ];

  activeModal = inject(NgbActiveModal);

  ngOnInit(): void {}

  onButtonClick(button: ModalButton): void {
    if (button.cancel) {
      this.activeModal.dismiss();
    } else {
      this.activeModal.close(true);
    }
  }
}
