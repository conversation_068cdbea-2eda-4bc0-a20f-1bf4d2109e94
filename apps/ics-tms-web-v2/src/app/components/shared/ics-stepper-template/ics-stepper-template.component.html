<div class="row">
  <div class="col-lg-10">
    <div class="row">
      <div class="card col-lg-10 col-lg-offset-1 m-auto px-0">
        <div class="card-content">
          <div [ngSwitch]="true">
            <span class="badge copy-badge" *ngSwitchCase="isCopy && !isRetry"
              >Copy</span
            >
            <span class="badge copy-badge" *ngSwitchCase="isRetry">Retry</span>
            <span
              class="badge copy-badge"
              *ngSwitchCase="!isRetry && !isCopy && isInstall"
              >Install</span
            >
            <span *ngSwitchDefault></span>
          </div>

          <mat-stepper
            orientation="vertical"
            [linear]="true"
            #stepper
            (selectionChange)="onStepChange($event)"
            class="stepper file-downloads-stepper"
            *ngIf="(isRetry && isCopyInitialized) || !isRetry"
          >
            <ng-template matStepperIcon="edit">
              <mat-icon class="check-circle">check_circle</mat-icon>
            </ng-template>
            <ng-template matStepperIcon="done">
              <mat-icon class="check-circle">check_circle</mat-icon>
            </ng-template>

            <!-- select destination company  -->
            <mat-step
              *ngIf="destinationCompanyForm"
              [stepControl]="destinationCompanyForm"
              [completed]="destinationCompanyForm.valid || isDisabled"
            >
              <ng-template matStepLabel>Select Destination Company</ng-template>
              <ng-template matStepContent>
                <app-select-company
                  [dataSource$]="consumers$"
                  [formGroup]="destinationCompanyForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.DESTINATION_COMPANY"
                  [isDisabled]="isDisabled"
                ></app-select-company>
              </ng-template>

              <div
                *ngIf="
                  destinationCompanyForm.valid &&
                  activeStep !== STEP_IDS.DESTINATION_COMPANY
                "
                class="selected-data-container"
              >
                <div class="selected">
                  <span>Selected</span>
                  <span>
                    {{
                      destinationCompanyForm.get('destinationCompany')?.value
                        .name
                    }}
                  </span>
                </div>
              </div>
            </mat-step>

            <!-- download name  -->
            <mat-step
              [aria-labelledby]="getAriaLabelledByForDownloadName()"
              [stepControl]="downloadNameForm"
              [completed]="downloadNameForm.valid || isDisabled"
              [state]="'done'"
            >
              <ng-template matStepLabel>Download Name</ng-template>
              <ng-template matStepContent>
                <app-download-name
                  [formGroup]="downloadNameForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.DOWNLOAD_NAME"
                  [isDisabled]="isDisabled"
                ></app-download-name>
              </ng-template>
              <div
                *ngIf="
                  (downloadNameForm.valid || isDisabled) &&
                  activeStep !== STEP_IDS.DOWNLOAD_NAME
                "
                class="data-container"
              >
                <span>
                  {{ downloadNameForm.value.downloadName }}
                </span>
              </div>
            </mat-step>

            <!-- device mode selection -->
            <mat-step
              *ngIf="shouldShowDeviceModeSelectionStep()"
              [aria-labelledby]="
                isCopy
                  ? 'cursor-not-allowed'
                  : downloadNameForm.valid
                    ? 'cursor-pointer'
                    : 'cursor-not-allowed'
              "
              [stepControl]="deviceModeForm"
              [completed]="deviceModeForm.valid"
              [state]="deviceModeForm.valid ? 'done' : ''"
              [editable]="!isCopy"
            >
              <ng-template matStepLabel>Select Device Mode</ng-template>
              <ng-template matStepContent>
                <app-select-device-mode
                  [formGroup]="deviceModeForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.DEVICE_MODE"
                  [isDisabled]="isDisabled"
                  [isCopy]="isCopy"
                ></app-select-device-mode>
              </ng-template>
              <div
                *ngIf="
                  deviceModeForm.valid && activeStep !== STEP_IDS.DEVICE_MODE
                "
                class="data-container"
              >
                <span>
                  {{
                    deviceModeForm.value.deviceMode === 'single'
                      ? 'Single Device'
                      : 'Multiple Devices'
                  }}
                </span>
              </div>
            </mat-step>

            <!-- choose file  -->
            <mat-step
              [aria-labelledby]="
                deviceModeForm.valid ? 'cursor-pointer' : 'cursor-not-allowed'
              "
              [stepControl]="chooseFileForm"
              [completed]="chooseFileForm.valid && !isFileNotPresent"
            >
              <ng-template matStepLabel>Choose a File</ng-template>
              <ng-template matStepContent>
                <app-choose-file
                  [stepper]="stepper"
                  [deviceFormGroup]="deviceSelectForm"
                  [formGroup]="chooseFileForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.CHOOSE_FILE"
                  [data$]="softwares$"
                  [type]="type"
                  [selectedCompany]="getSelectedCompany()"
                  [siteGroupCompanyMapping]="siteGroupCompanyMapping"
                  [isCopy]="isCopy"
                  [isDisabled]="isDisabled"
                  [isRetry]="isRetry"
                  [isInstall]="isInstall"
                ></app-choose-file>
              </ng-template>

              <ng-container *ngIf="isCopy && (isSoftwaresLoading$ | async)">
                <app-ics-loader></app-ics-loader>
              </ng-container>

              <ng-container *ngIf="!(isSoftwaresLoading$ | async)">
                <div
                  *ngIf="
                    isCopy &&
                      isFileNotPresent &&
                      !fileDeletedShown &&
                      activeStep !== STEP_IDS.CHOOSE_FILE;
                    else fileExists
                  "
                  class="file-deleted-status"
                >
                  The file used for this download has been deleted. <br />Click
                  to select a new file.
                </div>

                <ng-template #fileExists>
                  <ul
                    class="choosed-file-container"
                    *ngIf="
                      !isFileNotPresent &&
                      chooseFileForm.valid &&
                      activeStep !== STEP_IDS.CHOOSE_FILE
                    "
                  >
                    <li>
                      <span class="title">Name</span>
                      <span class="description">
                        {{ chooseFileForm.value.chooseFile.name }}
                      </span>
                    </li>
                    <li>
                      <span class="title">Product</span>
                      <span class="description">
                        {{ chooseFileForm.value.chooseFile.deviceType.name }}
                      </span>
                    </li>
                    <li>
                      <span class="title">Date Created</span>
                      <span class="description">
                        {{
                          chooseFileForm.value.chooseFile.uploadedDate
                            | dateFormat: 'MMM DD, YYYY'
                        }}
                      </span>
                    </li>
                    <li *ngIf="chooseFileForm.value.chooseFile.subDeviceType">
                      <span class="title">Sub Category</span>
                      <span class="description">
                        {{ chooseFileForm.value.chooseFile.subDeviceType }}
                      </span>
                    </li>
                    <li
                      *ngIf="
                        chooseFileForm.value.chooseFile.description &&
                        chooseFileForm.value.chooseFile.description !==
                          'undefined'
                      "
                    >
                      <span class="title">Description</span>
                      <span class="description">
                        {{ chooseFileForm.value.chooseFile.description }}
                      </span>
                    </li>
                  </ul>
                </ng-template>
              </ng-container>
            </mat-step>

            <!-- select devices -->
            <mat-step
              [aria-labelledby]="
                chooseFileForm.valid ? 'cursor-pointer' : 'cursor-not-allowed'
              "
              [stepControl]="deviceSelectForm"
              [completed]="deviceSelectForm.valid"
            >
              <ng-template matStepLabel>Select Devices</ng-template>
              <ng-template matStepContent>
                <app-select-devices
                  [stepper]="stepper"
                  [formGroup]="deviceSelectForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.SELECT_DEVICES"
                  [device]="chooseFileForm.get('chooseFile')?.value"
                  [siteGroups]="companySiteGroups"
                  [deployments]="deployments"
                  [isCopy]="isCopy"
                  [isRetry]="isRetry"
                  [isInstall]="isInstall"
                  [isDisabled]="isDisabled"
                  [deviceMode]="
                    shouldShowDeviceModeSelectionStep()
                      ? deviceModeForm.value.deviceMode
                      : 'multiple'
                  "
                >
                </app-select-devices>
              </ng-template>

              <ng-container *ngIf="isCopy && (isSoftwaresLoading$ | async)">
                <app-ics-loader></app-ics-loader>
              </ng-container>

              <ng-container *ngIf="!(isSoftwaresLoading$ | async)">
                <div
                  *ngIf="
                    this.deviceSelectForm.get('targets')?.value.length !== 0 &&
                    activeStep !== STEP_IDS.SELECT_DEVICES
                  "
                  class="selected-data-container"
                >
                  <div class="selected text-bold-red">
                    <span>Selected</span>
                    <p>
                      {{ this.deviceSelectForm.get('sites')?.value.length }}
                      Site<span
                        *ngIf="
                          this.deviceSelectForm.get('sites')?.value.length > 1
                        "
                        >s</span
                      >
                      and
                      {{ this.deviceSelectForm.get('targets')?.value.length }}
                      Device<span
                        *ngIf="
                          this.deviceSelectForm.get('targets')?.value.length > 1
                        "
                        >s</span
                      >
                      selected
                    </p>
                  </div>
                </div>
              </ng-container>
            </mat-step>

            <!-- Action -->
            <mat-step
              *ngIf="actionStepPresent"
              [aria-labelledby]="
                deviceSelectForm.valid ? 'cursor-pointer' : 'cursor-not-allowed'
              "
              [stepControl]="actionForm"
              [completed]="actionForm.valid || isDisabled"
            >
              <ng-template matStepLabel>Select Action</ng-template>
              <ng-template matStepContent>
                <app-select-action
                  [dataSource$]="availableActions"
                  [formGroup]="actionForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.ACTION"
                  [selectedAction]="chosenAction"
                  [isDisabled]="isDisabled"
                ></app-select-action>
              </ng-template>

              <div
                *ngIf="
                  (actionForm.valid || isDisabled) &&
                  activeStep !== STEP_IDS.ACTION
                "
                class="selected-data-container"
              >
                <div class="selected">
                  <span>Selected</span>
                  <span>{{ actionForm.get('chosenAction')?.value }}</span>
                </div>
              </div>
            </mat-step>
            <!-- Action -->

            <!--  schedule  -->
            <mat-step
              [aria-labelledby]="
                actionForm.valid ? 'cursor-pointer' : 'cursor-not-allowed'
              "
              [stepControl]="scheduleForm"
              [completed]="scheduleForm.valid"
            >
              <ng-template matStepLabel>Schedule</ng-template>
              <ng-template matStepContent>
                <app-schedule
                  [formGroup]="scheduleForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.SCHEDULE"
                  [actionStepPresent]="actionStepPresent"
                  [selectedAction]="actionForm"
                  [chosenAction]="chosenAction"
                  [isInstall]="isInstall"
                  [downloadEndTime]="downloadEndTime"
                  [isLastStep]="!shouldShowApproverStep()"
                ></app-schedule>
              </ng-template>

              <ul
                class="chosen-schedule-container"
                *ngIf="
                  scheduleForm.valid &&
                  activeStep !== STEP_IDS.ACTION &&
                  activeStep !== STEP_IDS.SCHEDULE
                "
              >
                <li
                  *ngIf="
                    getActionDetails().showDownloadTime && actionStepPresent
                  "
                >
                  <span class="title">Download Start Time</span>
                  <span class="description"
                    >{{ getFormattedDate('download') }} at {{ getTime() }}</span
                  >
                </li>

                <li
                  *ngIf="
                    getActionDetails().showDownloadWindow && actionStepPresent
                  "
                >
                  <span class="title">Download Window</span>
                  <span class="description">{{
                    scheduleForm.get('downloadWindow')?.value
                  }}</span>
                </li>

                <ng-container
                  *ngIf="scheduleForm.valid && activeStep !== STEP_IDS.SCHEDULE"
                >
                  <li>
                    <span class="title">Install Time</span>
                    <span class="description"
                      >{{ getFormattedDate() }} at {{ getTime() }}</span
                    >
                  </li>
                  <li>
                    <span class="title">Install Type</span>
                    <span class="description">{{
                      installType[scheduleForm.value.installType]
                    }}</span>
                  </li>
                  <li *ngIf="installTypeValue.value !== '2'">
                    <span class="title">Install Window</span>
                    <span class="description">{{
                      scheduleForm.value.installWindow
                    }}</span>
                  </li>
                  <li *ngIf="installTypeValue.value === '2'">
                    <span class="title">Install Frequency</span>
                    <span class="description">{{
                      scheduleForm.value.installFrequency
                    }}</span>
                  </li>
                </ng-container>
              </ul>
            </mat-step>

            <!-- Approver -->
            <mat-step
              *ngIf="shouldShowApproverStep()"
              [stepControl]="approvalForm"
              [completed]="approvalForm.valid"
            >
              <ng-template matStepLabel>Approver</ng-template>
              <ng-template matStepContent>
                <app-approval
                  [formGroup]="approvalForm"
                  [activeStep]="activeStep"
                  [stepId]="STEP_IDS.APPROVER"
                  [isDisabled]="isDisabled"
                  [approvers]="companyApprovers || []"
                ></app-approval>
              </ng-template>

              <!-- Display selected approver when not on approver step -->
              <div
                *ngIf="approvalForm.valid && activeStep !== STEP_IDS.APPROVER"
                class="selected-data-container"
              >
                <div class="selected">
                  <span>Selected</span>
                  <span>
                    {{
                      getApproverName(
                        approvalForm.get('selectedApprovers')?.value
                      )
                    }}
                  </span>
                </div>
              </div>
            </mat-step>
          </mat-stepper>

          <div class="button-container step-btn-container">
            <button
              type="button"
              class="btn btn-primary btn-box-shadow"
              [disabled]="!isFormValid()"
              (click)="handleCreatePackage()"
            >
              Continue
            </button>
            <button class="btn cancel-btn" (click)="handleRoute()">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
