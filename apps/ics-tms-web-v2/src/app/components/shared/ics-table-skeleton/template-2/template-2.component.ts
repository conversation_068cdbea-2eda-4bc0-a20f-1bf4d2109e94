import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  NgZone,
  OnInit,
  Output,
  inject,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, BehaviorSubject, combineLatest, map } from 'rxjs';
import { setHeaderName } from '../../../../store/actions/globalStore.actions';
import { IcsMenuTemplateComponent } from '../../ics-menu-template/ics-menu-template.component';
import { IcsFirstColumnTemplateComponent } from '../template-1/ics-first-column-template/ics-first-column-template.component';
import { DEVICE_OVERVIEW, getAssets } from 'src/app/constants/appConstants';
import { DEVICES, RKI, SITES } from 'src/app/constants/globalConstant';
import { ColDefType } from 'src/app/constants/tableColumnDef';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';
import { DevicesOtherInfoComponent } from 'src/app/modules/module-devices/components/devices-list/devices-other-info/devices-other-info.component';
import { DevicesSerialComponent } from 'src/app/modules/module-devices/components/devices-list/devices-serial/devices-serial.component';
import { DevicesStatusComponent } from 'src/app/modules/module-devices/components/devices-list/devices-status/devices-status.component';
import {
  DevicesResponse,
  DeviceType,
} from 'src/app/modules/module-devices/model/devices.model';
import { FiltersArray } from 'src/app/modules/module-site/constants/filter-bar-data';
import { AuthService } from 'src/app/services/auth.service';
import { loadDevicesData } from 'src/app/store/actions/deviceTable.action';
import { PaginationState } from 'src/app/modules/modules-remote/models/OfflinePackage.model';
import {
  selectDevicesData,
  selectDevicesLoading,
} from 'src/app/store/selectors/deviceTable.selector';
import { Template2Params } from 'src/app/models/template-2.model';
import { ResultsMetadata } from 'src/app/modules/module-site/models/sites-list.model';

@Component({
  selector: 'app-template-2',
  templateUrl: './template-2.component.html',
  styleUrls: ['./template-2.component.scss'],
})
export class Template2Component implements OnInit {
  @Input() columnDefs!: ColDefType[];

  @Input() type!: string;

  @Output() currentPageChange = new EventEmitter<number>();

  router = inject(Router);

  activatedRoute = inject(ActivatedRoute);

  store = inject(Store);

  cdr = inject(ChangeDetectorRef);

  authService = inject(AuthService);

  zone = inject(NgZone);

  rki = RKI;

  currentPage!: number;

  itemsPerPage!: number;

  totalPage!: number;

  hasData = false;

  searchType!: string;

  isPaginationVisible = false;

  newSelectedFilters: FiltersArray = {};

  isSites = false;

  isRki = false;

  isDevices = false;

  isSearch = false;

  visible!: boolean;

  pageIndex!: number;

  params!: Template2Params;

  pageSize = 20;

  isTransactionMonitoringEnabled = false;

  paginationState$ = new BehaviorSubject<PaginationState>({
    currentPage: 1,
    pageSize: 20,
    totalResults: 0,
    isPaginationVisible: false,
  });

  isLoading$ = this.store.select(selectDevicesLoading);

  rowData$ = new BehaviorSubject<CommonResponseData<any>>(null!);

  getAssets = getAssets;

  get filteredColumnDefs() {
    if (!this.isTransactionMonitoringEnabled) {
      return this.columnDefs.filter(col => col.key !== 'lastTransactionOn');
    }
    return this.columnDefs;
  }

  vm$: Observable<{
    isLoading: boolean;
    pagination: PaginationState;
    rowData: CommonResponseData<any>;
  }> = combineLatest([
    this.isLoading$,
    this.paginationState$,
    this.rowData$,
  ]).pipe(
    map(([isLoading, pagination, rowData]) => ({
      isLoading,
      pagination,
      rowData,
    }))
  );

  ngOnInit() {
    this.isTransactionMonitoringEnabled = AuthService.getFeatureFlags().some(
      flag =>
        flag.key === 'ENABLE_DEVICES_TRANSACTION_MONITORING' && flag.active
    );

    this.vm$.subscribe(() => {
      this.zone.run(() => this.cdr.markForCheck());
    });

    this.authService.searchType.subscribe(type => {
      this.searchType = type;
      this.isSearch = this.type === 'search';
      if (this.isSearch) {
        if (this.searchType !== 'sites') {
          this.updateData(this.store.select(selectDevicesData));
        }
      }
    });

    this.isDevices = this.type === 'devices';
    if (!this.isSearch) {
      // setting up initial params
      this.params = {
        pageIndex: 0,
        pageSize: 20,
      };

      // setting by url
      let showHiddenSites;
      let showHiddenDevices;
      this.activatedRoute.queryParams.subscribe(params => {
        if (params['showHiddenSites'])
          showHiddenSites = params['showHiddenSites'] === 'true';
        if (params['showHiddenDevices'])
          showHiddenDevices = params['showHiddenDevices'] === 'true';
        this.pageIndex = params['page'] ? +params['page'] : 0;
        if (this.isSites)
          this.newSelectedFilters.deviceStatus = params['deviceStatus'];
        this.newSelectedFilters.siteStatus = this.isDevices
          ? params['deviceStatus']
          : params['siteStatus'];
        this.newSelectedFilters.outOfService = params['deviceOOS'];
        this.newSelectedFilters.siteEvents = params['siteEvents'];
      });

      const updatedParams = {
        ...this.params,
        pageIndex: this.pageIndex,
        ...(this.newSelectedFilters.siteEvents &&
          this.newSelectedFilters.siteEvents.length > 0 && {
            'siteEvents[]': this.newSelectedFilters.siteEvents,
          }),
        ...(this.newSelectedFilters.siteStatus &&
          this.newSelectedFilters.siteStatus.length > 0 && {
            'statuses[]': this.newSelectedFilters.siteStatus,
          }),
        ...(this.newSelectedFilters.deviceStatus &&
          this.newSelectedFilters.deviceStatus.length > 0 && {
            'deviceStatuses[]': this.newSelectedFilters.deviceStatus,
          }),
        ...(this.newSelectedFilters.outOfService &&
          this.newSelectedFilters.outOfService.length > 0 && {
            'oosFilter[]': this.newSelectedFilters.outOfService,
          }),
        ...(showHiddenSites !== undefined && {
          showHiddenSites,
        }),
        ...(showHiddenDevices !== undefined && {
          showHiddenDevices,
        }),
      };

      if (this.isDevices) {
        this.store.dispatch(loadDevicesData({ params: updatedParams }));
      }
    }
    if (this.isDevices) {
      this.updateData(this.store.select(selectDevicesData));
    }
  }

  updateData(dataObservable: Observable<DevicesResponse>) {
    dataObservable.subscribe(data => {
      this.rowData$.next(data);
      this.updatePaginationState(data?.resultsMetadata);
      this.cdr.markForCheck();
    });
  }

  private updatePaginationState(metadata: ResultsMetadata): void {
    this.paginationState$.next({
      currentPage: metadata.pageIndex + 1,
      pageSize: 20,
      totalResults: metadata.totalResults,
      isPaginationVisible: metadata.totalResults > 20,
    });
  }

  handlePageChange(pageNumber: number): void {
    const pageIndex = pageNumber - 1;
    this.visible = this.visible === undefined ? false : this.visible;
    const updatedParams: Template2Params = this.settingsQueryParams(
      this.newSelectedFilters,
      this.visible,
      pageIndex
    );
    this.callingList(updatedParams);
  }

  componentTypeMapping: { [key: string]: any } = {
    IcsMenuTemplateComponent,
    DevicesSerialComponent,
    DevicesStatusComponent,
    DevicesOtherInfoComponent,
    IcsFirstColumnTemplateComponent,
  };

  getComponentType(componentName: string | undefined) {
    if (componentName) {
      const componentType = this.componentTypeMapping[componentName];
      return componentType;
    }
    return null;
  }

  handleRoute(item: any) {
    if (this.type === 'search') {
      switch (this.searchType) {
        case DEVICES:
          return this.routeToDevices(item);
        case SITES:
          return this.routeToSites(item);
        default:
          return null;
      }
    }

    switch (this.type) {
      case DEVICES:
        return this.routeToDevices(item);
      case RKI:
        return this.router.navigate(['rki', item?.id]);
      case SITES:
        return this.routeToSites(item);
      default:
        return null;
    }
  }

  routeToSites(item: any) {
    return this.router.navigate([
      'asset-management',
      'sites',
      item?.id,
      'details',
    ]);
  }

  routeToDevices(item: any) {
    this.store.dispatch(setHeaderName({ name: DEVICE_OVERVIEW }));
    return this.router.navigate(['sites', item?.siteId, item?.id, 'overview']);
  }

  isLastPage() {
    return this.currentPage === Math.ceil(this.totalPage / this.itemsPerPage);
  }

  checkForRelease(key: string, details: string | DeviceType | null) {
    if (key === 'deviceType')
      return typeof details === 'string' ? details : details?.name;

    if (key === 'lastTransactionOn') {
      if (!details) return '-';
      return details;
    }

    return key === 'release' && !details ? '-' : details;
  }

  isFilter(): boolean {
    let flag = false;
    this.activatedRoute.queryParams.subscribe(params => {
      flag =
        params['deviceOOS']?.length > 0 ||
        params['deviceStatus']?.length > 0 ||
        params['siteEvents']?.length > 0 ||
        params['statuses']?.length > 0;
    });
    return flag;
  }

  handleFiltersArray(filtersArray: FiltersArray) {
    if (this.areFiltersEqual(this.newSelectedFilters, filtersArray)) return;
    const updatedParams: Template2Params = this.settingsQueryParams(
      filtersArray,
      this.visible,
      0
    );
    this.callingList(updatedParams);
    this.newSelectedFilters = filtersArray;
  }

  handleCSVDownloads() {
    let showHiddenSites;
    let showHiddenDevices;
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['showHiddenSites'])
        showHiddenSites = params['showHiddenSites'] === 'true';
      if (params['showHiddenDevices'])
        showHiddenDevices = params['showHiddenDevices'] === 'true';
    });

    const updatedParams = {
      ...(this.isSites && { autoPoll: false }),
      isCSV: true,
      ...(this.isSites && { order: 'status-desc' }),
      pageIndex: 0,
      pageSize: -1,
      ...(this.newSelectedFilters.siteEvents &&
        this.isSites &&
        this.newSelectedFilters.siteEvents.length > 0 && {
          'siteEvents[]': this.newSelectedFilters.siteEvents,
        }),
      ...(this.newSelectedFilters.siteStatus &&
        this.isSites &&
        this.newSelectedFilters.siteStatus.length > 0 && {
          'statuses[]': this.newSelectedFilters.siteStatus,
        }),
      ...(this.newSelectedFilters.deviceStatus &&
        this.newSelectedFilters.deviceStatus.length > 0 && {
          'deviceStatuses[]': this.newSelectedFilters.deviceStatus,
        }),
      ...(this.newSelectedFilters.outOfService &&
        this.newSelectedFilters.outOfService.length > 0 && {
          'oosFilter[]': this.newSelectedFilters.outOfService,
        }),
      ...(showHiddenSites !== undefined && {
        showHiddenSites,
      }),
      ...(showHiddenDevices !== undefined && {
        showHiddenDevices,
      }),
    };

    if (this.isDevices) {
      this.store.dispatch(loadDevicesData({ params: updatedParams }));
    }
  }

  // Handle the visiblity for the device/sites
  handleVisibilty(visible: boolean) {
    if (this.visible !== visible) {
      this.visible = visible;
      const updatedParams = this.settingsQueryParams(
        this.newSelectedFilters,
        this.visible,
        0
      );
      this.callingList(updatedParams);
    }
  }

  // Dispatch the list on conditional basis
  callingList(params: Template2Params) {
    if (this.isDevices) this.store.dispatch(loadDevicesData({ params }));
    const queryParams = new URLSearchParams();

    if (this.pageIndex !== undefined) {
      queryParams.append('page', params['pageIndex'].toString());
    }

    if (params['siteEvents[]']) {
      const siteEvents = this.convertToArray(params['siteEvents[]']);
      siteEvents.forEach(value => {
        queryParams.append('siteEvents', value);
      });
    }

    if (params['statuses[]']) {
      const siteStatus = this.convertToArray(params['statuses[]']);
      siteStatus.forEach(value => {
        queryParams.append(this.isSites ? 'siteStatus' : 'deviceStatus', value);
      });
    }

    if (params['deviceStatuses[]']) {
      const deviceStatus = this.convertToArray(params['deviceStatuses[]']);
      deviceStatus.forEach(value => {
        queryParams.append('deviceStatus', value);
      });
    }

    if (params['oosFilter[]']) {
      const oosFilter = this.convertToArray(params['oosFilter[]']);
      oosFilter.forEach(value => {
        queryParams.append('deviceOOS', value);
      });
    }

    if (this.visible !== undefined) {
      queryParams.append(
        this.isSites ? 'showHiddenSites' : 'showHiddenDevices',
        this.visible?.toString()
      );
    }

    const queryString = queryParams.toString();
    const url = `/${this.type}?${queryString}`;
    this.router.navigateByUrl(url);
  }

  // Setting up the query params for devices/sites
  settingsQueryParams(
    filters: FiltersArray,
    visible?: boolean,
    pageIndex?: number
  ): Template2Params {
    return {
      ...this.params,
      ...(pageIndex && { pageIndex }),
      ...(filters.siteEvents &&
        filters.siteEvents.length > 0 && {
          'siteEvents[]': filters.siteEvents,
        }),
      ...(filters.siteStatus &&
        filters.siteStatus.length > 0 &&
        this.isSites && { 'statuses[]': filters.siteStatus }),
      ...(filters.deviceStatus &&
        filters.deviceStatus.length > 0 &&
        this.isDevices && { 'statuses[]': filters.deviceStatus }),
      ...(filters.deviceStatus &&
        this.isSites && {
          'deviceStatuses[]': filters.deviceStatus,
        }),
      ...(filters.outOfService &&
        filters.outOfService.length > 0 && {
          'oosFilter[]': filters.outOfService,
        }),
      ...(visible !== undefined &&
        this.isSites && { showHiddenSites: visible }),
      ...(visible !== undefined &&
        this.isDevices && { showHiddenDevices: visible }),
    };
  }

  convertToArray(item: string | string[]): string[] {
    return Array.isArray(item) ? item : [item];
  }

  areFiltersEqual(filters1: FiltersArray, filters2: FiltersArray): boolean {
    return JSON.stringify(filters1) === JSON.stringify(filters2);
  }

  hasScreenSize(key: string, data: DeviceType) {
    return key === 'deviceType' && data?.screenSize
      ? `(${data.screenSize}")`
      : '';
  }
}
