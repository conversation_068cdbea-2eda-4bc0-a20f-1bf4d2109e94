.template-2-main-container {
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.2),
    0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 2px 1px -1px rgba(0, 0, 0, 0.12);
  border-radius: 0.3rem;

  .custom-table {
    border-collapse: collapse !important;
    background-color: var(--color-white);
    width: 100%;
    border-bottom-left-radius: 0.3rem;
    border-bottom-right-radius: 0.3rem;

    // Table Head
    .table-head {
      .table-row:first-child {
        background-color: transparent;
        border-bottom: 0.1rem solid var(--color-black-shade-three);
      }

      th {
        padding: 1rem 1.5rem;
        color: var(--color-black-shade-two);
        font-size: 1.2rem;
      }
    }

    // table row
    .table-row {
      &:not(:last-child) {
        border-bottom: 0.1rem solid var(--color-black-shade-three);
      }

      &:hover {
        background-color: var(--list-hover-color);
      }

      td {
        padding: 1rem 1.5rem;
        font-size: 1.4rem;
        border-radius: 0.3rem;
      }
    }
  }

  .table-body {
    cursor: pointer;
  }
}

.no-data-found {
  position: relative;
  display: block;
  padding: 1.563rem 1.875rem;
  margin-bottom: -0.1rem;
  background-color: var(--color-white);
  text-align: center;
  color: var(--color-black-shade-two);
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;

  .no-data-icon {
    .search-icon {
      font-size: 20px !important;
      color: var(--dropdown-arrow);
    }

    .no-data-icon-img {
      align-items: center;
    }
  }

  .message {
    font-size: 1.4rem;
    font-weight: 500;
    margin-top: 2rem;
  }

  .small-message {
    font-size: 1.4rem;
    font-weight: 800;
    color: gray;
  }
}

.search {
  background-color: var(--color-bg-default) !important;
  border-top: none;
  box-shadow: none !important;
  border-radius: 0 !important;
  padding: 3rem 0 2rem 0 !important;
  margin: 0 !important;

  .no-search-data-msg {
    color: var(--color-black);
    font-weight: 400;
  }
}

.m-b {
  margin-bottom: 2rem;
}

.template-2-main-container.no-result {
  box-shadow: none !important;
  border-radius: 0 !important;
}
