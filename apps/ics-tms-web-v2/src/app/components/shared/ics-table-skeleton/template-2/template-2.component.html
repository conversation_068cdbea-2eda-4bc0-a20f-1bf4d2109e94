<div *ngIf="vm$ | async as vm">
  <section
    class="template-2-main-container"
    [ngClass]="{
      'no-result':
        !vm.isLoading && vm.rowData && !vm.rowData.results.length && isSearch,
    }"
  >
    <!-- Show ONLY search icon and text if no results and isSearch -->
    <ng-container
      *ngIf="
        !vm.isLoading && !vm.rowData?.results?.length && isSearch;
        else normalContent
      "
    >
      <div class="no-data-found search">
        <div class="no-data-icon">
          <i alt="no-result" class="search-icon gicon-search"></i>
        </div>
        <div class="message">
          <span class="no-search-data-msg">No Results Found</span>
        </div>
      </div>
    </ng-container>
    <ng-template #normalContent>
      <app-ics-filter-bar-header-template
        [headerType]="type"
        (getCSVDownload)="handleCSVDownloads()"
        (getVisibility)="handleVisibilty($event)"
        (getFiltersArray)="handleFiltersArray($event)"
        [totalResults]="vm.pagination.totalResults"
        *ngIf="!isSearch"
      ></app-ics-filter-bar-header-template>
      <table class="custom-table" *ngIf="!vm.isLoading">
        <thead class="table-head">
          <tr class="table-row">
            <th
              *ngFor="let col of filteredColumnDefs"
              [ngStyle]="{ width: col.width }"
            >
              {{ col.label }}
            </th>
          </tr>
        </thead>
        <tbody class="table-body">
          <tr
            *ngFor="let data of vm.rowData?.results"
            class="table-row"
            (click)="handleRoute(data)"
          >
            <td
              *ngFor="let col of filteredColumnDefs"
              [ngClass]="col.key"
              [ngStyle]="{ width: col.width }"
            >
              <div
                *ngIf="col.isComponent"
                [ngClass]="{ 'm-b': col.key === 'status' }"
              >
                <ng-container
                  *ngComponentOutlet="
                    getComponentType(col.componentName);
                    inputs: { dataSource: data.details }
                  "
                ></ng-container>
              </div>
              <div *ngIf="!col.isComponent" class="m-b">
                {{ checkForRelease(col.key, data.details[col.key]) }}
                <span class="fw-bold">
                  {{ hasScreenSize(col.key, data.details[col.key]) }}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <ng-container
        *ngIf="!vm.isLoading && !vm.rowData?.results?.length && !isSearch"
      >
        <div class="no-data-found" [ngClass]="{ search: isSearch }">
          <div class="no-data-icon">
            <img
              src="{{ getAssets() + 'img/device-icon.png' }}"
              class="no-data-icon-img"
              alt="error"
              *ngIf="isDevices"
            />
          </div>
          <div class="message">
            <span *ngIf="isDevices && !isFilter()">No Devices Added</span>
          </div>
          <div class="small-message">
            <span *ngIf="isDevices && !isFilter()"
              >Add a device to a site and it will show up here.</span
            >
            <span *ngIf="isDevices && isFilter()"
              >No device found for the selected filter(s).</span
            >
          </div>
        </div>
      </ng-container>
      <div class="bg-white" *ngIf="vm.isLoading">
        <app-ics-loader></app-ics-loader>
      </div>
    </ng-template>
  </section>
  <app-ics-pagination
    *ngIf="vm.pagination.isPaginationVisible && !vm.isLoading"
    [paginationState$]="paginationState$"
    (pageChange)="handlePageChange($event)"
  ></app-ics-pagination>
</div>
