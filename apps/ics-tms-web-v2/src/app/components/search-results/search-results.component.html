<div>
  <div *ngIf="total" class="results-found">
    <h3 *ngIf="searchedText; else noSearch">
      {{ total }} results found for {{ searchedText }}
    </h3>
    <ng-template #noSearch>
      <h3>{{ total }} results found.</h3>
    </ng-template>
  </div>
  <app-template-2
    [columnDefs]="colDefDevices"
    [type]="'search'"
    (currentPageChange)="onPageIndexChangeDevices($event)"
  ></app-template-2>
</div>
