import { Component, inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { Template2Params } from 'src/app/models/template-2.model';
import { devicesColDef } from 'src/app/modules/module-devices/components/devices-list/devices-list-column.model';
import { AuthService } from 'src/app/services/auth.service';
import { loadDevicesData } from 'src/app/store/actions/deviceTable.action';
import { selectDevicesData } from 'src/app/store/selectors/deviceTable.selector';

@Component({
  selector: 'app-search-results',
  templateUrl: './search-results.component.html',
  styleUrls: ['./search-results.component.scss'],
})
export class SearchResultsComponent implements OnInit, OnDestroy {
  routeSub!: Subscription;

  devicesParams: Template2Params = {
    pageIndex: 0,
    pageSize: 20,
    showHiddenDevices: true,
  };

  type = '';

  total = 0;

  searchedText = '';

  colDefDevices = devicesColDef;

  store = inject(Store);

  router = inject(Router);

  route = inject(ActivatedRoute);

  authService = inject(AuthService);

  ngOnInit() {
    this.routeSub = this.route.queryParams.subscribe(params => {
      const { q, src, page } = params;
      if (src === 'devices') {
        this.handleDevices(q, page);
      }
    });
  }

  handleDevices(query: string, page: number) {
    this.type = 'devices';
    this.authService.searchType.next('devices');
    this.searchedText = query;
    let newParams: Template2Params;
    if (query !== '') {
      newParams = {
        ...this.devicesParams,
        searchFilter: query,
        pageIndex: page,
      };
    } else {
      const { searchFilter, ...rest } = this.devicesParams;
      newParams = { ...rest, pageIndex: page };
    }
    this.devicesParams = newParams;
    this.store.dispatch(loadDevicesData({ params: this.devicesParams }));
    this.store.select(selectDevicesData).subscribe(data => {
      this.total = data.resultsMetadata.totalResults;
    });
  }

  ngOnDestroy() {
    this.routeSub.unsubscribe();
  }

  onPageIndexChangeDevices(currentPageIndex: number) {
    this.devicesParams = {
      ...this.devicesParams,
      pageIndex: currentPageIndex - 1,
    };
    this.router.navigate([], {
      queryParams: { page: currentPageIndex - 1 },
      queryParamsHandling: 'merge',
    });
  }
}
