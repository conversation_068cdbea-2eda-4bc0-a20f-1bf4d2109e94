import { Component, HostListener, inject, OnInit } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  RouteConfigLoadEnd,
  RouteConfigLoadStart,
  Router,
} from '@angular/router';
import { Store } from '@ngrx/store';
import { jwtDecode } from 'jwt-decode';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { GlobalState, LoginResponse } from './models/common';
import { API, TOKEN_KEY } from './constants/appConstants';
import { AuthService } from './services/auth.service';
import { ConfigService } from './services/config.service';
import {
  closeSidebar,
  loginSuccess,
  openSidebar,
  setApiUrl,
  setCaptchaSiteKey,
  setFeatureFlag,
  setFileDownloadsLimits,
  setHeaderName,
  setIsCaptchaEnabled,
  setLoginUserDetails,
} from './store/actions/globalStore.actions';
import {
  isLoginSuccessfull,
  selectSideBarStatus,
} from './store/selectors/globalStore.selectors';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  isChunkLoading = false;

  router = inject(Router);

  activatedRoute = inject(ActivatedRoute);

  showNavSearch = true;

  isNavbarChanged = false;

  private store = inject(Store<GlobalState>);

  private destroy$ = new Subject<void>();

  private configService = inject(ConfigService);

  isLoggedIn$ = new BehaviorSubject(false);

  isSideBarOpen$ = new BehaviorSubject(false);

  ngOnInit(): void {
    this.handleSubscriptions();
    this.setupRouterEventHandlers();
    this.checkAndDispatchLoginStatus();
    this.loadConfigFromJson();
    this.loadFeatureFlags();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupRouterEventHandlers(): void {
    this.router.events.pipe(takeUntil(this.destroy$)).subscribe(event => {
      if (event instanceof RouteConfigLoadStart) {
        this.isChunkLoading = true;
        return;
      }

      if (event instanceof RouteConfigLoadEnd) {
        this.isChunkLoading = false;
        return;
      }

      if (event instanceof NavigationEnd) {
        this.handleNavigationEnd();
      }
    });
  }

  private isMobileView(): boolean {
    return window.innerWidth < 768;
  }

  private handleNavigationEnd(): void {
    const routeWithData = this.getDeepestRouteWithData(this.activatedRoute);
    const {
      data: { css = '', navTitle = '' },
    } = routeWithData.snapshot;

    this.store.dispatch(setHeaderName({ name: navTitle }));

    this.toggleCssClass('view-is-full', css.includes('view-is-full'));
    this.toggleCssClass('is-view-full', css.includes('is-view-full'));
    this.toggleCssClass('view-is-centered', css.includes('view-is-centered'));
    this.toggleCssClass('view-site-detail', css.includes('view-site-detail'));

    if (css.includes('view-is-full') || this.isMobileView()) {
      this.store.dispatch(closeSidebar());
    } else {
      this.store.dispatch(openSidebar());
    }
  }

  private getDeepestRouteWithData(route: ActivatedRoute): ActivatedRoute {
    let currentRoute = route;
    while (currentRoute.firstChild) {
      currentRoute = currentRoute.firstChild;
    }

    let routeWithData: ActivatedRoute | null = currentRoute;
    while (routeWithData && !Object.keys(routeWithData.snapshot.data).length) {
      routeWithData = routeWithData.parent!;
    }

    return routeWithData;
  }

  private toggleCssClass(className: string, shouldAdd: boolean): void {
    document.documentElement.classList.toggle(className, shouldAdd);
  }

  private checkAndDispatchLoginStatus() {
    if (!localStorage.getItem(TOKEN_KEY)) {
      this.store.dispatch(loginSuccess({ success: false }));
    } else {
      this.store.dispatch(loginSuccess({ success: true }));

      const token = AuthService.getToken();
      if (token) {
        const decodedData = jwtDecode(token);
        this.store.dispatch(
          setLoginUserDetails({
            userDetails: {
              ...decodedData,
              userId: decodedData.sub,
            } as LoginResponse,
          })
        );
      }
    }
  }

  private loadConfigFromJson(): void {
    this.configService.getConfig().subscribe(data => {
      this.store.dispatch(setApiUrl({ apiUrl: `${data.api}/v1` }));
      localStorage.setItem(API, data.api);

      this.store.dispatch(
        setIsCaptchaEnabled({
          isCaptchaEnabled: data?.captcha?.enabled ?? false,
        })
      );

      this.store.dispatch(
        setCaptchaSiteKey({ siteKey: data?.captcha?.siteKey ?? '' })
      );

      this.store.dispatch(
        setFileDownloadsLimits({
          siteLimit: data.fileDownloadsDefaultSiteLimit ?? 100,
          targetLimit: data.fileDownloadsDefaultTargetLimit ?? 500,
        })
      );
    });
  }

  private loadFeatureFlags(): void {
    this.configService.getFeatureFlags().subscribe(data => {
      this.store.dispatch(setFeatureFlag({ featureFlags: data }));
    });
  }

  private handleSubscriptions(): void {
    this.store
      .select(isLoginSuccessfull)
      .pipe(takeUntil(this.destroy$))
      .subscribe(isLoggedIn => {
        this.isLoggedIn$.next(isLoggedIn);
      });

    this.store
      .select(selectSideBarStatus)
      .pipe(takeUntil(this.destroy$))
      .subscribe(isSideBarOpen => {
        this.isSideBarOpen$.next(isSideBarOpen);
        document.documentElement.classList.toggle(
          'global-menu-open',
          isSideBarOpen && this.isLoggedIn$.getValue()
        );
      });
  }

  updateNavSearch(showNavSearch: boolean) {
    this.showNavSearch = showNavSearch;
    this.isNavbarChanged = true;
  }

  searchToggle() {
    this.showNavSearch = !this.showNavSearch;
  }

  globalMenuCollapse() {
    this.isSideBarOpen$.getValue()
      ? this.store.dispatch(closeSidebar())
      : this.store.dispatch(openSidebar());
  }

  @HostListener('window:resize')
  onResize(): void {
    this.handleNavigationEnd();
  }
}
