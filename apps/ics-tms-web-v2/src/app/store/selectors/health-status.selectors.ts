import { createSelector, createFeatureSelector } from '@ngrx/store';
import { AppState } from '../app.store';

export const healthStatusState = createFeatureSelector<AppState>('app');

export const healthStatusData = createSelector(
  healthStatusState,
  state => state.healthStatus.data
);
export const healthStatusLoading = createSelector(
  healthStatusState,
  state => state.healthStatus.loading
);
export const healthStatusError = createSelector(
  healthStatusState,
  state => state.healthStatus.error
);
