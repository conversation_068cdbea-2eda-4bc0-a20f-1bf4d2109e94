import { createReducer, on } from '@ngrx/store';
import { Sites } from '../../models/sites.model';
import {
  loadSites,
  loadSitesFailure,
  loadSitesSuccess,
} from '../actions/sites.actions';

export interface SitesState {
  data: Sites;
  loading: boolean;
  loaded: boolean;
  error: unknown;
}

export const initialState: SitesState = {
  data: {
    results: [],
    resultMetadata: {
      totalResults: 0,
      pageIndex: 0,
      pageSize: 0,
    },
  },
  loading: false,
  loaded: false,
  error: '',
};

export const SitesReducer = createReducer(
  initialState,

  on(loadSites, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadSitesSuccess, (state, { sitesData, replace }) => {
    // If replace is true, use the new data directly
    // If replace is false (append mode), merge the results arrays
    const updatedData = replace
      ? sitesData
      : {
          ...sitesData,
          results: [...state.data.results, ...sitesData.results],
        };

    return {
      ...state,
      data: updatedData,
      loading: false,
      loaded: true,
      error: null,
    };
  }),

  on(loadSitesFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
