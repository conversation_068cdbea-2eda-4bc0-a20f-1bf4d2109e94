import { createReducer, on } from '@ngrx/store';
import { Devices } from '../../modules/module-devices/model/devices.model';
import * as action from '../actions/deviceTable.action';
import { CommonResponseData } from 'src/app/models/common';

export interface DevicesState {
  data: CommonResponseData<Devices>;
  loading: boolean;
  error?: unknown;
}

export const initialState: DevicesState = {
  data: {
    resultsMetadata: {
      pageIndex: 0,
      pageSize: 0,
      totalResults: 0,
    },
    results: [],
  },
  loading: false,
};

export const deviceTableReducer = createReducer(
  initialState,
  on(action.loadDevicesData, state => ({
    ...state,
    loading: true,
  })),
  on(action.loadDeviceDataSuccess, (state, { devicesData }) => ({
    ...state,
    data: devicesData,
    loading: false,
  })),
  on(action.loadDeviceDataFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  }))
);
