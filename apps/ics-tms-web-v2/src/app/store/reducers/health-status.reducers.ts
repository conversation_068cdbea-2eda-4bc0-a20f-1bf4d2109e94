import { createReducer, on } from '@ngrx/store';
import * as actions from '../actions/health-status.actions';
import { HealthStatus } from 'src/app/constants/health-status';

export interface HealthStatusState {
  data?: HealthStatus;
  loading?: boolean;
  error?: string | null;
}

export const initialState: HealthStatusState = {};

export const HealthStatusReducer = createReducer(
  initialState,
  on(actions.loadHealthStatusData, state => ({
    ...state,
    loading: true,
  })),
  on(actions.loadHealthStatusDataSuccess, (state, { data }) => ({
    ...state,
    data,
    loading: false,
  })),
  on(actions.loadHealthStatusDataFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
