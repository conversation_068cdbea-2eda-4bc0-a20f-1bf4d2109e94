import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import {
  loadSites,
  loadSitesFailure,
  loadSitesSuccess,
} from '../actions/sites.actions';
import { SitesService } from 'src/app/services/sites.service';

@Injectable()
export class SitesEffects {
  actions$ = inject(Actions);

  sitesService = inject(SitesService);

  getSitesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadSites),
      switchMap(action =>
        this.sitesService.getSitesData(action.params).pipe(
          map(data =>
            loadSitesSuccess({
              sitesData: data,
              replace: action.replace,
            })
          ),
          catchError(error => of(loadSitesFailure({ error })))
        )
      )
    )
  );
}
