import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import * as actions from '../actions/health-status.actions';
import { HealthStatusService } from 'src/app/services/health-status.service';

@Injectable()
export class HealthStatusEffects {
  actions$ = inject(Actions);

  healthStatusService = inject(HealthStatusService);

  getHealthStatusData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(actions.loadHealthStatusData),
      switchMap(action =>
        this.healthStatusService.getHealthStatusData(action.param).pipe(
          map(data => actions.loadHealthStatusDataSuccess({ data })),
          catchError(error =>
            of(actions.loadHealthStatusDataFailure({ error }))
          )
        )
      )
    )
  );
}
