import { Actions, createEffect, ofType } from '@ngrx/effects';
import {
  catchError,
  map,
  mergeMap,
  withLatestFrom,
  switchMap,
} from 'rxjs/operators';
import { inject, Injectable } from '@angular/core';
import { of, forkJoin } from 'rxjs';
import { Store } from '@ngrx/store';
import { HttpClient, HttpParams } from '@angular/common/http';
import { DevicesListDataService } from '../../modules/module-devices/services/table-data.service';
import { DevicesService } from '../../modules/module-site/services/devices.service';
import { Devices } from '../../modules/module-devices/model/devices.model';
import * as devicesAction from '../actions/deviceTable.action';
import { getApiConstants } from '../../modules/module-devices/constants/api';
import { selectDevicesData } from '../selectors/deviceTable.selector';

@Injectable()
export class DeviceTableEffects {
  actions$ = inject(Actions);

  devicesListDataService = inject(DevicesListDataService);

  devicesService = inject(DevicesService);

  store = inject(Store);

  http = inject(HttpClient);

  convertIntoParams(params: {}) {
    return new HttpParams({ fromObject: params });
  }

  loadData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(devicesAction.loadDevicesData),
      withLatestFrom(this.store.select(selectDevicesData)),
      mergeMap(([action, currentResults]) => {
        if (action.params.isCSV) {
          return this.http
            .get(getApiConstants().devices.getDeviceList, {
              responseType: 'text',
              params: this.convertIntoParams(action.params),
            })
            .pipe(
              map((csvData: string) => {
                this.downloadCSV(csvData);
                return devicesAction.loadDeviceDataSuccess({
                  devicesData: currentResults,
                });
              }),
              catchError(error =>
                of(devicesAction.loadDeviceDataFailure({ error }))
              )
            );
        }
        return this.devicesListDataService.getDevicesData(action.params).pipe(
          switchMap(data => {
            const deviceIds = data.results.map((device: Devices) =>
              device.id.toString()
            );
            if (deviceIds.length === 0) {
              return of({
                results: [],
                resultsMetadata: data.resultsMetadata,
              });
            }

            return forkJoin({
              devicesData: of(data),
              transactionData: this.devicesService
                .getLastTransactedOn(deviceIds)
                .pipe(
                  map(response => response.results || []),
                  catchError(_ => of([]))
                ),
            }).pipe(
              map(({ devicesData, transactionData }) => {
                const transactionMap = new Map();
                transactionData.forEach((item: any) => {
                  if (item.entityId && item.lastContactMax) {
                    transactionMap.set(
                      item.entityId.toString(),
                      item.lastContactMax
                    );
                  }
                });

                return {
                  results: devicesData.results.map((result: Devices) => ({
                    ...result,
                    details: {
                      id: result.id,
                      serialNumber: result.serialNumber,
                      deviceName: result.name,
                      statusStr: result.statusStr,
                      lastRegistered: result.lastRegistered,
                      lastContact: result.lastContact,
                      lastTransactionOn:
                        transactionMap.get(result.id.toString()) || null,
                      location: result.siteAddress,
                      deviceType: result.deviceType,
                      release: result.releaseVersion,
                      otherInfo: result.oosConditions,
                      statusAlarmTs: result.statusAlarmTs,
                    },
                  })),
                  resultsMetadata: devicesData.resultsMetadata,
                };
              })
            );
          }),
          map(data =>
            devicesAction.loadDeviceDataSuccess({ devicesData: data })
          ),
          catchError(error =>
            of(devicesAction.loadDeviceDataFailure({ error }))
          )
        );
      })
    )
  );

  downloadCSV(csvData: string) {
    const blob = new Blob([csvData], { type: 'text/csv' });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = 'devices-list.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
