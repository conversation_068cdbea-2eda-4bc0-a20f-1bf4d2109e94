import { createAction, props } from '@ngrx/store';
import { Sites, SitesParams } from '../../models/sites.model';

export const loadSites = createAction(
  '[Sites] Load Sites',
  props<{ params: SitesParams; replace: boolean }>()
);

export const loadSitesSuccess = createAction(
  '[Sites] Load Sites Success',
  props<{ sitesData: Sites; replace: boolean }>()
);

export const loadSitesFailure = createAction(
  '[Sites] Load Sites Failure',
  props<{ error: unknown }>()
);
