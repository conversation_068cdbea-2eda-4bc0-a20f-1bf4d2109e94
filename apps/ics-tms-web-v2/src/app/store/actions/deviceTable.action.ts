import { createAction, props } from '@ngrx/store';
import { DevicesParams } from '../../modules/module-devices/model/site-data.model';
import { Devices } from '../../modules/module-devices/model/devices.model';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';

export const loadDevicesData = createAction(
  '[Devices Data] Load Devices Data',
  props<{ params: DevicesParams }>()
);

export const loadDeviceDataSuccess = createAction(
  '[Devices Data] Load Devices Data Success',
  props<{ devicesData: CommonResponseData<Devices> }>()
);

export const loadDeviceDataFailure = createAction(
  '[Devices Data] Load Devices Data Failure',
  props<{ error: unknown }>()
);
