import { createAction, props } from '@ngrx/store';
import { HealthStatus } from 'src/app/constants/health-status';

export const loadHealthStatusData = createAction(
  '[Health Status] Load Health Status',
  props<{ param: string }>()
);

export const loadHealthStatusDataSuccess = createAction(
  '[Health Status] Load Health Status Success',
  props<{ data: HealthStatus }>()
);

export const loadHealthStatusDataFailure = createAction(
  '[Health Status] Load Health Status Failure',
  props<{ error: string | null }>()
);
