import { ConsumersReducer, ConsumersState } from './reducers/consumers.reducer';
import { DetailsReducer } from './reducers/details.reducer';
import {
  DeviceSummaryReducer,
  DeviceSummaryState,
} from './reducers/device-summary.reducer';
import {
  DeviceTypesReducer,
  DeviceTypesState,
} from './reducers/device-types.reducer';
import {
  EnittySettingsReducer,
  SettingsState,
} from './reducers/entity-settings.reducers';
import {
  HealthStatusReducer,
  HealthStatusState,
} from './reducers/health-status.reducers';
import {
  SiteGroupsReducer,
  SiteGroupsState,
} from './reducers/site-groups.reducers';
import { SitesReducer, SitesState } from './reducers/sites.reducers';
import { SoftwareReducer, SoftwareState } from './reducers/software.reducer';
import { TagsReducer, TagsState } from './reducers/tags.reducers';
import {
  UserGroupsReducer,
  UserGroupsState,
} from './reducers/user-groups.reducer';
import {
  UserPersonasReducer,
  UserPersonasState,
} from './reducers/user-personas.reducer';
import {
  UserRolesReducer,
  UserRolesState,
} from './reducers/user-roles.reducer';
import { UsersReducer, UsersState } from './reducers/users.reducer';
import {
  UnreadCountReducer,
  UnreadCountState,
} from './reducers/unread-notifications.reducer';
import { DetailsState } from './states/details.states';
import {
  ReleaseNotesReducer,
  ReleaseNotesState,
} from './reducers/release-notes.reducer';
import { CompanyReducer, CompanyState } from './reducers/company.reducer';

export interface AppState {
  tags: TagsState;
  siteGroups: SiteGroupsState;
  company: CompanyState;
  consumers: ConsumersState;
  details: DetailsState;
  deviceTypes: DeviceTypesState;
  software: SoftwareState;
  deviceSummary: DeviceSummaryState;
  releaseNotes: ReleaseNotesState;
  sites: SitesState;
  users: UsersState;
  userGroups: UserGroupsState;
  userRoles: UserRolesState;
  userPersonas: UserPersonasState;
  settings: SettingsState;
  unreadcount: UnreadCountState;
  healthStatus: HealthStatusState;
}

export const AppReducer = {
  tags: TagsReducer,
  siteGroups: SiteGroupsReducer,
  consumers: ConsumersReducer,
  details: DetailsReducer,
  deviceTypes: DeviceTypesReducer,
  software: SoftwareReducer,
  deviceSummary: DeviceSummaryReducer,
  releaseNotes: ReleaseNotesReducer,
  sites: SitesReducer,
  users: UsersReducer,
  userGroups: UserGroupsReducer,
  userRoles: UserRolesReducer,
  userPersonas: UserPersonasReducer,
  settings: EnittySettingsReducer,
  unreadcount: UnreadCountReducer,
  company: CompanyReducer,
  healthStatus: HealthStatusReducer,
};
