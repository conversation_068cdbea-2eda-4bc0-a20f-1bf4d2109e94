export const RESTRICTED_DEVICE_TYPES = ['OMNIA', 'GSOM', 'SmartCrind'];

export const devices = {
  common: {
    status: 'STATUS',
    terminalId: 'TERMINAL ID',
    serial: 'SERIAL',
    ipAddess: 'IP ADDRESS',
    type: 'TYPE',
    release: 'RELEASE',
    model: 'MODEL',
    lastContact: 'LAST CONTACT (UTC+5:30)',
    keyGroup: 'KEY GROUP',
    lastRKI: 'LAST RKI',
    auxiliaryDevice: 'AUXILIARY DEVICE SETUP',
    mainDevice: 'AUX/MAIN DEVICE LINK',
    promtSet: 'PROMPT SET',
    otherInfo: 'OTHER INFO',
    notes: 'NOTES',
    devicesActivity: 'Device Activity',
  },

  versions: {
    id: 4,
    name: 'VERSIONS',
    data: [
      {
        id: 1,
        section: 'PLATFORM',
        platformData: [
          'opt-firmware-ver',
          'opt-platform-ver',
          'opt-system-ver',
          'opt-platform-ver',
          'opt-system-ver',
        ],
      },
      {
        id: 2,
        section: 'VENDOR SPACE',
        vendorData: [
          'invenco-adaptor-ver',
          'invenco-adaptorlink-ver',
          'invenco-emulation-config-ver',
          'invenco-emulation-ver',
          'invenco-icp-ver',
          'invenco-serviceapp-ver',
        ],
      },
    ],
  },
};
