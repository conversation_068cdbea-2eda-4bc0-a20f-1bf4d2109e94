export interface DeviceHealth {
  INACTIVE: number;
  OPERATIONAL: number;
  OUT_OF_SERVICE: number;
  UNKNOWN: number;
}

export interface OOSCategory {}

export interface OOSCondition {}

export interface SiteEvents {
  NEW_SITE: number;
}

export interface SiteStatus {
  CRITICAL: number;
  INACTIVE: number;
  NORMAL: number;
  TOTAL: number;
  UNKNOWN: number;
  WARNING: number;
}

export interface HealthStatus {
  siteStatus: {
    TOTAL: number;
    NORMAL: number;
    WARNING: number;
    CRITICAL: number;
    UNKNOWN: number;
    INACTIVE: number;
    [key: string]: number;
  };
  deviceHealth: {
    OPERATIONAL: number;
    OUT_OF_SERVICE: number;
    UNKNOWN: number;
    INACTIVE: number;
    [key: string]: number;
  };
  siteEvents: {
    NEW_SITE: number;
    [key: string]: number;
  };
  oosCategory: {
    [key: string]: number;
  };
  oosCondition: {
    [key: string]: number;
  };
}
