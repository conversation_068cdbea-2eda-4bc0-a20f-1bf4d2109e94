import { SearchType } from '../models/search-results.model';

export const SEARCH_TYPES: SearchType[] = [
  {
    id: 0,
    name: 'Sites',
    icon: 'search-site-icon ii-site',
    placeholder: 'Search sites',
  },
  { id: 1, name: 'Tags', icon: 'gicon-tags', placeholder: 'Select tags' },
  {
    id: 2,
    name: 'Devices',
    icon: 'gicon-ics-asset-mgmt',
    placeholder: 'Search devices',
  },
];
export const RESOURCES: string[] = [
  'Developers',
  'Release notes',
  'Licenses',
  'User Guide',
];
