import { HttpContextToken } from '@angular/common/http';
import { NavItem } from '../models/navItem.model';

export const TITLE = 'ics-next-v2';
export const API = 'api';
export const TOKEN_KEY = 'token';
export const APP_FEATURE_FLAGS = 'appFeatureFlags';
export const PRESENT = 'PRESENT';
export const FEATURE_FLAGS = 'flags';
export const CSRF = 'csrf';
export const CSRF_HASH = '_csrf';
export const CSRF_HEADER = 'csrf-token';
export const CSRF_HASH_HEADER = 'csrf-token-h';

// feature flags constants
export enum FeatureFlags {
  MEDIA = 'MEDIA',
  FEATURE_REPORTING = 'REPORTING',
  PROVISION_DEVICES = 'PROVISION_DEVICES',
  TAMPER_CLEAR = 'TAMPER_CLEAR',
  OFFLINE_JOBS = 'OFFLINE_JOBS',
  OFFLINE_RKI = 'OFFLINE_RKI',
  DEVICES_SWAP_OUT = 'DEVICES_SWAP_OUT',
  FEATURE_PLAYLIST_MANAGEMENT = 'PLAYLIST_MANAGEMENT',
  FACTORY_RESET = 'FACTORY_RESET',
  CONFIG_MGMT = 'CONFIG_MGMT',
  SITE_DETAIL_V2 = 'SITE_DETAIL_V2',
  SITE_ATTRIBUTES = 'SITE_ATTRIBUTES',
  SHELL_SUPPORT = 'SHELL_SUPPORT',
  USE_KEYCLOAK = 'USE_KEYCLOAK',
  DEVICE_MERCHANT_RESET = 'DEVICE_MERCHANT_RESET',
  ENABLE_FUEL_PRICE_MULTISITES = 'ENABLE_FUEL_PRICE_MULTISITES',
  STAGED_SOFTWARE_ROLLOUT = 'STAGED_SOFTWARE_ROLLOUT',
  BLUEFIN = 'BLUEFIN',
  FILE_DOWNLOAD_APPROVAL_REQUIRED = 'FILE_DOWNLOAD_APPROVAL_REQ',
  DEFAULT = 'default',
  REMOTE_MANAGEMENT = 'REMOTE_MANAGEMENT',
  REMOTE_MGMT = 'remoteMgmt',
  MEDIA_MGMT = 'mediaMgmt',
  PLAYLIST = 'PLAYLIST',
  PLAYLIST_MANAGEMENT = 'PLAYLIST_MANAGEMENT',
  PLAYLIST_MGMT = 'playlistMgmt',
  GSTV = 'GSTV',
  REPORTING = 'reporting',
}

export const INSTALL_TYPE: { [key: number]: string } = {
  0: 'Sequential per site',
  1: 'All at once',
  2: 'Consecutive per site',
};

export const INSTALL_WINDOW_HOURS = [
  '1 hour',
  '3 hours',
  '6 hours',
  '12 hours',
  '18 hours',
  '24 hours',
];

export const INSTALL_FREQUENCY = [
  '15 minutes',
  '30 minutes',
  '45 minutes',
  '1 hour',
];

export const NAME_ONLY = 'NameOnly';

export const passwordRegex = '^(?=.*?[a-zA-z])(?=.*?[0-9])(?!.* ).{12,}$';

export const RESET_PASSWORD = 'reset-password';
export const SIGNUP = 'signup';
export const RESET_MFA = 'reset-mfa';
export const BAR_CODE = 'bar-code';
export const SECRET_KEY = 'secret-key';
export const LOGIN_USER = 'login-user';
export const MERCHANT_RESET = 'merchant-reset';
export const FULL_WIDTH_URL: string[] = [
  '/sites/overview',
  '/sites/config',
  '/sites/media',
  '/sites/versions',
  '/sites/history',
  '/sites/jobs',
  '/settings/account',
  '/settings/reset-password',
  '/settings/reset-password/two-factor',
  '/reports/alarm-history-report',
  '/reports/api-audit-report',
  '/reports/asset-report',
  '/reports/coupon-report',
  '/reports/device-health-status-history-report',
  '/reports/media-playback-history-report',
  '/reports/printer-error-report',
  '/reports/software-rollout-report',
  '/reports/status-report',
  '/reports/user-permissions-report',
  '/reports/version-report',
  '/dashboard',
  '/remote/config-management',
  '/report-management',
  '/fuel-price-management',
];

export const INCLUDE_LOGIN_BUTTON = [
  '/resend-password',
  '/resend-password/reset-email-sent',
  '/signup',
  '/resetpassword',
  '/legal/privacy',
  '/legal/terms',
];
export const AUTHENTICATOR_TOOLTIP = 'Google Authenticator for ';
export const DEVICE_DOWNLOAD_TOOLTIP =
  'Download CSV file based from device list';
export const DEVICE_VISIBLE_TOOLTIP = 'Devices in hidden sites are visible';
export const DEVICE_NOT_VISIBLE_TOOLTIP =
  'Devices in hidden sites are not visible';
export const SITE_DOWNLOAD_TOOLTIP = 'Download CSV file based from sites list';
export const SITE_NOT_VISIBLE_TOOLTIP = 'Some sites are hidden';
export const SITE_VISIBLE_TOOLTIP = 'All sites are visible';
export const LICENSES = 'Licenses';
export const DEVICE_OVERVIEW = 'Device Overview';

export const SITE_GROUP_ID = 'SITE_GROUP_ID';
export const COMPANY_ID = 'COMPANY_ID';

export const RELEASE_ID = 'RELEASE_ID';
export const CSS_CLASS_NAMES = {
  STATUS_FIELD: 'status-field',
  TIMESTAMP: 'timestamp',
  STATUS_MESSAGE: 'status-message',
  BULK_STATUS_FIELD: 'bulk-status-field',
  BULK_TIMESTAMP: 'bulk-timestamp',
  BULK_STATUS_MESSAGE: 'bulk-status-message',
};

export const getAssets = () => {
  const [splitPath] = window.location.hostname.split('.');
  switch (splitPath) {
    case 'localhost': {
      return '/assets/assets/';
    }
    case 'invencocloud': {
      return 'https://module-prod.invencocloud.com/tms-web-v2/assets/';
    }
    default: {
      return `https://module-${splitPath}.invencocloud.com/tms-web-v2/assets/`;
    }
  }
};

export const FILE_PATHS: string[] = [
  `${getAssets()}licenses/license-notes-g6-200.txt`,
  `${getAssets()}licenses/license-notes-g7-100-g6-300.txt`,
  `${getAssets()}licenses/license-notes-sdk.txt`,
  `${getAssets()}licenses/license-notes-ics.txt`,
  `${getAssets()}licenses/license-notes-license-text.txt`,
];

export const COMPANY_ADMIN = 'COMPANY_ADMIN';
export const PCI_DSS_COMPLIANCE = 'PCI_DSS_COMPLIANCE';

export const SKIP_ERROR_TOAST = new HttpContextToken<boolean>(() => false);

export const ID = 'id';

export enum ModalSizes {
  SMALL = 'sm',
  MEDIUM = 'md',
  LARGE = 'lg',
}

export enum ModalConstants {
  WINDOW_CLASS = 'common-details-popup in ',
  CONTAINER_SELECTOR = '#ng-modal-container',
  SIZE_SMALL = ModalSizes.SMALL,
  SIZE_MEDIUM = ModalSizes.MEDIUM,
  SIZE_LARGE = ModalSizes.LARGE,
}

export const DEVICE_TABS = [
  'overview',
  'device-config',
  'media',
  'versions',
  'history',
  'jobs',
  'app-config',
  'network',
  'pull-files',
  'copy-files',
  'challenge-response',
];

export const MEDIA_DOWNLOADS_TABS = ['downloads', 'create', 'copy'];

export enum SidebarViewState {
  DASHBOARD = 'dashboard',
  ASSET_MANAGEMENT = 'asset-management',
  DEVICES = 'devices',
  FILE_DOWNLOADS = 'file-downloads',
  REMOTE_PACKAGES = 'remote-packages',
  FILE_LIBRARY = 'file-library',
  BULK_OPERATIONS = 'bulk-operations',
  CONFIG_MANAGEMENT = 'config-management',
  DEPLOYMENT = 'deployment',
  FUEL_PRICE_MANAGEMENT = 'fuel-price-management',
  MEDIA_LIBRARY = 'media-library',
  PROMPTSETS = 'promptsets',
  MEDIA_DOWNLOADS = 'media-downloads',
  PLAYLIST_CONTENT = 'playlist-content',
  PLAYLIST_COUPON = 'playlist-coupon',
  PLAYLIST = 'playlist',
  REPORT_MANAGEMENT = 'report-management',
  SCHEDULE_LIST = 'schedule-list',
  RKI = 'rki',
  SETTINGS = 'settings',
  SECURE_REPORTS = 'secure-reports',
}

export const PATH_MATCHERS = {
  DASHBOARD: ['dashboard'],
  ASSET_MANAGEMENT: ['asset-management'],
  SITES_ADD: ['sites', 'add'],
  DEVICES: ['devices'],
  SITES: ['sites'],
  DEVICES_UNDER_SITES: DEVICE_TABS,
  REMOTE_DOWNLOADS: ['remote', 'downloads'],
  REMOTE_PACKAGES: ['remote', 'packages'],
  REMOTE_LIBRARY: ['remote', 'library'],
  BULK_OPERATIONS: ['remote', 'bulk-operations'],
  CONFIG_MANAGEMENT: ['remote', 'config-management'],
  DEPLOYMENT: ['deployment'],
  FUEL_PRICE_MANAGEMENT: ['fuel-price-management'],
  MEDIA_LIBRARY: ['media', 'library'],
  PROMPTSETS: ['media', 'promptsets'],
  MEDIA: ['media'],
  MEDIA_DOWNLOADS: MEDIA_DOWNLOADS_TABS,
  PLAYLIST_CONTENT: ['playlist', 'content'],
  PLAYLIST_COUPON: ['playlist', 'coupon'],
  PLAYLIST: ['playlist'],
  PLAYLIST_BUILDER: ['builder'],
  REPORT_MANAGEMENT: ['report-management'],
  SCHEDULE_LIST: ['schedule-list'],
  RKI: ['rki'],
  SETTINGS: ['settings'],
  ACCOUNT: ['account'],
  DOWNLOADS: ['downloads'],
  SECURE_REPORTS: ['secure-reports'],
};

export const SECURE_REPORTS_NAV: NavItem[] = [
  {
    id: 1,
    name: 'Secure Reports',
    featureFlag: 'default',
    isAllowed: true,
    views: [
      {
        id: 1,
        name: 'Secure Reports',
        href: 'secure-reports',
        icon: 'gicon-reporting',
        state: 'secure-reports',
        isAllowed: true,
      },
    ],
  },
];
