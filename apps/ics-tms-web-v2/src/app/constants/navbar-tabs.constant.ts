export interface NavbarTab {
  label: string;
  route: string;
  key: string;
  show?: (deviceTypeId: string) => boolean;
}

export const NAVBAR_TABS: NavbarTab[] = [
  { label: 'Overview', route: 'overview', key: 'overview' },
  { label: 'Device Config', route: 'device-config', key: 'device-config' },
  { label: 'Media', route: 'media', key: 'media' },
  { label: 'Versions', route: 'versions', key: 'versions' },
  { label: 'History', route: 'history', key: 'history' },
  { label: 'Jobs', route: 'jobs', key: 'jobs' },
  { label: 'App Config', route: 'app-config', key: 'app-config' },
  { label: 'Network', route: 'network', key: 'network' },
];
