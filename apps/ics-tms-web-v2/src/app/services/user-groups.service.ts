import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { getApiConstants } from '../modules/module-settings/constants/api';
import { TeamData } from '../modules/module-settings/model/team-data.model';
import { ID } from '../modules/modules-remote/constants/appConstant';
import {
  CommonResponseData,
  UserGroup,
  UserGroupParams,
} from 'src/app/models/common';

@Injectable({
  providedIn: 'root',
})
export class UserGroupsService {
  httpClient = inject(HttpClient);

  // Define the service URL for user groups
  serviceUrl = getApiConstants().settings.teams.getTeams;

  getTeamsData(
    params?: UserGroupParams
  ): Observable<CommonResponseData<UserGroup>> {
    const httpParams = params
      ? new HttpParams({ fromObject: params })
      : new HttpParams();
    return this.httpClient.get<CommonResponseData<UserGroup>>(
      `${getApiConstants().settings.teams.getTeams}`,
      { params: httpParams }
    );
  }

  getAllUserGroups(): Observable<any[]> {
    return this.httpClient.get<any>(this.serviceUrl).pipe(
      map(response => response?.data?.results),
      catchError(error => {
        // Handle error as needed, or rethrow
        console.error('Failed to fetch user groups:', error);
        return throwError(() => error);
      })
    );
  }

  getTeamDataById(id: string): Observable<TeamData> {
    return this.httpClient.get<TeamData>(
      `${getApiConstants().settings.teams.editTeams.getUserGroupById.replace(ID, id)}`
    );
  }

  updateTeam(teamsPayload: TeamData): Observable<TeamData> {
    return this.httpClient.put<TeamData>(
      `${getApiConstants().settings.teams.editTeams.putUpdateTeam.replace(ID, teamsPayload.id)}`,
      teamsPayload
    );
  }

  createTeam(teamsPayload: Omit<TeamData, 'id'>): Observable<TeamData> {
    return this.httpClient.post<TeamData>(
      `${getApiConstants().settings.teams.createTeams.postCreateTeam}`,
      teamsPayload
    );
  }

  deleteTeam(id: string): Observable<boolean> {
    return this.httpClient.delete<boolean>(
      `${getApiConstants().settings.teams.editTeams.deleteTeam.replace(ID, id)}`
    );
  }
}
