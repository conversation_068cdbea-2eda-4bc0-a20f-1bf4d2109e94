import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getBaseUrl } from '../constants/api';
import { Sites, SitesParams } from '../models/sites.model';

@Injectable({
  providedIn: 'root',
})
export class SitesService {
  http = inject(HttpClient);

  getSitesData(params: SitesParams): Observable<Sites> {
    // Construct the API URL according to the required format
    // /rest/v1/entities/sites?fields=siteId&fields=formattedAddress&fields=siteName&fields=siteTags&filters={}&pageIndex=0&pageSize=20

    // Define the fields we want to retrieve
    const fields = ['siteId', 'formattedAddress', 'siteName', 'siteTags'];

    // Construct the query parameters
    const queryParams: any = {};

    // Add fields parameters
    fields.forEach(field => {
      if (!queryParams.fields) {
        queryParams.fields = [];
      }
      queryParams.fields.push(field);
    });

    // Add filters parameter for site name if search text is provided
    // Using $contains instead of $regex as per the API requirements
    const filters = params.q
      ? { siteName: { $contains: params.q } }
      : { siteName: {} };
    queryParams.filters = JSON.stringify(filters);

    // Add pagination parameters
    queryParams.pageIndex = params.pageIndex;
    queryParams.pageSize = params.pageSize;

    // Use the correct API endpoint
    // The getBaseUrl() already includes '/v1', so we should use just '/entities/sites'
    return this.http.get<Sites>(`${getBaseUrl()}/entities/sites`, {
      params: queryParams,
    });
  }
}
