import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
// import { getApiConstants } from '../constants/api';
import { inject, Injectable } from '@angular/core';
import { getApiConstants } from '../constants/api';
import { HealthStatus } from '../constants/health-status';

@Injectable({
  providedIn: 'root',
})
export class HealthStatusService {
  httpClient = inject(HttpClient);

  getHealthStatusData(param: string): Observable<HealthStatus> {
    const httpParams = new HttpParams().set('type', param);
    return this.httpClient.get<HealthStatus>(
      getApiConstants().app.getHealthStatus,
      { params: httpParams }
    );
  }
}
