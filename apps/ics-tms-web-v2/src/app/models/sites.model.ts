export interface SitesResults {
  // Fields that can be either from original model or API response
  id?: string; // Original id field, may be replaced by siteId in API responses
  siteId?: string; // From API response
  name?: string; // Original name field, may be replaced by siteName in API responses
  siteName?: string; // From API response
  formattedAddress?: string; // Common to both

  // API response specific fields
  siteTags?: string[];
  totalDevices?: number;

  // Original model fields
  address?: string;
  allowedExternalReferenceTypes?: [];
  contactEmail?: string;
  contactPhone?: string;
  created?: string;
  externalReferences?: [
    {
      referenceId: string;
      referenceType: string;
    },
  ];
  isDefault?: boolean;
  keyGroup?: {
    id: string;
    name: string;
    owner: {
      id: string;
      name: string;
    };
    ref: string;
  };
  latitude?: number;
  longitude?: number;
  owner?: {
    id: string;
    name: string;
  };
  referenceId?: string;
  siteGroups?: [
    {
      id: string;
      name: string;
    },
  ];
  status?: number;
  suppressOffhoursAlarm?: boolean;
  tags?: [
    {
      id: number;
      name: string;
      siteCount: number;
    },
  ];
  timezoneId?: string;
  visible?: boolean;
}

export interface Sites {
  results: SitesResults[];
  // Support both metadata formats based on the API response
  resultMetadata?: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  // The actual format from the API response
  resultsMetadata?: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
}

export interface SitesParams {
  autoPoll: boolean;
  pageIndex: number;
  pageSize: number;
  showHiddenSites: boolean;
  tags?: string;
  q?: string;
}
