import {
  MediaEditActionButton,
  MediaEditAction<PERSON>ey,
  MediaUpdateActionButton,
  MediaUpdateAction<PERSON>ey,
  <PERSON><PERSON>,
  User,
} from '../models/media-library.modal';
import {
  Actions,
  PermissionModal,
  SortOption,
} from '../models/prompt-sets.modal';
import { ModalConstants } from 'src/app/constants/appConstants';

export const ITEM_ID = 'ITEM_ID';
export const MEDIA = 'media';
export const PAGE_SIZE = 20;
export const PAGE = 'page';
export const NAME = 'name';

export const SIZES: Size[] = [
  {
    name: 'Small',
    maxHeight: 479,
    maxWidth: 639,
    minHeight: 0,
    minWidth: 0,
  },
  {
    name: 'Medium',
    maxHeight: 800,
    maxWidth: 1280,
    minHeight: 480,
    minWidth: 640,
  },
  {
    name: 'Large',
    minHeight: 801,
    minWidth: 1281,
  },
  {
    name: 'Exactly 640 x 480',
    maxHeight: 480,
    maxWidth: 640,
    minHeight: 480,
    minWidth: 640,
  },
  {
    name: 'Exactly 1280 x 800',
    maxHeight: 800,
    maxWidth: 1280,
    minHeight: 800,
    minWidth: 1280,
  },
];

export const ORDER: { [key: string]: string } = {
  'Last Uploaded': 'uploaded',
  Name: 'name',
  Creator: 'uploader',
};

export const TARGET_ID = 'TARGET_ID';

export const MEDIA_PLACEHOLDER = {
  g6: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAEsCAYAAADtt+XCAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxMzggNzkuMTU5ODI0LCAyMDE2LzA5LzE0LTAxOjA5OjAxICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+IEmuOgAAAepJREFUeJztwTEBAAAMw6D6N53Z2AGsagAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHx2YAoD/ZL3kyAAAAAASUVORK5CYII=',
  g7: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAD6CAYAAACPpxFEAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxMzggNzkuMTU5ODI0LCAyMDE2LzA5LzE0LTAxOjA5OjAxICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+IEmuOgAAAZ1JREFUeJztwTEBAAAMw6D6N52p2AesagAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMCPA/kAA/2kTpmnAAAAAElFTkSuQmCC',
  error:
    'data:image/png;base64,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',
};

export const FILE = 'file';
export const ERROR_ICON = 'gicon-error';
export const IMAGE_ICON = 'gicon-photo_horz';
export const IMAGE_UPLOADED_ICON = 'gicon-photo_horz image-uploaded';
export const VIDEO_ICON = 'gicon-video';
export const VIDEO_UPLOADED_ICON = 'gicon-video video-uploaded';
export const FILE_ICON = 'gicon-file';
export const FILE_UPLOADED_ICON = 'gicon-file file-uploaded';
export const ERROR_FILE = 'error-file';
export const ERROR_MSG = `.jpg,.png,.gif,.webm,.pkg,.tar.gz.bin,.tgz.bin,.pkg,.zip,.ttf,.otf
`;
export const NULL = 'null';
export const ASSET_NAME = 'assetName';
export const ASSET_PACKAGE_NAME = 'assetPackageName';
export const DEVICE_TYPE = 'deviceType';
export const FILE_CONTENT = 'fileContent';
export const PUB_STATUS = 'pubstatus';
export const ASSET_ERROR = 'Something happened. Try again.';
export const UPLOADED = 'uploaded';
export const MAX_DB_FILE_SIZE = '500MB';

export const ACTIONS: Actions[] = [
  {
    name: 'Create New',
    icon: 'fa fa-plus',
  },
  {
    name: 'Import',
    icon: 'fa fa-download',
  },
];

export const PERMISSION_MODAL: PermissionModal[] = [
  {
    name: 'DRAFT',
    permissions: {
      MEDIA_DESIGNER: ['Edit', 'Create Preview', 'Clone', 'Delete', 'Export'],
      MEDIA_APPROVER: ['View'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'GENERATING_PREVIEW',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'PREVIEW',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Deploy', 'Request Approval', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'FOR_APPROVAL',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Details', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View', 'Approve', 'Reject', 'Details'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'REJECTED',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Details', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View', 'Details'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'PUBLISHING',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View', 'Details'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'PUBLISHED',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View', 'Details'],
      MEDIA_DEPLOYER: ['View', 'Deploy'],
    },
  },
  {
    name: 'PREVIEW_GENERATION_FAILED',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View', 'Details'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
  {
    name: 'PUBLISHING_FAILED',
    permissions: {
      MEDIA_DESIGNER: ['View', 'Clone', 'Delete'],
      MEDIA_APPROVER: ['View', 'Details'],
      MEDIA_DEPLOYER: ['View'],
    },
  },
];

export function GET_STATUS_COLOR(status: string) {
  switch (status) {
    case 'DRAFT':
      return 'var(--md-grey-700)';
    case 'GENERATING_PREVIEW':
      return 'var(--md-lime-800)';
    case 'PREVIEW_GENERATION_FAILED':
      return 'var(--md-red-600)';
    case 'PREVIEW':
      return 'var(--color-details-preview-lable-blue)';
    case 'FOR_APPROVAL':
      return 'var(--label-inactive)';
    case 'REJECTED':
      return 'var(--md-red-600)';
    case 'PUBLISHING':
      return 'var(--md-indigo-600)';
    case 'PUBLISHED':
      return 'var(--label-success)';
    case 'PUBLISHING_FAILED':
      return 'var(--md-red-600)';
    default:
      return '';
  }
}

export function GET_STATUS(status: string) {
  switch (status) {
    case 'DRAFT':
      return 'DRAFT';
    case 'GENERATING_PREVIEW':
      return 'GENERATING PREVIEW';
    case 'PREVIEW':
      return 'PREVIEW';
    case 'FOR_APPROVAL':
      return 'FOR APPROVAL';
    case 'REJECTED':
      return 'REJECTED';
    case 'PUBLISHING':
      return 'PUBLISHING';
    case 'PUBLISHED':
      return 'PUBLISHED';
    case 'PREVIEW_GENERATION_FAILED':
      return 'PREVIEW GENERATION FAILED';
    case 'PUBLISHING_FAILED':
      return 'PUBLISHING FAILED';
    default:
      return '';
  }
}

export function GET_APPROVAL_STATUS_COLOR(status: string | null) {
  switch (status) {
    case 'APPROVE':
      return 'var(--label-success)';
    case 'REJECT':
      return 'var(--md-red-600)';
    default:
      return 'var(--label-unknown';
  }
}

export function GET_APPROVAL_STATUS(status: string | null) {
  switch (status) {
    case 'APPROVE':
      return 'Approved';
    case 'REJECT':
      return 'Rejected';
    default:
      return 'Pending';
  }
}

export const SORT_OPTIONS: SortOption[] = [
  {
    id: 0,
    name: 'Last modified',
    title: 'Last modified',
    value: 'modified',
  },
  {
    id: 1,
    name: 'Name',
    title: 'Name',
    value: 'name',
  },
  {
    id: 2,
    name: 'Creator',
    title: 'Creator',
    value: 'creator',
  },
];

export const FILTER_ITEMS = [
  {
    group: 'Status',
    param: 'status',
    name: 'Draft',
    value: 'DRAFT',
  },
  {
    group: 'Status',
    param: 'status',
    name: 'Preview',
    value: 'PREVIEW',
  },
  {
    group: 'Status',
    param: 'status',
    name: 'For Approval',
    value: 'FOR_APPROVAL',
  },
  {
    group: 'Status',
    param: 'status',
    name: 'Published',
    value: 'PUBLISHED',
  },
  {
    group: 'Status',
    param: 'status',
    name: 'Rejected',
    value: 'REJECTED',
  },
];

export function PROMPT_BUILDER_ICONS(type: string) {
  switch (type) {
    case 'bg':
      return 'fa-square-o';
    case 'image':
      return 'fa-picture-o';
    case 'text':
      return 'fa-font';
    case 'touchmask':
      return 'fa-hand-pointer-o';
    case 'area':
      return 'fa-square';
    case 'video':
      return 'fa-video-camera';
    case 'input':
      return 'fa-i-cursor';
    default:
      return 'fa-square-o';
  }
}

export function PROMPT_ASSET_NAMES(type: string) {
  switch (type) {
    case 'bg':
      return 'Background';
    case 'image':
      return 'Image';
    case 'text':
      return 'Text';
    case 'touchmask':
      return 'Touch Mask';
    case 'area':
      return 'Touch Area';
    case 'video':
      return 'Video';
    case 'input':
      return 'Input';
    default:
      return type;
  }
}

export const MEDIA_TABS = ['IMAGES', 'VIDEOS', 'FONTS'];

export const MEDIA_LIBRARY_SORTING_OPTIONS: Record<string, string> = {
  uploaded: 'Last Uploaded',
  name: 'Name',
  uploader: 'Creator',
};

export const DEFAULT_USER: User = { id: '', user: '' };
export const DEFAULT_SIZE: Size = { name: '', minHeight: 0, minWidth: 0 };
export const DEFAULT_SORT = 'uploaded';
export const CREATED_BY_ME_LABEL = 'Created by me';
export const SIZE = 'Size';
export const USER = 'User';
export const USER_PROP = 'user';
export const FONTS = 'FONTS';

export const MEDIA_TYPE_BADGE_CLASS_MAP: Record<string, string> = {
  IMAGE: 'bg-light-blue',
  VIDEO: 'bg-orange-yellow',
  FONT: 'bg-dark-blue',
};

export const FONT_PREVIEW_CHARS = `
ABCDEFGHIJKLMNOPQRSTUVWXYZ
abcdefghijklmnopqrstuvwxyz
1234567890
@#%^&*()_+{}[]\\|;'":/.,><\`~!
`;

export const SCROLL_THRESHOLD = 1;

export const MEDIA_MODAL_BACKDROP_CLASS = 'media-modal-backdrop';

export const MIME_TYPE = 'mimeType';

export const FONT_FACE_TEMPLATE = (fontFamily?: string, url?: string) => `
  @font-face {
    font-family: '${fontFamily}';
    src: url('${url}');
  }
`;

export const STYLE = 'style';
export const LARGE = 'Large';

export const G6 = 'g6';
export const SMOOTH = 'smooth';
export const MODAL_OPEN = 'modal-open';

export const MEDIA_EDIT_BUTTONS: MediaEditActionButton[] = [
  {
    label: 'Edit',
    actionKey: MediaEditActionKey.EDIT,
    aria: 'Edit media name',
  },
  {
    label: 'Delete',
    actionKey: MediaEditActionKey.DELETE,
    aria: 'Delete media',
  },
];

export const MEDIA_UPDATE_BUTTONS: MediaUpdateActionButton[] = [
  {
    label: 'Cancel',
    class: 'btn-default',
    ariaLabel: 'Cancel editing',
    key: MediaUpdateActionKey.CANCEL,
  },
  {
    label: 'Update',
    class: 'btn-primary btn-update',
    ariaLabel: 'Update media name',
    key: MediaUpdateActionKey.UPDATE,
    showSpinner: true,
    loadingLabel: 'Updating',
  },
];

export const MEDIA_DELETE_MESSAGE =
  ' will be removed from the library. Do you wish to continue?';
export const PROMPT_SET_MEDIA_DELETE_MESSAGE =
  ' will be removed from the library but remain available to promptsets currently using it. Do you wish to continue?';

export const NO_MEDIA_FOUND_TEXT = {
  title: 'No Files Found',
  desc: 'Media you upload will be shown here.',
  icon: 'gicon-photo',
};

export const TOAST_MESSAGES = {
  UPLOAD_SUCCESS: '{count} of {total} items uploaded successfully!',
  FAILED_TO_FETCH_STATUS: 'Failed To Fetch Status',
  TOAST_ITEM_DELETED: 'Item Deleted',
  ITEM_UPDATED: 'Item Updated',
  ITEM_UPDATE_FAILED: 'Item Update Failed',
};

export const DOCUMENT_CLICK = 'document:click';
export const $EVENT = '$event';
export const WINDOW_SCROLL = 'window:scroll';

export const WINDOW_CLASSES = {
  UPLOAD_DETAILS_MODAL: `${ModalConstants.WINDOW_CLASS} upload-details-modal`,
  MEDIA_MODAL_POPUP: `${ModalConstants.WINDOW_CLASS} media-modal-popup`,
  DELETE_MEDIA_POPUP: `${ModalConstants.WINDOW_CLASS} delete-media-popup`,
};

export const FACE = 'face';

export const SLIDE_TYPES = {
  FONT_SLIDE: 'fontSlide',
  MEDIA_SLIDE: 'mediaSlide',
};

export enum FileTypePattern {
  IMAGE = '\\.(png|jpg|gif)$',
  VIDEO = '\\.(webm)$',
  FONT = '\\.(ttf|otf)$',
  TTF = '\\.(ttf)$',
  ASSET = '\\.(png|jpg|gif|webm|otf|ttf)$',
  PACKAGE = '\\.(pkg|tar\\.gz\\.bin|tgz\\.bin|zip)$',
  EXPANDED_FONT = '\\.(ttf|otf|woff|woff2|font)$',
  VALID_NAME = '^[A-Za-z0-9-._]+$',
}

export enum FontTypes {
  TTF = 'font/ttf',
  OTF = 'font/otf',
}

export enum MediaErrorMessages {
  SPECIAL_CHARS = ' contains special characters',
  GB = '1GB',
}

export enum GroupFilterDefaults {
  TITLE = 'Filter By:',
  TOOLTIP_TEXT = 'Filter Options',
  ICON_CLASS = 'gicon-filter',
}

export enum MediaTypes {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  FONT = 'FONT',
}

export enum MediaStatus {
  SUCCESS = 'success',
  ERROR = 'error',
}
export enum ImageTypes {
  ERROR = 'img/error.png',
  G6_IMG = 'img/g6.png',
  G7_IMG = 'img/g7.png',
}
export enum MimeTypes {
  GIF = 'gif',
  WEBM = 'webm',
}

export enum MediaDateFormats {
  FULL_DATE_TIME = 'MMM DD, YYYY [at] hh:mm a',
  DATE_ONLY = 'MMM D, YYYY',
  WEEKDAY_TIME = 'ddd h:mm A',
}

export const MEDIA_SORTING_TITLES: { [key: string]: string } = {
  uploaded: 'Recent Media',
  name: 'Name',
  uploader: 'Media by Creator',
};
