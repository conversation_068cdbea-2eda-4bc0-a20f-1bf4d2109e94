import { TemplateRef } from '@angular/core';

import { FilterItem } from 'src/app/models/common';

export interface MediaLibraryResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: MediaLibrary[];
}

export interface MediaLibrary {
  id?: string;
  name?: string;
  width?: null;
  height?: null;
  status?: string;
  size?: number;
  type?: string;
  uploaded?: string;
  company?: string;
  thumbnailUrl?: null;
  sourceUrl?: string;
  uploader?: Uploader;
  properties?: Record<string, string>;
  active?: boolean;
  deviceType?: string;
}

export interface Uploader {
  id?: string;
  fullName?: string;
  company?: Company;
}

export interface Company {
  id: string;
  name: string;
}

export interface LibraryParams {
  [key: string]: any;
  order: string;
  pageIndex: number;
  pageSize: number;
  type: string;
  maxHeight?: number;
  maxWidth?: number;
  minHeight?: number;
  minWidth?: number;
  uploader?: string;
}

export interface Size {
  name: string;
  maxHeight?: number;
  maxWidth?: number;
  minHeight: number;
  minWidth: number;
}

export interface User {
  id: string;
  user: string;
}
export interface Filters {
  user: User;
  size: Size;
}

export interface UploadingFile {
  id: number;
  name: string;
  uploadProgress: number;
  uploadInProgress: boolean;
  isErrorFile: boolean;
  errorMsg: string;
  type: string;
}

export interface GenericFilterGroup<T> {
  name: string;
  options: Array<FilterItem<T>>;
}

export interface MediaLibraryFilterGroup {
  name: string;
  options: Array<FilterItem<Size | User>>;
}

export interface MediaTemplateData extends MediaLibrary {
  derivedSrc?: string | null;
  index: number;
}

export interface MediaLibraryTemplate {
  $implicit: MediaTemplateData;
  index: number;
}

export type SlideTemplate = {
  data: MediaTemplateData;
  template: TemplateRef<MediaLibraryTemplate>;
};

export interface MediaEditActionButton {
  label: string;
  aria: string;
  actionKey: MediaEditActionKey;
}

export interface MediaUpdateActionButton {
  label: string;
  class: string;
  ariaLabel: string;
  key: MediaUpdateActionKey;
  showSpinner?: boolean;
  loadingLabel?: string;
}

export interface PublishStatus {
  name: string;
  published: number;
  unpublished: number;
}

export enum MediaEditActionKey {
  EDIT = 'edit',
  DELETE = 'delete',
}

export enum MediaUpdateActionKey {
  CANCEL = 'cancel',
  UPDATE = 'update',
}
