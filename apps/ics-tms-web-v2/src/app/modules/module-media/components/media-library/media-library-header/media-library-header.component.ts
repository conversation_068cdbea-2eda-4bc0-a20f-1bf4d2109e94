import {
  Component,
  inject,
  OnD<PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash';
import { BehaviorSubject, Observable } from 'rxjs';
import { getSelectedMediaDataItemsCount } from '../../../store/selectors/library.selectors';
import { MediaLibraryService } from '../../../services/media-library.service';
import {
  MEDIA_SORTING_TITLES,
  MODAL_OPEN,
  UPLOADED,
  WINDOW_CLASSES,
} from '../../../constants/appConstants';
import { MediaUploadDetailsComponent } from './media-upload-details/media-upload-details.component';
import { ModalConstants } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-media-library-header',
  templateUrl: './media-library-header.component.html',
  styleUrls: ['./media-library-header.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MediaLibraryHeaderComponent implements OnInit, OnDestroy {
  itemsCount$!: Observable<number>;

  isUploading$ = new BehaviorSubject<boolean>(false);

  mediaLibraryTitle = MEDIA_SORTING_TITLES[UPLOADED];

  private readonly store = inject(Store);

  private readonly mediaLibraryService = inject(MediaLibraryService);

  private readonly modalService = inject(NgbModal);

  ngOnInit(): void {
    this.handleSubscriptions();
  }

  ngOnDestroy(): void {
    document.body.classList.remove(MODAL_OPEN);
  }

  handleSubscriptions() {
    this.itemsCount$ = this.store.select(getSelectedMediaDataItemsCount);
    this.mediaLibraryService.getSelectedOrder().subscribe(order => {
      this.mediaLibraryTitle = MEDIA_SORTING_TITLES[order];
    });
  }

  handleFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const { files } = input;
    if (isEmpty(files)) return;

    this.isUploading$.next(true);
    document.body.classList.add(MODAL_OPEN);

    const modalRef = this.modalService.open(MediaUploadDetailsComponent, {
      windowClass: WINDOW_CLASSES.UPLOAD_DETAILS_MODAL,
      backdrop: false,
      container: ModalConstants.CONTAINER_SELECTOR,
    });

    modalRef.componentInstance.files = files;

    modalRef.result.then(() => {
      this.isUploading$.next(false);
      input.value = '';
    });
  }

  mayEditMedia(): boolean {
    return this.mediaLibraryService.mayEditMedia();
  }
}
