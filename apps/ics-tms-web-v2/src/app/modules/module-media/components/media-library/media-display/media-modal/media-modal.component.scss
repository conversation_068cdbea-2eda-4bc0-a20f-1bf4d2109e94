$media-gradient:
  45deg,
  var(--md-grey-10) 25%,
  transparent 25%,
  transparent 75%,
  var(--md-grey-10) 75%,
  var(--md-grey-10);

.media-modal-container {
  display: flex;
  background-color: var(--color-black-shade-one);
  outline: none;
  margin: 0 !important;

  .media-carousel {
    height: 100%;
    padding: 0;
    .media-carousel-responsive {
      padding-bottom: 75%;
      position: relative;
      display: block;
      overflow: hidden;
      height: 0;
      .media-carousel-display {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
        .media-carousel-item-flex {
          > img {
            display: block;
            box-shadow: 0 0.5rem 3.5rem var(--md-black-65);
          }
          .letterbox-image {
            max-width: 100%;
            height: auto;
            max-height: 100%;
          }
          .bg-checkerboard {
            background-color: var(--white);
            background-image: linear-gradient($media-gradient),
              linear-gradient($media-gradient);
            background-position:
              0 0,
              1rem 1rem;
            background-size: 2.1rem 2.1rem;
          }
          .font-preview {
            width: 100%;
            height: 100%;
            background-color: var(--md-red-grey-600);
            > pre {
              font-size: 2em;
              position: relative;
              top: 50%;
              overflow: hidden;
              width: 100%;
              transform: translateY(-50%);
              text-align: center;
              white-space: pre-line;
              border: none;
              background: none;
            }
          }
          &.font {
            padding: 0 !important;
          }
        }
      }
    }
    .media-carousel-counter {
      position: relative;
      top: -2rem;
      font-size: 1.3rem;
      color: var(--white);
      text-align: center;
    }
  }

  .media-modal-info {
    padding: 0;
    word-break: break-word;
    hyphens: auto;
    overflow-wrap: break-word;
    background-color: var(--white);
    .media-modal-meta {
      padding: 1rem 1rem 0 2rem;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .btn-modal-close {
        margin-left: auto;
        font-size: 1.6rem;
        line-height: 1;
        padding: 0.6rem 1rem;
      }
    }
    .media-name {
      padding: 0 2rem;
      font-size: 1.8rem;
      margin: 2rem 0;
    }
    .edit-name-container {
      padding: 0 2rem;
      margin: 1rem 0rem 2rem;
      .media-update-btn-group {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 1rem;
      }
    }
    .media-details {
      padding: 1rem 2rem 0;
      border-top: 0.1rem solid var(--md-red-grey-600);
      .details-title {
        font-size: 1.3rem;
        color: var(--md-grey-800);
        margin: 1rem 0 2rem;
      }
      .details-list {
        margin: 0;
        padding-left: 0;
        list-style: none;
        > li {
          font-size: 1.5rem;
          position: relative;
          margin-bottom: 2.5rem;
          display: flex;
          align-items: flex-start;
          > i {
            font-size: 1.8rem;
            color: var(--md-grey-700);
          }
          .list-sub-meta {
            font-size: 1.3rem;
            color: var(--md-grey-800);
          }
          .font-properties {
            &.list-sub-meta {
              margin-top: 0.3rem;
            }
          }
        }
      }
    }
  }
}

.delete-media-popup {
  overflow: hidden !important;
  .modal-content {
    border-radius: 0 !important;
  }
}
