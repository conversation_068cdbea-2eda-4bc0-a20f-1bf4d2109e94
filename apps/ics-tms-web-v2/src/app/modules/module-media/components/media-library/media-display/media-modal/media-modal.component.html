<div
  *ngIf="(mediaData$ | async)?.length ?? 0 > 0"
  class="row media-modal-container"
>
  <div class="col-md-8 media-carousel">
    <div class="media-carousel-responsive">
      <div class="media-carousel-display">
        <app-media-carousel
          [items]="(slideTemplates$ | async)!"
          [(activeIndex)]="currentIndex"
          [type]="type"
        ></app-media-carousel>
        <ng-template let-item let-i="index" #fontSlide>
          <div *ngIf="item.type === 'FONT'" class="font-preview">
            <style>
              @font-face {
                font-family: '{{item.id}}';
                src: url('{{item.sourceUrl}}');
              }
            </style>
            <pre [style.font-family]="'\'' + item?.id + '\''">
                {{ FONT_PREVIEW_CHARS }}</pre
            >
          </div>
        </ng-template>
        <ng-template let-item let-i="index" #mediaSlide>
          <img
            *ngIf="isMediaRenderable(item)"
            class="img-responsive bg-checkerboard letterbox-image"
            [src]="item.derivedSrc"
            (error)="onImageError($event)"
          />
        </ng-template>
      </div>
    </div>
    <div class="media-carousel-counter">
      {{ currentIndex + 1 }} of {{ mediaCount$ | async }} items
    </div>
  </div>

  <div class="col-md-4 media-modal-info">
    <div class="media-modal-meta clearfix">
      <div
        class="btn-group btn-group-sm"
        role="group"
        aria-label="Media Edit actions"
        *ngIf="mayEdit()"
      >
        <button
          *ngFor="let btn of editButtons"
          type="button"
          class="btn btn-default"
          [disabled]="isMediaEditButtonDisabled()"
          (click)="handleMediaEditAction(btn.actionKey)"
          [attr.aria-label]="btn.aria"
          tabindex="-1"
        >
          {{ btn.label }}
        </button>
      </div>
      <button
        type="button"
        class="btn btn-circle btn-modal-close"
        aria-label="Close modal"
        (click)="closeModal()"
        tabindex="-1"
      >
        <span class="icon gicon-close" aria-hidden="true"></span>
      </button>
    </div>

    <h3
      *ngIf="!(isEditing$ | async) && selectedSlideData"
      class="media-name"
      [ngClass]="{ 'mt-0': !mayEdit() }"
    >
      {{ selectedSlideData.name }}
    </h3>
    <div
      *ngIf="(isEditing$ | async) && selectedSlideData && mayEdit()"
      role="form"
      aria-labelledby="edit-name-label"
      class="edit-name-container"
    >
      <div class="form-group">
        <label id="edit-name-label" for="media-name-input">Name</label>
        <input
          type="text"
          class="ics-input"
          [(ngModel)]="editedMediaName"
          spellcheck="false"
          [disabled]="!!(isUpdating$ | async)"
          maxlength="255"
        />
      </div>

      <div class="media-update-btn-group">
        <button
          *ngFor="let btn of updateButtons"
          class="btn btn-sm"
          [ngClass]="btn.class"
          [disabled]="isMediaUpdateButtonDisabled(btn.key)"
          (click)="handleMediaUpdateAction(btn.key)"
          [attr.aria-label]="btn.ariaLabel"
          type="button"
        >
          {{
            (isUpdating$ | async) ? btn.loadingLabel || btn.label : btn.label
          }}
          <span
            *ngIf="btn.showSpinner && (isUpdating$ | async)"
            class="fa fa-spinner fa-pulse fa-fw"
            role="status"
            aria-hidden="true"
          ></span>
        </button>
      </div>
    </div>
    <div class="media-details" *ngIf="selectedSlideData">
      <h5 class="details-title">Details</h5>
      <ul class="details-list">
        <li aria-label="Media type and dimensions">
          <i class="gicon-terminal"></i>
          <div class="pl-20" *ngIf="type !== 'FONT'">
            <div>{{ selectedSlideData.deviceType }}</div>
            <div class="list-sub-meta">
              <span>{{ selectedSlideData.width || 'N/A' }}</span> &times;
              <span>{{ selectedSlideData.height || 'N/A' }}</span>
            </div>
          </div>
          <div class="pl-20" *ngIf="type === 'FONT'">
            <div for="font-properties">Properties</div>
            <div
              class="font-properties"
              *ngFor="
                let prop of getPropertiesArray(selectedSlideData?.properties)
              "
            >
              <div class="list-sub-meta">{{ prop.key }}: {{ prop.value }}</div>
            </div>
          </div>
        </li>
        <li aria-label="Modified date">
          <i class="gicon-calendar_schedule"></i>
          <div class="pl-20">
            <div aria-label="Upload date">
              {{ getDisplayDate(selectedSlideData.uploaded) }}
            </div>

            <div class="list-sub-meta" aria-label="Upload time">
              {{ getDisplayWeekdayTime(selectedSlideData.uploaded) }}
            </div>
          </div>
        </li>
        <li aria-label="Creator">
          <i class="gicon-account_box"></i>
          <div class="pl-20">{{ selectedSlideData.uploader?.fullName }}</div>
        </li>
      </ul>
    </div>
  </div>
</div>
