<div class="media-library-container">
  <div class="navbar navbar-default navbar-toolbar">
    <div class="container-fluid">
      <app-media-library-header
        class="navbar-header media-navbar-header"
      ></app-media-library-header>
      <div class="navbar-collapse media-navbar-collapse">
        <app-media-tabs
          class="masthead media-masthead"
          [ngClass]="{ 'media-tabs-mobile': isMobileView() }"
        ></app-media-tabs>
        <app-media-library-filter
          class="navbar-right media-library-filters"
        ></app-media-library-filter>
      </div>
    </div>
  </div>
  <app-media-display class="media-infinite-load"></app-media-display>
</div>
