import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  NgbActiveModal,
  NgbModal,
  NgbModalRef,
} from '@ng-bootstrap/ng-bootstrap';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import { DeleteAssetPopupComponent } from './delete-asset-popup/delete-asset-popup.component';
import {
  MediaEditActionKey,
  MediaLibrary,
  MediaLibraryTemplate,
  MediaUpdateActionKey,
  PublishStatus,
  SlideTemplate,
} from 'src/app/modules/module-media/models/media-library.modal';
import { MediaLibraryService } from 'src/app/modules/module-media/services/media-library.service';
import { ToastService } from 'src/app/services/toast.service';
import {
  FONT_PREVIEW_CHARS,
  ImageTypes,
  MEDIA_EDIT_BUTTONS,
  MEDIA_MODAL_BACKDROP_CLASS,
  MEDIA_UPDATE_BUTTONS,
  MediaDateFormats,
  MediaStatus,
  MediaTypes,
  SLIDE_TYPES,
  TOAST_MESSAGES,
  WINDOW_CLASSES,
} from 'src/app/modules/module-media/constants/appConstants';
import { updateMediaName } from 'src/app/modules/module-media/store/actions/library.actions';
import {
  getSelectedMediaData,
  getSelectedMediaDataItemsCount,
} from 'src/app/modules/module-media/store/selectors/library.selectors';
import { getAssets, ModalConstants } from 'src/app/constants/appConstants';
import { resolveCamelCase } from 'src/app/modules/module-media/utils/media-utils';

@Component({
  selector: 'app-media-modal',
  templateUrl: './media-modal.component.html',
  styleUrls: ['./media-modal.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MediaModalComponent implements OnInit, OnDestroy {
  @Input() mediaItemId!: string;

  @Output() updateService = new EventEmitter<MediaLibrary>();

  public readonly FONT_PREVIEW_CHARS = FONT_PREVIEW_CHARS;

  public imageLoadStatus = new Map<number, MediaStatus>();

  public readonly slideTemplates$ = new BehaviorSubject<SlideTemplate[]>([]);

  @ViewChild(SLIDE_TYPES.FONT_SLIDE, { static: false })
  fontSlide!: TemplateRef<MediaLibraryTemplate>;

  @ViewChild(SLIDE_TYPES.MEDIA_SLIDE, { static: false })
  mediaSlide!: TemplateRef<MediaLibraryTemplate>;

  currentIndex = -1;

  type: string = MediaTypes.IMAGE;

  editedMediaName = '';

  public readonly editButtons = MEDIA_EDIT_BUTTONS;

  public readonly updateButtons = MEDIA_UPDATE_BUTTONS;

  private readonly mediaLibraryService = inject(MediaLibraryService);

  private readonly store = inject(Store);

  private readonly activeModal = inject(NgbActiveModal);

  private readonly modalService = inject(NgbModal);

  private readonly toastService = inject(ToastService);

  private readonly destroy$ = new Subject<void>();

  public readonly mediaData$ = new BehaviorSubject<MediaLibrary[]>([]);

  public readonly isUpdating$ = new BehaviorSubject<boolean>(false);

  public readonly isEditing$ = new BehaviorSubject<boolean>(false);

  public readonly mediaCount$ = this.store.select(
    getSelectedMediaDataItemsCount
  );

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngAfterViewInit(): void {
    this.store
      .select(getSelectedMediaData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(mediaData => {
        this.updateSlideTemplates(mediaData);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    this.store
      .select(getSelectedMediaData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(mediaData => {
        this.mediaData$.next(mediaData);
        this.processMediaData();
      });
  }

  private processMediaData(): void {
    const mediaData = this.mediaData$.getValue();
    if (isEmpty(mediaData)) {
      return;
    }

    if (this.currentIndex === -1) {
      this.currentIndex = mediaData.findIndex(
        (item: MediaLibrary) => item.id === this.mediaItemId
      );
    }

    this.type = this.getMediaType(mediaData[0]?.type);
    this.updateSelectedSlide();
  }

  private updateSlideTemplates(mediaData: MediaLibrary[]): void {
    const templates: SlideTemplate[] = mediaData.map((item, i) => {
      let template: TemplateRef<MediaLibraryTemplate>;
      switch (item.type) {
        case MediaTypes.FONT:
          template = this.fontSlide;
          break;
        case MediaTypes.IMAGE:
        case MediaTypes.VIDEO:
          template = this.mediaSlide;
          break;
        default:
          template = this.mediaSlide;
      }

      return {
        data: {
          ...item,
          derivedSrc: this.getMediaSource(item),
          index: i,
        },
        template,
      };
    });

    this.slideTemplates$.next(templates);
  }

  private getMediaType(type?: string): string {
    switch (type) {
      case MediaTypes.IMAGE:
        return MediaTypes.IMAGE;
      case MediaTypes.VIDEO:
        return MediaTypes.VIDEO;
      case MediaTypes.FONT:
        return MediaTypes.FONT;
      default:
        return MediaTypes.IMAGE;
    }
  }

  private updateSelectedSlide(): void {
    const mediaData = this.mediaData$.getValue();
    if (this.currentIndex >= 0 && this.currentIndex < mediaData.length) {
      this.editedMediaName = this.selectedSlideData?.name || '';
      this.isEditing$.next(false);
    }
  }

  closeModal(): void {
    this.activeModal.close();
  }

  get selectedSlideData(): MediaLibrary {
    return this.mediaData$.getValue()[this.currentIndex];
  }

  editName(): void {
    if (!this.selectedSlideData) {
      return;
    }
    this.isEditing$.next(true);
    this.editedMediaName = this.selectedSlideData?.name ?? '';
  }

  cancelEdit(): void {
    this.isEditing$.next(false);
    if (this.selectedSlideData) {
      this.editedMediaName = this.selectedSlideData?.name ?? '';
    }
  }

  updateMediaName(): void {
    if (!this.selectedSlideData || !this.isNameChanged()) {
      return;
    }
    this.isUpdating$.next(true);
    const trimmedName = this.editedMediaName.trim();

    const updatedMedia: MediaLibrary = {
      ...this.selectedSlideData,
      name: trimmedName,
    };

    this.mediaLibraryService
      .updateMediaName(updatedMedia, this.selectedSlideData?.id ?? '')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: result => {
          if (result) {
            const currentData = this.mediaData$.getValue();
            const updatedData = [...currentData];
            updatedData[this.currentIndex] = updatedMedia;
            this.mediaData$.next(updatedData);
            this.isEditing$.next(false);
            this.isUpdating$.next(false);
            this.updateMediaData(updatedMedia);
            this.showToast(TOAST_MESSAGES.ITEM_UPDATED);
          }
        },
        error: () => {
          this.isUpdating$.next(false);
          this.showToast(TOAST_MESSAGES.ITEM_UPDATE_FAILED);
        },
      });
  }

  isNameChanged(): boolean {
    if (!this.selectedSlideData) {
      return false;
    }

    const trimmedName = this.editedMediaName.trim();
    return (
      this.selectedSlideData.name !== trimmedName && trimmedName.length > 0
    );
  }

  deleteMedia(): void {
    if (!this.selectedSlideData) {
      return;
    }

    this.mediaLibraryService
      .deletePop(this.selectedSlideData?.id ?? '')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: publishStatus => {
          this.openDeleteConfirmationModal(publishStatus);
        },
        error: () => {
          this.toastService.show({
            message: TOAST_MESSAGES.FAILED_TO_FETCH_STATUS,
          });
        },
      });
  }

  private openDeleteConfirmationModal(publishStatus?: PublishStatus): void {
    if (!this.selectedSlideData) {
      return;
    }

    const modalRef: NgbModalRef = this.modalService.open(
      DeleteAssetPopupComponent,
      {
        centered: true,
        container: ModalConstants.CONTAINER_SELECTOR,
        windowClass: WINDOW_CLASSES.DELETE_MEDIA_POPUP,
        size: ModalConstants.SIZE_SMALL,
        animation: true,
        backdropClass: MEDIA_MODAL_BACKDROP_CLASS,
      }
    );

    modalRef.componentInstance.name = this.selectedSlideData.name;
    modalRef.componentInstance.id = this.selectedSlideData.id;
    modalRef.componentInstance.type = this.selectedSlideData.type;
    modalRef.componentInstance.properties = this.selectedSlideData.properties;
    modalRef.componentInstance.publishStatus = publishStatus;

    modalRef.result
      .then((deleteConfirmed: boolean) => {
        if (deleteConfirmed && this.selectedSlideData) {
          this.activeModal.close(this.selectedSlideData.id);
        }
      })
      .catch(() => {});
  }

  private updateMediaData(updatedMedia: MediaLibrary) {
    this.store.dispatch(
      updateMediaName({
        mediaId: updatedMedia?.id ?? '',
        mediaName: updatedMedia?.name ?? '',
      })
    );
  }

  onImageError(event: Event): void {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.src = `${getAssets() + ImageTypes.ERROR}`;
    }
  }

  getPropertiesArray(
    properties: Record<string, string> = {}
  ): Array<{ key: string; value: string }> {
    return Object.keys(properties).map((key: string) => ({
      key: resolveCamelCase(key),
      value: properties[key],
    }));
  }

  private showToast(message: string): void {
    this.toastService.show({
      message,
    });
  }

  isMediaRenderable(item: MediaLibrary): boolean {
    return item.type === MediaTypes.IMAGE || item.type === MediaTypes.VIDEO;
  }

  getMediaSource(item: MediaLibrary) {
    return item.type === MediaTypes.IMAGE ? item.sourceUrl : item.thumbnailUrl;
  }

  handleMediaEditAction(actionKey: MediaEditActionKey): void {
    const actions: Record<MediaEditActionKey, () => void> = {
      edit: () => this.editName(),
      delete: () => this.deleteMedia(),
    };
    actions[actionKey]();
  }

  handleMediaUpdateAction(key: MediaUpdateActionKey): void {
    const actions: Record<MediaUpdateActionKey, () => void> = {
      cancel: () => this.cancelEdit(),
      update: () => this.updateMediaName(),
    };

    actions[key]?.();
  }

  isMediaUpdateButtonDisabled(key: MediaUpdateActionKey): boolean {
    const isUpdating = this.isUpdating$?.getValue?.() ?? false;

    const keyMap: Record<MediaUpdateActionKey, boolean> = {
      cancel: isUpdating,
      update: !this.isNameChanged() || isUpdating,
    };

    return keyMap[key];
  }

  isMediaEditButtonDisabled(): boolean {
    return (
      this.isEditing$.getValue() ||
      this.isUpdating$.getValue() ||
      !this.isMediaDesigner()
    );
  }

  getDisplayDate(date: string = dayjs().toISOString()): string {
    return dayjs(date).format(MediaDateFormats.DATE_ONLY);
  }

  getDisplayWeekdayTime(date: string = dayjs().toISOString()): string {
    return dayjs(date).format(MediaDateFormats.WEEKDAY_TIME);
  }

  mayEdit(): boolean {
    return this.mediaLibraryService.mayEditMedia();
  }

  isMediaDesigner(): boolean {
    return this.mediaLibraryService.isMediaDesigner();
  }
}
