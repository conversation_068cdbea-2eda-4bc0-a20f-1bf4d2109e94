.media-carousel-container {
  position: relative;
  .media-carousel-inner {
    position: relative;
    overflow: hidden;
    width: 100%;
    display: flex;
    position: relative;
    .media-carousel-item {
      flex: 0 0 100%;
      opacity: 0;
      transition:
        transform 0.4s ease,
        opacity 0.4s ease;
      width: 100%;
      pointer-events: none;
      &.active {
        opacity: 1 !important;
        transform: translateX(0);
        position: relative;
        pointer-events: auto;
      }

      &.prev {
        opacity: 1 !important;
        transform: translateX(-100%);
        position: absolute;
      }

      &.next {
        opacity: 1 !important;
        transform: translateX(100%);
        position: absolute;
      }

      &.hidden {
        display: none;
      }
      .media-embed-responsive {
        padding-bottom: 75%;
        position: relative;
        display: block;
        overflow: hidden;
        height: 0;
        .media-embed-responsive-item {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border: 0;
          .media-carousel-item-flex {
            display: flex;
            width: 100%;
            height: 100%;
            padding: 0 3em 0;
            align-items: center;
            justify-content: center;
            > img {
              box-shadow: 0 0.5rem 3.5rem var(--md-black-65);
            }
          }
        }
      }
    }
  }

  .media-carousel-control {
    text-shadow: 0 0.2rem 0.5rem var(--md-black-80);
    border: none;
    width: 25%;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.4s linear;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    font-size: 2rem;
    color: var(--white);
    text-align: center;

    &.right {
      left: auto;
      right: 0;
    }

    &.left,
    &.right {
      background: none;
    }

    .icon {
      position: absolute;
      top: 50%;
      font-size: 3.2rem;
      margin-top: -1rem;
      z-index: 5;
      display: inline-block;

      &:after {
        content: '';
        position: absolute;
        z-index: -1;
        width: 5rem;
        height: 5rem;
        border-radius: 50%;
        top: 50%;
        left: 50%;
        margin-left: -2.5rem;
        margin-top: -2.5rem;
        background-color: var(--md-black-26);
      }

      &.prev {
        left: 2em;
        margin-left: 1rem;
      }

      &.next {
        right: 2em;
        margin-right: 1rem;
      }
    }

    &.disabled {
      visibility: hidden;
      display: none;
    }

    &:hover {
      outline: 0;
      color: var(--white);
      text-decoration: none;
      opacity: 1;
    }
  }
}
