<div class="masthead-toolbar">
  <ul class="nav nav-pills pull-left">
    <li
      *ngFor="let tab of mediaTabs"
      (click)="onSelectionChange(tab)"
      class="media-tab"
    >
      <a
        class="media-tab-item"
        [ngStyle]="{
          'border-bottom-color':
            tab === (selectedMediaTab$ | async) ? '#3f51b5' : '#cccccc',
          'font-weight':
            tab === (selectedMediaTab$ | async) ? 'bold' : 'normal',
        }"
      >
        {{ tab }}</a
      >
    </li>
  </ul>
</div>
