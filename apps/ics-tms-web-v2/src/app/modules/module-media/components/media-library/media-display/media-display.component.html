<ng-container *ngIf="vm$ | async as vm">
  <div
    class="clearfix"
    [ngClass]="{
      'list-group media-list-group bs-primary': vm.isListView,
      'media-columns': !vm.isListView,
    }"
  >
    <div
      class="gallery-wrapper"
      *ngIf="(vm.mediaData?.length ?? 0) > 0 || !vm.isLoading"
    >
      <!-- grid view  -->
      <ng-container *ngIf="!vm.isListView">
        <div
          class="col-xs-6 col-md-4 col-lg-3 media-card"
          *ngFor="let mediaItem of vm.mediaData; trackBy: trackById"
        >
          <div
            role="button"
            class="d-block clearfix"
            (click)="openMediaModal(mediaItem?.id)"
          >
            <div class="no-focus ics-gallery-card media-card">
              <div class="card-wrapper">
                <div
                  class="media-parent"
                  [ngClass]="mediaItem?.type?.toLowerCase()"
                >
                  <img
                    *ngIf="
                      (mediaItem?.thumbnailUrl || mediaItem?.sourceUrl) &&
                      mediaItem?.type !== 'FONT'
                    "
                    [src]="imageSrc(mediaItem)"
                    [alt]="mediaItem?.name"
                    (error)="onImageError($event)"
                    loading="lazy"
                  />
                  <div
                    *ngIf="mediaItem?.type === 'FONT'"
                    class="img-responsive font-preview"
                  >
                    <pre
                      [id]="mediaItem?.id"
                      contenteditable="false"
                      [style.font-family]="'\'' + mediaItem?.id + '\''"
                    >
              {{ fontPreviewChars }}</pre
                    >
                  </div>
                  <div class="type-size-wrapper">
                    <div
                      class="type-wrapper"
                      [ngClass]="mediaItem?.type?.toLowerCase()"
                    >
                      <p>{{ mediaItem?.type }}</p>
                    </div>
                  </div>
                </div>
                <div class="media-info">
                  <p class="font-bold font-size-14 break-word media-name">
                    {{ mediaItem?.name }}
                  </p>
                  <p
                    class="font-size-12 text-grey-700"
                    *ngIf="
                      mediaItem?.type !== 'FONT' &&
                      mediaItem?.width &&
                      mediaItem?.height
                    "
                  >
                    {{ mediaItem?.width }} × {{ mediaItem?.height }}
                  </p>
                  <p class="font-size-12 text-grey-700">
                    {{ mediaItem?.uploader?.fullName }}
                  </p>
                  <p class="font-size-12 text-grey-700">
                    {{ formatDate(mediaItem?.uploaded) }} (UTC+05:30)
                  </p>
                </div>
                <div
                  class="bottom-border"
                  [ngClass]="mediaItem?.type?.toLowerCase()"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <!-- list view  -->
      <ng-container *ngIf="vm.isListView">
        <div
          *ngFor="let mediaItem of vm.mediaData; trackBy: trackById"
          class="list-group-item list-group-link"
        >
          <div
            role="button"
            class="d-block clearfix"
            (click)="openMediaModal(mediaItem?.id)"
          >
            <div class="no-focus ics-gallery-list-item media">
              <div class="media-wrapper"></div>

              <div class="info-wrapper">
                <div class="col-xs-5 column">
                  <p
                    class="font-bold font-size-14 media-name"
                    title="{{ mediaItem?.name }}"
                  >
                    {{ mediaItem?.name }}
                  </p>
                  <p
                    class="label status-label"
                    [ngClass]="mediaItem?.type?.toLowerCase()"
                  >
                    {{ mediaItem?.type }}
                  </p>
                  <span
                    class="font-size-12 text-grey-700 resolution"
                    *ngIf="
                      mediaItem?.type !== 'FONT' &&
                      mediaItem?.width &&
                      mediaItem?.height
                    "
                  >
                    {{ mediaItem?.width }} × {{ mediaItem?.height }}
                  </span>
                </div>
                <div class="col-xs-5 column">
                  <p class="font-size-12 text-grey-700">
                    {{ mediaItem?.uploader?.fullName }}
                  </p>
                  <p class="font-size-12 text-grey-700">
                    {{ formatDate(mediaItem?.uploaded) }} (UTC+05:30)
                  </p>
                </div>
                <div class="col-xs-2 column">
                  <button
                    tabindex="-1"
                    type="button"
                    class="btn btn-link pull-right"
                  >
                    View
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="clearfix col-xs-12">
      <div aria-label="end of feed">
        <div *ngIf="vm.totalResults === 0 || vm.isLoading">
          <ng-container *ngIf="vm.isLoading">
            <app-ics-loader></app-ics-loader>
          </ng-container>
          <div
            class="list-group-no-results no-animate no-border"
            *ngIf="vm.totalResults === 0 && !vm.isLoading"
          >
            <div class="panel-body-tall">
              <span
                class="mb10 text-grey-400 font-size-70"
                [ngClass]="noMediaFoundText.icon"
              ></span>
              <div aria-label="No Media Found">
                {{ noMediaFoundText.title }}
              </div>
              <small class="font-400">{{ noMediaFoundText.desc }}</small>
            </div>
          </div>
        </div>
        <div
          class="text-center pb15"
          *ngIf="vm.totalResults > 29 && !vm.isLoading"
        >
          <button
            type="button"
            class="btn btn-link btn-block panel-body-tall no-focus"
            (click)="scrollToTop()"
          >
            <span class="text-muted">Back to top</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-container>
