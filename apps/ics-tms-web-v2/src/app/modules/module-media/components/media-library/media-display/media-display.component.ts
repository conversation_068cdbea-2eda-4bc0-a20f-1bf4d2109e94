import {
  ChangeDetectionStrategy,
  Component,
  HostListener,
  inject,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  Observable,
  Subject,
  takeUntil,
} from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

import dayjs from 'dayjs';
import {
  $EVENT,
  FONT_FACE_TEMPLATE,
  FONT_PREVIEW_CHARS,
  FONTS,
  G6,
  ImageTypes,
  LARGE,
  MEDIA_MODAL_BACKDROP_CLASS,
  MEDIA_TYPE_BADGE_CLASS_MAP,
  MediaDateFormats,
  MediaTypes,
  MIME_TYPE,
  MimeTypes,
  NO_MEDIA_FOUND_TEXT,
  MODAL_OPEN,
  PAGE_SIZE,
  SCROLL_THRESHOLD,
  SMOOTH,
  STYLE,
  TOAST_MESSAGES,
  UPLOADED,
  WINDOW_CLASSES,
  WINDOW_SCROLL,
} from '../../../constants/appConstants';
import {
  LibraryParams,
  MediaLibrary,
} from '../../../models/media-library.modal';
import { MediaLibraryService } from '../../../services/media-library.service';
import {
  deleteMedia,
  loadSelectedMedia,
} from '../../../store/actions/library.actions';
import {
  getSelectedMediaData,
  getSelectedMediaDataItemsCount,
  getSelectedMediaDataLoading,
} from '../../../store/selectors/library.selectors';
import { MediaModalComponent } from './media-modal/media-modal.component';
import { ToastService } from 'src/app/services/toast.service';
import { getAssets, ModalConstants } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-media-display',
  templateUrl: './media-display.component.html',
  styleUrls: ['./media-display.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MediaDisplayComponent implements OnInit, OnDestroy {
  mediaData$!: Observable<MediaLibrary[]>;

  isDisplayMediaListView$!: Observable<boolean>;

  totalResults$!: Observable<number>;

  isLoading$!: Observable<boolean>;

  private readonly destroy$ = new Subject<void>();

  private readonly pageIndex$ = new BehaviorSubject<number>(0);

  private readonly mediaLibraryParams$ = new BehaviorSubject<LibraryParams>({
    order: UPLOADED,
    pageIndex: 0,
    pageSize: PAGE_SIZE,
    type: '',
  });

  private readonly scrollTrigger$ = new Subject<void>();

  public readonly mediaTypeBadgeMap = MEDIA_TYPE_BADGE_CLASS_MAP;

  public readonly fontPreviewChars = FONT_PREVIEW_CHARS;

  public readonly noMediaFoundText = NO_MEDIA_FOUND_TEXT;

  private readonly store = inject(Store);

  private readonly mediaLibraryService = inject(MediaLibraryService);

  private readonly modalService = inject(NgbModal);

  private readonly toastService = inject(ToastService);

  private readonly zone = inject(NgZone);

  public vm$!: Observable<{
    mediaData: MediaLibrary[];
    isListView: boolean;
    isLoading: boolean;
    totalResults: number;
  }>;

  constructor() {
    this.initializeObservables();
  }

  ngOnInit(): void {
    this.setupFontLoading();
    this.updateAndLoadMedia();
    this.setupScrollHandler();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeObservables() {
    this.mediaData$ = this.store.select(getSelectedMediaData);
    this.isDisplayMediaListView$ =
      this.mediaLibraryService.isDisplayMediaListView$;
    this.totalResults$ = this.store.select(getSelectedMediaDataItemsCount);
    this.isLoading$ = this.store.select(getSelectedMediaDataLoading);
    this.vm$ = combineLatest([
      this.mediaData$,
      this.isDisplayMediaListView$,
      this.isLoading$,
      this.totalResults$,
    ]).pipe(
      map(([mediaData, isListView, isLoading, totalResults]) => ({
        mediaData,
        isListView,
        isLoading,
        totalResults,
      }))
    );
  }

  private setupFontLoading(): void {
    combineLatest([this.mediaData$, this.mediaLibraryService.getSelectedTab()])
      .pipe(
        takeUntil(this.destroy$),
        filter(([_, selectedTab]) => selectedTab === FONTS),
        map(([mediaData, _]) => mediaData)
      )
      .subscribe(mediaData => {
        this.loadFonts(mediaData);
      });
  }

  private setupScrollHandler(): void {
    this.scrollTrigger$
      .pipe(
        takeUntil(this.destroy$),
        switchMap(() => combineLatest([this.totalResults$, this.isLoading$])),
        filter(([totalResults, isLoading]) => {
          const currentPageIndex = this.pageIndex$.value + 1;
          const hasMoreData = currentPageIndex * PAGE_SIZE < totalResults;
          const notLoading = !isLoading;
          return hasMoreData && notLoading;
        })
      )
      .subscribe(() => {
        this.loadMoreMedia();
      });
  }

  private loadFonts(mediaData: MediaLibrary[]) {
    mediaData.forEach(mediaItem => {
      if (mediaItem.type === MediaTypes.FONT) {
        this.appendFontStyle(mediaItem);
      }
    });
  }

  private appendFontStyle(mediaItem: MediaLibrary) {
    const style = document.createElement(STYLE);
    style.textContent = FONT_FACE_TEMPLATE(mediaItem?.id, mediaItem?.sourceUrl);
    document.head.append(style);
  }

  private updateAndLoadMedia(): void {
    combineLatest([
      this.mediaLibraryService.getSelectedTab(),
      this.mediaLibraryService.getSearchedText(),
      this.mediaLibraryService.getFilters(),
      this.mediaLibraryService.getSelectedOrder(),
    ])
      .pipe(
        takeUntil(this.destroy$),
        distinctUntilChanged(),
        tap(([tab, text, filters, order]) => {
          this.pageIndex$.next(0);

          const isFontTab = tab === FONTS;
          const hasSizeFilter = !isFontTab && filters.size.name;
          const isLarge = filters.size.name === LARGE;

          const params: LibraryParams = {
            order,
            pageIndex: 0,
            pageSize: PAGE_SIZE,
            type: tab.replace('S', ''),
            ...(text && { name: text }),
            ...(hasSizeFilter && {
              minHeight: filters.size.minHeight,
              minWidth: filters.size.minWidth,
            }),
            ...(hasSizeFilter &&
              !isLarge &&
              filters.size.maxHeight && { maxHeight: filters.size.maxHeight }),
            ...(hasSizeFilter &&
              !isLarge &&
              filters.size.maxWidth && { maxWidth: filters.size.maxWidth }),
            ...(!isFontTab && filters.user.id && { uploader: filters.user.id }),
          };

          this.mediaLibraryParams$.next(params);
        })
      )
      .subscribe(() => {
        this.loadMediaWithCurrentParams(true);
      });
    this.mediaLibraryService.loadMore$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadMoreMedia();
      });
  }

  openMediaModal(itemId?: string) {
    if (itemId) {
      const modalRef = this.modalService.open(MediaModalComponent, {
        container: ModalConstants.CONTAINER_SELECTOR,
        windowClass: WINDOW_CLASSES.MEDIA_MODAL_POPUP,
        centered: true,
        backdropClass: MEDIA_MODAL_BACKDROP_CLASS,
        animation: true,
      });
      document.body.classList.remove(MODAL_OPEN);
      this.setModalData(modalRef, itemId);
      this.handleModalResult(modalRef);
    }
  }

  setModalData(modalRef: NgbModalRef, itemId: string) {
    const modalReference = modalRef.componentInstance;
    modalReference.mediaItemId = itemId;
  }

  private handleModalResult(modalRef: NgbModalRef) {
    modalRef.result
      .then((deleteMediaId: string) => {
        if (deleteMediaId) {
          this.showToast(TOAST_MESSAGES.TOAST_ITEM_DELETED);
          this.store.dispatch(deleteMedia({ mediaId: deleteMediaId }));
        }
      })
      .catch(() => {});
  }

  private loadMediaWithCurrentParams(replace: boolean): void {
    const currentParams = this.mediaLibraryParams$.value;
    this.store.dispatch(loadSelectedMedia({ params: currentParams, replace }));
  }

  private loadMoreMedia(): void {
    const currentParams = this.mediaLibraryParams$.value;
    const nextPageIndex = currentParams.pageIndex + 1;

    const updatedParams = {
      ...currentParams,
      pageIndex: nextPageIndex,
    };

    this.pageIndex$.next(nextPageIndex);
    this.mediaLibraryParams$.next(updatedParams);

    this.store.dispatch(
      loadSelectedMedia({ params: updatedParams, replace: false })
    );
  }

  @HostListener(WINDOW_SCROLL, [$EVENT])
  onScrollEvent(): void {
    this.zone.run(() => this.onScroll());
  }

  private onScroll(): void {
    const target = document.documentElement;
    const isAtBottom =
      target.offsetHeight + target.scrollTop + SCROLL_THRESHOLD >=
      target.scrollHeight;

    if (!isAtBottom) return;

    this.scrollTrigger$.next();
  }

  public triggerLoadMore() {
    this.loadMoreMedia();
  }

  imageSrc(media: MediaLibrary) {
    const { thumbnailUrl, sourceUrl, properties, name } = media;
    const mimeType = properties?.[MIME_TYPE] || '';
    if (thumbnailUrl && !mimeType.includes(MimeTypes.GIF)) {
      return thumbnailUrl;
    }

    if (!mimeType.includes(MimeTypes.WEBM)) {
      return sourceUrl;
    }
    return name?.includes(G6)
      ? `${getAssets() + ImageTypes.G6_IMG}`
      : `${getAssets() + ImageTypes.G7_IMG}`;
  }

  onImageError(event: Event) {
    const target = event.target as HTMLImageElement;
    target.src = `${getAssets() + ImageTypes.ERROR}`;
  }

  showToast(message: string) {
    this.toastService.show({
      message,
    });
  }

  scrollToTop() {
    document.documentElement.scrollTo({ top: 0, behavior: SMOOTH });
  }

  trackById(_: number, item: MediaLibrary) {
    return item.id;
  }

  formatDate(date?: string | Date): string {
    return dayjs(date).format(MediaDateFormats.FULL_DATE_TIME);
  }
}
