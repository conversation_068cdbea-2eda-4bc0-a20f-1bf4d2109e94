.media-library-filters {
  .media-library-sort,
  .media-library-group-filter {
    margin-right: 0.5rem;
  }
  .btn-default-icon {
    color: var(--md-grey-800) !important;
    background-color: transparent !important;
    line-height: 1.5;
    border-radius: 3px;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    padding: 0 0.8rem !important;
    font-size: 2rem !important;
    &:focus,
    &:active {
      color: var(--md-grey-700) !important;
    }

    &:hover {
      background-color: var(--md-black-26) !important;
      color: var(--md-grey-900) !important;
    }
  }

  .ics-group-filter-dropdown {
    .tooltip {
      transform: translate(-1.5rem, 5rem) !important;
    }
  }

  .sort-prompt-set-dropdown {
    .tooltip {
      transform: translate(-1.5rem, 5rem) !important;
    }
  }

  .media-navbar-bottom-right {
    .navbar-form {
      .form-group {
        margin: 0;
      }
      .form-group-with-btn {
        .btn-clear {
          position: absolute;
          z-index: 1;
          top: 2px;
          right: 2px;
          width: 24px;
          height: 24px;
          border: none;
          font-size: 16px;
          padding: 5px;
          color: #bdbdbd;
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          background-color: #fff;
        }
      }
    }
  }

  .display-inline-block {
    .btn {
      line-height: 1.5 !important;
    }
  }

  .tooltip {
    transform: translate(-1.5rem, 3.9rem) !important;
    min-width: max-content;
  }
}
