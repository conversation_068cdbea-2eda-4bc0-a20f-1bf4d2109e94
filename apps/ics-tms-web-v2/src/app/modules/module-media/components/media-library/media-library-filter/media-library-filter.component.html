<app-ics-sort
  [sortByFilters]="sortingOptions"
  [defaultSortBy]="selectedSort"
  (sortByFilterChange)="onSortChange($event)"
  class="display-inline-block media-library-sort"
></app-ics-sort>
<ng-container *ngIf="selectedTab !== 'FONTS'">
  <app-ics-group-filter
    [filterGroups]="filterGroups"
    [title]="'Filter By:'"
    [tooltipText]="'Filter Options'"
    [iconClass]="'gicon-filter'"
    (filterChange)="onFilterChange($event)"
    class="display-inline-block media-library-group-filter"
  >
  </app-ics-group-filter>
</ng-container>
<button
  class="btn btn-default-icon navbar-btn no-focus"
  (click)="modifyDisplayMediaView()"
>
  <span
    *ngIf="!isMediaListView"
    ngbTooltip="List View"
    placement="bottom"
    class="icon gicon-list"
  ></span>
  <span
    *ngIf="isMediaListView"
    ngbTooltip="Grid View"
    placement="bottom"
    class="icon gicon-view_gird"
  ></span>
</button>

<div class="navbar-right media-navbar-bottom-right">
  <div class="navbar-left">
    <div class="navbar-form">
      <div
        class="form-group form-group-with-btn"
        [ngClass]="{
          'btn-visible': (searchControl.value ?? '').trim().length > 0,
        }"
      >
        <input
          type="text"
          placeholder="Search"
          class="ics-input"
          [formControl]="searchControl"
          [spellcheck]="false"
        />
        <button
          type="button"
          class="btn-clear"
          *ngIf="(searchControl.value ?? '').trim().length > 0"
          (click)="clearSearchedText()"
        >
          <i class="gicon-cancel"></i>
        </button>
      </div>
    </div>
  </div>
</div>
