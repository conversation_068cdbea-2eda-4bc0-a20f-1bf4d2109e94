<div class="navbar-brand">
  <div class="font-size-20">{{ mediaLibraryTitle }}</div>
  <div class="font-size-13 text-grey-700 mt5">
    <ng-container *ngIf="itemsCount$ | async as count">
      <span *ngIf="count !== 0">{{ count }} items</span>
    </ng-container>
  </div>
</div>

<div *ngIf="mayEditMedia()" class="navbar-right media-navbar-top-right">
  <button
    type="button"
    color="primary"
    class="btn btn-sm navbar-btn btn-primary btn-box-shadow"
    (click)="fileInput.click()"
    [disabled]="isUploading$ | async"
  >
    Upload
  </button>
  <input
    type="file"
    #fileInput
    hidden
    (change)="handleFileChange($event)"
    multiple
  />
</div>
