import { <PERSON>pe, PipeTransform, inject } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
  name: 'highlight',
})
export class HighlightPipe implements PipeTransform {
  private sanitizer = inject(DomSanitizer);

  transform(value: string, searchText: string): SafeHtml {
    if (!searchText || !value) {
      return this.sanitizer.bypassSecurityTrustHtml(value || '');
    }

    try {
      const stringValue = String(value);

      const escapedSearch = searchText.replace(/[-[\]{}()*+?.^$|]/g, '\\$&');

      const searchRegex = new RegExp(`(${escapedSearch})`, 'gi');

      const hasMatch = searchRegex.test(stringValue);
      searchRegex.lastIndex = 0;

      if (!hasMatch) {
        return this.sanitizer.bypassSecurityTrustHtml(stringValue);
      }

      const highlighted = stringValue.replace(
        searchRegex,
        (match, p1) => `<span class="highlight">${p1}</span>`
      );

      return this.sanitizer.bypassSecurityTrustHtml(highlighted);
    } catch (error) {
      return this.sanitizer.bypassSecurityTrustHtml(String(value));
    }
  }
}
