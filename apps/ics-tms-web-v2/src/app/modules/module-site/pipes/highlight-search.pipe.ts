import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'highlightSearch',
})
export class HighlightSearchPipe implements PipeTransform {
  transform(text: string, search: string): string {
    if (!search || !text) {
      return text || '';
    }

    try {
      const escapedSearch = search.replace(/[-[\]{}()*+?.^$|]/g, '\\$&');

      const regex = new RegExp(`(${escapedSearch})`, 'gi');

      const hasMatch = regex.test(text);
      regex.lastIndex = 0;

      if (!hasMatch) {
        return text;
      }

      const result = text.replace(
        regex,
        '<span class="highlight-letter">$1</span>'
      );
      return result;
    } catch (error) {
      return text;
    }
  }
}
