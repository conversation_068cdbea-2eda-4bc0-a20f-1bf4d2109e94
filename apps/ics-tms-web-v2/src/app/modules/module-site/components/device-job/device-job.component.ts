import { Component } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import {
  DATE_FORMAT,
  JOB_DATE_RANGES,
  ID,
  JOBS,
  TYPE_CONFIG_FILE_UPDATE,
  TYPE_CONFIGURE,
  TYPE_DOWNLOAD,
  TYPE_INSTALL,
  TYPE_REBOOT,
  TYPE_UPLOAD,
} from '../../constants/appConstants';
import { getApiConstants } from '../../constants/api';
import { Jobs, JobsDetails } from '../../models/jobs.modal';

@Component({
  selector: 'app-device-job',
  templateUrl: './device-job.component.html',
  styleUrls: ['./device-job.component.scss'],
})
export class DeviceJobComponent {
  selectedStartDate!: string;

  selectedEndDate!: string;

  deviceId!: string;

  jobsData: Jobs[] = [];

  jobDetails: JobsDetails[] = [];

  loading = true;

  activeIndex = -1;

  constructor(
    private http: HttpClient,
    private route: ActivatedRoute
  ) {
    this.route.params.subscribe(params => {
      this.deviceId = params['device_id'];
    });
  }

  getStartDate($event: string) {
    const date = new Date($event);
    date.setUTCDate(date.getUTCDate() - 1);
    date.setUTCHours(18);
    date.setUTCMinutes(30);
    date.setUTCSeconds(0);
    date.setUTCMilliseconds(0);
    this.selectedStartDate = date.toISOString();
  }

  getEndDate($event: string) {
    const date = new Date($event);
    date.setUTCDate(date.getUTCDate());
    date.setUTCHours(18);
    date.setUTCMinutes(29);
    date.setUTCSeconds(59);
    date.setUTCMilliseconds(999);
    this.selectedEndDate = date.toISOString();
  }

  protected readonly JOBS = JOBS;

  protected readonly JOB_DATE_RANGES = JOB_DATE_RANGES;

  onClickUpdate() {
    if (
      !this.deviceId ||
      !this.selectedStartDate ||
      !this.selectedEndDate ||
      !getApiConstants()?.device?.jobs?.getJobsData
    ) {
      return;
    }
    const params = {
      deviceId: this.deviceId,
      embargoEnd: this.selectedEndDate,
      embargoStart: this.selectedStartDate,
      status: '0,1,2,3,4',
    };
    this.http
      .get<Jobs[]>(getApiConstants().device.jobs.getJobsData, { params })
      .subscribe({
        next: data => {
          this.loading = false;
          if (Array.isArray(data)) {
            this.jobsData = data
              .map(job => {
                const typeStr = job.type.substring(4);
                const capitalizedType =
                  typeStr.charAt(0).toUpperCase() + typeStr.slice(1);
                return { ...job, type: capitalizedType };
              })
              .sort((a, b) => b.createdOn.localeCompare(a.createdOn));
          } else {
            this.jobsData = [];
          }
        },
        error: _ => {
          this.loading = false;
          this.jobsData = [];
        },
      });
  }

  getFileName(data: string) {
    const parsedObject = JSON.parse(data);
    return parsedObject.fileName;
  }

  getSourceName(data: string) {
    const parsedObject = JSON.parse(data);
    return parsedObject.sourcePath;
  }

  getDateAndTimeInLocal(embargo: string) {
    const date = new Date(embargo);

    const formatter = new Intl.DateTimeFormat('en-US', DATE_FORMAT);

    const parts = formatter.formatToParts(date);
    const formattedDateParts = parts.reduce(
      (formattedDate, { type, value }) => {
        switch (type) {
          case 'year':
            return formattedDate + value;
          case 'month':
            return `${formattedDate + value} `;
          case 'day':
            return `${formattedDate + value}, `;
          case 'hour':
            return `${formattedDate} at ${value}`;
          case 'minute':
            return `${formattedDate}:${value} `;
          case 'dayPeriod':
            return `${formattedDate + value.toLowerCase()} `;
          default:
            return formattedDate;
        }
      },
      ''
    );

    return `${formattedDateParts}(UTC+05:30)`;
  }

  handleOnClickJob(id: string, index: number) {
    if (this.activeIndex !== index) {
      this.jobDetails = [];
    }
    this.activeIndex = index;
    this.http
      .get<
        JobsDetails[]
      >(getApiConstants().device.jobs.getJobsDetails.replace(ID, id))
      .subscribe({
        next: data => {
          this.jobDetails = Array.isArray(data) ? data : [];
        },
        error: _ => {
          this.jobDetails = [];
        },
      });
  }

  protected readonly TYPE_REBOOT = TYPE_REBOOT;

  protected readonly TYPE_DOWNLOAD = TYPE_DOWNLOAD;

  protected readonly TYPE_CONFIGURE = TYPE_CONFIGURE;

  protected readonly TYPE_INSTALL = TYPE_INSTALL;

  protected readonly TYPE_UPLOAD = TYPE_UPLOAD;

  protected readonly TYPE_CONFIG_FILE_UPDATE = TYPE_CONFIG_FILE_UPDATE;

  getJobStatusClass(status: number): string {
    switch (status) {
      case 0:
        return 'new';
      case 2:
        return 'inProgress';
      case 3:
        return 'success';
      case 4:
        return 'failed';
      default:
        return '';
    }
  }

  getDeviceJobsStatusTxt(status: number): string {
    switch (status) {
      case 0:
        return 'New';
      case 2:
        return 'In Progress';
      case 3:
        return 'Completed';
      case 4:
        return 'Failed';
      default:
        return '';
    }
  }

  getConfigOrStorageName(data: string): string {
    try {
      const parsed = JSON.parse(data);
      const configName = parsed.configName || '';
      const storageName = parsed.storageName || '';
      if (configName && storageName) {
        return `${configName}, ${storageName}`;
      }
      return configName || storageName;
    } catch {
      return '';
    }
  }
}
