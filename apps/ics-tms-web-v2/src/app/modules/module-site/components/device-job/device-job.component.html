<div class="main-container">
  <div class="header">
    <div class="heading">Jobs</div>
    <div class="menu">
      <app-ics-select-dates
        (selectedStartDate)="getStartDate($event)"
        (selectedEndDate)="getEndDate($event)"
        (clickUpdate)="onClickUpdate()"
        [type]="JOBS"
        [useMaxDate]="false"
        [dateRanges]="JOB_DATE_RANGES"
      >
      </app-ics-select-dates>
    </div>
  </div>
  <div id="extra-padding" *ngIf="jobsData.length < 1 && !loading"></div>
  <div class="content">
    <div class="noDataFound" *ngIf="loading; else noData">
      <app-ics-loader></app-ics-loader>
    </div>
    <ng-template #noData>
      <div class="noDataFound" *ngIf="jobsData.length < 1">No jobs found.</div>
    </ng-template>
    <div class="jobs-accordion">
      <div ngbAccordion [closeOthers]="true">
        <div ngbAccordionItem *ngFor="let data of jobsData; index as i">
          <h2 (click)="handleOnClickJob(data.id, i)" ngbAccordionHeader>
            <button ngbAccordionButton>
              <div class="acc-btn">
                <div class="heading">
                  <span
                    class="jobs-label"
                    [ngClass]="getJobStatusClass(data.status)"
                    >{{ getDeviceJobsStatusTxt(data.status) }}</span
                  >
                  <span class="job-type">{{ data.type }}</span>
                  <em>
                    <span *ngIf="getFileName(data.data)" class="break-word">
                      for file {{ getFileName(data.data) }}
                    </span>
                  </em>
                  <em>
                    <span *ngIf="getSourceName(data.data)" class="break-word">
                      for source {{ getSourceName(data.data) }}
                    </span>
                  </em>
                  <em>
                    <span
                      *ngIf="getConfigOrStorageName(data.data)"
                      class="break-word"
                    >
                      for config {{ getConfigOrStorageName(data.data) }}
                    </span>
                  </em>
                </div>
                <div class="accordion-desc">
                  <span
                    >Created by {{ data.createdBy }}: Scheduled:
                    {{ getDateAndTimeInLocal(data.embargo) }}
                  </span>
                </div>
              </div>
            </button>
          </h2>
          <div ngbAccordionCollapse>
            <div ngbAccordionBody>
              <ng-template>
                <div *ngIf="jobDetails.length === 0">
                  <app-ics-loader></app-ics-loader>
                </div>

                <div class="acc-body" *ngIf="jobDetails.length !== 0">
                  <div class="acc-body-table">
                    <span class="status">Status</span>
                    <span class="job-timestamp">Timestamp</span>
                    <span class="message">Message</span>
                  </div>
                  <div class="acc-body-list" *ngFor="let details of jobDetails">
                    <span class="status-list">
                      <div
                        class="status-list-label new"
                        *ngIf="details.status === 0"
                      >
                        New
                      </div>
                      <div
                        class="status-list-label failed"
                        *ngIf="details.status === 4"
                      >
                        Failed
                      </div>
                      <div
                        class="status-list-label success"
                        *ngIf="details.status === 3"
                      >
                        Success
                      </div>
                      <div
                        class="status-list-label inProgress"
                        *ngIf="details.status === 2"
                      >
                        In progress
                      </div>
                    </span>
                    <span class="timestamp-list">{{
                      getDateAndTimeInLocal(details.changed)
                    }}</span>
                    <span class="message-list">{{ details.message }}</span>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
