import { TestBed, ComponentFixture } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

import { Jobs, JobsDetails } from '../../models/jobs.modal';
import {
  JOB_DATE_RANGES,
  JOBS,
  TYPE_CONFIG_FILE_UPDATE,
  TYPE_CONFIGURE,
  TYPE_DOWNLOAD,
  TYPE_INSTALL,
  TYPE_REBOOT,
  TYPE_UPLOAD,
} from '../../constants/appConstants';
import { DeviceJobComponent } from './device-job.component';

describe('DeviceJobComponent', () => {
  let component: DeviceJobComponent;
  let fixture: ComponentFixture<DeviceJobComponent>;
  let httpMock: HttpTestingController;

  const mockJobs: Jobs[] = [
    {
      id: 'job-1',
      deviceId: 123,
      destination: 'device-destination',
      type: 'TYPE_REBOOT',
      status: 3,
      expiration: '2023-12-31T23:59:59Z',
      embargo: '2023-01-03T00:00:00Z',
      data: JSON.stringify({ fileName: 'test-file.txt' }),
      createdOn: '2023-01-03T10:00:00Z',
      createdBy: 'admin',
    },
    {
      id: 'job-2',
      deviceId: 123,
      destination: 'device-destination',
      type: 'TYPE_INSTALL',
      status: 2,
      expiration: '2023-12-31T23:59:59Z',
      embargo: '2023-01-02T00:00:00Z',
      data: JSON.stringify({ sourcePath: '/path/to/source' }),
      createdOn: '2023-01-02T10:00:00Z',
      createdBy: 'admin',
    },
    {
      id: 'job-3',
      deviceId: 123,
      destination: 'device-destination',
      type: 'TYPE_DOWNLOAD',
      status: 1,
      expiration: '2023-12-31T23:59:59Z',
      embargo: '2023-01-01T00:00:00Z',
      data: JSON.stringify({
        configName: 'config.json',
        storageName: 'storage.db',
      }),
      createdOn: '2023-01-01T10:00:00Z',
      createdBy: 'admin',
    },
  ];

  const mockJobDetails: JobsDetails[] = [
    {
      changed: '2023-01-01T10:30:00Z',
      data: JSON.stringify({ progress: 50 }),
      jobId: 'job-1',
      message: 'In progress',
      status: 2,
    },
    {
      changed: '2023-01-01T10:25:00Z',
      data: JSON.stringify({ started: true }),
      jobId: 'job-1',
      message: 'Job started',
      status: 0,
    },
    {
      changed: '2023-01-01T10:35:00Z',
      data: null,
      jobId: 'job-1',
      message: 'Download completed',
      status: 3,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DeviceJobComponent],
      imports: [HttpClientTestingModule],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            params: of({
              device_id: 'test-device-123',
              siteId: 'test-site-456',
            }),
          },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceJobComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);

    component.deviceId = 'test-device-123';
    component.selectedStartDate = '2023-01-01T00:00:00Z';
    component.selectedEndDate = '2023-01-31T23:59:59Z';

    fixture.detectChanges();
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.loading).toBe(true);
      expect(component.jobsData).toEqual([]);
      expect(component.jobDetails).toEqual([]);
      expect(component.activeIndex).toBe(-1);
    });

    it('should set deviceId from route params', () => {
      expect(component.deviceId).toBe('test-device-123');
    });

    it('should expose protected constants', () => {
      expect(component['JOBS']).toBe(JOBS);
      expect(component['JOB_DATE_RANGES']).toBe(JOB_DATE_RANGES);
      expect(component['TYPE_REBOOT']).toBe(TYPE_REBOOT);
      expect(component['TYPE_DOWNLOAD']).toBe(TYPE_DOWNLOAD);
      expect(component['TYPE_CONFIGURE']).toBe(TYPE_CONFIGURE);
      expect(component['TYPE_INSTALL']).toBe(TYPE_INSTALL);
      expect(component['TYPE_UPLOAD']).toBe(TYPE_UPLOAD);
      expect(component['TYPE_CONFIG_FILE_UPDATE']).toBe(
        TYPE_CONFIG_FILE_UPDATE
      );
    });
  });

  describe('Date Handling', () => {
    describe('getStartDate', () => {
      it('should set selectedStartDate with correct UTC adjustments', () => {
        const testDate = '2023-01-15';
        component.getStartDate(testDate);

        const expectedDate = new Date(testDate);
        expectedDate.setUTCDate(expectedDate.getUTCDate() - 1);
        expectedDate.setUTCHours(18);
        expectedDate.setUTCMinutes(30);
        expectedDate.setUTCSeconds(0);
        expectedDate.setUTCMilliseconds(0);

        expect(component.selectedStartDate).toBe(expectedDate.toISOString());
      });

      it('should handle edge case dates', () => {
        const testDate = '2023-01-01';
        component.getStartDate(testDate);

        expect(component.selectedStartDate).toContain(
          '2022-12-31T18:30:00.000Z'
        );
      });
    });

    describe('getEndDate', () => {
      it('should set selectedEndDate with correct UTC adjustments', () => {
        const testDate = '2023-01-15';
        component.getEndDate(testDate);

        const expectedDate = new Date(testDate);
        expectedDate.setUTCDate(expectedDate.getUTCDate());
        expectedDate.setUTCHours(18);
        expectedDate.setUTCMinutes(29);
        expectedDate.setUTCSeconds(59);
        expectedDate.setUTCMilliseconds(999);

        expect(component.selectedEndDate).toBe(expectedDate.toISOString());
      });

      it('should handle same day correctly', () => {
        const testDate = '2023-01-15';
        component.getEndDate(testDate);

        expect(component.selectedEndDate).toContain('2023-01-15T18:29:59.999Z');
      });
    });

    describe('getDateAndTimeInLocal', () => {
      it('should format date correctly with UTC+05:30 timezone', () => {
        const testDate = '2023-01-15T10:30:00Z';
        const result = component.getDateAndTimeInLocal(testDate);

        expect(result).toContain('(UTC+05:30)');
        expect(result).toMatch(
          /\w{3} \d{1,2}, \d{4} at \d{1,2}:\d{2} (am|pm) \(UTC\+05:30\)/
        );
      });

      it('should handle different date formats', () => {
        const testDate = '2023-12-25T15:45:30Z';
        const result = component.getDateAndTimeInLocal(testDate);

        expect(result).toContain('(UTC+05:30)');
        expect(result).toMatch(/\w{3} \d{1,2}, \d{4}/);
      });
    });
  });

  describe('Jobs Data Management', () => {
    describe('onClickUpdate', () => {
      beforeEach(() => {
        component.deviceId = 'test-device-123';
        component.selectedStartDate = '2023-01-01T00:00:00Z';
        component.selectedEndDate = '2023-01-31T23:59:59Z';
      });

      it('onClickUpdate should fetch jobs data and update component state', () => {
        component.onClickUpdate();

        const jobsReq = httpMock.expectOne(
          req => req.url === 'null/v1/jobs' && req.method === 'GET'
        );
        expect(jobsReq.request.params.get('deviceId')).toBe('test-device-123');
        expect(jobsReq.request.params.get('embargoStart')).toBe(
          '2023-01-01T00:00:00Z'
        );
        expect(jobsReq.request.params.get('embargoEnd')).toBe(
          '2023-01-31T23:59:59Z'
        );
        expect(jobsReq.request.params.get('status')).toBe('0,1,2,3,4');

        jobsReq.flush(mockJobs);

        expect(component.loading).toBe(false);
        expect(component.jobsData.length).toBe(3);
        expect(component.jobsData[0].type).toBe('_REBOOT');
        expect(component.jobsData[1].type).toBe('_INSTALL');
        expect(component.jobsData[2].type).toBe('_DOWNLOAD');
      });

      it('should sort jobs by createdOn date in descending order', () => {
        component.onClickUpdate();

        const sortReq = httpMock.expectOne(
          req => req.url === 'null/v1/jobs' && req.method === 'GET'
        );
        sortReq.flush(mockJobs);

        expect(component.jobsData[0].createdOn).toBe('2023-01-03T10:00:00Z');
        expect(component.jobsData[1].createdOn).toBe('2023-01-02T10:00:00Z');
        expect(component.jobsData[2].createdOn).toBe('2023-01-01T10:00:00Z');
      });

      it('should transform job types correctly', () => {
        const jobsWithDifferentTypes = [
          { ...mockJobs[0], type: 'TYPE_CONFIGURE' },
          { ...mockJobs[1], type: 'TYPE_UPLOAD' },
          { ...mockJobs[2], type: 'TYPE_CONFIG_FILE_UPDATE' },
        ];

        component.onClickUpdate();

        const typesReq = httpMock.expectOne(
          req => req.url === 'null/v1/jobs' && req.method === 'GET'
        );
        typesReq.flush(jobsWithDifferentTypes);

        expect(component.jobsData[0].type).toBe('_CONFIGURE');
        expect(component.jobsData[1].type).toBe('_UPLOAD');
        expect(component.jobsData[2].type).toBe('_CONFIG_FILE_UPDATE');
      });

      it('should handle empty response', () => {
        component.onClickUpdate();

        const emptyReq = httpMock.expectOne(
          req => req.url === 'null/v1/jobs' && req.method === 'GET'
        );
        emptyReq.flush([]);

        expect(component.loading).toBe(false);
        expect(component.jobsData).toEqual([]);
      });
    });

    describe('handleOnClickJob', () => {
      beforeEach(() => {
        component.jobDetails = [mockJobDetails[0]];
        component.activeIndex = 0;
      });

      it('handleOnClickJob should fetch job details and set active index', () => {
        const jobId = 'job-1';
        const index = 0;

        component.handleOnClickJob(jobId, index);

        const detailsReq = httpMock.expectOne(
          req =>
            req.url === `null/v1/jobs/${jobId}/history` && req.method === 'GET'
        );

        detailsReq.flush(mockJobDetails);

        expect(component.activeIndex).toBe(index);
        expect(component.jobDetails).toEqual(mockJobDetails);
      });

      it('should clear job details when clicking different job', () => {
        const jobId = 'job-2';
        const index = 2;

        component.handleOnClickJob(jobId, index);

        expect(component.jobDetails).toEqual([]);
        expect(component.activeIndex).toBe(index);

        const clearJobReq = httpMock.expectOne(
          req =>
            req.url === `null/v1/jobs/${jobId}/history` && req.method === 'GET'
        );
        clearJobReq.flush(mockJobDetails);
      });

      it('should not clear job details when clicking same job', () => {
        const jobId = 'job-1';
        const index = 0;
        const originalJobDetails = [...component.jobDetails];

        component.handleOnClickJob(jobId, index);

        expect(component.jobDetails).toEqual(originalJobDetails);

        const sameJobReq = httpMock.expectOne(
          req =>
            req.url === `null/v1/jobs/${jobId}/history` && req.method === 'GET'
        );
        sameJobReq.flush(mockJobDetails);
      });
    });
  });

  describe('Data Parsing Methods', () => {
    describe('getFileName', () => {
      it('should extract fileName from JSON data', () => {
        const data = '{"fileName":"test-file.zip","other":"value"}';
        const result = component.getFileName(data);

        expect(result).toBe('test-file.zip');
      });

      it('should handle missing fileName property', () => {
        const data = '{"other":"value"}';
        const result = component.getFileName(data);

        expect(result).toBeUndefined();
      });

      it('should throw error for invalid JSON', () => {
        const data = 'invalid-json';

        expect(() => component.getFileName(data)).toThrow();
      });
    });

    describe('getSourceName', () => {
      it('should extract sourcePath from JSON data', () => {
        const data = '{"sourcePath":"/path/to/source","other":"value"}';
        const result = component.getSourceName(data);

        expect(result).toBe('/path/to/source');
      });

      it('should handle missing sourcePath property', () => {
        const data = '{"other":"value"}';
        const result = component.getSourceName(data);

        expect(result).toBeUndefined();
      });

      it('should throw error for invalid JSON', () => {
        const data = 'invalid-json';

        expect(() => component.getSourceName(data)).toThrow();
      });
    });

    describe('getConfigOrStorageName', () => {
      it('should return both configName and storageName when both exist', () => {
        const data =
          '{"configName":"test-config","storageName":"test-storage"}';
        const result = component.getConfigOrStorageName(data);

        expect(result).toBe('test-config, test-storage');
      });

      it('should return only configName when storageName is missing', () => {
        const data = '{"configName":"test-config"}';
        const result = component.getConfigOrStorageName(data);

        expect(result).toBe('test-config');
      });

      it('should return only storageName when configName is missing', () => {
        const data = '{"storageName":"test-storage"}';
        const result = component.getConfigOrStorageName(data);

        expect(result).toBe('test-storage');
      });

      it('should return empty string when both are missing', () => {
        const data = '{"other":"value"}';
        const result = component.getConfigOrStorageName(data);

        expect(result).toBe('');
      });

      it('should return empty string for invalid JSON', () => {
        const data = 'invalid-json';
        const result = component.getConfigOrStorageName(data);

        expect(result).toBe('');
      });

      it('should handle empty strings in configName and storageName', () => {
        const data = '{"configName":"","storageName":""}';
        const result = component.getConfigOrStorageName(data);

        expect(result).toBe('');
      });
    });
  });

  describe('Status Methods', () => {
    describe('getJobStatusClass', () => {
      it('should return correct CSS class for status 0 (new)', () => {
        expect(component.getJobStatusClass(0)).toBe('new');
      });

      it('should return correct CSS class for status 2 (in progress)', () => {
        expect(component.getJobStatusClass(2)).toBe('inProgress');
      });

      it('should return correct CSS class for status 3 (success)', () => {
        expect(component.getJobStatusClass(3)).toBe('success');
      });

      it('should return correct CSS class for status 4 (failed)', () => {
        expect(component.getJobStatusClass(4)).toBe('failed');
      });

      it('should return empty string for unknown status', () => {
        expect(component.getJobStatusClass(1)).toBe('');
        expect(component.getJobStatusClass(5)).toBe('');
        expect(component.getJobStatusClass(-1)).toBe('');
      });
    });

    describe('getDeviceJobsStatusTxt', () => {
      it('should return correct text for status 0 (new)', () => {
        expect(component.getDeviceJobsStatusTxt(0)).toBe('New');
      });

      it('should return correct text for status 2 (in progress)', () => {
        expect(component.getDeviceJobsStatusTxt(2)).toBe('In Progress');
      });

      it('should return correct text for status 3 (completed)', () => {
        expect(component.getDeviceJobsStatusTxt(3)).toBe('Completed');
      });

      it('should return correct text for status 4 (failed)', () => {
        expect(component.getDeviceJobsStatusTxt(4)).toBe('Failed');
      });

      it('should return empty string for unknown status', () => {
        expect(component.getDeviceJobsStatusTxt(1)).toBe('');
        expect(component.getDeviceJobsStatusTxt(5)).toBe('');
        expect(component.getDeviceJobsStatusTxt(-1)).toBe('');
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should not call API if deviceId is falsy', () => {
      component.deviceId = '';
      component.selectedStartDate = '2023-01-01T00:00:00Z';
      component.selectedEndDate = '2023-01-31T23:59:59Z';
      component.onClickUpdate();
      expect(() =>
        httpMock.expectNone(req => req.url.includes('jobs'))
      ).not.toThrow();
    });
    it('should not call API if selectedStartDate is falsy', () => {
      component.deviceId = 'test-device-123';
      component.selectedStartDate = '';
      component.selectedEndDate = '2023-01-31T23:59:59Z';
      component.onClickUpdate();
      expect(() =>
        httpMock.expectNone(req => req.url.includes('jobs'))
      ).not.toThrow();
    });
    it('should not call API if selectedEndDate is falsy', () => {
      component.deviceId = 'test-device-123';
      component.selectedStartDate = '2023-01-01T00:00:00Z';
      component.selectedEndDate = '';
      component.onClickUpdate();
      expect(() =>
        httpMock.expectNone(req => req.url.includes('jobs'))
      ).not.toThrow();
    });

    it('should return empty string if getConfigOrStorageName receives invalid JSON', () => {
      expect(component.getConfigOrStorageName('not-json')).toBe('');
    });
    it('should set jobDetails to [] if handleOnClickJob errors', () => {
      component.activeIndex = 0;
      component.handleOnClickJob('job-1', 1);
      const detailsReq = httpMock.expectOne(req => req.url.includes('job-1'));
      detailsReq.error(new ErrorEvent('Network error'));
      expect(component.jobDetails).toEqual([]);
    });
    it('should throw or return undefined if getFileName receives invalid JSON', () => {
      expect(() => component.getFileName('not-json')).toThrow();
    });
    it('should throw or return undefined if getSourceName receives invalid JSON', () => {
      expect(() => component.getSourceName('not-json')).toThrow();
    });
    it('should handle HTTP errors gracefully', () => {
      component.deviceId = 'test-device-123';
      component.selectedStartDate = '2023-01-01T00:00:00Z';
      component.selectedEndDate = '2023-01-31T23:59:59Z';
      component.onClickUpdate();

      const errorReq = httpMock.expectOne(
        req => req.url === 'null/v1/jobs' && req.method === 'GET'
      );
      errorReq.error(new ErrorEvent('Network error'));

      expect(component).toBeTruthy();
    });

    it('should handle malformed job data', () => {
      component.deviceId = 'test-device-123';
      component.selectedStartDate = '2023-01-01T00:00:00Z';
      component.selectedEndDate = '2023-01-31T23:59:59Z';
      const malformedJobs = [
        {
          id: 'job-1',
          deviceId: 123,
          destination: 'device-destination',
          type: 'INVALID_TYPE',
          status: 0,
          expiration: '2023-12-31T23:59:59Z',
          embargo: '2023-01-01T00:00:00Z',
          data: 'invalid-json',
          createdOn: '2023-01-01T10:00:00Z',
          createdBy: 'admin',
        },
      ];

      component.onClickUpdate();

      const malformedReq = httpMock.expectOne(
        req => req.url === 'null/v1/jobs' && req.method === 'GET'
      );
      malformedReq.flush(malformedJobs);

      expect(component.jobsData.length).toBe(1);
      expect(component.jobsData[0].type).toBe('LID_TYPE');
    });

    it('should handle null and undefined values', () => {
      expect(() =>
        component.getDateAndTimeInLocal('2023-01-15T10:30:00Z')
      ).not.toThrow();
      expect(() => component.getJobStatusClass(null as any)).not.toThrow();
      expect(() =>
        component.getDeviceJobsStatusTxt(undefined as any)
      ).not.toThrow();
      expect(() =>
        component.getFileName('{"fileName": "test.txt"}')
      ).not.toThrow();
      expect(() =>
        component.getSourceName('{"sourcePath": "/path/to/source"}')
      ).not.toThrow();
      expect(() =>
        component.getConfigOrStorageName('{"configName": "config.json"}')
      ).not.toThrow();
    });
  });

  describe('Integration Tests', () => {
    it('should complete full workflow: set dates, fetch jobs, select job, fetch details', () => {
      component.getStartDate('2023-01-01');
      component.getEndDate('2023-01-31');

      expect(component.selectedStartDate).toBeDefined();
      expect(component.selectedEndDate).toBeDefined();

      component.onClickUpdate();

      const jobsReq = httpMock.expectOne(
        req => req.url === 'null/v1/jobs' && req.method === 'GET'
      );
      jobsReq.flush(mockJobs);

      expect(component.jobsData.length).toBe(3);
      expect(component.loading).toBe(false);

      component.handleOnClickJob('job-1', 0);

      const detailsReq = httpMock.expectOne(
        req => req.url === 'null/v1/jobs/job-1/history' && req.method === 'GET'
      );
      detailsReq.flush(mockJobDetails);

      expect(component.activeIndex).toBe(0);
      expect(component.jobDetails).toEqual(mockJobDetails);
    });
  });
});
