.main-container {
  box-shadow:
    0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
    0 0.1rem 0 rgba(0, 0, 0, 0.14),
    0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
  background-color: var(--details-popup-bg);
  width: 100%;
  float: left;
  position: relative;
  min-height: 0.1rem;
  border-radius: 0.3rem;

  .header {
    color: var(--color-black-shade-one);
    border-color: var(--color-black-shade-three);
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
    border-bottom: 0.1rem solid var(--color-border);
    padding: 1rem 1.5rem;
    align-items: flex-start;
    display: flex;
    justify-content: space-between;

    .heading {
      font-size: 2rem;
      font-weight: 400;
      line-height: 1;
      display: inline-block;
      margin: 0.7rem 0;
    }

    .menu {
      position: relative;
      transition: all ease-in 0.1s;

      button {
        cursor: pointer;
        color: var(--color-black-shade-one);
        border: 0.1rem solid;
        border-color: var(--dropdown-border-hover);
        background-color: var(--dropdown-by-default);
        user-select: none;
        outline: none;
        font-size: 1.2rem;
        line-height: 1.5;
        padding: 0.6rem 1.2rem;
        border-radius: 0.3rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;

        p {
          margin-bottom: 0rem;
        }
      }

      button:hover {
        background-color: var(--color-bg-jobs);
      }

      .date-picker-component {
        position: absolute;
        width: 100%;
        background-color: var(--details-popup-bg);
        min-width: 70rem;
        padding: 0;
        right: 0;
        border-radius: 0.3rem;
        height: auto;
        z-index: 10000;
        margin-top: 0.8rem;
        box-shadow:
          0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
          0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
          0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);

        &.open {
          display: block;
        }
      }
    }
  }

  #extra-padding {
    padding-bottom: 2rem;
  }

  .content {
    position: relative;
    display: block;
    border: 0.1rem solid var(--color-border);
    background-color: var(--details-popup-bg);
    border-bottom-right-radius: 0.3rem;
    border-bottom-left-radius: 0.3rem;

    .noDataFound {
      padding: 2rem 2rem;
      text-align: center;
      font-size: 1.4rem;
      color: var(--color-black-shade-two);
    }

    .jobs-accordion {
      .acc-btn {
        display: flex;
        width: 100%;
        padding: 1rem 1.5rem;
        flex-direction: column;

        .heading {
          display: flex;
          align-items: center;

          .jobs-label {
            font-size: 1.05rem;
            display: inline;
            padding: 0.4rem 0.5rem;
            color: var(--divider-color);
            border-radius: 0.25em;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
          }

          .new {
            background-color: var(--color-black-shade-two);
          }

          .failed {
            background-color: var(--color-bg-red);
          }

          .success {
            background-color: var(--color-bg-green);
          }

          .inProgress {
            background-color: var(--color-primary);
          }

          p {
            margin-bottom: 0;
            margin-left: 0.8rem;
            line-height: normal !important;
            font-size: 1.4rem;
            span {
              color: var(--color-black-shade-two);
              word-break: break-word;
              font-size: 1.4rem;
              font-weight: 400;
              hyphens: auto;
              font-style: italic;
              overflow-wrap: break-word;
            }
          }
          margin-bottom: 0.8rem;
        }

        .accordion-desc {
          line-height: normal;

          span {
            margin: 0;
            font-size: 1.3rem;
            color: var(--color-black-shade-two);
            line-height: normal;
          }
        }
      }

      .acc-body {
        border-top: 0.1rem solid var(--color-border);

        .acc-body-table {
          border-bottom: 0.1rem solid var(--color-border);
          display: flex;
          padding: 0.8rem 0;
          font-size: 1.4rem;

          .status {
            width: 20%;
          }

          .job-timestamp {
            width: 60%;
          }

          .message {
            width: 20%;
          }
        }

        .acc-body-list {
          padding: 0.8rem 0;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          font-size: 1.4rem;

          .status-list {
            width: 20%;

            .status-list-label {
              font-size: 1.05rem;
              width: fit-content;
              padding: 0.4rem 0.5rem;
              color: var(--divider-color);
              border-radius: 0.25em;
              line-height: 1;
              text-align: center;
              white-space: nowrap;
            }

            .new {
              background-color: var(--color-black-shade-two);
            }

            .failed {
              background-color: var(--color-bg-red);
            }

            .success {
              background-color: var(--color-bg-green);
            }

            .inProgress {
              background-color: var(--color-primary);
            }
          }

          .timestamp-list {
            width: 60%;
          }

          .message-list {
            width: 20%;
          }
        }
      }
    }
  }
}

.accordion-button {
  padding: 0;
}

.accordion-button::after {
  display: none;
}

.accordion-button:focus {
  box-shadow: none;
}

.accordion-item {
  border: none;
  border-bottom: 0.1rem solid var(--color-border);
}

.accordion-header {
  margin: 0;
  transition: all ease-in 0.1s;
}

.accordion-button:not(.collapsed) {
  color: var(--color-black);
  background-color: var(--color-white-shade-two);
}

.accordion-body {
  background-color: var(--color-white-shade-two);
}

.job-type {
  margin-bottom: 0;
  margin-left: 0.8rem;
  line-height: normal !important;
  font-size: 1.6rem;
  color: var(--color-black);
  word-break: break-word;
  font-weight: 400;
}

.break-word {
  word-break: break-word;
  font-size: 1.4rem;
  color: var(--color-black-shade-two);
  margin-left: 0.2rem;
  font-weight: 400;
  hyphens: auto;
  overflow-wrap: break-word;
}
