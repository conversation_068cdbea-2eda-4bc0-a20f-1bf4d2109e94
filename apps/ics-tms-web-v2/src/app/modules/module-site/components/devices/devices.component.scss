.device-container {
  top: -2rem;
  position: relative;
  overflow-x: hidden;
  margin: 0rem -1.5rem 3rem -1.5rem;

  .p-2 {
    padding: 1rem 1.5rem !important;
  }

  .toolbar-fixed-top {
    border-radius: 0;
    position: fixed;
    top: 5.1rem;
    width: 100%;
    z-index: 1050;
    padding-right: 2rem;
    padding-left: 2rem;
    background-color: var(--md-indigo-600);
    box-shadow:
      0 0.3rem 0.4rem 0 rgba(0, 0, 0, 0.14),
      0 0.3rem 0.3rem -0.2rem rgba(0, 0, 0, 0.1);

    &.no-shadow {
      box-shadow: none;
    }
  }

  .myOptionsDropdown {
    transform: translateX(-7.25rem);
    .disableReboot {
      cursor: not-allowed !important;
    }
  }

  .navbar-collapse {
    display: flex !important;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;

    .navbar-nav {
      flex-direction: row;
      gap: 0.5rem;
    }
  }

  @media (max-width: 99.198rem) {
    .navbar-collapse {
      flex-wrap: wrap;

      .navbar-nav {
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;
        justify-content: flex-start;
        gap: 0.25rem;
        margin-bottom: 0.5rem;

        .nav-item {
          flex: 1 1 auto;
          min-width: 12rem;
        }
      }
    }

    .container-fluid > [ngbDropdown] {
      margin-left: 0;
      width: 100%;
      margin-top: 0.5rem;

      > .options-btn {
        width: 100%;
        text-align: center;
      }
    }
  }

  @media (max-width: 57.598rem) {
    .navbar-collapse {
      .navbar-nav {
        flex-direction: column;
        gap: 0;

        .nav-item {
          flex: none;
          width: 100%;
        }
      }
    }
  }

  .container-fluid {
    > [ngbDropdown] {
      margin-left: auto;
      white-space: nowrap;
    }
  }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding: 1.1rem 1.5rem;
  }

  .device-main-header-container {
    position: relative;
    box-shadow:
      0 0.3rem 0.4rem 0 rgba(0, 0, 0, 0.14),
      0 0.3rem 0.3rem -0.2rem rgba(0, 0, 0, 0.1);
    background-color: var(--color-email-not-verified);
    margin: -2.1rem -1.5rem 3rem -1.5rem;
    padding: 2rem 4rem;
    padding-bottom: 0;

    .app-navbar {
      background-color: var(--color-primary);
      color: var(--color-white);
      margin-top: 0;
      left: 0;
      padding: 1.4rem 0.9rem;

      .opacity-0 {
        opacity: 0;
        cursor: default;
      }

      ul.breadcrumb {
        list-style: none;
        margin-bottom: 2rem;

        i {
          padding: 0 0.8rem;
          font-size: 2rem;
        }

        li {
          display: inline;
          font-weight: 400;
          font-size: 1.4rem;
          cursor: pointer;

          a {
            color: var(--details-popup-bg);
            text-decoration: none;
            cursor: pointer;
            max-width: 15rem;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
          }
        }
      }

      h1 {
        font-size: 3.6rem;
        font-weight: 400;
        padding-left: 0;
      }
    }
    .masthead-toolbar {
      min-height: 4.4rem;
    }
  }

  .navbar {
    min-height: 4.4rem;
    margin-bottom: 0.2rem;
    border: 0rem solid transparent;
    .container-fluid {
      margin: 0;
    }
    &-collapse {
      padding-left: 0rem;
    }
  }

  .navbar-items {
    background-color: var(--color-primary);
    left: 0;
    cursor: pointer;
    padding-bottom: 0;

    #navbarNav {
      .navbar-nav {
        display: flex;
        gap: 0.5rem;

        .activeTab {
          border-bottom: 0.2rem solid var(--color-white);

          a {
            color: var(--color-white) !important;
          }
        }

        li {
          font-weight: 500;
          font-size: 1.5rem;

          a {
            color: var(--color-white);
          }

          &:hover {
            a {
              color: var(--color-white);
            }
          }
        }
      }
    }
  }

  .nav-link:hover,
  .nav-link:focus {
    color: var(--color-white);
  }

  .tile-card {
    width: 100%;
    border-radius: 0.3rem;
    border: none;
    padding: 0;
    font-size: 1.4rem;
  }

  .grid-area {
    color: var(--color-black-shade-one);
    margin: 1.6rem;
    position: relative;

    mat-grid-tile ::ng-deep .mat-grid-tile-content {
      align-items: flex-start !important;
      justify-content: flex-start !important;
    }
  }

  .device-container {
    background-color: var(--color-bg-default);
    padding-bottom: 1.6rem;
  }

  .card-item {
    border-right: 0.06rem solid var(--color-border);

    .status-container {
      .inner-status-contents {
        display: flex;
        align-items: center;
        color: var(--color-black);

        #unknown {
          font-size: 1.8rem;
          height: fit-content;
          color: var(--md-grey-500);
        }

        #operational {
          font-size: 1.8rem;
          color: var(--label-success);
          width: fit-content;
          height: fit-content;
        }

        #out_of_service {
          font-size: 1.8rem;
          color: var(--color-bg-red);
          width: fit-content;
          height: fit-content;
        }

        #inactive {
          color: var(--label-inactive);
          font-size: 1.8rem;
          width: fit-content;
          height: fit-content;
        }

        .status-text {
          font-size: 1.4rem;
          margin: 0;
          color: var(--md-grey-500);
        }

        .col-green {
          color: var(--color-bg-green);
        }

        .col-red {
          color: var(--label-danger);
        }

        .col-gray {
          color: var(--label-inactive);
        }

        .years-text {
          margin: 0;
          color: var(--color-black-shade-two);
          font-size: 1.3rem;
        }
      }
    }
  }

  .middle-container-device {
    border-radius: 0.3rem;
  }

  .key-group-icon {
    font-size: 1.4rem !important;
    color: var(--color-black-shade-two) !important;
  }

  .first-main-card {
    display: flex !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    min-height: 6.3rem;

    h5 {
      font-size: 1.2rem;
      color: var(--color-card-item-label);
      margin-bottom: 0.4rem;
      font-weight: bold;
    }

    span {
      font-size: 1.4rem;
      color: var(--color-card-item-label);
    }
  }

  .last-layout-container {
    .last-common-layout {
      background: none;
      border-bottom: 0.1rem solid var(--color-border);
      box-shadow: none;

      h3 {
        font-size: 2rem;
        font-weight: 400;
        margin: 1.6rem 0 1rem;
        padding-bottom: 1rem;
      }
    }

    .deviceAlarm-list {
      margin-top: 0.8rem;

      .alarm-data {
        display: flex;
        border-bottom: 0.1rem solid var(--color-border);

        .col-red {
          color: var(--label-danger);
        }

        i {
          margin-top: 0.3rem;
          color: var(--label-warning);
          font-size: 1.4rem;
        }

        .deviceAlarm-content {
          margin-left: 0.8rem;
          display: flex;
          flex-direction: column;
          gap: 0.2rem;
          margin-bottom: 0.5rem;

          .heading-alarm {
            margin: 0;
            font-size: 1.4rem;
          }

          .desc-alarm {
            margin: 0;
            font-size: 1.3rem;
            color: var(--color-black-shade-two);
          }
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .common-layout {
    margin: 0 1.4rem 0 1.4rem;

    .common-card-1 {
      .card {
        .card-body {
          height: 5rem;
          padding: 0 0 0 1.5rem !important;
        }

        .card-details {
          display: flex;
          padding: 1rem 0;
          width: 11.6rem;
        }
      }
    }
  }

  .tile-card-title {
    display: flex;
    padding: 0 1rem;
    align-items: center;
    justify-content: space-between;
    margin: 0;
    background-color: var(--list-group-item-hover);
    border-bottom: 0.1rem solid var(--color-border);
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;

    .time-container-heading {
      display: flex;
      align-items: center;

      .time-icon {
        font-size: 2.1rem;
        margin-right: 0.8rem;
        color: var(--color-time-icon);
        font-weight: 500;
      }

      .time-label {
        font-size: 1.6rem;
        font-weight: 600;
      }

      h4 {
        font-weight: 600;
        font-size: 1.5rem;
        color: var(--color-time-icon);
        margin: 0;
      }

      h5 {
        color: var(--label-unknown);
        margin: 0;
        font-size: 1.1rem;
      }

      .date-utc-row {
        padding-left: 0.2rem;

        .utc-label {
          font-size: 0.8rem;
          color: var(--color-black-shade-three) !important;
          font-weight: 300;
        }
      }
    }

    .icon-row-item-wrapper {
      display: flex;
      align-items: center;

      .icon-row-item {
        display: flex;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center;
        -ms-flex-direction: column;
        flex-direction: column;
        border-right: 0.1rem solid var(--color-border);
        height: 4rem;
        padding: 0.8rem 1rem 0.4rem;
        cursor: pointer;

        &:first-child {
          border-left: 0.1rem solid var(--color-border);
        }

        > .icon {
          color: var(--color-time-icon);
        }
      }

      .master {
        font-size: 2.1rem;
        .icon {
          transform-style: preserve-3d;
          transition: transform 2s cubic-bezier(0.4, 0.2, 0.2, 1);
        }
        &:hover .icon,
        .icon.master-crown-animate:hover {
          animation: rotate-crown-y 2s cubic-bezier(0.4, 0.2, 0.2, 1);
        }
      }

      @keyframes rotate-crown-y {
        0% {
          transform: rotateY(0deg);
        }
        100% {
          transform: rotateY(360deg);
        }
      }

      .auxiliary > .icon {
        font-size: 2.4rem;
      }
    }

    .temperature-container {
      cursor: pointer;
      display: flex;
      align-items: center;
      position: relative;
      padding: 0.5rem 1rem 0.4rem;

      .thermometer-icon {
        font-size: 2.1rem;
        color: var(--color-time-icon);
      }

      p {
        margin-top: 1.5rem;
        width: 4.25rem;
        height: 100%;
        font-size: 1.5rem;
      }

      .deg {
        position: absolute;
        transition: all 0.4s ease-out;
        opacity: 0;

        &.c {
          transform: translate(1.6rem, -1.8rem);
        }

        &.f {
          transform: translate(1.6rem, 1.8rem);
        }

        &.show {
          opacity: 1;
          transform: translate(1.6rem, 0.5rem);
        }
      }
    }
  }

  .devices-nav-item {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    font-size: smaller;
  }

  #dropdownBasic1 {
    font-weight: 500;
    padding: 0.5rem 1rem;
    text-transform: uppercase;
  }

  .dropdown-menu {
    left: -7rem;
    top: 3.8rem;
    min-width: 16rem;
    box-shadow:
      0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
      0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
      0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
    margin: 0.2rem 0 0;
    padding: 0.5rem 0;
    text-align: left;
    border: none;
  }

  .dropdown-item {
    font-size: 1.4rem;
    padding: 0.3rem 2rem;
  }

  .dropdown-item:hover {
    text-decoration: none;
    color: var(--color-black-shade-one);
    background-color: var(--color-bg-default);
  }

  .bottom-singleLine-rows {
    .auxiliary-device {
      padding: 1rem 1.5rem;
      border-bottom: 0.1rem solid var(--color-border);

      .device-meta-heading {
        font-size: 1.1rem;
        text-transform: uppercase;
        margin-top: 0;
        margin-bottom: 0.4rem;
        font-weight: bold;
      }

      .device-status-container {
        display: flex;

        .device-status {
          display: flex;
          white-space: nowrap;
          text-overflow: ellipsis;

          .device-icon {
            font-size: 1.8rem;
          }

          .label {
            display: flex;
            align-items: center;
            padding: 0.1rem 0.2rem 0.1rem 0.6rem;
            color: var(--color-white);
            gap: 0.5rem;
            border-radius: 0.25rem;
            font-weight: bold;

            span {
              &:last-child {
                background-color: var(--color-white);
                border-radius: 0 0.25rem 0.25rem 0;
                padding: 0 0.6rem;

                > i {
                  color: var(--color-black);
                  font-size: 1.2rem;

                  &:first-child {
                    margin-right: 0.5rem;
                  }
                }
              }
            }
          }

          .label-blue-gray {
            background-color: var(--label-inactive);
          }

          .label-away {
            background-color: var(--label-unknown);
            padding-right: 0.6rem;
          }
        }

        span {
          font-size: 1.4rem;
        }

        .swap-call-icon {
          font-size: 1.8rem;
        }
      }
    }

    .prompset-device-container {
      padding: 1rem 1.5rem;

      .promptset-header {
        display: flex;
        align-items: center;
        gap: 1rem;

        .promptset-icon {
          color: var(--color-time-icon);
          font-size: 1.4rem;
          height: fit-content;
          width: fit-content;
          margin-bottom: 0.5rem;
        }
      }

      .prompset-device-detail {
        > a {
          text-decoration: none;
          color: var(--color-time-icon);

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .other-info {
      padding: 1rem 1.5rem;

      h5 {
        margin-bottom: 0.5rem;
      }

      .device-meta-other-info {
        display: flex;
        align-items: center;
        gap: 0.2rem;

        .other-info-icon {
          font-size: 1.8rem;
          color: var(--color-time-icon);
        }

        .other-info-bg {
          color: var(--color-bg-red);
        }
      }
    }

    .notes-row {
      padding: 1rem 1.5rem;

      p {
        color: var(--color-time-icon);
        font-size: 1.4rem;
        margin: 0;
      }

      .add-notes {
        font-style: italic;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .options-btn {
    font-size: 1.4rem;
    font-weight: bold;
    border-radius: 0;
    background-color: transparent;
    color: rgba(255, 255, 255, 0.7);
    border: none;
    transition: all ease-in 0.1s;
    padding: 1.1rem 1.5rem;

    &:hover {
      color: rgba(255, 255, 255, 0.9);
      background-color: rgba(255, 255, 255, 0.2);
    }

    &[aria-expanded='true']:focus {
      color: rgba(255, 255, 255, 0.9);
      background-color: rgba(255, 255, 255, 0.2);
    }

    &:active {
      background-color: transparent;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .device-overview-tooltip.tooltip-inner {
    text-wrap: none;
    width: fit-content;
    font-size: 1.6rem !important;
  }

  .display-none {
    display: none;
  }

  .extra-padding-full-screen {
    width: 4.4rem;
  }

  .box-shadow-none {
    box-shadow: none !important;
  }

  .challenge-response {
    margin-left: 25%;
    width: 50%;
  }
}
