import {
  Component,
  HostListener,
  OnInit,
  ViewEncapsulation,
  inject,
  ChangeDetectorRef,
} from '@angular/core';
import { DatePipe } from '@angular/common';
import { Store } from '@ngrx/store';
import { Observable, Subject, takeUntil } from 'rxjs';
import semver from 'semver';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezonePlugin from 'dayjs/plugin/timezone';
import {
  devicesSelector,
  isLoadingSelector,
} from '../../store/selectors/devices.selector';
import * as devicesActions from '../../store/actions/devices.actions';
import { loadDeviceAlarmData } from '../../store/actions/device-alarm.actions';
import { DeviceAlarm } from '../../models/device-alarm.modal';
import { deviceAlarmSelector } from '../../store/selectors/device-alarm.selectors';
import { EditDeviceInfoComponent } from '../device-option-modals/edit-device-info/edit-device-info.component';
import { MoveDeviceOptionsComponent } from '../device-option-modals/move-device-options/move-device-options.component';
import { loadSites } from '../../../../store/actions/sites.actions';
import { RebootDeviceComponent } from '../device-option-modals/reboot-device/reboot-device.component';
import { RecommissionDeviceComponent } from '../device-option-modals/recommission-device/recommission-device.component';
import { DeleteDeviceComponent } from '../device-option-modals/delete-device/delete-device.component';
import { loadDeviceList } from '../../store/actions/device-list.actions';
import { deviceListSelector } from '../../store/selectors/device-list.selectors';
import { loadDeviceSiteData } from '../../store/actions/device-site.actions';
import { deviceSiteSelector } from '../../store/selectors/device-site.selectors';
import { selectAlarmRulesData } from '../../store/selectors/alarm-rules.selectors';
import { DeviceList } from '../../models/device-list.modal';
import { DeviceSite } from '../../models/device-site-data.modal';
import { AlarmRules } from '../../models/alarm-rules.modal';
import { DeviceTypes } from '../../models/device-types.modal';
import { loadSelfData } from '../../store/actions/self.actions';
import { SelfData } from '../../models/self.modal';
import { loadAlarmRules } from '../../store/actions/alarm-rules.actions';
import {
  selfDataSelector,
  selfLoadingSelector,
} from '../../store/selectors/self.selectors';
import { loadDeviceTypes } from '../../../../store/actions/device-types.actions';
import {
  selectDeviceTypesData,
  selectDeviceTypesIsLoading,
} from '../../../../store/selectors/device-types.selector';
import {
  ALLOWED_DEVICE_TYPES,
  DEVICE_ACTIVITY,
  DEVICE_OFFLINE,
  DEVICE_TYPES,
  FULL_PAGE_ITEMS,
  UNSUPPORTED_CONFIG_DEVICES,
  UNSUPPORTED_MEDIA_DEVICES,
  UNSUPPORTED_VERSION_DEVICES,
} from '../../constants/appConstants';
import { HeaderStateService } from '../../services/header-state.service';
// Removed DevicePropertiesService import as we're using store
import { DeviceData } from '../../models/devices.interface';
import { SwapDeviceComponent } from '../device-option-modals/swap-device/swap-device.component';
import { setHeaderName } from '../../../../store/actions/globalStore.actions';
import { AuthService } from 'src/app/services/auth.service';
import { SitesParams } from 'src/app/models/sites.model';
import { devices } from 'src/app/constants/device';
import { NAVBAR_TABS, NavbarTab } from 'src/app/constants/navbar-tabs.constant';

@Component({
  selector: 'app-devices',
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.scss'],
  providers: [DatePipe],
  encapsulation: ViewEncapsulation.None,
})
export class DevicesComponent implements OnInit {
  datePipe = inject(DatePipe);

  store = inject(Store);

  route = inject(ActivatedRoute);

  router = inject(Router);

  authService = inject(AuthService);

  cdr = inject(ChangeDetectorRef);

  private modalService = inject(NgbModal);

  private headerStateService = inject(HeaderStateService);

  private destroy$ = new Subject<void>();

  tabs: NavbarTab[] = [];

  data!: DeviceData;

  selectedTab = 'overview';

  devicesData$: Observable<any> = this.store
    .select(devicesSelector)
    .pipe(takeUntil(this.destroy$));

  isLoading$: Observable<boolean> = this.store
    .select(isLoadingSelector)
    .pipe(takeUntil(this.destroy$));

  deviceProperties = devices.common;

  siteId!: string;

  deviceId!: string;

  disableReboot = false;

  deviceAlarmData!: DeviceAlarm[];

  DeviceList!: DeviceList;

  DeviceTypes!: DeviceTypes[];

  DeviceSite!: DeviceSite;

  AlarmRules!: AlarmRules[];

  SelfData!: SelfData;

  showCelsius = false;

  showFahrenheit = true;

  timeZone!: string;

  datetime!: string;

  formattedTime = '';

  protected readonly Number = Number;

  protected readonly DEVICE_TYPES = DEVICE_TYPES;

  protected readonly FULL_PAGE_ITEMS = FULL_PAGE_ITEMS;

  protected readonly DEVICE_ACTIVITY = DEVICE_ACTIVITY;

  protected readonly DEVICE_OFFLINE = DEVICE_OFFLINE;

  isToolbarFixed = false;

  isHistoryHeaderSticky = false;

  ngOnInit(): void {
    this.devicePageRouterEvent();
    this.dispachMetaData();
    this.getDeviceData();
    this.getMetaData();
    this.getTimeZones();
    document.body.style.overflowX = 'hidden';
    // No need to call subscribeToDevicePropertyChanges() as we're using store
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    document.body.style.overflowX = '';
  }

  // Removed subscribeToDevicePropertyChanges method as we're using store

  private devicePageRouterEvent(): void {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        const url = event.urlAfterRedirects;
        const urlSegments = url.split('/');
        this.selectedTab = urlSegments[urlSegments.length - 1];
      }
    });

    this.route.params.subscribe(params => {
      this.siteId = params['site_id'];
      this.deviceId = params['device_id'];
    });

    if (this.route.snapshot.children[0].url[0].path)
      this.selectedTab = this.route.snapshot.children[0].url[0].path;
  }

  private getDeviceData(): void {
    this.devicesData$.subscribe(data => {
      const device = data?.devicesData?.devicesReducers?.data;
      if (device && Object.keys(device).length > 0 && device.id) {
        this.data = device;

        // Update deviceProperties when device data changes
        // This ensures real-time updates from both initial load and property changes
        if (device.description) {
          this.deviceProperties.notes = device.description;
        }

        this.filterTabs();

        // Trigger change detection to ensure UI updates
        this.cdr.detectChanges();
      }
    });
  }

  private dispachMetaData(): void {
    this.store.dispatch(
      devicesActions.getData({ siteId: this.siteId, deviceId: this.deviceId })
    );
    this.store.dispatch(loadDeviceAlarmData({ deviceId: this.deviceId }));
    this.store.dispatch(loadDeviceList());
    this.store.dispatch(loadDeviceSiteData({ siteId: this.siteId }));
    this.store.dispatch(loadDeviceTypes({}));
    this.store.dispatch(loadAlarmRules({ siteId: this.siteId }));
    this.store.dispatch(loadSelfData());
  }

  private getTimeZones(): void {
    this.timeZone = this.data?.config?.timeZone ?? '';

    this.datetime = this.data?.config?.dateTime ?? '';
  }

  private getMetaData(): void {
    this.store
      .select(selectAlarmRulesData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.AlarmRules = data;
      });

    this.store
      .select(selfDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.SelfData = data;
        this.authService.setUser(data);
      });

    this.store
      .select(deviceAlarmSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.deviceAlarmData = [...data].sort(
          (a, b) =>
            new Date(b.modified).getTime() - new Date(a.modified).getTime()
        );
      });

    this.store
      .select(deviceListSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.DeviceList = data;
      });

    this.store
      .select(deviceSiteSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.DeviceSite = data;
      });

    this.store
      .select(selectDeviceTypesData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.DeviceTypes = data;
      });

    this.headerStateService.historyHeaderSticky$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isSticky => {
        this.isHistoryHeaderSticky = isSticky;
        this.cdr.detectChanges();
      });
  }

  filterTabs() {
    this.tabs = NAVBAR_TABS.filter(tab => {
      const deviceTypeId = this.data?.deviceType?.id || '';

      switch (tab.key) {
        case 'device-config':
          return this.showDeviceConfigTab(deviceTypeId);
        case 'media':
          return this.showMediaTab(deviceTypeId);
        case 'versions':
          return this.showVersionTab(deviceTypeId);
        case 'history':
          return this.showHistoryTab();
        default:
          return true;
      }
    });
  }

  @HostListener('window:scroll', [])
  onWindowScroll() {
    const scrollY =
      window.pageYOffset || document.documentElement.scrollTop || 0;
    const shouldBeFixed = scrollY >= 120;

    if (this.isToolbarFixed !== shouldBeFixed) {
      this.isToolbarFixed = shouldBeFixed;
      this.cdr.detectChanges();
    }
  }

  changeTab(selectedTab: string) {
    this.store.dispatch(
      setHeaderName({
        name: `Device ${selectedTab
          .charAt(0)
          .toUpperCase()}${selectedTab.slice(1)}`,
      })
    );
    this.router.navigateByUrl(
      `/sites/${this.siteId}/${this.deviceId}/${selectedTab}`
    );
    this.selectedTab = selectedTab;
  }

  getDateAndTime(time: any, format: string) {
    return this.datePipe.transform(time, format);
  }

  getISOTimestamp(datetime = '', timezone = ''): string {
    if (!datetime || !timezone) return '';

    dayjs.extend(utc);
    dayjs.extend(timezonePlugin);
    try {
      const dateInTz = dayjs(datetime).tz(timezone);
      const dateStr = dateInTz.format('MMM D YYYY');
      const offset = dateInTz.format('Z');
      return `${dateStr} (UTC ${offset})`;
    } catch (e) {
      return '';
    }
  }

  getFormattedTime(datetime = '', timezone = ''): string {
    if (!datetime || !timezone) return '';

    try {
      const dateInTz = dayjs.utc(datetime).tz(timezone);
      return dateInTz.format('hh:mm A');
    } catch (e) {
      return '';
    }
  }

  showFahrenheitTemp() {
    this.showCelsius = true;
    this.showFahrenheit = false;
  }

  showCelsiusTemp() {
    this.showCelsius = false;
    this.showFahrenheit = true;
  }

  getYearsDifference(lastRegistered: string, lastContacted: string) {
    const lastRegisteredDate = new Date(lastRegistered);
    const lastContactDate = new Date(lastContacted);

    const timeDifference =
      lastContactDate.getTime() - lastRegisteredDate.getTime();

    const yearsDifference = timeDifference / (1000 * 60 * 60 * 24 * 365.25);
    const monthsDifference = timeDifference / (1000 * 60 * 60 * 24 * 30.44);

    if (yearsDifference < 1) {
      return `${monthsDifference.toFixed()} months`;
    }
    return `${yearsDifference.toFixed()} years`;
  }

  calculateNoOfYears(modified: string) {
    const currentDate = new Date();
    const lastRegisteredDate = new Date(modified);
    const timeDifference = currentDate.getTime() - lastRegisteredDate.getTime();

    const yearsDifference = timeDifference / (1000 * 60 * 60 * 24 * 365.25);
    const monthsDifference = timeDifference / (1000 * 60 * 60 * 24 * 30.44);
    const weeksDifference = timeDifference / (1000 * 60 * 60 * 24 * 7);
    const daysDifference = timeDifference / (1000 * 60 * 60 * 24);
    const hoursDifference = timeDifference / (1000 * 60 * 60);
    const minutesDifference = timeDifference / (1000 * 60);

    if (yearsDifference >= 1) {
      return `${yearsDifference.toFixed()} years`;
    }
    if (monthsDifference >= 1) {
      return `${monthsDifference.toFixed()} months`;
    }
    if (weeksDifference >= 1) {
      return `${weeksDifference.toFixed()} weeks`;
    }
    if (daysDifference >= 1) {
      return `${daysDifference.toFixed()} days`;
    }
    if (hoursDifference >= 1) {
      return `${hoursDifference.toFixed()} hours`;
    }
    return `${minutesDifference.toFixed()} minutes`;
  }

  openEditDeviceInfoModal(
    ipAddress: any,
    description: any,
    serialNumber: string
  ) {
    const modalRef = this.modalService.open(EditDeviceInfoComponent, {
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });
    modalRef.componentInstance.name = ipAddress;
    modalRef.componentInstance.description = description;
    modalRef.componentInstance.deviceId = this.deviceId;
    modalRef.componentInstance.siteId = this.siteId;
    modalRef.componentInstance.serialNumber = serialNumber;
    modalRef.componentInstance.deviceType = this.data.deviceType.name;
  }

  openMoveDeviceModal() {
    const params: SitesParams = {
      autoPoll: false,
      pageIndex: 0,
      pageSize: 20,
      showHiddenSites: true,
    };
    this.store.dispatch(loadSites({ params, replace: true }));
    const modalRef = this.modalService.open(MoveDeviceOptionsComponent, {
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });
    modalRef.componentInstance.siteId = this.siteId;
    modalRef.componentInstance.deviceId = this.deviceId;
    modalRef.componentInstance.deviceName = this.data.name;
    modalRef.componentInstance.deviceType = this.data.deviceType.name;
    modalRef.componentInstance.deviceDescription = this.data.description;
    modalRef.componentInstance.serialNumber =
      this.data.serialNumber || this.data.name;
  }

  openRebootDeviceModal() {
    const modalRef = this.modalService.open(RebootDeviceComponent, {
      centered: true,
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });
    modalRef.componentInstance.deviceId = this.deviceId;
    modalRef.result.then(result => {
      if (result) this.disableReboot = true;
    });
  }

  openRecommissionDeviceModal() {
    const modalRef = this.modalService.open(RecommissionDeviceComponent, {
      centered: true,
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });
    modalRef.componentInstance.deviceId = this.deviceId;
  }

  openDeleteDeviceModal(name: string) {
    const modalRef = this.modalService.open(DeleteDeviceComponent, {
      centered: true,
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });
    modalRef.componentInstance.name = name;
    modalRef.componentInstance.deviceId = this.deviceId;
  }

  clickedCopy(event: string) {
    this.selectedTab = event;
    this.router.navigateByUrl(
      `/sites/${this.siteId}/${this.deviceId}/${event}`
    );
  }

  onClickBackByChild(event: string) {
    this.selectedTab = event;
    this.router.navigateByUrl(
      `/sites/${this.siteId}/${this.deviceId}/${event}`
    );
  }

  openChallengeResponse() {
    this.store.dispatch(setHeaderName({ name: 'Challenge / Response' }));
    this.selectedTab = 'challenge-response';
    this.router.navigateByUrl(
      `/sites/${this.siteId}/${this.deviceId}/challenge-response`
    );
  }

  mayEdit() {
    let isValid!: boolean | null;
    this.store.select(selfLoadingSelector).subscribe(data => {
      isValid = !data;
    });
    if (isValid) {
      return AuthService.isAllowedAccess('WRITE_SITES');
    }
    return false;
  }

  showDeviceSwap(id: string) {
    let isLoading = true;
    let isValid!: boolean;

    this.store.select(selectDeviceTypesIsLoading).subscribe(data => {
      isLoading = data;
    });

    if (!isLoading) {
      isValid = this.DeviceTypes.some(
        item => item.id === id && item.featureflags.includes('DEVICES_SWAP_OUT')
      );
    } else {
      isValid = false;
    }
    return isValid;
  }

  openSwapDeviceModal() {
    const modalRef = this.modalService.open(SwapDeviceComponent, {
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });
    modalRef.componentInstance.siteId = this.data.siteId;
    modalRef.componentInstance.deviceId = this.data.id;
    modalRef.componentInstance.serial = this.data.name;
    modalRef.componentInstance.serialNumber = this.data.serialNumber;
    modalRef.componentInstance.siteName = this.data.siteName;
    modalRef.componentInstance.deviceType = this.data.deviceType.name;
  }

  allowResetAuthentication(id: string) {
    const allowedDeviceTypes = ALLOWED_DEVICE_TYPES;

    let isValid!: boolean | null;
    this.store.select(selfLoadingSelector).subscribe(data => {
      isValid = !data;
    });

    if (isValid) {
      return (
        allowedDeviceTypes.includes(id) &&
        AuthService.isAllowedAccess('RESET_AUTHENTICATION') &&
        this.data.presence !== 'OUT_OF_INSTANCE'
      );
    }
    return false;
  }

  showChallengeResponse() {
    return (
      (this.hasDeviceFeatureFlag(
        this.data?.deviceType?.id || '',
        'TAMPER_CLEAR'
      ) &&
        this.hasRole('TAMPER_CLEAR')) ||
      (this.hasDeviceFeatureFlag(
        this.data?.deviceType?.id || '',
        'FACTORY_RESET'
      ) &&
        this.hasRole('FACTORY_RESET') &&
        semver.satisfies(
          semver.coerce(this.data.releaseVersion) || '',
          '>=3.2.5'
        ))
    );
  }

  hasDeviceFeatureFlag(id: string, featureFlag: string) {
    let isLoading = true;
    let isValid!: boolean;
    this.store.select(selectDeviceTypesIsLoading).subscribe(data => {
      isLoading = data;
    });
    if (!isLoading) {
      isValid = this.DeviceTypes.some(
        item => item.id === id && item.featureflags.includes(featureFlag)
      );
    } else isValid = false;

    return isValid;
  }

  hasRole(role: string) {
    let isValid!: boolean | null;
    this.store.select(selfLoadingSelector).subscribe(data => {
      isValid = !data;
    });

    if (isValid) {
      return AuthService.hasRole(role);
    }
    return false;
  }

  getDeviceActivity(code: string) {
    return DEVICE_ACTIVITY[code];
  }

  showMediaTab(id: string) {
    const unsupportedDevices = UNSUPPORTED_MEDIA_DEVICES;
    if (unsupportedDevices.indexOf(id) > -1) {
      return false;
    }
    const company = AuthService.getCompany();
    if (!company) {
      return false;
    }
    if (company.featureFlags && company.featureFlags.length) {
      return company.featureFlags.some(flag =>
        ['MEDIA', 'PLAYLIST', 'GSTV'].includes(flag)
      );
    }
    return false;
  }

  showVersionTab(id: string) {
    return !(UNSUPPORTED_VERSION_DEVICES.indexOf(id) > -1);
  }

  showDeviceConfigTab(id: string) {
    return !(UNSUPPORTED_CONFIG_DEVICES.indexOf(id) > -1);
  }

  showHistoryTab(): boolean {
    return true;
  }

  convertToFahrenheit(celsius: number) {
    return Math.ceil((celsius * 9) / 5 + 32);
  }

  canOptionsVisible(): boolean {
    return this.mayEdit() || this.showChallengeResponse();
  }

  canEditDevice() {
    return (
      AuthService.isAllowedAccess('WRITE_DEVICES') &&
      this.data.presence !== 'OUT_OF_INSTANCE'
    );
  }

  get terminalRank() {
    const terminalRank = this.data.configData?.['cfg.net-terminal-rank'];
    return terminalRank ? terminalRank.value : '';
  }

  get auxMainDevice() {
    return this.data.auxDevice || this.data.mainDevice;
  }

  getAuxMainUrl(auxMainDevice: any) {
    return `/sites/${auxMainDevice.siteId}/${auxMainDevice.id}/overview`;
  }

  getAuxStatus(currentDevice: DeviceData) {
    const auxStatusKey = 'client.invenco-emulation-aux-status';

    if (
      currentDevice.configData[auxStatusKey] &&
      currentDevice.configData[auxStatusKey].value
    ) {
      return currentDevice.configData[auxStatusKey].value;
    }
    if (
      currentDevice.mainDevice &&
      currentDevice.mainDevice.configData[auxStatusKey] &&
      currentDevice.mainDevice.configData[auxStatusKey].value
    ) {
      return currentDevice.mainDevice.configData[auxStatusKey].value;
    }

    return null;
  }

  get isAppFFEnabled_ADA() {
    const appFeatureFlags = this.authService.appFeatureFlags
      ? JSON.parse(this.authService.appFeatureFlags)
      : { ADA: false, RENDITIONS: false };
    return appFeatureFlags.ADA;
  }

  getDeviceStatusIcon() {
    if (this.auxMainDevice) {
      switch (this.auxMainDevice.status) {
        case 0:
          return 'remove_circle';
        case 1:
          return 'check_circle';
        case 2:
          return 'cancel';
        default:
          return 'cloud_off';
      }
    }
    return 'remove_circle';
  }

  get alarms() {
    return this.data.alarmRulesSettings.suspended;
  }

  isTimeInFuture(time: number) {
    if (!time) return false;
    const now = new Date().valueOf();
    return now <= time;
  }

  getIconsForOtherInfo(category: string) {
    switch (category) {
      case 'Security':
        return 'no_encryption';
      case 'OPT Tampered':
        return 'pan_tool';
      case 'OPT in Safe Mode':
        return 'security';
      case 'Component Disconnected':
        return 'power';
      case 'Site Integration':
        return 'swap_calls';
      default:
        return '';
    }
  }

  getDevicePromptSetURL(id: string) {
    return `/media/promptsets/${id}?readOnly=true`;
  }

  getTabRouteId(tab: NavbarTab) {
    return `link${tab.label.replace(/\s+/g, '')}`;
  }
}
