<div class="device-container" *ngIf="data">
  <div
    class="device-main-header-container"
    [ngClass]="{ 'box-shadow-none': FULL_PAGE_ITEMS.includes(selectedTab) }"
  >
    <!-- Nav bar contains breadcrum and ip -->
    <nav
      class="app-navbar"
      aria-label="Breadcrumb navigation for site and IP address"
    >
      <ul class="breadcrumb">
        <li><a routerLink="/sites">Sites</a></li>
        <i class="material-icons">keyboard_arrow_right</i>
        <li>
          <a [routerLink]="['/asset-management', 'sites', siteId, 'details']">{{
            data.siteName
          }}</a>
        </li>
        <i class="material-icons">keyboard_arrow_right</i>
        <li>
          <a [routerLink]="['/sites', data.siteId, data.id, 'overview']">{{
            data.name
          }}</a>
        </li>
        <i class="material-icons" *ngIf="selectedTab === 'pull-files'"
          >keyboard_arrow_right</i
        >
        <li *ngIf="selectedTab === 'pull-files'">Pull Files</li>
      </ul>
      <h1
        [ngClass]="{ 'opacity-0': selectedTab === 'pull-files' }"
        class="toolbar-ip"
      >
        {{ data.name || null }}
      </h1>
    </nav>

    <div
      class="masthead-toolbar"
      *ngIf="FULL_PAGE_ITEMS.includes(selectedTab)"
    ></div>

    <!-- Nav bar contains nav items -->
    <nav
      class="navbar navbar-expand-lg navbar-dark navbar-items"
      aria-label="Main Navigation"
      [ngClass]="{
        'display-none': FULL_PAGE_ITEMS.includes(selectedTab),
        'toolbar-fixed-top': isToolbarFixed,
        'no-shadow':
          isToolbarFixed && isHistoryHeaderSticky && selectedTab === 'history',
      }"
    >
      <div class="container-fluid">
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav">
            <li
              class="nav-item"
              role="presentation"
              *ngFor="let tab of tabs"
              routerLinkActive="is-active"
              [routerLinkActiveOptions]="{ exact: true }"
              [ngClass]="{ activeTab: selectedTab === tab.key }"
            >
              <a
                [id]="getTabRouteId(tab)"
                [routerLink]="[siteId, deviceId, tab.route]"
                class="nav-link"
                (click)="changeTab(tab.key)"
              >
                {{ tab.label }}
              </a>
            </li>
          </ul>
        </div>
        <div ngbDropdown class="d-inline-block" *ngIf="canOptionsVisible()">
          <button
            type="button"
            class="options-btn"
            id="optionsDropdown"
            ngbDropdownToggle
          >
            OPTIONS
          </button>
          <div ngbDropdownMenu aria-labelledby="" class="myOptionsDropdown">
            <button
              *ngIf="mayEdit()"
              class="edit-device-device-option"
              (click)="
                openEditDeviceInfoModal(
                  data.name,
                  data.description,
                  data.serialNumber
                )
              "
              ngbDropdownItem
            >
              Edit device info
            </button>
            <hr *ngIf="mayEdit()" class="mb-3 mt-3 p-0" />
            <button
              *ngIf="mayEdit()"
              (click)="openMoveDeviceModal()"
              ngbDropdownItem
            >
              Move
            </button>
            <button
              *ngIf="showDeviceSwap(data.deviceType?.id || '')"
              (click)="openSwapDeviceModal()"
              ngbDropdownItem
            >
              Swap
            </button>
            <button
              *ngIf="mayEdit()"
              [disabled]="disableReboot"
              [ngClass]="{ disableReboot: disableReboot }"
              (click)="openRebootDeviceModal()"
              ngbDropdownItem
            >
              Reboot
            </button>
            <button
              *ngIf="allowResetAuthentication(data.deviceType?.id || '')"
              (click)="openRecommissionDeviceModal()"
              ngbDropdownItem
            >
              Recommission
            </button>
            <button
              *ngIf="showChallengeResponse()"
              (click)="openChallengeResponse()"
              ngbDropdownItem
            >
              Challenge / Response
            </button>
            <hr *ngIf="mayEdit()" class="mt-3 mb-3 p-0" />
            <button
              *ngIf="mayEdit()"
              class="delete-btn-device-option"
              (click)="openDeleteDeviceModal(data.name)"
              ngbDropdownItem
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </nav>
  </div>

  <div
    *ngIf="FULL_PAGE_ITEMS.includes(selectedTab) && selectedTab !== 'overview'"
  >
    <div *ngIf="selectedTab === 'copy-files'">
      <app-copy-file-stepper
        (selectedTab)="onClickBackByChild($event)"
        [device_id]="deviceId"
        [site_id]="siteId"
      ></app-copy-file-stepper>
    </div>
    <div *ngIf="selectedTab === 'pull-files'">
      <app-pull-files
        (selectedTab)="onClickBackByChild($event)"
      ></app-pull-files>
    </div>
    <div
      *ngIf="selectedTab === 'challenge-response'"
      class="challenge-response"
    >
      <app-challenge-response
        (selectedTab)="onClickBackByChild($event)"
      ></app-challenge-response>
    </div>
  </div>

  <!-- The common layout for sites detail -->
  <div class="row common-layout" *ngIf="!FULL_PAGE_ITEMS.includes(selectedTab)">
    <div
      class="col col-lg-3 common-card-1"
      *ngIf="selectedTab !== 'app-config'"
    >
      <div class="card tile-card">
        <div
          *ngIf="data.config?.dateTime && data.config?.['temperature']"
          class="card-body tile-card-title"
        >
          <div *ngIf="data.config?.dateTime" class="time-container-heading">
            <i class="fa-regular fa-clock time-icon"></i>
            <ng-container *ngIf="data.config?.dateTime; else timeNotVisible">
              <ng-container
                *ngIf="
                  data?.config?.['isMaster'] && data.status !== 3;
                  else allInOneLine
                "
              >
                <!-- Media Sync Master present: time at top, date+UTC below -->
                <div class="time-date-utc-master-block">
                  <div class="time-label">
                    {{ data.config?.dateTime | dateFormat: 'hh:mm a' }}
                  </div>
                </div>
                <div class="date-utc-row">
                  <span class="date-label">{{
                    data.config?.dateTime | dateFormat: 'MMM DD, YYYY'
                  }}</span>
                  <span class="utc-label">(UTC +12:00)</span>
                </div>
              </ng-container>
              <ng-template #allInOneLine>
                <!-- No master: all in one line -->
                <div class="time-date-utc-inline">
                  <span class="time-label">{{
                    getFormattedTime(
                      data.config?.dateTime,
                      data.config?.timeZone
                    )
                  }}</span>
                  <span class="utc-label">
                    {{
                      getISOTimestamp(
                        data.config?.dateTime,
                        data.config?.timeZone
                      )
                    }}</span
                  >
                </div>
              </ng-template>
            </ng-container>
            <ng-template #timeNotVisible>
              <h4>N/A</h4>
            </ng-template>
          </div>

          <div class="card-details">
            <div class="icon-row-item-wrapper">
              <!-- Media Sync Master -->
              <div
                class="master icon-row-item"
                *ngIf="data.config?.['isMaster'] && data.status !== 3"
                ngbTooltip="Media Sync Master"
              >
                <i class="fa-solid fa-crown icon master-crown-animate"></i>
              </div>

              <!-- Auxiliary -->
              <div
                class="auxiliary icon-row-item"
                ngbTooltip="This is an auxiliary device"
                *ngIf="terminalRank === 'aux' && isAppFFEnabled_ADA"
              >
                <mat-icon class="icon">accessible</mat-icon>
              </div>
            </div>

            <!-- Temperature -->
            <div
              *ngIf="data.config?.['temperature']; else noTemperature"
              (mouseover)="showFahrenheitTemp()"
              (mouseleave)="showCelsiusTemp()"
              class="temperature-container"
              [ngbTooltip]="'Temperature'"
            >
              <i
                class="fa fa-thermometer-half thermometer-icon"
                aria-hidden="true"
              ></i>
              <p class="deg c" [ngClass]="{ show: showCelsius }">
                {{ data.config?.['temperature'] }}°C
              </p>
              <p class="deg f" [ngClass]="{ show: showFahrenheit }">
                {{
                  convertToFahrenheit(Number(data.config?.['temperature']))
                }}°F
              </p>
            </div>

            <ng-template #noTemperature>
              <div class="temperature-container" [ngbTooltip]="'Temperature'">
                <i
                  class="fa fa-thermometer-half thermometer-icon"
                  aria-hidden="true"
                ></i>
                <span> &nbsp;&nbsp;---- </span>
              </div>
            </ng-template>
          </div>
        </div>

        <!-- Status | Terminal ID -->
        <div class="first-main-card p-2">
          <div class="card-item col-lg-6 p-2">
            <h5>{{ deviceProperties.status }}</h5>
            <div class="status-container">
              <div
                class="inner-status-contents"
                *ngIf="data.statusStr === 'UNKNOWN'"
              >
                <mat-icon id="unknown">cloud_off</mat-icon>
                &nbsp;
                <span class="status-text">Unknown&nbsp;</span>
                <span *ngIf="data.statusAlarmTs"
                  >/&nbsp;
                  <span class="years-text">
                    {{
                      data.statusAlarmTs
                        | dateFormat: 'MMM DD, YYYY' : true : true : true : true
                    }}
                  </span>
                </span>
              </div>

              <div
                class="inner-status-contents"
                *ngIf="data.statusStr === 'OPERATIONAL'"
              >
                <mat-icon id="operational">check_circle</mat-icon>
                &nbsp;
                <p class="status-text col-green">Operational</p>
              </div>

              <div
                class="inner-status-contents"
                *ngIf="data.statusStr === 'OUT_OF_SERVICE'"
              >
                <mat-icon id="out_of_service">cancel</mat-icon>
                &nbsp;
                <p class="status-text col-red">Out of Service</p>
                <p class="years-text">
                  &nbsp;/&nbsp;{{
                    data.statusAlarmTs
                      | dateFormat: 'MMM DD, YYYY' : true : true : true : true
                  }}&nbsp;years
                </p>
              </div>

              <div
                class="inner-status-contents"
                *ngIf="data.statusStr === 'INACTIVE'"
              >
                <mat-icon id="inactive">remove_circle</mat-icon>
                &nbsp;
                <p class="status-text col-gray">Inactive</p>
              </div>
            </div>
          </div>

          <div class="col-lg-6 p-2">
            <h5>{{ deviceProperties.terminalId }}</h5>
            <span *ngIf="data.config?.['terminalId'] !== null">{{
              data.config?.['terminalId']
            }}</span>
            <span *ngIf="data.config?.['terminalId'] === null">-</span>
          </div>
        </div>
        <hr class="m-0 p-0" />

        <!-- Serial | IP Address -->
        <div class="first-main-card p-2">
          <div class="card-item col-lg-6 p-2">
            <h5>{{ deviceProperties.serial }}</h5>
            <span>{{ data.serialNumber }}</span>
          </div>
          <div class="col-lg-6 p-2">
            <h5>{{ deviceProperties.ipAddess }}</h5>
            <span *ngIf="data.ipAddress !== null">{{
              data.ipAddress || null
            }}</span>
            <span *ngIf="data.ipAddress === null"> - </span>
          </div>
        </div>
        <hr class="m-0 p-0" />

        <!-- Type | Release -->
        <div class="first-main-card p-2">
          <div class="card-item col-lg-6 p-2">
            <h5>{{ deviceProperties.type }}</h5>
            <span *ngIf="data.deviceType">{{ data.deviceType.id }}</span>
          </div>
          <div class="col-lg-6 p-2">
            <h5>{{ deviceProperties.release }}</h5>
            <span *ngIf="data.releaseVersion !== null">{{
              data.releaseVersion || null
            }}</span>
            <span *ngIf="data.releaseVersion === null">-</span>
          </div>
        </div>
        <hr class="m-0 p-0" />

        <!-- Model | Last Contact -->
        <div class="first-main-card p-2">
          <div class="card-item col-lg-6 p-2">
            <h5>{{ deviceProperties.model }}</h5>
            <span *ngIf="data.config">{{ data.config.terminalType }} </span>
          </div>
          <div class="col-lg-6 p-2">
            <h5>{{ deviceProperties.lastContact }}</h5>
            <span *ngIf="data.lastContact !== null"
              >{{ getDateAndTime(data.lastContact, 'MMM d, y') }} at
              {{ getDateAndTime(data.lastContact, 'h:mm a') }}</span
            >
            <span *ngIf="data.lastContact === null">Never</span>
          </div>
        </div>
        <hr class="m-0 p-0" />

        <!-- Key Group | Last RKI -->
        <div class="first-main-card p-2">
          <div class="card-item col-lg-6 p-2">
            <h5>{{ deviceProperties.keyGroup }}</h5>
            <span>{{ data.keyGroupId }}</span>
            <div *ngIf="DEVICE_TYPES.indexOf(data.deviceType?.id || '') > -1">
              <span>{{ data.keyGroupRef }}</span>
              <i
                *ngIf="
                  data.inFlight &&
                  data.siteKeygroupId !== data.keyGroupId &&
                  data.siteKeygroupId !== null
                "
                placement="top-left"
                ngbTooltip="An RKI process is in-flight for this device"
                class="fa fa-cog fa-spin text-muted key-group-icon"
              ></i>
              <i
                *ngIf="
                  !data.inFlight &&
                  data.siteKeygroupId !== data.keyGroupId &&
                  data.siteKeygroupId !== null
                "
                placement="top-left"
                ngbTooltip="Device key group does not match the site key group"
                class="fa fa-exclamation-circle text-muted key-group-icon"
              ></i>
            </div>
          </div>
          <div class="col-lg-6 p-2">
            <h5>{{ deviceProperties.lastRKI }}</h5>
            <span>{{
              data.lastSuccessfulRki | dateFormat: 'MMM DD, YYYY [at] hh:mm a'
            }}</span>
          </div>
        </div>
        <hr class="m-0 p-0" />

        <div class="second-main-card bottom-singleLine-rows">
          <!-- Auxiliary Device Setup | Auxiliary -->
          <div
            class="auxiliary-device"
            *ngIf="
              isAppFFEnabled_ADA &&
              (terminalRank == 'aux' || terminalRank == 'main')
            "
          >
            <div class="row">
              <div class="col-sm-12">
                <div class="device-meta-heading">
                  <h5 *ngIf="terminalRank === 'main'">
                    {{ deviceProperties.auxiliaryDevice }}
                  </h5>
                  <h5 *ngIf="terminalRank === 'aux'">
                    {{ deviceProperties.mainDevice }}
                  </h5>
                </div>

                <div class="device-status-container">
                  <div class="device-status">
                    <ng-container
                      *ngIf="
                        data.presence !== 'OUT_OF_INSTANCE';
                        else outOfInstance
                      "
                    >
                      <ng-container
                        *ngIf="
                          auxMainDevice && auxMainDevice.status === 4;
                          else showIcons
                        "
                      >
                        <span class="label label-blue-gray label-nested">
                          <span>N/A</span>
                          <span class="text-light">
                            <i
                              class="fa-solid fa-bell-slash"
                              *ngIf="!alarms"
                            ></i>
                            <i
                              class="fa-solid fa-gear fa-spin fa-fw"
                              *ngIf="
                                isTimeInFuture(
                                  data.alarmRulesSettings.suspendedByDeviceUntil
                                )
                              "
                            ></i>
                          </span>
                        </span>
                      </ng-container>

                      <ng-template #showIcons>
                        <mat-icon class="device-icon"
                          >{{ getDeviceStatusIcon() }}
                        </mat-icon>
                      </ng-template>
                    </ng-container>

                    <ng-template #outOfInstance>
                      <span
                        *ngIf="data.presence === 'OUT_OF_INSTANCE'"
                        class="ms-2"
                      >
                        <span
                          aria-label="OUT_OF_INSTANCE"
                          class="label label-away"
                          >External</span
                        >
                      </span>
                    </ng-template>
                  </div>

                  <span *ngIf="terminalRank === 'main'">Auxiliary Unit</span>
                  <span *ngIf="terminalRank === 'aux'">Main Unit</span> /
                  <a
                    *ngIf="auxMainDevice"
                    [href]="getAuxMainUrl(auxMainDevice)"
                  >
                    {{ auxMainDevice.name }}
                  </a>
                  <span *ngIf="!auxMainDevice"> NA </span>

                  <span
                    *ngIf="
                      getAuxStatus(data) === 'disconnected' &&
                      (terminalRank === 'aux' || terminalRank === 'main')
                    "
                    class="float-end"
                  >
                    <mat-icon class="text-muted swap-call-icon">
                      swap_calls</mat-icon
                    >
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Prompt Set -->
          <div class="prompset-device-container">
            <div class="promptset-header">
              <mat-icon class="promptset-icon">photo_library</mat-icon>
              <h5>{{ deviceProperties.promtSet }}</h5>
            </div>

            <ng-container *ngIf="data.promptSet?.id">
              <div class="prompset-device-detail">
                <a
                  target="_blank"
                  [href]="getDevicePromptSetURL(data.promptSet.id)"
                >
                  {{ data.promptSet.name }}
                  <span class="text-muted"> {{ data.promptSet.version }} </span>
                </a>
              </div>
            </ng-container>
          </div>
          <hr class="m-0 p-0" />

          <!-- Other Info -->
          <div class="other-info">
            <h5>{{ deviceProperties.otherInfo }}</h5>
            <ng-container *ngIf="data.alarmRulesSettings?.suspended">
              <span class="device-meta-other-info">
                <mat-icon class="text-muted other-info-icon"
                  >notifications_off</mat-icon
                >
                <span>Alarms Snoozed </span>
              </span>
            </ng-container>

            <ng-container *ngIf="data.presence == 'OUT_OF_INSTANCE'">
              <span class="device-meta-other-info" ngbToolTip="External">
                <span aria-label="EXTERNAL">
                  <i
                    class="fa-solid fa-arrow-up-right-from-square other-info-icon"
                  ></i>
                </span>
              </span>
            </ng-container>

            <ng-container *ngFor="let oos of data.oosConditions">
              <span class="device-meta-other-info">
                <mat-icon class="other-info-icon other-info-bg">
                  {{ getIconsForOtherInfo(oos.category) }}
                </mat-icon>
                <span>
                  &nbsp;{{ oos.category }}&nbsp;/&nbsp;{{ oos.condition }}
                </span>
              </span>
            </ng-container>
          </div>
          <hr class="m-0 p-0" />

          <!-- Notes -->
          <ng-container *ngIf="canEditDevice()">
            <div class="notes-row">
              <h5>NOTES</h5>
              <p *ngIf="data.description">{{ data.description }}</p>
              <p
                (click)="
                  openEditDeviceInfoModal(
                    data.name,
                    data.description,
                    data.serialNumber
                  )
                "
                *ngIf="!data.description"
                class="add-notes"
              >
                Add Notes
              </p>
            </div>
          </ng-container>
        </div>
      </div>
    </div>

    <div
      class="middle-container-device"
      [ngClass]="{
        'col-lg-12': selectedTab === 'app-config',
        'col-lg-7': selectedTab !== 'app-config',
      }"
    >
      <div *ngIf="selectedTab === 'device-config'" class="tile-card">
        <app-devices-config></app-devices-config>
      </div>
      <div *ngIf="selectedTab === 'jobs'" class="tile-card">
        <app-device-job></app-device-job>
      </div>
      <div *ngIf="selectedTab === 'overview'" class="tile-card">
        <app-devices-overview
          (activeURL)="clickedCopy($event)"
          [deviceData$]="devicesData$"
        ></app-devices-overview>
      </div>
      <div *ngIf="selectedTab === 'media'" class="tile-card">
        <app-devices-media></app-devices-media>
      </div>
      <div *ngIf="selectedTab === 'versions'" class="tile-card">
        <app-devices-versions></app-devices-versions>
      </div>
      <div *ngIf="selectedTab === 'history'" class="tile-card">
        <app-device-history></app-device-history>
      </div>
      <div *ngIf="selectedTab === 'app-config'" class="tile-card">
        <app-device-app-config
          *ngIf="data"
          [deviceDetails]="data"
        ></app-device-app-config>
      </div>
      <div *ngIf="selectedTab === 'network'" class="tile-card">
        <app-device-network></app-device-network>
      </div>
    </div>

    <div
      class="col col-lg-2 last-layout-container"
      *ngIf="selectedTab !== 'app-config'"
    >
      <div class="last-common-layout">
        <h3 class="mb-1">{{ deviceProperties.devicesActivity }}</h3>
      </div>
      <div class="deviceAlarm-list" *ngFor="let alarm of deviceAlarmData">
        <div *ngIf="alarm.status" class="alarm-data">
          <i
            [ngClass]="{ 'col-red': alarm.code === DEVICE_OFFLINE }"
            class="fa fa-bell pointer"
            ngbTooltip="Active Alarm"
          ></i>
          <div
            class="deviceAlarm-content"
            [ngbTooltip]="
              alarm.modified
                | dateFormat: 'dddd, MMM DD YYYY [at] hh:mm a (UTCZ)'
            "
          >
            <p class="heading-alarm">
              Device {{ getDeviceActivity(alarm.code) }}
            </p>
            <p class="desc-alarm">
              {{ alarm.modified | dateFormat: '' : true }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
