<div class="move-device-options-container">
  <div class="top-heading-move-device">
    <div class="heading">
      <h4>Move device</h4>
      <strong (click)="activeModal.dismiss('Cross click')" aria-hidden="true"
        >×</strong
      >
    </div>
    <div class="small-text">
      <span>Select a different site to move the device to.</span>
    </div>
  </div>

  <div class="move-device-input-area">
    <input
      type="text"
      [(ngModel)]="searchSite"
      (ngModelChange)="onSearchSite()"
      placeholder="Search"
      class="ics-input move-device-option-input"
      [spellcheck]="false"
    />
  </div>
  <div
    class="loading-container"
    *ngIf="isLoading && !filteredSitesData?.length"
  >
    <app-ics-loader></app-ics-loader>
  </div>

  <div
    class="site-list-container-moveDevice"
    (scroll)="onScroll($event)"
    *ngIf="
      filteredSitesData &&
      (filteredSitesData.length > 0 ||
        (!isLoading && filteredSitesData.length === 0))
    "
  >
    <div
      class="no-results"
      *ngIf="filteredSitesData && filteredSitesData.length === 0"
    >
      <p>No sites found. Please try a different search term.</p>
    </div>

    <div
      class="sites-list-content"
      *ngFor="let site of filteredSitesData; let i = index"
    >
      <label
        for="site-radio{{ i }}"
        class="site-name"
        [innerHTML]="highlightSearchText(site.siteName || '', searchSite)"
        >{{ site.siteName || '' }}</label
      >
      <input
        name="siteGroup"
        [checked]="site.siteId === selectedSite"
        [value]="site.siteId || ''"
        (click)="onSitesChange(site.siteId || '')"
        class="sites-radio-input"
        id="site-radio{{ i }}"
        type="radio"
      />
    </div>

    <div class="loading-more-indicator" *ngIf="loadingMore$ | async">
      <app-ics-loader></app-ics-loader>
    </div>
  </div>

  <div class="button-moveDeviceOptions-container">
    <button
      (click)="activeModal.close('Close click')"
      class="btn cancel-button"
    >
      Cancel
    </button>
    <button
      (click)="moveDevice()"
      [disabled]="!selectedSite"
      class="btn-primary move-button"
    >
      Move
    </button>
  </div>
</div>
