.move-device-options-container {
  width: 60rem;
  height: fit-content;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .top-heading-move-device {
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    .heading {
      display: flex;
      justify-content: space-between;

      h4 {
        margin: 0.7rem 0;
        font-size: 1.8rem;
        font-weight: 500;
      }

      strong {
        cursor: pointer;
        opacity: 0.2;
        font-size: 2.1rem;

        &:hover {
          opacity: 0.5;
        }
      }
    }

    .small-text {
      span {
        font-size: 1.2rem;
        color: var(--color-black-shade-two);
      }
    }
  }

  .move-device-input-area {
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    .move-device-option-input {
      margin-bottom: 0;
    }
  }

  .site-list-container-moveDevice {
    max-height: 40rem;
    cursor: pointer;
    overflow-y: scroll;

    .sites-list-content {
      border-bottom: 0.1rem solid var(--color-border);
      display: flex;
      justify-content: space-between;
      cursor: inherit;
      padding: 1rem 1.5rem;

      &:hover {
        background-color: var(--dropdown-bg-filter-sites);
      }

      .site-name {
        cursor: inherit;
        margin: 0;
        width: 90%;
        font-weight: 400;
        font-size: 1.4rem;
        line-height: 1.42857143;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
      }

      .sites-radio-input {
        cursor: inherit;
        height: 1.425rem;
        font-size: 1.6rem;
        transform: scale(1.1);
      }

      span {
        padding: 0.625rem 0 1.6rem 0.938rem;
        opacity: 0.8;
        font-size: 1.4rem;
      }
    }

    .hover-none {
      &:hover {
        background-color: var(--color-white);
        cursor: default;
      }
    }
  }

  .button-moveDeviceOptions-container {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    border-top: 0.1rem solid var(--color-border);

    button {
      font-size: 1.4rem;
    }

    .move-button {
      padding: 0.6rem 1.2rem;
      border: none;
      border-radius: 0.3rem;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
      transition: all ease-in 0.1s;
      color: var(--color-white);
      gap: 1rem;

      &:active {
        background-color: var(--color-blue);
      }

      &:disabled {
        opacity: 0.65 !important;
        cursor: not-allowed !important;
        box-shadow: none;

        &:hover {
          background-color: var(--color-primary);
        }
      }
    }

    .cancel-button {
      padding: 0.6rem 1.2rem;
      color: var(--color-primary);
      font-weight: 500;
      border-radius: 0.3rem;
      margin-right: 43.3609rem;

      &:hover {
        color: var(--color-blue);
      }

      &:active {
        border: none;
      }
    }
  }

  .no-sites-found {
    overflow: hidden;
    padding: 1rem 1.5rem;

    span {
      padding: 0;
      font-size: 1.4rem;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 15rem;

    .loading-text {
      margin-top: 1rem;
      font-size: 1.4rem;
      color: var(--color-black-shade-two);
    }
  }

  .no-results {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 10rem;
    text-align: center;
    color: var(--color-black-shade-two);
    font-size: 1.4rem;
    padding: 0 2rem;
  }

  .loading-more-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);
    background-color: rgba(0, 0, 0, 0.02);
    font-style: italic;

    app-ics-loader {
      margin-right: 1rem;
      transform: scale(0.7);
    }

    .loading-more-text {
      font-size: 1.2rem;
      color: var(--color-black-shade-two);
    }
  }
}
