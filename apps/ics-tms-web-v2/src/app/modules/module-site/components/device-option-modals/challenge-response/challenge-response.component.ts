import {
  Component,
  EventEmitter,
  inject,
  OnInit,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { BehaviorSubject, Observable } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import semver from 'semver';
import { SelfData } from '../../../models/self.modal';
import { SelectResponseType } from '../../../constants/appConstants';
import * as devicesActions from '../../../store/actions/devices.actions';
import { devicesSelector } from '../../../store/selectors/devices.selector';
import { loadSelfData } from '../../../store/actions/self.actions';
import { ChallengeResponseData } from '../../../models/challenge-response.model';
import { ChallengeResponseVerifyMfaComponent } from './challenge-response-verify-mfa/challenge-response-verify-mfa.component';
import { ModalConstants } from 'src/app/constants/appConstants';
import { DeviceType } from 'src/app/models/common';
import { selectDeviceTypesData } from 'src/app/store/selectors/device-types.selector';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-challenge-response',
  templateUrl: './challenge-response.component.html',
  styleUrls: ['./challenge-response.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ChallengeResponseComponent implements OnInit {
  availableResponseType: SelectResponseType[] = [];

  responseData: SelectResponseType[] = [
    {
      code: 'TAMPER_CLEAR',
      text: 'Tamper Clear',
    },
    {
      code: 'FACTORY_RESET',
      text: 'Factory Reset',
    },
  ];

  deviceComponent = [
    {
      code: 'UPC',
      text: 'UPC/APC',
    },
    {
      code: 'SDC',
      text: 'SDC',
    },
  ];

  selectedResponse: any;

  selectedDevice: any;

  step = 'request';

  serialNumber = '';

  device_id!: string;

  site_id!: string;

  deviceData!: any;

  selfData!: SelfData;

  ChallengeResponseData!: ChallengeResponseData;

  challengeText = '';

  requestByText = '';

  devicesData$!: Observable<any>;

  private modalService = inject(NgbModal);

  private store = inject(Store);

  private deviceTypes$ = new BehaviorSubject<DeviceType[]>([]);

  @Output() selectedTab = new EventEmitter<string>();

  constructor(private route: ActivatedRoute) {
    this.route.params.subscribe(params => {
      this.device_id = params['device_id'];
      this.site_id = params['site_id'];
    });
    this.store.dispatch(
      devicesActions.getData({ siteId: this.site_id, deviceId: this.device_id })
    );
    this.devicesData$ = this.store.pipe(select(devicesSelector));
    this.store.dispatch(loadSelfData());
    this.store.select(selectDeviceTypesData).subscribe(deviceTypes => {
      this.deviceTypes$.next(deviceTypes);
    });
  }

  ngOnInit(): void {
    this.devicesData$.subscribe(data => {
      this.deviceData = data.devicesData.devicesReducers.data;
      this.selfData = data.deviceSelfData.data;
      if (this.selfData.roles) this.filterResponseData();
    });
  }

  private filterResponseData() {
    this.availableResponseType = this.responseData.filter(item => {
      const hasRole = this.hasUserRole(item);
      const hasFeature = this.hasDeviceFeatureFlag(
        this.deviceData.deviceType.id,
        item.code
      );
      let isFeatureEnabled = true;
      let factoryResetFlag = false;
      const featureFlags = AuthService.getFeatureFlags();
      switch (item.code) {
        case 'TAMPER_CLEAR':
          isFeatureEnabled = featureFlags.some(
            flag => flag.key === 'tamperClear' && flag.active
          );
          break;
        case 'FACTORY_RESET': {
          factoryResetFlag = featureFlags.some(
            flag => flag.key === 'factoryReset' && flag.active
          );
          const coercedVersion = semver.coerce(
            this.deviceData.releaseVersion || ''
          );
          isFeatureEnabled =
            factoryResetFlag &&
            coercedVersion !== null &&
            semver.satisfies(coercedVersion, '>=3.2.5');
          break;
        }
        default:
          isFeatureEnabled = false;
          break;
      }
      return hasRole && hasFeature && isFeatureEnabled;
    });
    if (this.availableResponseType.length === 1) {
      this.selectedResponse = this.availableResponseType[0].code;
    }
  }

  hasUserRole(type: SelectResponseType): boolean {
    return this.selfData.roles.includes(type.code);
  }

  hasDeviceFeatureFlag(deviceTypeId: string, featureFlag: string): boolean {
    if (!this.deviceTypes$.getValue() || !this.deviceTypes$.getValue().length)
      return false;

    return !!this.deviceTypes$
      .getValue()
      .find(
        type =>
          type.id === deviceTypeId && type.featureflags.includes(featureFlag)
      );
  }

  onDeviceComponentChange(event: string) {
    if (event === 'UPC') {
      this.serialNumber = this.deviceData.serialNumber;
    } else {
      this.serialNumber = this.deviceData.config.sdcSerialNumber;
    }
  }

  validButton() {
    const pattern = /^[0-9]{8}$/;
    const isChallengeValid = pattern.test(this.challengeText);
    if (
      this.selectedResponse &&
      this.selectedDevice &&
      this.serialNumber &&
      this.requestByText &&
      isChallengeValid
    ) {
      return true;
    }
    return false;
  }

  onClickRequest() {
    const modalRef = this.modalService.open(
      ChallengeResponseVerifyMfaComponent,
      {
        centered: true,
        windowClass: ModalConstants.WINDOW_CLASS,
        container: ModalConstants.CONTAINER_SELECTOR,
        size: 'sm',
      }
    );
    modalRef.componentInstance.challenge = this.challengeText;
    modalRef.componentInstance.operation = this.selectedResponse;
    modalRef.componentInstance.requestedBy = this.requestByText;
    modalRef.componentInstance.serialNumber = this.serialNumber;
    modalRef.componentInstance.device_id = this.device_id;

    modalRef.result.then(result => {
      this.step = 'response';
      this.ChallengeResponseData = result;
    });
  }

  onClickBack() {
    this.selectedTab.emit('overview');
  }

  getCurrentTime() {
    const now = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: '2-digit',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Kolkata',
    };
    let formattedDate = new Intl.DateTimeFormat('en-US', options).format(now);
    formattedDate = formattedDate.replace(/(\d{2})/, '$1');
    formattedDate = formattedDate.replace('AM', 'am').replace('PM', 'pm');
    return `${formattedDate} (UTC+05:30)`;
  }
}
