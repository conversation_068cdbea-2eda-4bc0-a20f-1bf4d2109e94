<div class="challenge-response-container">
  <div class="main-container">
    <h3>Create a Challenge / Response Request</h3>
    <div class="border-bottom"></div>

    <div class="challenges-container">
      <div class="top-input-container">
        <div class="response challenges-response-ng-select">
          <span class="label">Response Type</span>
          <div class="input-ng-select" *ngIf="step === 'request'">
            <ng-select
              [items]="availableResponseType"
              bindLabel="text"
              bindValue="code"
              [(ngModel)]="selectedResponse"
              placeholder="Select a response type"
            >
              <ng-template ng-option-tmp let-item="item" let-index="index">
                <p class="item-name-text">{{ item.text }}</p>
              </ng-template>
            </ng-select>
          </div>
          <div class="input-ng-select" *ngIf="step === 'response'">
            <p class="non-input">{{ selectedResponse }}</p>
          </div>
        </div>

        <div class="response challenges-response-ng-select">
          <span class="label">Device Component</span>
          <div class="input-ng-select" *ngIf="step === 'request'">
            <ng-select
              [items]="deviceComponent"
              bindLabel="text"
              bindValue="code"
              [(ngModel)]="selectedDevice"
              (ngModelChange)="onDeviceComponentChange($event)"
              placeholder="Select a device component"
            >
              <ng-template ng-option-tmp let-item="item" let-index="index">
                <p class="item-name-text">{{ item.text }}</p>
              </ng-template>
            </ng-select>
          </div>
          <div class="input-ng-select" *ngIf="step === 'response'">
            <p class="non-input">{{ selectedDevice }}</p>
          </div>
        </div>

        <div class="response">
          <span class="label">Serial number</span>
          <div class="input-ng-select" *ngIf="step === 'request'">
            <input
              class="ics-input custom-input"
              type="text"
              [(ngModel)]="serialNumber"
              placeholder="Enter the serial number of the device"
            />
          </div>
          <div class="input-ng-select" *ngIf="step === 'response'">
            <p class="non-input">{{ serialNumber }}</p>
          </div>
        </div>

        <div class="response">
          <span class="label">Device name</span>
          <div class="input-ng-select">
            <p class="non-input">{{ deviceData.name }}</p>
          </div>
        </div>

        <div class="response">
          <span class="label">Site</span>
          <div class="input-ng-select">
            <p class="non-input">{{ deviceData.siteName }}</p>
          </div>
        </div>

        <div class="border-bottom"></div>

        <div class="response" *ngIf="step === 'request'">
          <label for="challenge" class="label text-align-middle"
            >Challenge</label
          >
          <div class="input-ng-select">
            <input
              [(ngModel)]="challengeText"
              maxlength="8"
              id="challenge"
              class="ics-input custom-input"
              type="text"
              placeholder="Enter the challenge"
            />
          </div>
        </div>

        <div class="response">
          <label for="requester" class="label text-align-middle"
            >Requested by</label
          >
          <div class="input-ng-select" *ngIf="step === 'request'">
            <input
              [(ngModel)]="requestByText"
              id="requester"
              class="ics-input custom-input"
              type="text"
              placeholder="Enter the name of the requester"
            />
          </div>
          <div class="input-ng-select" *ngIf="step === 'response'">
            <p class="non-input">{{ requestByText }}</p>
          </div>
        </div>

        <div class="border-bottom" *ngIf="step === 'request'"></div>

        <div class="response" *ngIf="step === 'response'">
          <label for="requester" class="label text-align-middle"
            >Created by</label
          >
          <div class="input-ng-select">
            <p class="non-input">
              {{ ChallengeResponseData.createdBy.fullName }}
            </p>
          </div>
        </div>

        <div class="response" *ngIf="step === 'response'">
          <label for="requester" class="label text-align-middle"
            >Created on</label
          >
          <div class="input-ng-select">
            <p class="non-input">{{ getCurrentTime() }}</p>
          </div>
        </div>

        <div class="response" *ngIf="step === 'response'">
          <label for="requester" class="label text-align-middle"
            >Created on</label
          >
          <div class="input-ng-select">
            <p class="non-input challenge-response-code">
              {{ ChallengeResponseData.response }}
            </p>
          </div>
        </div>

        <div class="border-bottom" *ngIf="step === 'response'"></div>
      </div>

      <div class="challenges-button-container">
        <button
          *ngIf="step === 'request'"
          [disabled]="!validButton()"
          (click)="onClickRequest()"
          class="btn-primary challenge-request-btn"
        >
          Request
        </button>
        <button
          *ngIf="step === 'request'"
          (click)="onClickBack()"
          class="btn challenge-cancel-btn"
        >
          Cancel
        </button>
        <button
          *ngIf="step === 'response'"
          (click)="onClickBack()"
          class="btn-primary challenge-request-btn"
        >
          Back
        </button>
      </div>
    </div>
  </div>
</div>
