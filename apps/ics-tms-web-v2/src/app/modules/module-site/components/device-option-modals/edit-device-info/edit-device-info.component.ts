import { Component, inject, Input, OnInit, OnDestroy } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { getBaseUrl } from '../../../../../constants/api';
import * as devicesActions from '../../../store/actions/devices.actions';
import { ToastService } from '../../../../../services/toast.service';

@Component({
  selector: 'app-edit-device-info',
  templateUrl: './edit-device-info.component.html',
  styleUrls: ['./edit-device-info.component.scss'],
})
export class EditDeviceInfoComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  activeModal = inject(NgbActiveModal);

  toastService = inject(ToastService);

  @Input() name!: string;

  @Input() description!: string;

  @Input() deviceType!: string;

  @Input() deviceId!: string;

  @Input() siteId!: string;

  @Input() serialNumber!: string;

  deviceName = '';

  deviceDescription = '';

  http = inject(HttpClient);

  store = inject(Store);

  ngOnInit(): void {
    this.deviceName = this.name;
    this.deviceDescription = this.description;
  }

  updateEditDevice() {
    this.http
      .put(`${getBaseUrl()}/devices/${this.deviceId}`, {
        deploymentType: 'maintenance-window',
        description: this.description,
        type: this.deviceType,
        name: this.name,
        siteId: this.siteId,
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.activeModal.close();
          this.store.dispatch(
            devicesActions.getData({
              siteId: this.siteId,
              deviceId: this.deviceId,
            })
          );
          this.toastService.show({ message: 'Device updated' });
        },
        error: err => {
          this.toastService.show({ message: err });
        },
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
