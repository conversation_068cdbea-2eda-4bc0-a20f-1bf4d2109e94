import { Component, inject, Input, OnDestroy } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { getApiConstants } from '../../../constants/api';
import { ID } from '../../../constants/appConstants';
import { ToastService } from '../../../../../services/toast.service';

@Component({
  selector: 'app-recommission-device',
  templateUrl: './recommission-device.component.html',
  styleUrls: ['./recommission-device.component.scss'],
})
export class RecommissionDeviceComponent implements OnDestroy {
  private destroy$ = new Subject<void>();

  activeModal = inject(NgbActiveModal);

  toastService = inject(ToastService);

  @Input() deviceId!: string;

  http = inject(HttpClient);

  clickRecommission() {
    this.http
      .put(
        getApiConstants().device.recommission.putRecommission.replace(
          ID,
          this.deviceId
        ),
        {}
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.activeModal.close();
          this.toastService.show({ message: 'Recommissioned Device' });
        },
        error: err => {
          this.toastService.show({ message: err });
        },
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
