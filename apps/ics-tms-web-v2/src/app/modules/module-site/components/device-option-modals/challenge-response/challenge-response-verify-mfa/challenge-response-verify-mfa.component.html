<div class="rki-verification-container">
  <h4 id="rki-verification-title">Verification Required</h4>

  <p id="rki-verification-desc">
    Re-enter your MFA code to create a new request for a key loading session
  </p>

  <input
    [ngClass]="{ 'border-red': isMfaError }"
    [(ngModel)]="mfaCode"
    type="text"
    class="ics-input"
    placeholder="MFA Code"
  />
  <p *ngIf="isMfaError" class="mfa-invalid">Incorrect MFA Code</p>

  <div class="rki-verification-btn-container">
    <button class="btn-mfa-cancel" (click)="closeModal()">CANCEL</button>
    <button class="btn btn-primary btn-mfa-confirm" (click)="confirmMFA()">
      CONFIRM
    </button>
  </div>
</div>
