.swap-device-modal-container {
  width: 60rem;
  height: fit-content;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .swap-device-title {
    padding: 1rem 1.5rem;

    #swap-modal-title {
      margin: 0;
      font-size: 1.8rem;
      line-height: 1.42;
      font-weight: 500;
    }

    border-bottom: 0.1rem solid var(--color-border);
  }

  .form-container-swap-device {
    padding-top: 1.6rem;

    .form-container {
      display: flex;
      flex-direction: row;
      border-bottom: 0.1rem solid var(--color-border);

      .old-device,
      .new-device {
        flex: 1;
        padding: 0 1.5rem 0.8rem 1.5rem;

        .device-title {
          font-size: 1.4rem;
          margin-top: 1.6rem;
          margin-bottom: 1.6rem;
          font-family: inherit;
          font-weight: 500;
          line-height: 1.1;
        }

        .serial {
          display: flex;
          flex-direction: column;
          margin-bottom: 1.5rem;

          .serial-label {
            font-weight: 700;
            touch-action: manipulation;
            font-size: 1.4rem;
            margin-bottom: 0.8rem;
          }

          .disabled-swap-input {
            cursor: not-allowed;
            background-color: var(--color-bg-fa);
            border-radius: 0.3rem;
            border: 0.1rem solid var(--color-border);
            padding: 0.6rem 1.2rem;
            font-size: 1.4rem;
          }
        }

        .new-device-container {
          .dropdown-item-name {
            font-size: 1.4rem;
          }

          .dropdown-item-siteName {
            font-size: 1.2rem;
          }

          .ng-select-container {
            cursor: text;

            input {
              cursor: text !important;
              font-size: 1.4rem;
            }
          }

          .dropdown-display-none {
            .ng-dropdown-panel.ng-select-bottom {
              display: none !important;
              opacity: 0 !important;
            }
          }

          .ng-option {
            padding: 0.4rem 1.5rem;
            border-bottom: 0.1rem solid var(--color-border);
          }

          .ng-option-marked {
            background-color: var(--color-primary);
            color: var(--color-white);
          }

          .ng-dropdown-panel.ng-select-bottom {
            margin-top: 0.2rem;
            max-height: 25rem;
            overflow-y: scroll;
          }

          .ng-select.ng-select-opened > .ng-select-container {
            box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
            border-color: rgba(68, 138, 255, 0.75) !important;
          }

          .ng-select.ng-select-opened.ng-select-bottom > .ng-select-container {
            border-bottom-right-radius: 0.3rem;
            border-bottom-left-radius: 0.3rem;
          }

          .ng-select .ng-arrow-wrapper {
            display: none;
          }

          .ng-option {
            p {
              margin: 0;
            }
          }

          .ng-select .ng-clear-wrapper {
            display: none;
          }

          .ng-select .ng-select-container .ng-value-container .ng-placeholder {
            font-size: 1.4rem;
          }

          .ng-select-focused {
            box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75) !important;
            border-color: rgba(68, 138, 255, 0.75) !important;
            border-radius: 0.3rem;
          }

          input,
          select {
            height: 1.8rem;
          }
        }

        .name {
          display: flex;
          flex-direction: column;
          padding-bottom: 2rem;
          border-bottom: 0.1rem solid var(--color-border);

          .name-label {
            font-weight: 700;
            touch-action: manipulation;
            font-size: 1.4rem;
            margin-bottom: 0.8rem;

            .char-count {
              font-size: 85%;
              color: var(--color-black-shade-two);
              font-weight: 700;
              margin-bottom: 0.8rem;
            }
          }

          .swap-device-name-input {
            font-size: 1.4rem;
            margin: 0;
          }
        }

        .device-details {
          margin-top: 2rem;
          display: flex;
          flex-direction: column;
          gap: 1.6rem;

          .swap-detail-label {
            font-weight: 700;
            font-size: 1.4rem;

            .swap-detail-data {
              font-size: 1.4rem;
              font-weight: 400;
            }

            .site:hover {
              text-decoration: underline;
              cursor: pointer;
            }
          }
        }
      }

      .old-device {
        border-right: 0.1rem solid var(--color-border);
      }
    }

    .circle-arrow-right {
      width: 100%;
      text-align: center;
      position: absolute;
      margin-top: -15.6rem;

      .fa-chevron-circle-right {
        font-size: 1em;
      }

      .fa-chevron-circle-right:before {
        background: var(--color-white);
      }
    }
  }

  .swap-device-button-container {
    padding: 1.6rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    border-top: 1rem solid var(--color-border);

    button {
      margin-left: 1rem;
      font-size: 1.4rem;
    }

    .swap-button {
      padding: 0 1.2rem;
      border: none;
      font-weight: 500;
      border-radius: 0.3rem;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
      transition: all ease-in 0.1s;
      color: var(--color-white);
      &:active {
        background-color: var(--color-blue);
      }

      &:disabled {
        opacity: 0.65 !important;
        cursor: not-allowed !important;
        box-shadow: none;

        &:hover {
          background-color: var(--color-primary);
        }
      }
    }

    .swap-cancel-button {
      padding: 0.6rem 1.2rem;
      color: var(--color-primary);
      font-weight: 500;
      border-radius: 0.3rem;

      &:hover {
        color: var(--color-blue);
      }

      &:active {
        border: none;
      }
    }
  }
}
