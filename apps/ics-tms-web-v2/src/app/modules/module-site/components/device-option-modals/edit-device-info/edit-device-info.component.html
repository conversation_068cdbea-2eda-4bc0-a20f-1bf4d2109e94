<div class="edit-device-info-container">
  <div class="top-heading-edit-device">
    <div class="heading">
      <h4>Edit Device Info</h4>
      <strong (click)="activeModal.dismiss('Cross click')" aria-hidden="true"
        >×</strong
      >
    </div>
    <div class="small-text">
      <span>Serial:</span>&nbsp;<span>{{ serialNumber }}</span>
    </div>
  </div>

  <div class="input-area-edit-device">
    <div class="device-name" style="display: flex; flex-direction: column">
      <label
        for="device-name"
        style="
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 700;
        "
        >Name</label
      >
      <input
        [(ngModel)]="name"
        id="device-name"
        class="ics-input"
        type="text"
        placeholder="Enter a name for the device"
        style="width: 100%"
      />
    </div>

    <div class="device-note">
      <div class="textarea-label-device-edit">
        <label for="device-notes">Notes</label>
        <span>Optional</span>
      </div>
      <textarea
        [(ngModel)]="description"
        id="device-notes"
        class="ics-input device-edit-textarea"
      ></textarea>
    </div>
  </div>

  <div class="button-area-device-edit">
    <button
      (click)="activeModal.close('Close click')"
      class="btn cancel-button"
    >
      Cancel
    </button>
    <button
      (click)="updateEditDevice()"
      [disabled]="
        deviceName.toString() === name.trim().toString() &&
        deviceDescription === description
      "
      class="btn-primary update-button"
    >
      Update
    </button>
  </div>
</div>
