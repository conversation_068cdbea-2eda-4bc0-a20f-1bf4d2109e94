<div class="swap-device-modal-container">
  <div class="swap-device-title">
    <h4 id="swap-modal-title">Swap Device</h4>
  </div>
  <div class="form-container-swap-device">
    <div class="form-container">
      <div class="old-device">
        <h5 class="device-title">Old Device</h5>
        <div class="serial">
          <label class="serial-label">Serial</label>
          <strong class="disabled-swap-input">{{ serialNumber }}</strong>
        </div>
        <div class="name">
          <span class="name-label"
            >Name
            <span class="char-count"
              >{{ oldNameTotalChar }} chars left</span
            ></span
          >
          <input
            type="text"
            [(ngModel)]="oldDeviceName"
            (ngModelChange)="onOldDeviceNameChange($event)"
            class="ics-input swap-device-name-input"
            placeholder="Enter old device name"
          />
        </div>
        <div class="device-details">
          <span class="swap-detail-label"
            >Site:
            <span class="site swap-detail-data" (click)="navigateToId()">{{
              siteName
            }}</span>
          </span>
          <span class="swap-detail-label"
            >Device: <span class="site swap-detail-data">{{ deviceType }}</span>
          </span>
        </div>
      </div>
      <div class="new-device">
        <h5 class="device-title">New Device</h5>
        <div class="serial new-device-container">
          <label class="serial-label">Serial</label>
          <ng-select
            #devices
            [items]="devicesList.results"
            [multiple]="false"
            [ngClass]="{
              'dropdown-display-none': devicesList.results.length <= 0,
            }"
            bindLabel="label"
            placeholder="Search by serial number"
            [(ngModel)]="selectedOption"
            (change)="onOptionChange($event)"
            (search)="onInputChange($event)"
            (keydown.Enter)="onEnter()"
            [isOpen]="isDropdownOpen"
            (blur)="devices.searchTerm = ''; isDropdownOpen = false"
          >
            <ng-template ng-label-tmp let-item="item">
              {{ item.serialNumber }}
            </ng-template>
            <ng-template
              ng-option-tmp
              let-item="item"
              let-index="index"
              let-search="searchTerm"
            >
              <span
                class="dropdown-item-name"
                [innerHTML]="highlightSearchText(item.name, search)"
              ></span>
              <p class="m-0 dropdown-item-siteName">{{ item.siteName }}</p>
            </ng-template>
          </ng-select>
        </div>
        <div class="name">
          <span class="name-label"
            >Name
            <span class="char-count"
              >{{ newNameTotalChar }} chars left</span
            ></span
          >
          <input
            type="text"
            [(ngModel)]="newDeviceName"
            (ngModelChange)="onNewDeviceNameChange($event)"
            class="ics-input swap-device-name-input"
            placeholder="Enter new device name"
          />
        </div>
        <div class="device-details">
          <span class="swap-detail-label"
            >Site:
            <span class="site swap-detail-data" *ngIf="selectedOption">
              {{ selectedOption.siteName || '...' }}
            </span>
          </span>
          <span class="swap-detail-label"
            >Device:
            <span
              class="site swap-detail-data"
              *ngIf="selectedOption.deviceType"
            >
              {{ selectedOption.deviceType.id || '...' }}
            </span>
          </span>
        </div>
      </div>
    </div>
    <div class="circle-arrow-right">
      <i class="fa fa-lg fa-chevron-circle-right"></i>
    </div>
  </div>
  <div class="swap-device-button-container">
    <button
      (click)="activeModal.close('Close click')"
      class="btn swap-cancel-button"
    >
      Cancel
    </button>
    <button
      (click)="onClickSwap()"
      [disabled]="!isSwapValid()"
      class="btn-primary swap-button"
    >
      Swap
    </button>
  </div>
</div>
