.delete-device-container {
  width: 40rem;
  height: fit-content;
  padding-top: 0.8rem;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .heading-delete-device {
    padding: 1rem 1.5rem;

    #delete-title {
      font-size: 1.8rem;
      font-weight: 500;
      line-height: 1.43;
      margin-bottom: 1.6rem;
    }

    #delete-description {
      font-size: 1.4rem;
      line-height: 1.43;

      strong {
        font-weight: 500;
      }
    }
  }

  .button-delete-device {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    .no-btn {
      font-size: 1.4rem;
      margin-right: 0.438rem;
      border-color: var(--dropdown-border-hover);
      background-color: var(--dropdown-by-default);
      font-weight: 500;
      border-radius: 0.3rem;
      padding-right: 1.85em;
      padding-left: 1.85em;

      &:hover {
        border-color: var(--dropdown-border-hover);
        background-color: var(--dropdown-hover);
      }

      &:active {
        background-color: var(--btn-bg-hover);
        border-color: var(--placeholder-text-color);
      }
    }

    .yes-btn {
      font-size: 1.4rem;
      font-weight: 500;
      padding-right: 1.85em;
      padding-left: 1.85em;
      border-radius: 0.3rem;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
      transition: all ease-in 0.1s;

      &:active {
        background-color: var(--color-blue);
        border-color: var(--color-blue);
      }
    }
  }
}
