import { Component, inject, Input, OnDestroy } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { getApiConstants } from '../../../constants/api';
import { ID } from '../../../constants/appConstants';
import { ToastService } from '../../../../../services/toast.service';

@Component({
  selector: 'app-delete-device',
  templateUrl: './delete-device.component.html',
  styleUrls: ['./delete-device.component.scss'],
})
export class DeleteDeviceComponent implements OnDestroy {
  private destroy$ = new Subject<void>();

  activeModal = inject(NgbActiveModal);

  toastService = inject(ToastService);

  @Input() name!: string;

  @Input() deviceId!: string;

  http = inject(HttpClient);

  router = inject(Router);

  clickDelete() {
    this.http
      .delete(
        getApiConstants().device.deleteDevice.deleteDevice.replace(
          ID,
          this.deviceId
        )
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toastService.show({ message: 'Device Deleted' });
          this.activeModal.close();
          this.router.navigateByUrl('/sites');
        },
        error: err => {
          this.toastService.show({ message: err });
        },
      });
    this.activeModal.close();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
