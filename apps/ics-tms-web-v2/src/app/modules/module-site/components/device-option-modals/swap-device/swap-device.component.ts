import {
  Component,
  inject,
  Input,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { NgSelectComponent } from '@ng-select/ng-select';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  DevicesList,
  devicesListData,
} from '../../../models/devices-list.model';
import { DevicesService } from '../../../services/devices.service';
import { getApiConstants } from '../../../constants/api';
import { ID } from '../../../constants/appConstants';
import { ToastService } from '../../../../../services/toast.service';

@Component({
  selector: 'app-swap-device',
  templateUrl: './swap-device.component.html',
  styleUrls: ['./swap-device.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SwapDeviceComponent implements OnInit {
  @ViewChild('devices') devices!: NgSelectComponent;

  @Input() siteId!: string;

  @Input() serial!: string;

  @Input() deviceId!: string;

  @Input() siteName!: string;

  @Input() deviceType!: string;

  @Input() serialNumber!: string;

  activeModal = inject(NgbActiveModal);

  toastService = inject(ToastService);

  oldNameTotalChar!: number;

  newNameTotalChar!: number;

  oldDeviceName!: string;

  newDeviceName!: string;

  router = inject(Router);

  http = inject(HttpClient);

  devicesService = inject(DevicesService);

  ngOnInit(): void {
    this.oldDeviceName = `Ex - ${this.siteName} ${this.serial}`;
    this.newDeviceName = this.serial;
    this.oldNameTotalChar = 100 - this.oldDeviceName.trim().length;
    this.newNameTotalChar = 100 - this.newDeviceName.trim().length;
  }

  devicesList: DevicesList = {
    results: [],
    resultsMetadata: {
      pageIndex: 0,
      pageSize: 0,
      totalResults: 0,
    },
  };

  selectedOption!: devicesListData;

  isDropdownOpen = false;

  onOptionChange(selectedValue: devicesListData): void {
    if (selectedValue) {
      this.isDropdownOpen = false;
      this.devices.blur();
    }
  }

  onInputChange(event: { term: string; items: devicesListData[] }): void {
    const inputValue = event.term.trim().toLocaleLowerCase();
    this.selectedOption = {} as devicesListData;

    if (inputValue.length > 0) {
      this.isDropdownOpen = true;
      this.devicesService
        .getDevicesList(this.deviceType, event.term.trim())
        .subscribe(data => {
          this.devicesList = data;
          if (this.devicesList.results.length !== 0) {
            this.devicesList.results.forEach(item => {
              if (item.serialNumber !== this.serialNumber) {
                const newItem = {
                  ...item,
                  label: `${item.name} - ${item.serialNumber}`,
                };
                Object.assign(item, newItem);
              }
            });
          }
        });
    } else this.isDropdownOpen = false;
  }

  onEnter(): void {
    this.selectedOption = {} as devicesListData;
    this.isDropdownOpen = false;
  }

  navigateToId() {
    this.router.navigate(['/sites', this.siteId]);
  }

  onOldDeviceNameChange(event: string) {
    this.oldNameTotalChar = 100 - event.trim().length;
  }

  onNewDeviceNameChange(event: string) {
    this.newNameTotalChar = 100 - event.trim().length;
  }

  highlightSearchText(text: string, searchText: string): string {
    const regex = new RegExp(searchText, 'gi');
    return text.replace(regex, match => `<strong>${match}</strong>`);
  }

  onClickSwap() {
    this.selectedOption.name = this.newDeviceName;
    delete this.selectedOption.label;

    const params = {
      newDevice: this.selectedOption,
      oldDeviceName: this.oldDeviceName,
    };
    this.http
      .post(
        getApiConstants().device.swap.postSwapDevices.replace(
          ID,
          this.deviceId
        ),
        params
      )
      .subscribe({
        next: () => {
          this.router.navigateByUrl(
            `/sites/${this.siteId}/${this.selectedOption.id}/overview`
          );
          this.activeModal.close('Close click');
          this.toastService.show({ message: 'Device swap success!' });
        },
        error: error => {
          this.toastService.show({ message: error });
        },
      });
  }

  isSwapValid() {
    return this.selectedOption && this.oldDeviceName && this.newDeviceName;
  }
}
