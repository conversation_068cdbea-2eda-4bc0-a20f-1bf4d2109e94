// Host element styling
:host {
  display: block;
  width: 59.4rem;
}

// Modal styling based on the original AngularJS implementation
.modal-header {
  border-bottom: 0.1rem solid var(--color-border);
  padding: 1.5rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;

  // Ensure proper alignment for all content
  .modal-title,
  .small.text-muted {
    text-align: left;
  }

  // Remove padding for schedule modal subtitle
  .small.text-muted {
    padding-left: 0;
  }

  .close {
    margin-top: -0.2rem;
    margin-left: auto; // Push to the right
    font-size: 2.1rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 0.1rem 0 #fff;
    opacity: 0.2;
    background: transparent;
    border: 0;
    padding: 0;

    &:hover {
      opacity: 0.5;
      cursor: pointer;
    }
  }

  .modal-title {
    margin: 0;
    line-height: 1.42857143;
    font-size: 1.8rem;
    font-weight: 500;

    &.schedule-modal-title {
      color: #f39c12; // Orange color for schedule warning
    }
  }

  .fa-exclamation-triangle {
    color: #f39c12;
    margin-right: 0.8rem;
  }

  .small.text-muted {
    margin-top: 0.5rem;
    margin-bottom: 0;
  }
}

.modal-body {
  position: relative;
  padding: 1.5rem;

  p {
    margin-bottom: 1.5rem;
    line-height: 1.5;
    font-size: 1.4rem;
  }

  .schedule-modal-description {
    color: #333;
  }
}

// Schedule modal specific styles
.schedule-modal-body {
  padding: 0 !important;
}

.schedule-modal-header {
  justify-content: flex-start !important;
  padding-left: 1.5rem;

  .modal-title,
  .small.text-muted {
    padding-left: 0;
    margin-left: 0;
  }
}

.modal-footer {
  padding: 1.5rem;
  border-top: 0.1rem solid var(--color-border);
  display: flex;
  align-items: center;

  .btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 0.1rem solid transparent;
    padding: 0.6rem 1.2rem;
    font-size: 1.4rem;
    line-height: 1.42857143;
    border-radius: 0.4rem;
    margin-left: 0.5rem;
  }

  .btn-link {
    font-weight: 400;
    color: var(--color-primary);
    border-radius: 0;
    background-color: transparent;
    border: 0;

    &:hover {
      color: var(--color-blue);
      text-decoration: underline;
    }

    &.schedule-modal-cancel {
      color: #777;
    }
  }

  .btn-primary {
    color: #fff;
    background-color: var(--color-primary);
    border-color: var(--color-primary-dark);

    &.btn-box-shadow {
      box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.16);
    }

    &.btn-wide {
      min-width: 12rem;
      height: 3.6rem; // Fixed height to prevent size changes
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &:hover {
      background-color: var(--color-blue);
    }

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
      box-shadow: none;
    }

    // Styles for the small-loader class
    .small-loader {
      display: inline-block;
      vertical-align: middle;
      margin-left: 1rem; // Increased spacing between text and loader
      transform: scale(0.4); // Make the loader smaller
      line-height: 1; // Prevent line-height issues
    }

    // Ensure consistent button content height
    span {
      line-height: 1;
      display: inline-flex;
      align-items: center;
    }

    // Make sure the loader is properly aligned with text
    ::ng-deep .small-loader .ics-loader-container {
      display: inline-block;
      vertical-align: middle;
    }

    // Ensure the loader has the same color as the button text
    ::ng-deep .small-loader .ics-loader {
      border-color: #fff;
      border-right-color: transparent;
    }
  }
}

// Modal with tree select styling
.modal-has-tree-select {
  min-height: 30rem;
  max-height: 40rem;
  overflow-y: auto;
}

// Hide content
.hidden {
  display: none;
}

// Schedule date/time picker styles
.schedule-datetime-container {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.schedule-label {
  margin-bottom: 1.5rem;

  label {
    font-weight: 500;
    display: block;
    font-size: 1.6rem;
    color: #333;
  }
}

.schedule-inputs-container {
  position: relative;
}

.date-input-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.date-display {
  padding: 0.8rem 1.2rem;
  border: 0.1rem solid #d6d6d6;
  border-radius: 0.3rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 20rem;
  background-color: white;
  font-size: 1.4rem;
  color: #333;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--color-primary);
    box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.15);
  }

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  .dropdown-icon {
    font-size: 1rem;
    margin-left: 1rem;
    color: #6c757d;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .date-input-container {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
  }

  .time-inputs-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .date-display {
    padding: 0.8rem 1.2rem;
    border: 0.1rem solid #d6d6d6;
    border-radius: 0.3rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 20rem;
    background-color: white;
    font-size: 1.4rem;
    color: #333;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary);
      box-shadow: 0 0.1rem 0.3rem rgba(0, 0, 0, 0.15);
      border-radius: 0.3rem;
      font-size: 1.4rem;
      color: #333;
      transition: all 0.2s ease;
      margin: 0;

      &:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
      }
    }

    // Time button styles
    .time-btn {
      position: absolute;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.4rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6c757d;
      transition: all 0.2s ease;
      width: 100%;
      height: 2rem;

      &:hover {
        color: var(--color-primary);
        background-color: rgba(0, 123, 255, 0.1);
        border-radius: 50%;
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .time-btn-up {
      top: -2rem;
    }

    .time-btn-down {
      bottom: -2rem;
    }
  }

  .time-separator {
    margin: 0 0.5rem;
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
  }
}

.floating-datepicker {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: block;
  float: none;
  min-width: 16rem;
  margin: 0.2rem 0 0;
  padding: 0;
  list-style: none;
  text-align: left;
  border: 0.1rem solid #ccc;
  border-radius: 0;
  background-color: #fff;
  background-clip: padding-box;
  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.15);

  // Customize the datepicker to match your app's style
  ::ng-deep {
    // Override Bootstrap's bg-primary for selected days
    .bg-primary {
      --bs-bg-opacity: 1;
      background-color: #3f51b5 !important;
    }

    .ngb-dp-header {
      background-color: white;
      border-bottom: none;
      padding-bottom: 1rem;

      // Remove dropdown styling for month/year navigation
      .ngb-dp-navigation-select {
        display: none; // Hide the dropdown selectors
      }

      // Improve arrow navigation buttons
      .ngb-dp-arrow {
        .btn-link {
          color: #495057;
          border: none;
          background: none;
          font-size: 1.5rem;
          padding: 0.2rem 0.5rem;
          margin: 0;

          &:hover {
            color: var(--color-primary);
            text-decoration: none;
          }
        }
      }

      // Center the month/year display
      .ngb-dp-navigation-chevron {
        border-width: 0.2rem 0.2rem 0 0;
        height: 0.8rem;
        width: 0.8rem;
      }

      // Style the month-year text
      .ngb-dp-month-name {
        font-size: 1.2rem;
        height: 2rem;
        line-height: 2rem;
        text-align: center;
        background-color: transparent;
        color: #000;
      }
    }

    // Properly align the weekday headers with date columns
    .ngb-dp-weekdays {
      border-bottom: 0.1rem solid var(--bs-border-color, #e9e9e9);
      border-radius: 0;
      background-color: transparent;
      padding-bottom: 0.5rem;

      .ngb-dp-weekday {
        color: #333;
        font-style: normal;
        font-weight: 400;
        text-align: center;
        width: 2.5rem;
        font-size: 1rem;
        line-height: 2rem;
      }
    }

    // Improve date cell styling
    .ngb-dp-day {
      width: 2.5rem;
      height: 2.5rem;
      line-height: 2.5rem;
      text-align: center;
      padding: 0;

      .btn-light {
        width: 100%;
        height: 100%;
        border-radius: 0;
        border: none;
        background: none;
        padding: 0;
        line-height: inherit;
        color: #333;

        &:hover {
          background-color: #f5f5f5;
        }
      }

      &.disabled .btn-light {
        opacity: 0.3;
      }

      &.selected .btn-light {
        background-color: var(--color-primary) !important;
        color: white !important;
        font-weight: normal;
      }

      &.today .btn-light {
        background-color: var(--color-primary) !important;
        color: white !important;
      }
    }

    .ngb-dp-arrow-btn {
      color: var(--color-primary);
    }

    .ngb-dp-month {
      font-size: 1.4rem;
    }
  }

  &[hidden] {
    display: none;
  }

  &[hidden] {
    display: none;
  }
}

.schedule-form {
  padding: 0;

  .schedule-content {
    padding: 0;
  }

  .schedule-label {
    padding: 1.5rem 0;
    margin-bottom: 0;

    label {
      font-weight: 500;
      font-size: 1.4rem;
      color: #333;
      margin-bottom: 0;
      padding-left: 1.5rem;
    }
  }

  .schedule-datetime-container {
    margin: 0;
    border-bottom: 0.1rem solid #e4e4e4;

    .schedule-inputs-container {
      display: flex;
      flex-direction: column;
      padding: 0 0 0 1.6rem;
    }

    // Date and time in a single row
    .datetime-row {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 2rem;
      margin-bottom: 1.5rem;

      .date-picker-container {
        position: relative;

        .date-display {
          width: 20rem;
          padding: 0.6rem 1.2rem;
          border: 0.1rem solid #ccc;
          border-radius: 0.4rem;
          background-color: white;
          text-align: center;
          cursor: pointer;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          .dropdown-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1rem;
            color: #777;
          }
        }

        .floating-datepicker {
          position: absolute;
          top: 100%;
          left: 0;
          z-index: 1000;
          margin-top: 0.2rem;
          background-color: white;
          border: 0.1rem solid #ccc;
          border-radius: 0.4rem;
          box-shadow: 0 0.2rem 1rem rgba(0, 0, 0, 0.1);
        }
      }

      .time-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .time-control-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        width: 4rem;
      }

      .time-btn {
        background: none;
        border: none;
        width: 100%;
        height: 2.4rem;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #555;
        cursor: pointer;

        &:hover {
          color: #333;
        }

        i {
          font-size: 1.2rem;
        }
      }

      .time-input {
        width: 100%;
        text-align: center;
        padding: 0.6rem 0;
        border: 0.1rem solid #ccc;
        border-radius: 0.4rem;
        font-size: 1.4rem;
      }

      .time-separator {
        font-size: 1.8rem;
        font-weight: bold;
        color: #333;
        margin: 0 0.5rem;
        padding-top: 0.5rem;
      }
    }
  }
}

// Button styles for schedule
.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  padding: 0.8rem 1.6rem;
  font-weight: 500;
  transition: all 0.2s ease;

  &.btn-box-shadow {
    box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.16);
  }

  &:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-0.1rem);
    box-shadow: 0 0.4rem 0.8rem rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.1);
  }
}
