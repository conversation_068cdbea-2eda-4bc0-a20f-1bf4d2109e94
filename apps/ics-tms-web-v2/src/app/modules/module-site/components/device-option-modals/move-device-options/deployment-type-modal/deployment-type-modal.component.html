<div
  class="modal-header"
  [ngClass]="{ 'schedule-modal-header': currentStep === 'schedule' }"
>
  <div style="display: flex; flex-direction: column; align-items: flex-start">
    <div style="display: flex; align-items: center">
      <i
        class="fa fa-exclamation-triangle"
        style="color: orange; margin-right: 0.8rem"
        *ngIf="currentStep !== 'form' && currentStep !== 'schedule'"
      ></i>
      <h4
        class="modal-title"
        [ngClass]="{ 'schedule-modal-title': currentStep === 'scheduleWindow' }"
      >
        <ng-container *ngIf="currentStep === 'form'">Move device</ng-container>
        <ng-container *ngIf="currentStep === 'selectDeploymentType'"
          >These changes may affect configurations</ng-container
        >
        <ng-container
          *ngIf="
            currentStep === 'immediateConfirmation' ||
            currentStep === 'maintenanceConfirmation'
          "
          >Are you sure you want to deploy?</ng-container
        >
        <ng-container *ngIf="currentStep === 'scheduleWindow'"
          >There is already a scheduled deployment in the upcoming maintenance
          window</ng-container
        >
        <ng-container *ngIf="currentStep === 'schedule'"
          >Confirm update</ng-container
        >
      </h4>
    </div>
    <p class="small text-muted" *ngIf="currentStep === 'form'">
      Select a different site to move the device to.
    </p>
    <p class="small text-muted" *ngIf="currentStep === 'schedule'">
      This may trigger deployment of multiple configuration instances
    </p>
  </div>
  <button
    type="button"
    class="close"
    (click)="activeModal.dismiss('Close click')"
    aria-label="Close"
    *ngIf="currentStep !== 'schedule'"
  >
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div
  [ngClass]="{
    'modal-body': currentStep !== 'form',
    'schedule-modal-body': currentStep === 'schedule',
  }"
>
  <ng-container *ngIf="currentStep === 'selectDeploymentType'">
    <p>
      Any affected configurations for these sites will automatically deploy
      during the maintenance window for each site (daily)
    </p>
  </ng-container>

  <ng-container
    *ngIf="
      currentStep === 'immediateConfirmation' ||
      currentStep === 'maintenanceConfirmation'
    "
  >
    <p>
      Deploying configuration will reboot any affected device(s), which may
      cause temporary disruption to service at site.
    </p>
  </ng-container>

  <ng-container *ngIf="currentStep === 'scheduleWindow'">
    <p
      [innerHTML]="scheduleWindowDescription"
      class="schedule-modal-description"
    ></p>
  </ng-container>

  <ng-container *ngIf="currentStep === 'schedule'">
    <form #scheduleForm="ngForm" autocomplete="off" class="schedule-form">
      <div class="schedule-content">
        <div class="schedule-label">
          <label>Schedule deployments for later</label>
        </div>
        <!-- Date time picker to schedule the date -->
        <div id="schedule" class="schedule-datetime-container">
          <div class="schedule-inputs-container">
            <!-- Date and time controls in a single row -->
            <div class="datetime-row">
              <!-- Date picker dropdown -->
              <div class="date-picker-container">
                <div
                  class="date-display btn btn-default"
                  (click)="toggleDatepicker()"
                >
                  {{ formatSelectedDate() }}
                  <span class="dropdown-icon">▼</span>
                </div>
                <ngb-datepicker
                  #datepicker
                  [(ngModel)]="selectedDate"
                  [minDate]="minDate"
                  [maxDate]="maxDate"
                  [hidden]="!isDatePickerOpen"
                  (dateSelect)="onDateSelect()"
                  name="datepicker"
                  class="floating-datepicker"
                  [displayMonths]="1"
                  [navigation]="'arrows'"
                  [showWeekNumbers]="false"
                  outsideDays="hidden"
                >
                </ngb-datepicker>
              </div>

              <!-- Time picker with up/down controls -->
              <div class="time-controls">
                <!-- Hours -->
                <div class="time-control-container">
                  <button
                    type="button"
                    class="time-btn time-btn-up"
                    (click)="incrementHour()"
                  >
                    <i class="fa fa-chevron-up"></i>
                  </button>
                  <input
                    type="text"
                    class="time-input"
                    [(ngModel)]="selectedTime.hour"
                    name="hour"
                    placeholder="15"
                    (blur)="validateTime()"
                    maxlength="2"
                  />
                  <button
                    type="button"
                    class="time-btn time-btn-down"
                    (click)="decrementHour()"
                  >
                    <i class="fa fa-chevron-down"></i>
                  </button>
                </div>

                <!-- Separator -->
                <div class="time-separator">:</div>

                <!-- Minutes -->
                <div class="time-control-container">
                  <button
                    type="button"
                    class="time-btn time-btn-up"
                    (click)="incrementMinute()"
                  >
                    <i class="fa fa-chevron-up"></i>
                  </button>
                  <input
                    type="text"
                    class="time-input"
                    [(ngModel)]="selectedTime.minute"
                    name="minute"
                    placeholder="31"
                    (blur)="validateTime()"
                    maxlength="2"
                  />
                  <button
                    type="button"
                    class="time-btn time-btn-down"
                    (click)="decrementMinute()"
                  >
                    <i class="fa fa-chevron-down"></i>
                  </button>
                </div>
              </div>
            </div>
            <!-- Datepicker moved inside date-picker-container -->
          </div>
        </div>
      </div>
    </form>
  </ng-container>

  <div class="modal-footer">
    <div style="display: flex; width: 100%; justify-content: space-between">
      <div>
        <button
          type="button"
          class="btn btn-link"
          id="btnDeviceMoveCancel"
          (click)="activeModal.dismiss('Cancel')"
          [ngClass]="{
            'schedule-modal-cancel': currentStep === 'scheduleWindow',
          }"
        >
          Cancel
        </button>
      </div>

      <div
        style="display: flex; gap: 1rem; flex-wrap: wrap"
        [ngStyle]="{
          'justify-content': currentStep === 'form' ? 'flex-end' : 'flex-start',
        }"
      >
        <!-- Schedule button (only shown if user has schedule role) -->
        <ng-container
          *ngIf="currentStep === 'selectDeploymentType' && hasScheduleRole"
        >
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            (click)="openScheduleModal()"
            style="font-size: 1.2rem"
          >
            Schedule
          </button>
        </ng-container>

        <!-- Primary action button -->
        <ng-container *ngIf="currentStep === 'selectDeploymentType'">
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            (click)="handlePrimaryAction()"
            style="font-size: 1.2rem"
          >
            Continue to Immediate Deployment
          </button>
        </ng-container>

        <ng-container *ngIf="currentStep === 'immediateConfirmation'">
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            (click)="deploymentTypeVar = 'immediate'; onClickConfirm()"
            style="font-size: 1.2rem"
            [disabled]="isDeploying"
          >
            <span *ngIf="!isDeploying">Deploy Immediately</span>
            <span *ngIf="isDeploying">
              {{ deployingText }}
              <app-ics-loader class="small-loader"></app-ics-loader>
            </span>
          </button>
        </ng-container>

        <ng-container *ngIf="currentStep === 'maintenanceConfirmation'">
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            (click)="deploymentTypeVar = 'maintenance-window'; onClickConfirm()"
            style="font-size: 1.2rem"
            [disabled]="isDeploying"
          >
            <span *ngIf="!isDeploying">Wait for maintenance window</span>
            <span *ngIf="isDeploying">
              {{ deployingText }}
              <app-ics-loader class="small-loader"></app-ics-loader>
            </span>
          </button>
        </ng-container>

        <ng-container *ngIf="currentStep === 'scheduleWindow'">
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            (click)="onClickConfirm()"
            style="font-size: 1.2rem"
            [disabled]="isDeploying"
          >
            <span *ngIf="!isDeploying">Okay</span>
            <span *ngIf="isDeploying">
              {{ deployingText }}
              <app-ics-loader class="small-loader"></app-ics-loader>
            </span>
          </button>
        </ng-container>

        <ng-container *ngIf="currentStep === 'schedule'">
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            [disabled]="!isValidScheduleTime"
            (click)="handleScheduleAction()"
            style="font-size: 1.2rem; margin-left: auto"
          >
            Update
          </button>
        </ng-container>

        <!-- Secondary action button -->
        <ng-container *ngIf="currentStep === 'selectDeploymentType'">
          <button
            type="button"
            class="btn btn-primary btn-box-shadow btn-wide"
            (click)="handleSecondaryAction()"
            style="font-size: 1.2rem"
          >
            Wait for Maintenance Window
          </button>
        </ng-container>
      </div>
    </div>
  </div>
</div>
