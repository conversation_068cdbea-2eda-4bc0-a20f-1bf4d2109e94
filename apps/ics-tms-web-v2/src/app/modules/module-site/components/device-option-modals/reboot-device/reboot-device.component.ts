import { Component, inject, Input, OnDestroy } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToastService } from '../../../../../services/toast.service';
import { getApiConstants } from '../../../constants/api';

@Component({
  selector: 'app-reboot-device',
  templateUrl: './reboot-device.component.html',
  styleUrls: ['./reboot-device.component.scss'],
})
export class RebootDeviceComponent implements OnDestroy {
  private destroy$ = new Subject<void>();

  activeModal = inject(NgbActiveModal);

  toastService = inject(ToastService);

  @Input() deviceId!: string;

  http = inject(HttpClient);

  clickReboot() {
    this.http
      .post(getApiConstants().device.reboot.postReboot, {
        data: '{}',
        destination: 'invenco.system',
        deviceId: this.deviceId,
        type: 'sys.reboot',
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toastService.show({ message: 'Device Rebooting' });
          this.activeModal.close(true);
        },
        error: err => {
          this.toastService.show({ message: err });
        },
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
