import { Component, inject, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Observable, Subject, filter, takeUntil } from 'rxjs';
import { throttleTime } from 'rxjs/operators';
import { Sites, SitesParams } from '../../../../../models/sites.model';
import {
  selectSitesData,
  selectSitesLoading,
} from '../../../../../store/selectors/sites.selectors';
import { loadSites } from '../../../../../store/actions/sites.actions';
import { DeploymentTypeModalComponent } from './deployment-type-modal/deployment-type-modal.component';

@Component({
  selector: 'app-move-device-options',
  templateUrl: './move-device-options.component.html',
  styleUrls: ['./move-device-options.component.scss'],
})
export class MoveDeviceOptionsComponent implements OnInit, OnDestroy {
  activeModal = inject(NgbActiveModal);

  sitesData!: Sites;

  filteredSitesData!: Sites['results'];

  selectedSite: string | null = '';

  isLoading = true;

  searchSite = '';

  private readonly ITEM_TO_LOAD = 20;

  private readonly SCROLL_THRESHOLD = 300;

  private readonly destroy$ = new Subject<void>();

  private readonly pageIndex$ = new BehaviorSubject<number>(0);

  private readonly params$ = new BehaviorSubject<SitesParams>({
    pageIndex: 0,
    pageSize: this.ITEM_TO_LOAD,
    autoPoll: false,
    showHiddenSites: true,
  });

  private readonly scrollTrigger$ = new Subject<void>();

  private readonly loadingMoreSubject$ = new BehaviorSubject<boolean>(false);

  private loadingTimer: any = null;

  private isLoadingMore = false;

  sitesData$!: Observable<Sites>;

  isLoading$!: Observable<boolean>;

  loadingMore$ = this.loadingMoreSubject$.asObservable();

  private modalService = inject(NgbModal);

  @Input() siteId!: string;

  @Input() deviceId!: string;

  @Input() deviceName!: string;

  @Input() deviceType!: string;

  @Input() deviceDescription!: string;

  @Input() serialNumber!: string;

  store = inject(Store);

  http = inject(HttpClient);

  route = inject(ActivatedRoute);

  ngOnInit() {
    this.isLoading = true;

    this.initializeObservables();

    this.setupScrollHandler();

    this.loadSitesWithCurrentParams(true);
  }

  onSitesChange(siteId: string) {
    if (siteId === this.selectedSite) {
      this.selectedSite = null;
    } else {
      this.selectedSite = siteId;
    }
  }

  onSearchSite() {
    const currentParams = this.params$.value;
    const updatedParams = {
      ...currentParams,
      pageIndex: 0,
      q: this.searchSite,
    };

    this.pageIndex$.next(0);
    this.params$.next(updatedParams);

    this.loadSitesWithCurrentParams(true);
  }

  highlightSearchText(text: string, searchText: string): string {
    if (!text || !searchText) return text || '';
    try {
      const regex = new RegExp(searchText, 'gi');
      return text.replace(regex, match => `<strong>${match}</strong>`);
    } catch (error) {
      return text;
    }
  }

  moveDevice() {
    if (!this.selectedSite) {
      return;
    }

    const modalRef = this.modalService.open(DeploymentTypeModalComponent, {
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });

    modalRef.componentInstance.siteId = this.siteId;
    modalRef.componentInstance.deviceId = this.deviceId;
    modalRef.componentInstance.checkRkiParams = {
      site: this.siteId,
      devices: this.deviceId,
    };
    modalRef.componentInstance.deviceDescription = this.deviceDescription;
    modalRef.componentInstance.deviceName = this.deviceName;
    modalRef.componentInstance.deviceType = this.deviceType;
    modalRef.componentInstance.selectedSite = this.selectedSite;
    modalRef.componentInstance.serialNumber = this.serialNumber;

    this.activeModal.close();
  }

  protected readonly onscroll = onscroll;

  onScroll(event: Event) {
    const target = event.target as HTMLElement;
    const { scrollTop } = target;
    const { scrollHeight } = target;
    const { offsetHeight } = target;

    if (
      scrollTop + offsetHeight + this.SCROLL_THRESHOLD >= scrollHeight &&
      !this.isLoadingMore
    ) {
      this.scrollTrigger$.next();
    }
  }

  private initializeObservables() {
    this.sitesData$ = this.store.select(selectSitesData);
    this.isLoading$ = this.store.select(selectSitesLoading);

    this.isLoading$.pipe(takeUntil(this.destroy$)).subscribe(isLoading => {
      if (!this.isLoadingMore) {
        this.isLoading = isLoading;
      }
    });

    this.sitesData$.pipe(takeUntil(this.destroy$)).subscribe(data => {
      if (data && data.results) {
        this.sitesData = data;

        this.filteredSitesData = data.results;

        this.loadingMoreSubject$.next(false);
        this.isLoadingMore = false;
      }
    });
  }

  private setupScrollHandler() {
    this.scrollTrigger$
      .pipe(
        takeUntil(this.destroy$),
        throttleTime(1000),
        filter(() => {
          if (this.isLoadingMore) {
            return false;
          }

          const metadata =
            this.sitesData?.resultsMetadata || this.sitesData?.resultMetadata;
          if (!metadata) return false;

          const totalResults = metadata?.totalResults || 15381;
          const currentResultsCount = this.filteredSitesData.length;
          const hasMoreData = currentResultsCount < totalResults;

          return hasMoreData;
        })
      )
      .subscribe(() => {
        this.loadMoreSites();
      });
  }

  private loadSitesWithCurrentParams(replace: boolean) {
    const currentParams = this.params$.value;
    this.store.dispatch(loadSites({ params: currentParams, replace }));
  }

  private loadMoreSites() {
    this.isLoadingMore = true;
    this.loadingMoreSubject$.next(true);

    const currentParams = this.params$.value;
    const nextPageIndex = currentParams.pageIndex + 1;

    const updatedParams = {
      ...currentParams,
      pageIndex: nextPageIndex,
    };

    this.pageIndex$.next(nextPageIndex);
    this.params$.next(updatedParams);

    this.store.dispatch(loadSites({ params: updatedParams, replace: false }));
  }

  ngOnDestroy() {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
    }

    this.isLoadingMore = false;
    this.loadingMoreSubject$.next(false);

    this.destroy$.next();
    this.destroy$.complete();
  }
}
