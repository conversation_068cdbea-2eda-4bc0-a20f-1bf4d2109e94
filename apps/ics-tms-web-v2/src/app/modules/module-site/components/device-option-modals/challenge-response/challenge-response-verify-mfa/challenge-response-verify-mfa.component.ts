import { Component, Input, inject } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { getApiConstants } from '../../../../constants/api';
import { ID } from '../../../../constants/appConstants';

@Component({
  selector: 'app-challenge-response-verify-mfa',
  templateUrl: './challenge-response-verify-mfa.component.html',
  styleUrls: ['./challenge-response-verify-mfa.component.scss'],
})
export class ChallengeResponseVerifyMfaComponent {
  @Input() challenge!: string;

  @Input() operation!: string;

  @Input() requestedBy!: string;

  @Input() serialNumber!: string;

  @Input() device_id!: string;

  isMfaError = false;

  mfaCode: string | number = '';

  activeModal = inject(NgbActiveModal);

  http = inject(HttpClient);

  closeModal() {
    this.activeModal.close('CLOSEDDDD');
  }

  confirmMFA() {
    const params = {
      challenge: this.challenge,
      operation: this.operation,
      requestedBy: this.requestedBy,
      serialNumber: this.serialNumber,
      mfaCode: this.mfaCode,
    };
    this.http
      .post(
        getApiConstants().device.challengeResponse.postRequest.replace(
          ID,
          this.device_id
        ),
        params
      )
      .subscribe({
        next: data => {
          this.isMfaError = false;
          this.activeModal.close(data);
        },
        error: () => {
          this.isMfaError = true;
        },
      });
  }
}
