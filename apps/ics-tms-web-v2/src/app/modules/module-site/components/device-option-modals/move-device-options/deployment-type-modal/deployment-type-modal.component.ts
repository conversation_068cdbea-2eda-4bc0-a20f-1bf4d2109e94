import { Component, inject, Input, OnInit, OnDestroy } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import {
  NgbActiveModal,
  NgbDateStruct,
  NgbModal,
} from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { getApiConstants } from '../../../../constants/api';
import { ID } from '../../../../constants/appConstants';
import { ToastService } from '../../../../../../services/toast.service';
import { getBaseUrl } from '../../../../../../constants/api';
import { ModalConfirmationComponent } from '../../../../../../components/shared/ics-stepper-template/modal-confirmation/modal-confirmation.component';

interface MaintenanceWindow {
  from: string;
  to: string;
  isNext: boolean;
}

const getApiEndpoints = () => {
  const moduleApiConstants = getApiConstants();

  return {
    getMaintenanceWindow: `${getBaseUrl()}/entities/entity/settings/tenants`,
    getRkiCheck: moduleApiConstants.device.move.getRkiCheck.replace('//', '/'),
    putMoveDevice: moduleApiConstants.device.move.putMoveDevice,
  };
};

@Component({
  selector: 'app-deployment-type-modal',
  templateUrl: './deployment-type-modal.component.html',
  styleUrls: ['./deployment-type-modal.component.scss'],
})
export class DeploymentTypeModalComponent implements OnInit, OnDestroy {
  private subscriptions: Array<{ unsubscribe: () => void }> = [];

  private loadingTimer: ReturnType<typeof setTimeout> | null = null;

  @Input() siteId!: string;

  @Input() deviceId!: string;

  @Input() checkRkiParams!: { site: string; devices: string };

  @Input() deviceName!: string;

  @Input() deviceType!: string;

  @Input() deviceDescription!: string;

  @Input() selectedSite!: string;

  @Input() serialNumber!: string;

  currentStep = 'selectDeploymentType';

  deploymentTypeVar = 'maintenance-window';

  scheduleDateTime = '';

  scheduleWindowDescription = '';

  isProgressing = false;

  isDeploying = false;

  deployingText = 'Deploying';

  selectedDate!: NgbDateStruct;

  selectedTime = { hour: 0, minute: 0 };

  minDate!: NgbDateStruct;

  maxDate!: NgbDateStruct;

  isDatePickerOpen = false;

  hasScheduleRole = false;

  user: any = null;

  maintenance: MaintenanceWindow | null = null;

  isValidScheduleTime = true;

  private maintenanceCache: MaintenanceWindow | null = null;

  private lastMaintenanceFetchTime: number | null = null;

  private readonly cacheDuration = 15;

  steps = {
    form: {
      title: 'Move device',
      description: 'Select a different site to move the device to.',
      primaryAction: 'Move',
      secondaryAction: '',
    },
    selectDeploymentType: {
      title: 'These changes may affect configurations',
      description:
        'Any affected configurations for these sites will automatically deploy during the maintenance window for each site (daily)',
      primaryAction: 'Continue to Immediate Deployment',
      secondaryAction: 'Wait for Maintenance Window',
    },
    immediateConfirmation: {
      title: 'Are you sure you want to deploy?',
      description:
        'Deploying configuration will reboot any affected device(s), which may cause temporary disruption to service at site.',
      primaryAction: 'Deploy Immediately',
      secondaryAction: '',
    },
    maintenanceConfirmation: {
      title: 'Are you sure you want to deploy?',
      description:
        'Deploying configuration will reboot any affected device(s), which may cause temporary disruption to service at site.',
      primaryAction: 'Wait for maintenance window',
      secondaryAction: '',
    },
    scheduleWindow: {
      title:
        'There is already a scheduled deployment in the upcoming maintenance window',
      description:
        'This deployment will be moved to the following maintenance window, once the current maintenance is complete.',
      primaryAction: 'Okay',
      secondaryAction: '',
    },
    schedule: {
      title: 'Confirm update',
      description:
        'This may trigger deployment of multiple configuration instances',
      primaryAction: 'Update',
      secondaryAction: '',
    },
  };

  activeModal = inject(NgbActiveModal);

  toastService = inject(ToastService);

  http = inject(HttpClient);

  router = inject(Router);

  modalService = inject(NgbModal);

  ngOnInit(): void {
    const today = new Date();
    this.minDate = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 5);
    this.maxDate = {
      year: maxDate.getFullYear(),
      month: maxDate.getMonth() + 1,
      day: maxDate.getDate(),
    };

    this.selectedDate = { ...this.minDate };

    this.selectedTime = {
      hour: today.getHours(),
      minute: today.getMinutes(),
    };

    this.validateTime();

    this.hasScheduleRole = true;
  }

  handlePrimaryAction() {
    switch (this.currentStep) {
      case 'selectDeploymentType':
        this.currentStep = 'immediateConfirmation';
        break;

      case 'immediateConfirmation':
        this.deploymentTypeVar = 'immediate';
        this.onClickConfirm();
        break;

      case 'maintenanceConfirmation':
        this.fetchMaintenanceWindow();
        if (this.maintenance?.isNext) {
          this.currentStep = 'scheduleWindow';
          this.scheduleWindowDescription = this.convertAndFormatScheduleWindow(
            this.maintenance.from,
            this.maintenance.to
          );
        } else {
          this.deploymentTypeVar = 'maintenance-window';
          this.onClickConfirm();
        }
        break;

      case 'scheduleWindow':
        this.activeModal.close('Scheduled');
        break;

      case 'schedule':
        this.handleScheduleAction();
        break;

      default:
        break;
    }
  }

  handleSecondaryAction() {
    if (this.currentStep === 'selectDeploymentType') {
      this.currentStep = 'maintenanceConfirmation';
      this.fetchMaintenanceWindow();
    }
  }

  openScheduleModal() {
    this.currentStep = 'schedule';
  }

  formatSelectedDate(): string {
    if (!this.selectedDate) {
      const today = new Date();
      return `${today.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' })}`;
    }

    const date = new Date(
      this.selectedDate.year,
      this.selectedDate.month - 1,
      this.selectedDate.day
    );
    return `${date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' })}`;
  }

  toggleDatepicker(): void {
    this.isDatePickerOpen = !this.isDatePickerOpen;
  }

  onDateSelect(): void {
    this.isDatePickerOpen = false;
    this.validateTime();
  }

  handleScheduleAction() {
    const { year } = this.selectedDate;
    const month = this.selectedDate.month - 1;
    const { day } = this.selectedDate;
    const { hour } = this.selectedTime;
    const { minute } = this.selectedTime;

    const scheduledDate = new Date(year, month, day, hour, minute, 0);

    this.scheduleDateTime = scheduledDate.toISOString();

    this.deploymentTypeVar = 'schedule';

    this.onClickConfirm();
  }

  validateTime(): void {
    const now = new Date();
    const selectedDateTime = new Date(
      this.selectedDate.year,
      this.selectedDate.month - 1,
      this.selectedDate.day,
      this.selectedTime.hour,
      this.selectedTime.minute
    );

    const isToday =
      now.getDate() === this.selectedDate.day &&
      now.getMonth() === this.selectedDate.month - 1 &&
      now.getFullYear() === this.selectedDate.year;

    if (isToday) {
      this.isValidScheduleTime = selectedDateTime > now;

      if (!this.isValidScheduleTime) {
        const futureTime = new Date(now.getTime() + 5 * 60000);
        this.selectedTime.hour = futureTime.getHours();
        this.selectedTime.minute = futureTime.getMinutes();
        this.isValidScheduleTime = true;
      }
    } else {
      this.isValidScheduleTime = true;
    }
  }

  incrementHour(): void {
    this.selectedTime.hour = (this.selectedTime.hour + 1) % 24;
  }

  decrementHour(): void {
    this.selectedTime.hour = (this.selectedTime.hour - 1 + 24) % 24;
  }

  incrementMinute(): void {
    this.selectedTime.minute = (this.selectedTime.minute + 1) % 60;
  }

  decrementMinute(): void {
    this.selectedTime.minute = (this.selectedTime.minute - 1 + 60) % 60;
  }

  async fetchMaintenanceWindow() {
    const currentTime = Math.floor(Date.now() / 1000);

    if (
      this.maintenanceCache &&
      this.lastMaintenanceFetchTime &&
      currentTime - this.lastMaintenanceFetchTime < this.cacheDuration
    ) {
      this.maintenance = this.maintenanceCache;
      return;
    }

    try {
      const response = await firstValueFrom(
        this.http.get<any>(getApiEndpoints().getMaintenanceWindow)
      );

      if (
        response &&
        response.settings &&
        response.settings.maintenanceWindow
      ) {
        this.maintenance = response.settings.maintenanceWindow;
        this.maintenanceCache = response.settings.maintenanceWindow;
        this.lastMaintenanceFetchTime = currentTime;
      } else {
        console.warn('Maintenance window not found in tenant settings');
      }
    } catch (err) {
      console.error('Error fetching maintenance window:', err);
    }
  }

  convertAndFormatScheduleWindow(from: string, to: string): string {
    const fromDate = new Date(from);
    const toDate = new Date(to);

    const formattedFromTime = fromDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const formattedToTime = toDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
    const formattedDate = fromDate.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });

    return `This deployment will be moved to the following maintenance window, once the current maintenance is complete.<br><br> Est. ${formattedFromTime}-${formattedToTime} GMT ${formattedDate}`;
  }

  onClickConfirm() {
    this.loadingTimer = setTimeout(() => {
      this.isDeploying = true;
      this.isProgressing = true;
    }, 300);
    const subscription = this.http
      .get(getApiEndpoints().getRkiCheck, { params: this.checkRkiParams })
      .subscribe({
        next: (response: any) => {
          if (this.loadingTimer) {
            clearTimeout(this.loadingTimer);
            this.loadingTimer = null;
          }
          this.isDeploying = false;
          this.isProgressing = false;

          if (response?.devicesToRKI?.length > 0) {
            this.showRkiConfirmation();
          } else {
            this.moveDevice();
          }
        },
        error: _error => {
          if (this.loadingTimer) {
            clearTimeout(this.loadingTimer);
            this.loadingTimer = null;
          }
          this.isDeploying = false;
          this.isProgressing = false;

          this.toastService.show({
            message: 'Error checking RKI requirements',
            type: 'error',
          });
        },
      });
    this.subscriptions.push(subscription);
  }

  private showRkiConfirmation(): void {
    const modalRef = this.modalService.open(ModalConfirmationComponent, {
      centered: true,
      backdrop: 'static',
      keyboard: false,
      container: '#ng-modal-container',
      windowClass: 'common-details-popup in',
      size: 'sm',
    });

    modalRef.componentInstance.title = 'RKI Confirmation';
    modalRef.componentInstance.textContent =
      'This action will trigger an RKI. Do you wish to continue?';
    modalRef.componentInstance.buttons = [
      {
        label: 'NO',
        cancel: true,
        class: 'btn-default btn-link-default btn-wide-modal mr7',
      },
      {
        label: 'YES',
        primary: true,
        class: 'btn-box-shadow btn-primary btn-wide-modal',
      },
    ];

    modalRef.result.then(
      result => {
        if (result === true || result === 'YES') {
          this.isDeploying = true;
          this.isProgressing = true;
          this.moveDevice();
        } else {
          this.isDeploying = false;
          this.isProgressing = false;
        }
      },
      () => {
        this.isDeploying = false;
        this.isProgressing = false;
        this.deployingText = 'Deploying';
      }
    );
  }

  private moveDevice(): void {
    const requestPayload: Record<string, any> = {
      deploymentType: this.deploymentTypeVar,
      description: this.deviceDescription,
      name: this.deviceName,
      siteId: this.selectedSite,
      serialNumber: this.serialNumber,
    };

    if (this.deploymentTypeVar === 'schedule' && this.scheduleDateTime) {
      requestPayload['scheduledDateTime'] = this.scheduleDateTime;
    }

    if (this.deviceType) {
      requestPayload['deviceType'] = this.deviceType;
    }

    const subscription = this.http
      .put(
        getApiEndpoints().putMoveDevice.replace(ID, this.deviceId),
        requestPayload
      )
      .subscribe({
        next: (response: any) => {
          if (this.deploymentTypeVar === 'schedule') {
            this.toastService.show({
              message: response.message || 'Device scheduled for move',
              type: 'success',
              delay: 6000,
            });
          } else {
            this.toastService.show({
              message: 'Device moved',
              type: 'success',
              delay: 4000,
            });

            this.router.navigate(['/sites', this.selectedSite]);
          }

          this.activeModal.close('Device moved');
        },
        error: (err: any) => {
          this.isProgressing = false;
          this.toastService.show({
            message: err.error?.message || 'Error moving device',
            type: 'error',
            delay: 6000,
          });
        },
      });
    this.subscriptions.push(subscription);
  }

  ngOnDestroy(): void {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }

    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
  }
}
