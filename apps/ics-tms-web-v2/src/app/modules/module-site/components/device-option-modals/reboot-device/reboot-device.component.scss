.reboot-device-container {
  width: 40rem;
  height: fit-content;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .heading-reboot-device {
    padding: 1rem 1.5rem;

    #reboot-title {
      font-size: 1.8rem;
      font-weight: 500;
      line-height: 1.43;
      margin-bottom: 0.1rem;
    }

    #reboot-description {
      font-size: 1.4rem;
      line-height: 1.43;
    }
  }

  .button-reboot-device {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    .no-btn {
      font-size: 1.4rem;
      font-weight: 500;
      background-color: var(--color-white);
      border: none;
      padding-right: 1.25em;
      padding-left: 1.25em;
      border-radius: 0.3rem;
    }

    .yes-btn {
      font-size: 1.4rem;
      font-weight: 500;
      padding-right: 1.25em;
      padding-left: 1.25em;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
      transition: all ease-in 0.1s;
      border-radius: 0.3rem;

      &:active {
        background-color: var(--color-blue);
        border-color: var(--color-blue);
      }
    }
  }
}
