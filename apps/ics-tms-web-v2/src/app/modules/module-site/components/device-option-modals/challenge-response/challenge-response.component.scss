.challenge-response-container {
  display: flex;
  justify-content: center;

  .main-container {
    width: 100%;
    height: fit-content;
    box-shadow:
      0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
      0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.14),
      0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
    margin-top: -9rem;
    z-index: 5;
    background-color: var(--color-white);
    border-radius: 0.3rem;
    padding: 1rem;

    h3 {
      padding: 1rem 1.5rem;
      font-size: 1.8rem;
      margin-bottom: 0;
      margin-top: 0;
      font-weight: 500;
      line-height: 1.1;
      color: var(--color-white);
      background-color: var(--md-cyan-a500);
    }

    .border-bottom {
      margin: 0 1rem;
      border-bottom: 0.1rem solid var(--color-border);
    }

    .challenges-container {
      .top-input-container {
        padding: 2rem 1.563rem;
        padding-bottom: 0;
        flex-direction: column;
        display: flex;

        .border-bottom {
          border-bottom: 0.1rem solid var(--color-border);
          margin-bottom: 2rem;
          margin: 0 0 2rem 0;
        }

        .challenges-response-ng-select {
          input {
            width: 100%;
            height: 1.563rem;
          }

          .ng-dropdown-panel {
            width: 35%;
            padding-top: 0.2rem;
            padding-bottom: 0.4rem;
          }

          .ng-select .ng-arrow-wrapper .ng-arrow {
            transform: scale(0.8);
            border-color: var(--color-black) transparent transparent;
          }

          .ng-select.ng-select-single .ng-select-container {
            background-color: var(--dropdown-by-default);
            cursor: pointer;
            transition:
              border-color ease-in-out 0.15s,
              box-shadow ease-in-out 0.15s;

            &:hover {
              box-shadow: none;
            }
          }

          .ng-select.ng-select-single .ng-select-container .ng-value-container {
            cursor: pointer;
          }

          .ng-select .ng-clear-wrapper {
            display: none;
          }

          .ng-dropdown-panel
            .ng-dropdown-panel-items
            .ng-option.ng-option-selected {
            color: var(--color-white) !important;
            font-weight: 500 !important;
            background-color: var(--color-primary) !important;
          }

          .ng-dropdown-panel
            .ng-dropdown-panel-items
            .ng-option.ng-option-marked {
            color: var(--color-black);
            background-color: var(--color-bg-default);
          }

          .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
            color: var(--color-black);
            background-color: var(--color-white);
          }

          .ng-select
            .ng-select-container
            .ng-value-container
            .ng-input
            > input {
            cursor: pointer;
          }

          .ng-select.ng-select-opened > .ng-select-container {
            box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
            background-color: var(--color-bg-default);
            border-color: rgba(68, 138, 255, 0.75) !important;
            outline: 0;
          }

          .ng-select.ng-select-single .ng-select-container {
            height: fit-content;
          }

          .ng-select.ng-select-single .ng-select-container:hover {
            border-color: var(--dropdown-border-hover);
            background-color: var(--dropdown-hover);
          }

          .ng-select.ng-select-focused:not(.ng-select-opened)
            > .ng-select-container {
            border-color: rgba(68, 138, 255, 0.75) !important;
            box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
          }
        }

        width: 100%;

        .response {
          display: flex;
          margin-bottom: 1.5rem;

          .label {
            width: 25%;
            font-weight: 600;
            font-size: 1.4rem;
            color: var(--color-black) !important;
            text-align: left;
          }

          .text-align-middle {
            display: flex;
            align-items: center;
          }

          .input-ng-select {
            width: 55%;

            .custom-input {
              height: 3.4rem !important;
              margin-bottom: 0;
            }

            .non-input {
              font-size: 1.4rem;
              margin: 0;
            }

            .challenge-response-code {
              font-size: 1.6rem;
              font-weight: 600;
              margin-top: -0.4rem;
              letter-spacing: 0.8rem;
            }

            input::placeholder {
              font-size: 1.6rem !important;
            }
          }
        }
      }

      .challenges-button-container {
        display: flex;
        justify-content: flex-start;
        padding: 0 1rem;
        gap: 1rem;
        margin-bottom: 1rem;

        .challenge-request-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 0.3rem;
          color: var(--color-white);
          font-weight: 500;
          font-size: 1.4rem;

          &:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            box-shadow: none;

            &:hover {
              background-color: var(--color-email-not-verified);
            }
          }
        }

        .challenge-cancel-btn {
          border-color: var(--btn-cancel-color);
          background-color: var(--btn-cancel-color);
          font-weight: 500;
          font-size: 1.4rem;
          padding: 0.8rem 1.8rem;
          color: var(--color-black);

          &:hover {
            border-color: var(--btn-cancel-hover-color);
            background-color: var(--btn-cancel-hover-color);
          }
        }
      }
    }
  }
}

.item-name-text {
  font-size: 1.4rem;
  margin: 0 !important;
  padding-left: 0.8rem;
}
