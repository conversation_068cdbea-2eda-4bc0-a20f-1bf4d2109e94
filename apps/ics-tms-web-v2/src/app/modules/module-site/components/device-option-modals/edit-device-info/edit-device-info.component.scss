.edit-device-info-container {
  height: fit-content;
  width: 40rem;
  border-radius: 0.4rem;
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.5);

  .top-heading-edit-device {
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    .heading {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      line-height: 1.4;

      h4 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 500;
      }

      strong {
        cursor: pointer;
        opacity: 0.2;
        font-size: 2.1rem;

        &:hover {
          opacity: 0.5;
        }
      }
    }

    .small-text {
      span {
        font-size: 1.2rem;
        color: var(--color-black-shade-two);

        &:first-child {
          font-weight: 700;
          font-size: 1.2rem;
          color: var(--color-black-shade-two);
        }
      }
    }
  }

  .input-area-edit-device {
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    .device-name {
      margin-bottom: 1.5rem;

      label {
        display: block;
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 0.8rem;
      }

      input {
        margin-bottom: 0;
        width: 100%;
      }
    }

    .device-note {
      margin-bottom: 1.5rem;

      textarea {
        margin-bottom: 0;
      }
    }

    .device-edit-textarea {
      min-height: 8rem;
      resize: none;
    }

    .textarea-label-device-edit {
      display: flex;
      justify-content: space-between;

      label {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 0.8rem;
      }

      span {
        color: var(--color-black-shade-two);
        font-size: 1.2rem;
      }
    }
  }

  .button-area-device-edit {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: flex-end;

    button {
      margin-left: 1.6rem;
      font-size: 1.4rem;
    }

    .update-button {
      padding: 0.6rem 1.2rem;
      border-radius: 0.3rem;
      border: none;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
      color: var(--color-white);
      transition: all ease-in 0.1s;

      &:active {
        background-color: var(--color-blue);
      }

      &:disabled {
        background-color: var(--color-primary);
        cursor: not-allowed !important;
        opacity: 0.65 !important;
        box-shadow: none;
      }
    }

    .cancel-button {
      padding: 0.6rem 1.2rem;
      color: var(--color-email-not-verified);
      font-weight: 500;
      border-radius: 0.3rem;

      &:hover {
        color: var(--color-blue);
      }

      &:active {
        border: none;
      }
    }
  }
}
