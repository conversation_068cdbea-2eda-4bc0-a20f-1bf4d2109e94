import { Component, inject, OnInit, ViewEncapsulation } from '@angular/core';
import { Store } from '@ngrx/store';
import { ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import * as versionActions from '../../store/actions/devices-version.actions';
import { deviceVersionDataSelector } from '../../store/selectors/devices-versions.selector';
import {
  DeviceVersions,
  VersionInfo,
} from '../../models/device-versions.modal';

@Component({
  selector: 'app-devices-versions',
  templateUrl: './devices-versions.component.html',
  styleUrls: ['./devices-versions.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class DevicesVersionsComponent implements OnInit {
  result: any;

  platformData: VersionInfo[] = [];

  platformKeys: { key: string; value: string | null }[] = [];

  originalPlatformKeys: { key: string; value: string | null }[] = [];

  vendorsData: VersionInfo[] = [];

  vendorsKeys: { key: string; value: string | null }[] = [];

  originalVendorsKeys: { key: string; value: string | null }[] = [];

  versionQuery!: string;

  siteId!: string;

  deviceId!: string;

  DeviceVersionsData: DeviceVersions = {} as DeviceVersions;

  filteredExceptions: any[] = [];

  exceptions: any[] = [];

  epsplugins: any[] = [];

  filteredEpsPlugins: any[] = [];

  store = inject(Store);

  private destroy$ = new Subject<void>();

  protected readonly Object = Object;

  route = inject(ActivatedRoute);

  ngOnInit(): void {
    this.initializeRouteParams();
    this.getDeviceVersionsData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeRouteParams(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.siteId = params['site_id'];
      this.deviceId = params['device_id'];
      if (this.deviceId) {
        this.dispatchDeviceVersionsData();
      }
    });
  }

  private dispatchDeviceVersionsData(): void {
    this.store.dispatch(
      versionActions.loadDeviceVersionsData({ deviceId: this.deviceId })
    );
  }

  private getDeviceVersionsData(): void {
    this.store
      .select(deviceVersionDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.DeviceVersionsData = data;

        if (this.DeviceVersionsData?.platform) {
          this.platformData = this.DeviceVersionsData.platform;

          this.platformKeys = this.platformData.map(obj => {
            const keys = Object.keys(obj || {});
            const firstKey = keys[0];
            if (!firstKey) {
              return { key: '', value: null };
            }
            const splitString = firstKey.split('.');
            return {
              key: splitString[splitString.length - 1],
              value: obj[firstKey],
            };
          });
          this.originalPlatformKeys = [...this.platformKeys];
        }
        if (this.DeviceVersionsData?.vendor) {
          this.vendorsData = this.DeviceVersionsData.vendor;
          this.vendorsKeys = this.vendorsData.map(obj => {
            const keys = Object.keys(obj || {});
            const firstKey = keys[0];
            if (!firstKey) {
              return { key: '', value: null };
            }
            const splitString = firstKey.split('.');
            return {
              key: splitString[splitString.length - 1],
              value: obj[firstKey],
            };
          });
          this.originalVendorsKeys = [...this.vendorsKeys];
        }
        if (this.DeviceVersionsData?.exceptions) {
          this.exceptions = this.DeviceVersionsData.exceptions;
          this.filterExceptions();
        }
        if (this.DeviceVersionsData?.epsplugins) {
          this.epsplugins = this.DeviceVersionsData.epsplugins;
          this.filteredEpsPlugins = this.filterEspPlugins();
        }
      });
  }

  onQueryChange(event: string) {
    if (event) {
      this.platformKeys = this.originalPlatformKeys.filter(
        obj => obj.key.includes(event) || obj.value?.includes(event)
      );

      this.vendorsKeys = this.originalVendorsKeys.filter(
        obj => obj.key.includes(event) || obj.value?.includes(event)
      );
    } else {
      this.platformKeys = [...this.originalPlatformKeys];
      this.vendorsKeys = [...this.originalVendorsKeys];
    }
    this.filterExceptions();
  }

  filterExceptions() {
    if (this.versionQuery) {
      const query = this.versionQuery.toLowerCase();
      this.filteredExceptions = this.exceptions.filter(
        item =>
          item.metric?.toLowerCase().includes(query) ||
          item.value?.toLowerCase().includes(query) ||
          item.expected?.toLowerCase().includes(query)
      );
    } else {
      this.filteredExceptions = [...this.exceptions];
    }
  }

  highlightSearchText(key: string | null, versionQuery: string) {
    const regex = new RegExp(versionQuery, 'gi');
    if (versionQuery)
      return key?.replace(
        regex,
        match => `<span class="text-highlight">${match}</span>`
      );
    return key;
  }

  filterEspPlugins() {
    if (!Array.isArray(this.epsplugins) || this.epsplugins.length === 0) {
      return [];
    }

    const purifiedPlugins: any[] = [];

    this.epsplugins.forEach((epsplugin: any) => {
      const purifyExceptions = ['invenco.infx-eps.app.app-ver'];
      const originalMetricKey = Object.keys(epsplugin)[0];
      const metric = purifyExceptions.includes(originalMetricKey)
        ? originalMetricKey
        : this.purifyMetricData(originalMetricKey);

      purifiedPlugins.push({
        metric,
        value: epsplugin[originalMetricKey],
      });
    });

    return purifiedPlugins;
  }

  private purifyMetricData(data: string): string {
    const reg = /(invenco.)(\w*.){2}/;
    return data.replace(reg, '');
  }
}
