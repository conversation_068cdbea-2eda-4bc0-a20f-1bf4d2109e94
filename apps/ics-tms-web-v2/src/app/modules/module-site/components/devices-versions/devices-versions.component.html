<div class="card versions-card">
  <div class="card-title versions-card-title">
    <h2>Versions</h2>
    <div class="versions-search-input">
      <input
        [(ngModel)]="versionQuery"
        (ngModelChange)="onQueryChange($event)"
        class="ics-input"
        type="text"
        placeholder="Search by keyword"
      />
    </div>
  </div>

  <div
    class="device-version-body"
    *ngIf="
      vendorsKeys.length > 0 ||
      platformKeys.length > 0 ||
      filteredEpsPlugins.length > 0 ||
      filteredExceptions.length > 0
    "
  >
    <!-- Platform -->
    <div class="device-version-listing" *ngIf="platformKeys.length > 0">
      <p class="heading">PLATFORM</p>
      <div class="device-version-row" *ngFor="let platform of platformKeys">
        <p
          class="dversion-bold"
          [innerHTML]="highlightSearchText(platform.key, versionQuery)"
        ></p>
        <p
          class="dversion-light"
          [innerHTML]="highlightSearchText(platform.value, versionQuery)"
        ></p>
      </div>
    </div>

    <!-- Platform Package Mismatches -->
    <div
      *ngIf="filteredExceptions.length > 0"
      class="device-version-listing exception-listing"
    >
      <p class="heading">PLATFORM PACKAGE MISMATCHES</p>
      <div
        class="device-version-row exception-row"
        *ngFor="let item of filteredExceptions"
      >
        <div
          class="col-md-6 name"
          [innerHTML]="highlightSearchText(item.metric, versionQuery)"
        ></div>
        <div class="col-md-3 value wrong">
          <span ngbTooltip="Available version">
            <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
          </span>
          <span
            [innerHTML]="highlightSearchText(item.value, versionQuery)"
          ></span>
        </div>
        <div class="col-md-3 value correct">
          <span ngbTooltip="Expected version">
            <i class="fa fa-check-circle" aria-hidden="true"></i>
          </span>
          <span
            [innerHTML]="highlightSearchText(item.expected, versionQuery)"
          ></span>
        </div>
      </div>
    </div>

    <!-- Vendor Space -->
    <div class="device-version-listing" *ngIf="vendorsKeys.length > 0">
      <p class="heading vendor-space">VENDOR SPACE</p>
      <div class="device-version-row" *ngFor="let vendor of vendorsKeys">
        <p
          class="dversion-bold"
          [innerHTML]="highlightSearchText(vendor.key, versionQuery)"
        ></p>
        <p
          class="dversion-light"
          [innerHTML]="highlightSearchText(vendor.value, versionQuery)"
        ></p>
      </div>
    </div>

    <div class="device-version-listing" *ngIf="filteredEpsPlugins.length > 0">
      <div class="heading">EPS Plugin</div>

      <div class="device-version-row" *ngFor="let plugin of filteredEpsPlugins">
        <p
          class="dversion-bold"
          [innerHTML]="highlightSearchText(plugin.metric, versionQuery)"
        ></p>
        <p
          class="dversion-light"
          [innerHTML]="highlightSearchText(plugin.value, versionQuery)"
        ></p>
      </div>
    </div>
  </div>

  <div
    *ngIf="
      vendorsKeys.length === 0 &&
      platformKeys.length === 0 &&
      filteredEpsPlugins.length === 0 &&
      filteredExceptions.length === 0
    "
    class="no-versions-found"
  >
    <span>No versions found for this device.</span>
  </div>
</div>
