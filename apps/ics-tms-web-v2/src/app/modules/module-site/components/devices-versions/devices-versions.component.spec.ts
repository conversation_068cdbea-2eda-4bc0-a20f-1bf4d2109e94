import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject, of } from 'rxjs';
import * as versionActions from '../../store/actions/devices-version.actions';
import { deviceVersionDataSelector } from '../../store/selectors/devices-versions.selector';
import {
  DeviceVersions,
  VersionInfo,
} from '../../models/device-versions.modal';
import { DevicesVersionsComponent } from './devices-versions.component';

describe('DevicesVersionsComponent', () => {
  let component: DevicesVersionsComponent;
  let fixture: ComponentFixture<DevicesVersionsComponent>;
  let mockStore: any;
  let mockActivatedRoute: any;
  let storeSelectSpy: jasmine.Spy;
  let storeDispatchSpy: jasmine.Spy;

  const mockDeviceVersionsData: DeviceVersions = {
    platform: [
      { 'platform.version.core': '1.0.0' },
      { 'platform.version.ui': '2.1.0' },
      { 'platform.system.kernel': '5.4.0' },
    ] as VersionInfo[],
    vendor: [
      { 'vendor.driver.graphics': '460.32.03' },
      { 'vendor.firmware.bios': '2.15.1' },
      { 'vendor.software.antivirus': '21.3.0' },
    ] as VersionInfo[],
    exceptions: [
      { metric: 'CPU Temperature', value: '85°C', expected: '70°C' },
      { metric: 'Memory Usage', value: '95%', expected: '80%' },
      { metric: 'Disk Space', value: '98%', expected: '85%' },
    ],
    epsplugins: [
      { 'eps.plugin.payment': '3.2.1' },
      { 'eps.plugin.loyalty': '2.5.0' },
      { 'eps.plugin.security': '1.8.3' },
    ],
  };

  const mockEmptyDeviceVersionsData: DeviceVersions = {
    platform: [],
    vendor: [],
    exceptions: [],
    epsplugins: [],
  };

  beforeEach(async () => {
    const storeSpy = jasmine.createSpyObj('Store', ['select', 'dispatch']);
    const activatedRouteSpy = {
      params: of({ site_id: 'test-site-123', device_id: 'test-device-456' }),
    };

    await TestBed.configureTestingModule({
      declarations: [DevicesVersionsComponent],
      providers: [
        { provide: Store, useValue: storeSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DevicesVersionsComponent);
    component = fixture.componentInstance;
    mockStore = TestBed.inject(Store) as any;
    mockActivatedRoute = TestBed.inject(ActivatedRoute) as any;

    storeSelectSpy = mockStore.select.and.returnValue(
      of(mockDeviceVersionsData)
    );
    storeDispatchSpy = mockStore.dispatch.and.stub();
  });

  afterEach(() => {
    fixture.destroy();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.platformData).toEqual([]);
      expect(component.platformKeys).toEqual([]);
      expect(component.originalPlatformKeys).toEqual([]);
      expect(component.vendorsData).toEqual([]);
      expect(component.vendorsKeys).toEqual([]);
      expect(component.originalVendorsKeys).toEqual([]);
      expect(component.filteredExceptions).toEqual([]);
      expect(component.exceptions).toEqual([]);
      expect(component.DeviceVersionsData).toEqual({} as DeviceVersions);
    });

    it('should have protected Object reference', () => {
      expect(component['Object']).toBe(Object);
    });

    it('should initialize destroy$ Subject', () => {
      expect(component['destroy$']).toBeInstanceOf(Subject);
    });
  });

  describe('ngOnInit', () => {
    it('should call all initialization methods', () => {
      spyOn(component, 'getDeviceVersionsData' as any);
      spyOn(component, 'initializeRouteParams' as any);

      component.ngOnInit();

      expect(component['getDeviceVersionsData']).toHaveBeenCalled();
      expect(component['initializeRouteParams']).toHaveBeenCalled();
    });
  });

  describe('ngOnDestroy', () => {
    it('should complete destroy$ subject', () => {
      spyOn(component['destroy$'], 'next');
      spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(component['destroy$'].next).toHaveBeenCalled();
      expect(component['destroy$'].complete).toHaveBeenCalled();
    });
  });

  describe('Route Parameter Handling', () => {
    it('should initialize route parameters correctly', () => {
      component['initializeRouteParams']();

      expect(component.siteId).toBe('test-site-123');
      expect(component.deviceId).toBe('test-device-456');
      expect(mockActivatedRoute.params).toBeDefined();
    });

    it('should handle route params subscription', () => {
      const mockParams = { site_id: 'new-site', device_id: 'new-device' };
      component.route = {
        params: of(mockParams),
      } as any;

      component['initializeRouteParams']();

      expect(component.siteId).toBe('new-site');
      expect(component.deviceId).toBe('new-device');
    });
  });

  describe('Store Interactions', () => {
    it('should dispatch loadDeviceVersionsData action', () => {
      component.deviceId = 'test-device-456';

      component['dispatchDeviceVersionsData']();

      expect(storeDispatchSpy).toHaveBeenCalledWith(
        versionActions.loadDeviceVersionsData({ deviceId: 'test-device-456' })
      );
    });

    it('should select device version data from store', () => {
      component['getDeviceVersionsData']();

      expect(mockStore.select).toHaveBeenCalledWith(deviceVersionDataSelector);
    });

    it('should handle store subscription and process data', () => {
      component['getDeviceVersionsData']();

      expect(component.DeviceVersionsData).toEqual(mockDeviceVersionsData);
      expect(component.platformData).toEqual(mockDeviceVersionsData.platform);
      expect(component.vendorsData).toEqual(mockDeviceVersionsData.vendor);
      expect(component.exceptions).toEqual(mockDeviceVersionsData.exceptions);
    });
  });

  describe('Platform Data Processing', () => {
    beforeEach(() => {
      component['getDeviceVersionsData']();
    });

    it('should process platform data correctly', () => {
      expect(component.platformKeys).toEqual([
        { key: 'core', value: '1.0.0' },
        { key: 'ui', value: '2.1.0' },
        { key: 'kernel', value: '5.4.0' },
      ]);
    });

    it('should create original platform keys backup', () => {
      expect(component.originalPlatformKeys).toEqual(component.platformKeys);
      expect(component.originalPlatformKeys).not.toBe(component.platformKeys);
    });

    it('should handle platform data with complex key paths', () => {
      const complexPlatformData = [
        { 'platform.system.version.major.minor.patch': '1.2.3' },
      ] as VersionInfo[];

      storeSelectSpy.and.returnValue(
        of({
          ...mockDeviceVersionsData,
          platform: complexPlatformData,
        })
      );

      component['getDeviceVersionsData']();

      expect(component.platformKeys[0]).toEqual({
        key: 'patch',
        value: '1.2.3',
      });
    });

    it('should handle empty platform data', () => {
      storeSelectSpy.and.returnValue(
        of({
          ...mockDeviceVersionsData,
          platform: [],
        })
      );

      component['getDeviceVersionsData']();

      expect(component.platformData).toEqual([]);
      expect(component.platformKeys).toEqual([]);
    });
  });

  describe('Vendor Data Processing', () => {
    beforeEach(() => {
      component['getDeviceVersionsData']();
    });

    it('should process vendor data correctly', () => {
      expect(component.vendorsKeys).toEqual([
        { key: 'graphics', value: '460.32.03' },
        { key: 'bios', value: '2.15.1' },
        { key: 'antivirus', value: '21.3.0' },
      ]);
    });

    it('should create original vendor keys backup', () => {
      expect(component.originalVendorsKeys).toEqual(component.vendorsKeys);
      expect(component.originalVendorsKeys).not.toBe(component.vendorsKeys);
    });

    it('should handle vendor data with single key', () => {
      const singleVendorData = [{ driver: '1.0.0' }] as VersionInfo[];

      storeSelectSpy.and.returnValue(
        of({
          ...mockDeviceVersionsData,
          vendor: singleVendorData,
        })
      );

      component['getDeviceVersionsData']();

      expect(component.vendorsKeys[0]).toEqual({
        key: 'driver',
        value: '1.0.0',
      });
    });

    it('should handle empty vendor data', () => {
      storeSelectSpy.and.returnValue(
        of({
          ...mockDeviceVersionsData,
          vendor: [],
        })
      );

      component['getDeviceVersionsData']();

      expect(component.vendorsData).toEqual([]);
      expect(component.vendorsKeys).toEqual([]);
    });
  });

  describe('Exception Data Processing', () => {
    beforeEach(() => {
      component['getDeviceVersionsData']();
    });

    it('should process exceptions data correctly', () => {
      expect(component.exceptions).toEqual(mockDeviceVersionsData.exceptions);
      expect(component.filteredExceptions).toEqual(
        mockDeviceVersionsData.exceptions
      );
    });

    it('should handle empty exceptions data', () => {
      const emptyExceptionsData = {
        platform: [],
        vendor: [],
        exceptions: [],
      };
      storeSelectSpy.and.returnValue(of(emptyExceptionsData));

      component['getDeviceVersionsData']();

      expect(component.exceptions).toEqual([]);
    });

    it('should call filterExceptions when exceptions exist', () => {
      spyOn(component, 'filterExceptions');

      component['getDeviceVersionsData']();

      expect(component.filterExceptions).toHaveBeenCalled();
    });
  });

  describe('Search and Filter Functionality', () => {
    beforeEach(() => {
      component['getDeviceVersionsData']();
    });

    describe('onQueryChange', () => {
      it('should filter platform keys by key match', () => {
        component.onQueryChange('core');

        expect(component.platformKeys).toEqual([
          { key: 'core', value: '1.0.0' },
        ]);
      });

      it('should filter platform keys by value match', () => {
        component.onQueryChange('1.0.0');

        expect(component.platformKeys).toEqual([
          { key: 'core', value: '1.0.0' },
        ]);
      });

      it('should filter vendor keys by key match', () => {
        component.onQueryChange('graphics');

        expect(component.vendorsKeys).toEqual([
          { key: 'graphics', value: '460.32.03' },
        ]);
      });

      it('should filter vendor keys by value match', () => {
        component.onQueryChange('460.32.03');

        expect(component.vendorsKeys).toEqual([
          { key: 'graphics', value: '460.32.03' },
        ]);
      });

      it('should reset to original keys when query is empty', () => {
        component.onQueryChange('test');
        component.onQueryChange('');

        expect(component.platformKeys).toEqual(component.originalPlatformKeys);
        expect(component.vendorsKeys).toEqual(component.originalVendorsKeys);
      });

      it('should handle null values in filtering', () => {
        component.originalPlatformKeys = [
          { key: 'test', value: null },
          { key: 'valid', value: 'value' },
        ];
        component.originalVendorsKeys = [
          { key: 'test', value: null },
          { key: 'valid', value: 'value' },
        ];

        component.onQueryChange('test');

        expect(component.platformKeys).toEqual([{ key: 'test', value: null }]);
        expect(component.vendorsKeys).toEqual([{ key: 'test', value: null }]);
      });

      it('should call filterExceptions after filtering', () => {
        spyOn(component, 'filterExceptions');

        component.onQueryChange('test');

        expect(component.filterExceptions).toHaveBeenCalled();
      });

      it('should handle partial matches', () => {
        component.onQueryChange('gr');

        expect(component.vendorsKeys).toEqual([
          { key: 'graphics', value: '460.32.03' },
        ]);
      });

      it('should be case sensitive for key and value matching', () => {
        component.onQueryChange('CORE');

        expect(component.platformKeys).toEqual([]);
      });
    });

    describe('filterExceptions', () => {
      beforeEach(() => {
        component.exceptions = mockDeviceVersionsData.exceptions!;
      });

      it('should filter exceptions by metric (case insensitive)', () => {
        component.versionQuery = 'cpu';

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual([
          { metric: 'CPU Temperature', value: '85°C', expected: '70°C' },
        ]);
      });

      it('should filter exceptions by value (case insensitive)', () => {
        component.versionQuery = '95%';

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual([
          { metric: 'Memory Usage', value: '95%', expected: '80%' },
        ]);
      });

      it('should filter exceptions by expected value (case insensitive)', () => {
        component.versionQuery = '85%';

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual([
          { metric: 'Disk Space', value: '98%', expected: '85%' },
        ]);
      });

      it('should return all exceptions when query is empty', () => {
        component.versionQuery = '';

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual(component.exceptions);
      });

      it('should return all exceptions when query is undefined', () => {
        component.versionQuery = undefined as any;

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual(component.exceptions);
      });

      it('should handle exceptions with missing properties', () => {
        component.exceptions = [
          { metric: 'Test', value: null, expected: undefined },
          { metric: null, value: 'Test Value', expected: 'Expected' },
        ];
        component.versionQuery = 'test';

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual([
          { metric: 'Test', value: null, expected: undefined },
          { metric: null, value: 'Test Value', expected: 'Expected' },
        ]);
      });

      it('should handle case insensitive matching', () => {
        component.versionQuery = 'CPU';

        component.filterExceptions();

        expect(component.filteredExceptions).toEqual([
          { metric: 'CPU Temperature', value: '85°C', expected: '70°C' },
        ]);
      });
    });
  });

  describe('Text Highlighting', () => {
    it('should highlight matching text with span', () => {
      const result = component.highlightSearchText('test string', 'test');

      expect(result).toBe('<span class="text-highlight">test</span> string');
    });

    it('should handle case insensitive highlighting', () => {
      const result = component.highlightSearchText('Test String', 'test');

      expect(result).toBe('<span class="text-highlight">Test</span> String');
    });

    it('should highlight multiple matches', () => {
      const result = component.highlightSearchText('test test string', 'test');

      expect(result).toBe(
        '<span class="text-highlight">test</span> <span class="text-highlight">test</span> string'
      );
    });

    it('should return original string when query is empty', () => {
      const result = component.highlightSearchText('test string', '');

      expect(result).toBe('test string');
    });

    it('should return original string when query is falsy', () => {
      const result = component.highlightSearchText('test string', null as any);

      expect(result).toBe('test string');
    });

    it('should handle null input string', () => {
      const result = component.highlightSearchText(null, 'test');

      expect(result).toBeUndefined();
    });

    it('should handle undefined input string', () => {
      const result = component.highlightSearchText(undefined as any, 'test');

      expect(result).toBeUndefined();
    });

    it('should handle special regex characters in query', () => {
      const result = component.highlightSearchText('test.string', '.');

      // The current implementation treats '.' as a regex pattern, matching any character
      expect(result).toContain('<span class="text-highlight">');
    });

    it('should handle partial word matches', () => {
      const result = component.highlightSearchText('testing', 'test');

      expect(result).toBe('<span class="text-highlight">test</span>ing');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty device versions data', () => {
      storeSelectSpy.and.returnValue(of(mockEmptyDeviceVersionsData));

      component['getDeviceVersionsData']();

      expect(component.platformData).toEqual([]);
      expect(component.vendorsData).toEqual([]);
      expect(component.exceptions).toEqual([]);
    });

    it('should handle null device versions data', () => {
      storeSelectSpy.and.returnValue(of(null as any));

      expect(() => component['getDeviceVersionsData']()).not.toThrow();
    });

    it('should handle undefined device versions data', () => {
      storeSelectSpy.and.returnValue(of(undefined as any));

      expect(() => component['getDeviceVersionsData']()).not.toThrow();
    });

    it('should handle malformed version info objects', () => {
      const malformedData = {
        platform: [
          {},
          { '': 'empty-key' },
          { 'valid.key': 'valid-value' },
        ] as VersionInfo[],
        vendor: [],
        exceptions: [],
      };

      storeSelectSpy.and.returnValue(of(malformedData));

      component['getDeviceVersionsData']();

      // Component should handle malformed data gracefully
      expect(component.platformKeys.length).toBeGreaterThanOrEqual(0);
      expect(component.platformData).toEqual(malformedData.platform);
    });

    it('should handle route params with missing values', () => {
      component.route = {
        params: of({}),
      } as any;

      component['initializeRouteParams']();

      expect(component.siteId).toBeUndefined();
      expect(component.deviceId).toBeUndefined();
    });

    it('should handle store subscription errors gracefully', () => {
      storeSelectSpy.and.returnValue(of(null));

      expect(() => component['getDeviceVersionsData']()).not.toThrow();
    });
  });

  describe('Integration Tests', () => {
    it('should complete full initialization workflow', () => {
      spyOn(component, 'filterExceptions');
      // Set deviceId before ngOnInit since dispachDeviceVersionsData is called before initializeRouteParams
      component.deviceId = 'test-device-456';

      component.ngOnInit();

      expect(component.siteId).toBe('test-site-123');
      expect(component.deviceId).toBe('test-device-456');
      expect(storeDispatchSpy).toHaveBeenCalledWith(
        versionActions.loadDeviceVersionsData({ deviceId: 'test-device-456' })
      );
      expect(component.DeviceVersionsData).toEqual(mockDeviceVersionsData);
      expect(component.filterExceptions).toHaveBeenCalled();
    });

    it('should handle complete search workflow', () => {
      component.ngOnInit();

      component.onQueryChange('core');

      expect(component.platformKeys).toEqual([{ key: 'core', value: '1.0.0' }]);
      expect(component.vendorsKeys).toEqual([]);
    });

    it('should maintain data integrity throughout component lifecycle', () => {
      component.ngOnInit();

      const originalPlatformKeys = [...component.originalPlatformKeys];
      const originalVendorsKeys = [...component.originalVendorsKeys];

      component.onQueryChange('test');
      component.onQueryChange('');

      expect(component.platformKeys).toEqual(originalPlatformKeys);
      expect(component.vendorsKeys).toEqual(originalVendorsKeys);
    });
  });
});
