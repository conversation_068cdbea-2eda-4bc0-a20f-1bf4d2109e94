.versions-card {
  border: none;

  .versions-card-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0;
    align-items: flex-start;
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    h2 {
      font-size: 2rem;
      font-weight: 400;
      margin: 0.7rem 0;
    }

    .versions-search-input {
      input {
        margin-bottom: 0;
      }
    }
  }

  .device-version-body {
    .device-version-listing {
      padding: 1rem 1.5rem;

      &:last-child {
        .device-version-row:last-child {
          border: none;
        }
      }

      .heading {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--label-unknown);
        margin: 0.7rem 0;
      }

      .device-version-row {
        display: flex;
        border-bottom: 0.1rem solid var(--color-border);

        p {
          flex: 1;
        }

        .dversion-bold {
          font-weight: 700;
          font-size: 1.4rem;
          padding: 0.6rem 0;
          margin: 0;
        }

        .dversion-light {
          color: var(--color-black-shade-two);
          font-size: 1.4rem;
          padding: 0.6rem 3rem 0.6rem 1rem;
          margin: 0;
        }

        .text-highlight {
          background-color: var(--color-text-hightlight);
        }
      }
    }
  }

  .no-versions-found {
    padding: 1.6rem 0.938rem;
    text-align: center;
    color: var(--label-unknown);
    font-size: 1.4rem;
  }
}
