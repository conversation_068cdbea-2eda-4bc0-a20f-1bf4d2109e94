.pull-files-container {
  display: flex;
  justify-content: center;
  margin-left: 25%;
  width: 50%;
  position: relative;
  margin-top: -9.5rem;

  .pull-files-stepper-container {
    width: 100%;
    border-radius: 0.3rem;
    background: var(--color-white);

    .pull-files-input-title {
      padding: 1rem 1.5rem;
      width: 100%;
      margin-top: 1.6rem;
      height: 8.6rem;

      .form-group {
        position: relative;
      }

      .pull-file-title {
        width: 100%;

        input {
          font-size: 2.4rem;
          font-weight: 300;
          line-height: 1;
          height: 3.4rem;
          padding: 1rem 2rem 1rem 1.5rem;
          width: 100%;
          transition:
            border-color ease-in-out 0.15s,
            box-shadow ease-in-out 0.15s;
          border-radius: 0.3rem;
          background-color: var(--color-white);
          border: 0.1rem solid var(--color-white);
          outline: none;
          box-shadow: none;

          &::placeholder {
            opacity: 1;
            font-size: 2.4rem;
            color: var(--color-black-shade-two);
          }

          &:hover {
            border: 0.1rem solid var(--color-border) !important;
            background-color: var(--color-bg-fa);
          }

          &:focus {
            border-color: var(--color-primary) !important;
            background-color: var(--color-white);
            box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
          }
        }

        i {
          position: absolute;
          top: 50%;
          right: 1.6rem;
          transform: translateY(-50%);
          opacity: 0.3;
          font-size: 1.4rem;

          &:hover {
            opacity: 0.5;
          }

          &:focus {
            opacity: 0;
          }
        }
      }

      .pull-files-input-error {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 1.4rem;
        margin-top: -0.8rem;

        .left-container {
          color: var(--color-bg-red);
        }

        .right-container {
          .zero-left-error {
            color: var(--color-bg-red);
          }
        }
      }
    }

    .pull-files-stepper {
      margin-top: -1.6rem;
    }

    .pull-files-btn-container {
      margin-left: 2.5rem;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 2.4rem;
      gap: 2rem;

      .copy-files-btn {
        padding: 0.6rem 1.2rem;
        pointer-events: all;

        &:disabled {
          opacity: 0.65;
          cursor: not-allowed;
          box-shadow: none;
        }
      }

      .copy-file-cancel-btn {
        border-color: var(--btn-cancel-color);
        background-color: var(--btn-cancel-color);
        font-weight: 500;
        font-size: 1.4rem;
        padding: 0.6rem 1.2rem;
        color: var(--color-black);
      }
    }
  }
}

.pull-from-btn {
  width: 104%;
  margin-top: 2rem;

  .pull-from-button {
    width: fit-content;
    border-radius: 0.3rem;
    border: none;
    font-size: 1.4rem;
    color: var(--color-white);
    transition: all ease-in 0.1s;
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    padding-left: 2rem;
    padding-right: 2rem;
    box-shadow:
      0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
      0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
      box-shadow: none;

      &:hover {
        box-shadow: none;
        background-color: var(--color-primary);
      }
    }
  }
}
