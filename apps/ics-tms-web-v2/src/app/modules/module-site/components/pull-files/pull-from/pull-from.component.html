<form
  class="pull-from-container"
  [ngClass]="{
    'd-none': activeStepIndex !== 0,
  }"
>
  <div class="pull-from-options">
    <div
      class="option-btn"
      [ngClass]="{ 'option-btn-active': selectedOption === CURRENT_DEVICE }"
      (click)="
        selectedOption = CURRENT_DEVICE; selectedTab.emit(CURRENT_DEVICE)
      "
    >
      Current Device
    </div>
    <div
      class="option-btn"
      [ngClass]="{ 'option-btn-active': selectedOption === CURRENT_SITE }"
      (click)="selectedOption = CURRENT_SITE; selectedTab.emit(CURRENT_SITE)"
    >
      Current Site
    </div>
    <div
      class="option-btn"
      [ngClass]="{ 'option-btn-active': selectedOption === SITE_TAGS }"
      (click)="selectedOption = SITE_TAGS; selectedTab.emit(SITE_TAGS)"
    >
      Site Tags
    </div>
  </div>
  <div class="selected-option-body">
    <div
      class="current-site-or-tags"
      *ngIf="selectedOption === CURRENT_SITE || selectedOption === SITE_TAGS"
    >
      Files will be pulled only from EDGE devices that are either Operational or
      Out-of-Service
    </div>
    <div class="select-tags" *ngIf="selectedOption === SITE_TAGS">
      <app-ics-gray-tags-multiselect
        [TagsData]="formGroup.get('tags')?.value"
        placeHolderText="Search or filter by tags"
        (tagsToParent)="getSelectedTags($event)"
        [usage]="'pullFrom'"
        [placeHolderText]="'Select tags'"
      ></app-ics-gray-tags-multiselect>
    </div>
    <div
      class="sites-count"
      *ngIf="selectedSiteByTags.length && selectedOption === SITE_TAGS"
    >
      <button
        type="button"
        class="count"
        (click)="isCollapsed = !isCollapsed"
        [attr.aria-expanded]="!isCollapsed"
        aria-controls="collapseExample"
        (click)="onCollapse()"
      >
        <i class="fa-solid fa-caret-right caret" *ngIf="!collapseSiteCount"></i>
        <i class="fa-solid fa-caret-down caret" *ngIf="collapseSiteCount"></i>
        <span class="text">{{ selectedSiteByTags.length }} sites found</span>
      </button>
      <div #collapse="ngbCollapse" [(ngbCollapse)]="isCollapsed">
        <div class="site-collapse">
          <ul class="site-list">
            <li class="site-name" *ngFor="let site of selectedSiteByTags">
              {{ site['name'] }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="pull-from-btn">
    <button matStepperNext class="btn-primary pull-from-button">
      Continue
    </button>
  </div>
</form>

<div
  *ngIf="formGroup.valid && activeStepIndex !== 0"
  class="selected-tags-container"
>
  <div class="tags" *ngIf="selectedOption === SITE_TAGS">
    <div class="tags-card" *ngFor="let tag of selectedTags">
      {{ tag }}
    </div>
  </div>
  <span class="selectedText" *ngIf="selectedOption === CURRENT_DEVICE"
    >Current Device</span
  >
  <span class="selectedText" *ngIf="selectedOption === CURRENT_SITE"
    >Current Site</span
  >
</div>
