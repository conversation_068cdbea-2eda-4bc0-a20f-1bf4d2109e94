<form
  [ngClass]="{
    'd-visible': activeStepIndex === 3,
    'd-none': activeStepIndex !== 3,
  }"
>
  <div class="add-notifications-container">
    <app-ics-searchable-dropdown
      [items]="(users$ | async)?.results!"
      [selectedItems]="(selectedUsers$ | async)!"
      (selectionChange)="updateMembers($event)"
      [required]="true"
      [filterParams]="['fullName', 'email']"
      [placeholder]="'Add a person'"
      [emptyStateMessage]="'Add some people to this team'"
      [displayField]="'fullName'"
      [clearConditionFn]="checkIfClearDisabled"
    >
    </app-ics-searchable-dropdown>

    <div class="added-users-list">
      <div *ngFor="let user of selectedUsers" class="user-card">
        <div class="left-container">
          <div class="avatar-img">
            <span>{{ getStringInitials(user.fullName).substring(0, 2) }}</span>
          </div>
          <div class="user-names">
            <p>{{ user.fullName }}</p>
            <p class="user-desc" *ngIf="user.id === currentUserData.sub">
              You automatically receive this request
            </p>
          </div>
        </div>
        <i
          *ngIf="user.id !== currentUserData.sub"
          (click)="removeUserAndAddToDD(user)"
          class="fas fa-times"
        ></i>
      </div>
    </div>

    <app-ics-searchable-dropdown
      [items]="(userGroups$ | async)?.results ?? []"
      [selectedItems]="(selectedUserGroups$ | async)!"
      (selectionChange)="updateUserGroups($event)"
      [filterParams]="['name']"
      [placeholder]="'Add a team'"
      [displayField]="'name'"
      [clearConditionFn]="checkIfClearDisabled"
    >
    </app-ics-searchable-dropdown>

    <div *ngIf="selectedUsers.length > 1" class="notification-desc-banner">
      <div class="icon-box">
        <mat-icon>info</mat-icon>
      </div>
      <div class="desc-banner">
        Additional users will see all of the files/folders you selected for all
        of the sites requested.
      </div>
    </div>
  </div>
</form>
