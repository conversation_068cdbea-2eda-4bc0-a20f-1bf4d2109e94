.select-file-container {
  box-shadow:
    0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
    0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.14),
    0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
  width: 104%;

  .select-file-table-heading {
    display: flex;
    align-items: center;
    box-shadow: 0 0.2rem 0.8rem -0.2rem rgba(0, 0, 0, 0.5);
    background-color: var(--color-bg-fa);
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--color-black-shade-two);
    padding: 1rem 1.5rem;
    position: relative;
    z-index: 5;

    .file-icon-col {
      width: 8.33333333%;
    }

    .selectAllFileInput {
      height: fit-content;
      font-size: 1.6rem;
      font-weight: 700;
      transform: scale(1.1);
      color: var(--label-unknown);
      cursor: pointer;
    }

    .application-col {
      width: 25%;
    }

    .name-path-col {
      width: 41.66666667%;
    }

    .last-snapshot-col {
      width: 25%;
    }
  }

  .select-files-row {
    height: 28.4rem;
    overflow-x: hidden;
    cursor: pointer;

    .selectedFileBGBlue {
      background-color: var(--list-group-item-selected);

      &:hover {
        background-color: var(--list-group-item-selected) !important;
      }
    }

    .file-row {
      display: flex;
      align-items: flex-start;
      padding: 1rem 1.5rem;
      border-bottom: 0.1rem solid var(--color-border);

      &:hover {
        background-color: var(--list-hover-color);
      }

      .file-icon-col-row {
        width: 8.33333333%;
        text-align: center;

        .file-icon {
          color: var(--color-folder-icon);
          font-size: 1.3rem;
        }

        .fa-sync-alt {
          margin-left: -1.6rem;
          font-size: 1.4rem;
          color: var(--color-white);
          background: var(--label-info);
          padding: 0.2rem;
          border-radius: 50%;
          animation: icon-spin 2s infinite linear;
        }

        @keyframes icon-spin {
          0% {
            transform: rotate(0);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }

      .selectFileInput {
        height: fit-content;
        font-size: 1.6rem;
        font-weight: 700;
        transform: scale(1.1);
        color: var(--label-unknown);
        cursor: pointer;
      }

      .name-path-col-row {
        width: 41.66666667%;

        .fileName {
          font-size: 1.4rem;
          font-weight: 500;
        }

        .filePath {
          font-size: 1.3rem;
          font-weight: normal;
        }
      }

      .application-col-row {
        width: 25%;
        font-size: 1.3rem;
      }

      .last-snapshot-col-row {
        width: 25%;
        font-size: 1.3rem;
      }
    }
  }
}

.select-file-or-directory-btn {
  padding-top: 1.6rem;
  width: 104%;
  margin-top: 2.5rem;

  .pull-from-button {
    width: fit-content;
    border-radius: 0.3rem;
    border: none;
    font-size: 1.4rem;
    color: var(--color-white);
    transition: all ease-in 0.1s;
    font-weight: 500;
    padding: 0.7rem 1.7rem;
    box-shadow:
      0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
      0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
      box-shadow: none;

      &:hover {
        box-shadow: none;
        background-color: var(--color-primary);
      }
    }
  }
}

.selectedFiles-pull-files {
  padding-left: 1.6rem;
  padding-top: 1.6rem;
  display: flex;
  font-size: 1.4rem;
  gap: 2rem;

  .heading {
    font-size: 1.2rem;
    line-height: 1.5;
    text-align: right;
    font-weight: 500;
    color: var(--text-color-gray);
  }

  .file-names {
    display: flex;
    flex-direction: column;
  }
}
