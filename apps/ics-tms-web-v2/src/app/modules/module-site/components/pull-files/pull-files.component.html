<div class="pull-files-container">
  <div class="pull-files-stepper-container">
    <div class="pull-files-input-title header">
      <div class="form-group pull-file-title">
        <input
          type="text"
          name="pullFileTitle"
          id="pullFileTitle"
          [maxlength]="100"
          [(ngModel)]="pullFileTitle"
          (focus)="onInputFocus()"
          (blur)="onInputBlur()"
          autocomplete="off"
        />
        <i class="fa-solid fa-pen" [hidden]="isIconHidden"></i>
      </div>

      <div class="pull-files-input-error">
        <div class="left-container">
          <div *ngIf="pullFileTitle.trim().length === 1">
            Invalid name! Alphanumerics only.
          </div>
          <div *ngIf="pullFileTitle.trim().length <= 0">
            A name is required.
          </div>
        </div>
        <div class="right-container">
          <div
            *ngIf="
              pullFileTitle.trim().length >= 90 &&
              pullFileTitle.trim().length < 100
            "
          >
            {{ 100 - pullFileTitle.trim().length }} left
          </div>
          <div
            class="zero-left-error"
            *ngIf="pullFileTitle.trim().length >= 100"
          >
            0 left
          </div>
        </div>
      </div>
    </div>

    <div class="pull-files-stepper">
      <mat-stepper
        orientation="vertical"
        [linear]="true"
        #stepper
        (selectionChange)="onStepChange($event)"
        class="stepper"
      >
        <ng-template matStepperIcon="edit">
          <mat-icon class="check-circle">check_circle</mat-icon>
        </ng-template>

        <mat-step [completed]="isPullFormValid" [stepControl]="pullFrom">
          <ng-template matStepLabel>Pull From</ng-template>
          <app-pull-from
            [formGroup]="pullFrom"
            [activeStepIndex]="activeStepIndex"
            (selectedTab)="selectedOptionPullFrom($event)"
            (selectedTagsData)="getSelectedTags($event)"
          >
          </app-pull-from>
        </mat-step>

        <mat-step [stepControl]="selectFileOrDirectory">
          <ng-template matStepLabel>Select Files/Directory</ng-template>
          <app-select-file-or-directories
            [formGroup]="selectFileOrDirectory"
            [activeStepIndex]="activeStepIndex"
          >
          </app-select-file-or-directories>
        </mat-step>

        <mat-step [completed]="isDateStepComplete">
          <ng-template matStepLabel>Select Date Range</ng-template>
          <form
            [ngClass]="{
              'd-visible': activeStepIndex === 2,
              'd-none': activeStepIndex !== 2,
            }"
          >
            <app-ics-select-dates
              (selectedStartDate)="getStartDate($event)"
              (selectedEndDate)="getEndDate($event)"
              (clickUpdate)="onClickUpdate()"
              [type]="'pull-files'"
              [useMaxDate]="false"
              [dateRanges]="PULL_DATE_RANGES"
            ></app-ics-select-dates>

            <div class="pull-from-btn">
              <button matStepperNext class="btn-primary pull-from-button">
                Continue
              </button>
            </div>
          </form>
        </mat-step>

        <mat-step [stepControl]="notification">
          <ng-template matStepLabel>Add Notifications</ng-template>
          <app-add-notification
            [formGroup]="notification"
            [activeStepIndex]="activeStepIndex"
          >
          </app-add-notification>
        </mat-step>
      </mat-stepper>
    </div>

    <div class="pull-files-btn-container">
      <button
        (click)="handleCreatePackage()"
        [disabled]="isBtnValid()"
        class="stepper-step-btn btn btn-primary btn-box-shadow copy-files-btn"
      >
        Create Request
      </button>
      <button
        (click)="onClickCancel()"
        class="btn btn-cancel copy-file-cancel-btn"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
