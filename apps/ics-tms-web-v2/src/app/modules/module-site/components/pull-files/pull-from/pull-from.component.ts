import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { SitesResults } from '../../../../../models/sites.model';
import {
  CURRENT_DEVICE,
  CURRENT_SITE,
  SITE_TAGS,
} from '../../../constants/appConstants';

@Component({
  selector: 'app-pull-from',
  templateUrl: './pull-from.component.html',
  styleUrls: ['./pull-from.component.scss'],
})
export class PullFromComponent {
  @Input() formGroup!: FormGroup;

  @Input() activeStepIndex!: number;

  selectedOption = CURRENT_DEVICE;

  selectedSiteByTags: SitesResults[] = [];

  sites: SitesResults[] = [];

  isCollapsed = true;

  collapseSiteCount = false;

  selectedTags: string[] = [];

  protected readonly CURRENT_DEVICE = CURRENT_DEVICE;

  protected readonly CURRENT_SITE = CURRENT_SITE;

  protected readonly SITE_TAGS = SITE_TAGS;

  @Output() selectedTab = new EventEmitter<string>();

  @Output() selectedTagsData = new EventEmitter<string[]>();

  getSelectedTags(event: string[]) {
    const sites: SitesResults[] = this.formGroup.get('sites')?.value;
    this.selectedTags = event;
    this.selectedSiteByTags = sites.filter(
      site => site.tags?.some(tag => event.includes(tag.name)) || false
    );
    this.selectedTagsData.emit(this.selectedTags);
    this.selectedTab.emit(this.selectedOption);
  }

  onCollapse() {
    this.collapseSiteCount = !this.collapseSiteCount;
  }
}
