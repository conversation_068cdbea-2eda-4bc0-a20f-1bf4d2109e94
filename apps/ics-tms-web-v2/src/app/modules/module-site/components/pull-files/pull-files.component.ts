import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { Observable, Subject, takeUntil } from 'rxjs';
import { DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { loadSites } from '../../../../store/actions/sites.actions';
import { selectSitesData } from '../../../../store/selectors/sites.selectors';
import { loadTags } from '../../../../store/actions/tags.actions';
import { selectTagsData } from '../../../../store/selectors/tags.selectors';
import { loadDeviceFiles } from '../../store/actions/device-file.actions';
import { deviceFileDataSelector } from '../../store/selectors/device-file.selectors';
import { DeviceFile } from '../../models/device-file.modal';
import {
  CURRENT_DEVICE,
  CURRENT_SITE,
  SITE_TAGS,
} from '../../constants/appConstants';
import { devicesSelector } from '../../store/selectors/devices.selector';
import { DeviceData } from '../../models/devices.interface';
import * as devicesActions from '../../store/actions/devices.actions';
import { Tags } from '../../../../models/tags.model';
import { getApiConstants } from '../../constants/api';
import { ToastService } from 'src/app/services/toast.service';
import { setHeaderName } from 'src/app/store/actions/globalStore.actions';
import { DEVICE_OVERVIEW } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-pull-files',
  templateUrl: './pull-files.component.html',
  styleUrls: ['./pull-files.component.scss'],
})
export class PullFilesComponent implements OnInit {
  @Output() selectedTab = new EventEmitter<string>();

  hasTyped = false;

  isIconHidden = false;

  pullFileTitle = '00 NCR-Shell EPS 001 NCR-EPS 2024-01-25-1314';

  activeStepIndex = 0;

  parentForm: FormGroup;

  filesData: DeviceFile[] = [];

  isPullFormValid = true;

  selectedTags: string[] = [];

  containTags = false;

  selectedFiles: DeviceFile[] = [];

  devicesData$: Observable<any>;

  deviceData!: DeviceData;

  private siteId!: string;

  private deviceId!: string;

  currentPullFromTab = CURRENT_DEVICE;

  tagsData: Tags[] = [];

  isDateStepComplete = false;

  selectedStartDate!: string;

  selectedEndDate!: string;

  private destroy$ = new Subject<void>();

  PULL_DATE_RANGES = [
    'Today',
    'Yesterday',
    'Last 24 Hours',
    'Last 30 Days',
    'Last 90 Days',
    'Custom',
  ];

  store = inject(Store);

  route = inject(ActivatedRoute);

  deviceStore = inject(Store<any>);

  http = inject(HttpClient);

  toastService = inject(ToastService);

  router = inject(Router);

  formBuilder = inject(FormBuilder);

  constructor() {
    this.parentForm = this.formBuilder.group({
      pullFrom: this.formBuilder.group({
        sites: [[]],
        tags: [[]],
      }),
      selectFileOrDirectory: this.formBuilder.group({
        file: [[]],
        fileSize: [''],
        selectedFiles: [[]],
      }),
      notification: this.formBuilder.group({
        notifications: [],
        selectedNotification: [[]],
      }),
    });
    this.devicesData$ = this.deviceStore.pipe(select(devicesSelector));
  }

  ngOnInit() {
    this.getPullFilesData();
    this.initializeRouteParams();
    this.initializePullFilesDispatch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeRouteParams(): void {
    this.route.params.subscribe(params => {
      this.siteId = params['site_id'];
      this.deviceId = params['device_id'];
    });
  }

  private initializePullFilesDispatch(): void {
    this.store.dispatch(
      loadSites({
        params: {
          autoPoll: false,
          pageIndex: 0,
          pageSize: -1,
          showHiddenSites: true,
        },
        replace: true,
      })
    );
    this.store.dispatch(loadTags());

    this.store.dispatch(loadDeviceFiles({ deviceId: this.deviceId }));
  }

  private getPullFilesData(): void {
    this.store
      .select(selectSitesData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.parentForm.get('pullFrom')?.get('sites')?.setValue(data.results);
      });
    this.store
      .select(deviceFileDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.filesData = data;
        this.parentForm
          .get('selectFileOrDirectory')
          ?.get('file')
          ?.setValue(this.filesData);
        this.parentForm
          .get('selectFileOrDirectory')
          ?.get('fileSize')
          ?.setValue(this.filesData.length);
      });
    this.store
      .select(selectTagsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.tagsData = data;
        this.parentForm.get('pullFrom')?.get('tags')?.setValue(data);
      });
    this.store.dispatch(
      devicesActions.getData({ siteId: this.siteId, deviceId: this.deviceId })
    );
    this.devicesData$.subscribe(data => {
      this.deviceData = data.devicesData.devicesReducers.data;
      this.pullFileTitle = `${this.deviceData.siteName} ${
        this.deviceData.name
      } ${this.getCurrentTimeNumber()}`;
    });
  }

  getStartDate($event: string) {
    const date = new Date($event);
    date.setUTCDate(date.getUTCDate() - 1);
    date.setUTCHours(18);
    date.setUTCMinutes(30);
    date.setUTCSeconds(0);
    date.setUTCMilliseconds(0);
    this.selectedStartDate = date.toISOString().substring(0, 10);
  }

  getEndDate($event: string) {
    const date = new Date($event);
    date.setUTCDate(date.getUTCDate());
    date.setUTCHours(18);
    date.setUTCMinutes(29);
    date.setUTCSeconds(59);
    date.setUTCMilliseconds(999);
    this.selectedEndDate = date.toISOString().substring(0, 10);
  }

  onClickUpdate() {
    this.isDateStepComplete = !!(
      this.selectedStartDate && this.selectedEndDate
    );
  }

  getCurrentTimeNumber() {
    const currentDate = new Date();
    const datePipe = new DatePipe('en-US');
    return datePipe.transform(currentDate, 'yyyy-MM-dd-HHmm');
  }

  onInputFocus() {
    this.isIconHidden = true;
    this.hasTyped = false;
  }

  onInputBlur() {
    this.isIconHidden = false;
    this.hasTyped = true;
  }

  onStepChange(event: StepperSelectionEvent) {
    this.activeStepIndex = event.selectedIndex;
  }

  get pullFrom() {
    return this.parentForm.get('pullFrom') as FormGroup;
  }

  get selectFileOrDirectory() {
    return this.parentForm.get('selectFileOrDirectory') as FormGroup;
  }

  get notification() {
    return this.parentForm.get('notification') as FormGroup;
  }

  selectedOptionPullFrom(event: string) {
    this.currentPullFromTab = event;
    if (event === CURRENT_DEVICE || event === CURRENT_SITE) {
      this.isPullFormValid = true;
    } else if (event === SITE_TAGS) {
      this.isPullFormValid = this.containTags;
    } else {
      this.isPullFormValid = false;
    }
  }

  isBtnValid() {
    this.selectedFiles = this.selectFileOrDirectory.get('selectedFiles')?.value;
    return !(
      this.isPullFormValid &&
      this.selectedFiles.length > 0 &&
      this.pullFileTitle.trim().length > 1 &&
      this.isDateStepComplete
    );
  }

  getSelectedTags(event: string[]) {
    this.selectedTags = event;
    this.containTags = event.length > 0;
  }

  handleCreatePackage() {
    const selectedFilesDirectory: DeviceFile[] =
      this.selectFileOrDirectory.get('selectedFiles')?.value;
    const selectedFileIds = selectedFilesDirectory.map(file => file.id);
    const filteredFiles = this.filesData.filter(file =>
      selectedFileIds.includes(file.id)
    );
    const transformedFiles = filteredFiles.map(file => ({
      path: file.filePath,
      applicationId: file.applicationId,
    }));

    const params: {
      name: string;
      files: { path: string; applicationId: string }[];
      devices: { id: string | number }[];
      sites: { id: string | number }[];
      siteTags: { id: string | number }[];
      users: { id: string }[];
      endDate: string;
      startDate: string;
    } = {
      name: this.pullFileTitle,
      files: transformedFiles,
      devices: [],
      sites: [],
      siteTags: [],
      users: [],
      endDate: this.selectedEndDate,
      startDate: this.selectedStartDate,
    };

    if (this.currentPullFromTab === CURRENT_DEVICE) {
      params.devices = [{ id: this.deviceId }];
      params.sites = [];
      params.siteTags = [];
    } else if (this.currentPullFromTab === CURRENT_SITE) {
      params.devices = [];
      params.sites = [{ id: this.siteId }];
      params.siteTags = [];
    } else {
      params.devices = [];
      params.sites = [];
      params.siteTags = this.tagsData
        .filter(tag => this.selectedTags.includes(tag.name))
        .map(tag => ({ id: tag.id }));
    }

    params.users = this.notification
      .get('selectedNotification')
      ?.value.map((user: DeviceData) => ({
        id: user.id,
      }));

    this.http
      .post(getApiConstants().device.pullFiles.postFileUploadRequests, params)
      .subscribe({
        next: () => {
          this.toastService.show({
            message:
              "File request sent. We'll notify you when it's ready to download.",
          });
          this.router.navigate([
            `/sites/${this.siteId}/${this.deviceId}/overview`,
          ]);
        },
      });
  }

  onClickCancel() {
    this.router.navigate([`/sites/${this.siteId}/${this.deviceId}/overview`]);
    this.store.dispatch(setHeaderName({ name: DEVICE_OVERVIEW }));
  }
}
