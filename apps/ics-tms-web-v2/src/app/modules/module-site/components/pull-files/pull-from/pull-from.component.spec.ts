import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SitesResults } from '../../../../../models/sites.model';
import {
  CURRENT_DEVICE,
  CURRENT_SITE,
  SITE_TAGS,
} from '../../../constants/appConstants';
import { PullFromComponent } from './pull-from.component';

describe('PullFromComponent', () => {
  let component: PullFromComponent;
  let fixture: ComponentFixture<PullFromComponent>;
  let mockFormGroup: FormGroup;

  const mockSitesResults: SitesResults[] = [
    {
      id: '1',
      siteId: 'site-1',
      name: 'Site One',
      siteName: 'Site One',
      formattedAddress: '123 Main St, City, State',
      siteTags: ['retail', 'urban'],
      totalDevices: 5,
      address: '123 Main St',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0001',
      latitude: 40.7128,
      longitude: -74.006,
      status: 1,
      visible: true,
      tags: [{ id: 1, name: 'retail', siteCount: 10 }] as [
        { id: number; name: string; siteCount: number },
      ],
    },
    {
      id: '2',
      siteId: 'site-2',
      name: 'Site Two',
      siteName: 'Site Two',
      formattedAddress: '456 Oak Ave, Town, State',
      siteTags: ['gas-station', 'suburban'],
      totalDevices: 3,
      address: '456 Oak Ave',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0002',
      latitude: 41.8781,
      longitude: -87.6298,
      status: 1,
      visible: true,
      tags: [{ id: 3, name: 'gas-station', siteCount: 8 }] as [
        { id: number; name: string; siteCount: number },
      ],
    },
    {
      id: '3',
      siteId: 'site-3',
      name: 'Site Three',
      siteName: 'Site Three',
      formattedAddress: '789 Pine Rd, Village, State',
      siteTags: ['retail', 'rural'],
      totalDevices: 2,
      address: '789 Pine Rd',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0003',
      latitude: 34.0522,
      longitude: -118.2437,
      status: 1,
      visible: true,
      tags: [{ id: 1, name: 'retail', siteCount: 10 }] as [
        { id: number; name: string; siteCount: number },
      ],
    },
    {
      id: '4',
      siteId: 'site-4',
      name: 'Site Four',
      siteName: 'Site Four',
      formattedAddress: '321 Elm St, Metro, State',
      siteTags: ['convenience'],
      totalDevices: 4,
      address: '321 Elm St',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0004',
      latitude: 39.7392,
      longitude: -104.9903,
      status: 1,
      visible: true,
      tags: [{ id: 6, name: 'convenience', siteCount: 7 }] as [
        { id: number; name: string; siteCount: number },
      ],
    },
    {
      id: '5',
      siteId: 'site-5',
      name: 'Site Five',
      siteName: 'Site Five',
      formattedAddress: '654 Maple Dr, Suburb, State',
      siteTags: [],
      totalDevices: 1,
      address: '654 Maple Dr',
      contactEmail: '<EMAIL>',
      contactPhone: '555-0005',
      latitude: 25.7617,
      longitude: -80.1918,
      status: 0,
      visible: false,
      tags: undefined,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PullFromComponent],
      imports: [ReactiveFormsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(PullFromComponent);
    component = fixture.componentInstance;

    mockFormGroup = new FormGroup({
      sites: new FormControl(mockSitesResults),
      selectedOption: new FormControl(CURRENT_DEVICE),
      selectedTags: new FormControl([]),
    });

    component.formGroup = mockFormGroup;
    component.activeStepIndex = 1;

    fixture.detectChanges();
  });

  afterEach(() => {
    fixture.destroy();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.selectedOption).toBe(CURRENT_DEVICE);
      expect(component.selectedSiteByTags).toEqual([]);
      expect(component.sites).toEqual([]);
      expect(component.isCollapsed).toBe(true);
      expect(component.collapseSiteCount).toBe(false);
      expect(component.selectedTags).toEqual([]);
    });

    it('should have required input properties', () => {
      expect(component.formGroup).toBeDefined();
      expect(component.activeStepIndex).toBe(1);
    });

    it('should have protected readonly constants', () => {
      expect(CURRENT_DEVICE).toBe('currentDevice');
      expect(CURRENT_SITE).toBe('currentSite');
      expect(SITE_TAGS).toBe('siteTags');
    });

    it('should initialize selectedOption with CURRENT_DEVICE constant', () => {
      const newComponent = new PullFromComponent();
      expect(newComponent.selectedOption).toBe(CURRENT_DEVICE);
    });

    it('should initialize arrays as empty', () => {
      const newComponent = new PullFromComponent();
      expect(newComponent.selectedSiteByTags).toEqual([]);
      expect(newComponent.sites).toEqual([]);
      expect(newComponent.selectedTags).toEqual([]);
    });

    it('should initialize boolean flags correctly', () => {
      const newComponent = new PullFromComponent();
      expect(newComponent.isCollapsed).toBe(true);
      expect(newComponent.collapseSiteCount).toBe(false);
    });
  });

  describe('Output EventEmitters', () => {
    it('should have selectedTab EventEmitter', () => {
      expect(component.selectedTab).toBeDefined();
      expect(component.selectedTab.emit).toEqual(jasmine.any(Function));
    });

    it('should have selectedTagsData EventEmitter', () => {
      expect(component.selectedTagsData).toBeDefined();
      expect(component.selectedTagsData.emit).toEqual(jasmine.any(Function));
    });
  });

  describe('getSelectedTags method', () => {
    beforeEach(() => {
      spyOn(component.selectedTagsData, 'emit');
      spyOn(component.selectedTab, 'emit');
    });

    it('should filter sites by selected tags', () => {
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTags).toEqual(['retail']);
      expect(component.selectedSiteByTags.length).toBe(2);
      expect(component.selectedSiteByTags[0].id).toBe('1');
      expect(component.selectedSiteByTags[1].id).toBe('3');
    });

    it('should filter sites by multiple tags', () => {
      const selectedTags = ['retail', 'gas-station'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTags).toEqual(['retail', 'gas-station']);
      expect(component.selectedSiteByTags.length).toBe(3);
      expect(component.selectedSiteByTags.map(site => site.id)).toEqual([
        '1',
        '2',
        '3',
      ]);
    });

    it('should return empty array when no sites match selected tags', () => {
      const selectedTags = ['nonexistent-tag'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTags).toEqual(['nonexistent-tag']);
      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should handle empty tags array', () => {
      const selectedTags: string[] = [];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTags).toEqual([]);
      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should emit selectedTagsData with selected tags', () => {
      const selectedTags = ['retail', 'urban'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTagsData.emit).toHaveBeenCalledWith([
        'retail',
        'urban',
      ]);
    });

    it('should emit selectedTab with current selectedOption', () => {
      const selectedTags = ['retail'];
      component.selectedOption = CURRENT_SITE;
      component.getSelectedTags(selectedTags);

      expect(component.selectedTab.emit).toHaveBeenCalledWith(CURRENT_SITE);
    });

    it('should handle sites with null tags', () => {
      const sitesWithNullTags: SitesResults[] = [
        {
          id: '6',
          name: 'Site Six',
          tags: undefined,
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithNullTags);
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should handle sites with undefined tags', () => {
      const sitesWithUndefinedTags: SitesResults[] = [
        {
          id: '7',
          name: 'Site Seven',
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithUndefinedTags);
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should handle sites with empty tags array', () => {
      const sitesWithEmptyTags: SitesResults[] = [
        {
          id: '8',
          name: 'Site Eight',
          tags: undefined,
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithEmptyTags);
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should handle case-sensitive tag matching', () => {
      const selectedTags = ['RETAIL'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should handle partial tag name matching', () => {
      const selectedTags = ['ret'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should filter sites correctly when tags contain special characters', () => {
      const sitesWithSpecialTags: SitesResults[] = [
        {
          id: '9',
          name: 'Site Nine',
          tags: [{ id: 7, name: 'tag-with-dash', siteCount: 1 }] as [
            { id: number; name: string; siteCount: number },
          ],
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithSpecialTags);
      const selectedTags = ['tag-with-dash'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(1);
      expect(component.selectedSiteByTags[0].id).toBe('9');
    });

    it('should handle null sites array from form control', () => {
      mockFormGroup.get('sites')?.setValue(null);
      const selectedTags = ['retail'];

      expect(() => component.getSelectedTags(selectedTags)).toThrow();
    });

    it('should handle undefined sites array from form control', () => {
      mockFormGroup.get('sites')?.setValue(undefined);
      const selectedTags = ['retail'];

      expect(() => component.getSelectedTags(selectedTags)).toThrow();
    });

    it('should handle when sites form control does not exist', () => {
      mockFormGroup.removeControl('sites');
      const selectedTags = ['retail'];

      expect(() => component.getSelectedTags(selectedTags)).toThrow();
    });

    it('should update selectedTags before filtering', () => {
      component.selectedTags = ['old-tag'];
      const selectedTags = ['new-tag'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTags).toEqual(['new-tag']);
      expect(component.selectedTags).not.toContain('old-tag');
    });

    it('should emit events after filtering is complete', () => {
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTagsData.emit).toHaveBeenCalledTimes(1);
      expect(component.selectedTab.emit).toHaveBeenCalledTimes(1);
    });
  });

  describe('onCollapse method', () => {
    it('should toggle collapseSiteCount from false to true', () => {
      component.collapseSiteCount = false;
      component.onCollapse();
      expect(component.collapseSiteCount).toBe(true);
    });

    it('should toggle collapseSiteCount from true to false', () => {
      component.collapseSiteCount = true;
      component.onCollapse();
      expect(component.collapseSiteCount).toBe(false);
    });

    it('should toggle collapseSiteCount multiple times correctly', () => {
      expect(component.collapseSiteCount).toBe(false);

      component.onCollapse();
      expect(component.collapseSiteCount).toBe(true);

      component.onCollapse();
      expect(component.collapseSiteCount).toBe(false);

      component.onCollapse();
      expect(component.collapseSiteCount).toBe(true);
    });

    it('should not affect other component properties', () => {
      const originalSelectedOption = component.selectedOption;
      const originalSelectedTags = component.selectedTags;
      const originalIsCollapsed = component.isCollapsed;

      component.onCollapse();

      expect(component.selectedOption).toBe(originalSelectedOption);
      expect(component.selectedTags).toBe(originalSelectedTags);
      expect(component.isCollapsed).toBe(originalIsCollapsed);
    });
  });

  describe('Constants Integration', () => {
    it('should use CURRENT_DEVICE constant correctly', () => {
      expect(component.selectedOption).toBe(CURRENT_DEVICE);
      expect(CURRENT_DEVICE).toBe('currentDevice');
    });

    it('should use CURRENT_SITE constant correctly', () => {
      expect(CURRENT_SITE).toBe('currentSite');
    });

    it('should use SITE_TAGS constant correctly', () => {
      expect(SITE_TAGS).toBe('siteTags');
    });

    it('should have all required constants available', () => {
      expect(CURRENT_DEVICE).toBeDefined();
      expect(CURRENT_SITE).toBeDefined();
      expect(SITE_TAGS).toBeDefined();
    });
  });

  describe('Form Integration', () => {
    it('should work with different FormGroup configurations', () => {
      const alternativeFormGroup = new FormGroup({
        sites: new FormControl([]),
        otherControl: new FormControl('test'),
      });

      component.formGroup = alternativeFormGroup;
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags).toEqual([]);
    });

    it('should handle FormGroup with additional controls', () => {
      mockFormGroup.addControl('extraControl', new FormControl('extra'));
      const selectedTags = ['retail'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(2);
    });

    it('should work when activeStepIndex changes', () => {
      component.activeStepIndex = 0;
      expect(component.activeStepIndex).toBe(0);

      component.activeStepIndex = 5;
      expect(component.activeStepIndex).toBe(5);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle very large arrays of sites', () => {
      const largeSitesArray: SitesResults[] = [];
      for (let i = 0; i < 1000; i++) {
        largeSitesArray.push({
          id: `site-${i}`,
          name: `Site ${i}`,
          tags: [{ id: i, name: 'common-tag', siteCount: 1 }],
        });
      }

      mockFormGroup.get('sites')?.setValue(largeSitesArray);
      const selectedTags = ['common-tag'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(1000);
    });

    it('should handle sites with duplicate tag names', () => {
      const sitesWithDuplicateTags: SitesResults[] = [
        {
          id: '10',
          name: 'Site Ten',
          tags: [{ id: 1, name: 'duplicate', siteCount: 1 }] as [
            { id: number; name: string; siteCount: number },
          ],
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithDuplicateTags);
      const selectedTags = ['duplicate'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(1);
    });

    it('should handle empty string tags', () => {
      const sitesWithEmptyStringTags: SitesResults[] = [
        {
          id: '11',
          name: 'Site Eleven',
          tags: [{ id: 1, name: '', siteCount: 1 }] as [
            { id: number; name: string; siteCount: number },
          ],
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithEmptyStringTags);
      const selectedTags = [''];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(1);
    });

    it('should handle null FormGroup', () => {
      component.formGroup = null as any;
      const selectedTags = ['retail'];

      expect(() => component.getSelectedTags(selectedTags)).toThrow();
    });

    it('should handle undefined FormGroup', () => {
      component.formGroup = undefined as any;
      const selectedTags = ['retail'];

      expect(() => component.getSelectedTags(selectedTags)).toThrow();
    });

    it('should handle very long tag names', () => {
      const longTagName = 'a'.repeat(1000);
      const sitesWithLongTags: SitesResults[] = [
        {
          id: '12',
          name: 'Site Twelve',
          tags: [{ id: 1, name: longTagName, siteCount: 1 }],
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithLongTags);
      const selectedTags = [longTagName];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(1);
    });

    it('should handle sites with mixed tag structures', () => {
      const sitesWithMixedTags: SitesResults[] = [
        {
          id: '13',
          name: 'Site Thirteen',
          tags: [{ id: 1, name: 'tag1', siteCount: 1 }],
        },
        {
          id: '14',
          name: 'Site Fourteen',
          tags: undefined,
        },
        {
          id: '15',
          name: 'Site Fifteen',
        },
      ];

      mockFormGroup.get('sites')?.setValue(sitesWithMixedTags);
      const selectedTags = ['tag1'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedSiteByTags.length).toBe(1);
      expect(component.selectedSiteByTags[0].id).toBe('13');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow of tag selection and collapse', () => {
      expect(component.collapseSiteCount).toBe(false);
      expect(component.selectedSiteByTags).toEqual([]);

      spyOn(component.selectedTagsData, 'emit');
      spyOn(component.selectedTab, 'emit');

      const selectedTags = ['retail', 'urban'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedTags).toEqual(['retail', 'urban']);
      expect(component.selectedSiteByTags.length).toBe(2);
      expect(component.selectedTagsData.emit).toHaveBeenCalledWith([
        'retail',
        'urban',
      ]);
      expect(component.selectedTab.emit).toHaveBeenCalledWith(CURRENT_DEVICE);

      component.onCollapse();
      expect(component.collapseSiteCount).toBe(true);

      component.onCollapse();
      expect(component.collapseSiteCount).toBe(false);
    });

    it('should maintain state consistency across multiple operations', () => {
      component.selectedOption = CURRENT_SITE;
      component.isCollapsed = false;

      const selectedTags = ['gas-station'];
      component.getSelectedTags(selectedTags);

      expect(component.selectedOption).toBe(CURRENT_SITE);
      expect(component.isCollapsed).toBe(false);
      expect(component.selectedTags).toEqual(['gas-station']);

      component.onCollapse();

      expect(component.selectedOption).toBe(CURRENT_SITE);
      expect(component.isCollapsed).toBe(false);
      expect(component.selectedTags).toEqual(['gas-station']);
      expect(component.collapseSiteCount).toBe(true);
    });

    it('should handle rapid successive tag selections', () => {
      spyOn(component.selectedTagsData, 'emit');
      spyOn(component.selectedTab, 'emit');

      component.getSelectedTags(['retail']);
      component.getSelectedTags(['gas-station']);
      component.getSelectedTags(['convenience']);
      component.getSelectedTags(['retail', 'urban']);

      expect(component.selectedTags).toEqual(['retail', 'urban']);
      expect(component.selectedSiteByTags.length).toBe(2);
      expect(component.selectedTagsData.emit).toHaveBeenCalledTimes(4);
      expect(component.selectedTab.emit).toHaveBeenCalledTimes(4);
    });
  });
});
