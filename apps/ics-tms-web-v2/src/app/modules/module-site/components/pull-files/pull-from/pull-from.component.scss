.pull-from-container {
  .pull-from-options {
    display: flex;
    flex-direction: row;
    gap: 2.4rem;

    .option-btn {
      width: 14rem;
      height: 4.8rem;
      border-radius: 0.2rem;
      transition: all ease-in 0.1s;
      font-size: 1.8rem;
      padding: 1rem;
      text-align: center;
      border: 0.1rem solid var(--color-border);
      cursor: pointer;

      &:hover {
        box-shadow:
          0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
          0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.14),
          0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
      }
    }

    .option-btn-active {
      border-color: var(--color-primary);
      box-shadow:
        0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
        0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.14),
        0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
    }
  }

  .selected-option-body {
    .current-site-or-tags {
      border: 0.1rem solid var(--color-border-label);
      padding: 1rem 1.5rem;
      background-color: var(--color-bg-label);
      border-radius: 0.3rem;
      font-size: 1.4rem;
      margin-top: 2.4rem;
    }

    .select-tags {
      font-size: 1.4rem;
      width: 40%;
      margin-top: 2rem;
    }

    .sites-count {
      margin-top: 2.4rem;
      margin-left: 0.8rem;

      .count {
        font-size: 1.2rem;
        line-height: 1.5;
        padding: 0.438rem 0rem 0.438rem 0.1rem;
        border-radius: 0.188em;
        transition: all ease-in 0.1s;
        font-weight: 500;
        border: transparent;
        background-color: transparent;
        color: var(--color-primary);

        .caret {
          transition: transform 0.3s ease;
        }

        span {
          margin-left: 0.388rem;
        }

        &:hover {
          color: var(--logo-color-two);
        }
      }

      .site-collapse {
        display: block;

        .site-list {
          overflow: auto;
          max-width: 25rem;
          max-height: 15.625rem;
          padding-left: 0;
          list-style: none;
          margin-bottom: 1.6rem;
          margin-top: 0;
          font-size: 1.4rem;

          .site-name {
            display: block;
            padding: 0.088rem 0;
          }

          &::-webkit-scrollbar {
            width: 0.938rem;
            height: 0.938rem;
          }

          &::-webkit-scrollbar-track {
            background: var(--color-scrollbar-track);
          }

          &::-webkit-scrollbar-thumb {
            background: var(--btn-cancel-hover-color);
          }
        }
      }
    }
  }

  .pull-from-btn {
    width: 104%;
    margin-top: 2rem;

    .pull-from-button {
      width: fit-content;
      border-radius: 0.3rem;
      border: none;
      font-size: 1.4rem;
      color: var(--color-white);
      transition: all ease-in 0.1s;
      font-weight: 500;
      padding: 0.6rem 1.2rem;
      padding-left: 2rem;
      padding-right: 2rem;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);

      &:disabled {
        opacity: 0.65;
        cursor: not-allowed;
        box-shadow: none;

        &:hover {
          box-shadow: none;
          background-color: var(--color-primary);
        }
      }
    }
  }
}

.selected-tags-container {
  .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
    padding: 0.1rem 0.03125rem;

    .tags-card {
      font-size: 1.2rem;
      max-width: 12.5rem;
      margin-right: 0.8rem;
      padding: 0.4rem 0.8rem;
      font-weight: 500;
      overflow: hidden;
      border: 0.1rem solid var(--color-border);
      cursor: pointer;
      text-overflow: ellipsis;
      border-radius: 0.25em;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
    }
  }

  .selectedText {
    font-size: 1.2rem;
    font-weight: 500;
    line-height: 1.5;
    margin-left: 0.03125rem;
    color: var(--logo-color-font);
  }
}
