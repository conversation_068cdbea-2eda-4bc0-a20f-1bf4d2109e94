.add-notifications-container {
  width: 50%;

  .added-users-list {
    box-shadow:
      0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
      0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
    overflow-y: auto;
    max-height: 35rem;
    margin-top: 1.6rem;
    font-size: 1.4rem;
    margin-bottom: 1.4rem;

    .user-card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.85rem 1.3rem;

      &:last-child {
        border-bottom: none;
      }

      border-bottom: 0.1rem solid var(--color-border);

      .left-container {
        display: flex;
        align-items: center;

        .avatar-img {
          width: 1.75rem;
          height: 1.75rem;
          background-color: var(--btn-color-disabled);
          border-radius: 50%;
          position: relative;
          padding: 1.6rem;

          span {
            position: absolute;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            display: flex;
            font-size: 1.4rem;
            justify-content: center;
            align-items: center;
            transform: translate(-50%, -50%);
            color: var(--color-white);
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            padding: 0.8rem;
            text-align: center;
            word-wrap: normal;
          }
        }

        .user-names {
          display: flex;
          flex-direction: column;

          p {
            margin-left: 0.938rem;
            margin-bottom: 0;
            font-size: 1.4rem;
            font-weight: 400;
            color: var(--color-black);
          }

          .user-desc {
            color: var(--color-black-shade-two);
            font-size: 85%;
          }
        }
      }

      i {
        color: var(--color-black-shade-two);

        &:hover {
          cursor: pointer;
          color: var(--color-black);
        }
      }
    }
  }

  .notification-desc-banner {
    height: fit-content;
    border: 0.1rem solid var(--label-info);
    font-size: 1.3rem;
    margin: 2rem 0;
    border-radius: 0.3rem;
    background-color: var(--divider-color);
    display: flex;

    .icon-box {
      display: flex;
      align-items: center;
      padding: 0 1.6rem;
      background-color: var(--label-info);
      font-size: 1.3rem;
      color: var(--color-white);
    }

    .desc-banner {
      padding: 1.6rem 2.4rem;
      font-size: 1.3rem;
    }
  }
}
