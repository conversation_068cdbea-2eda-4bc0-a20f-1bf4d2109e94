import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DateFormatPipe } from '../../../../../utils/date-format.pipe';
import { DeviceFile } from '../../../models/device-file.modal';

@Component({
  selector: 'app-select-file-or-directories',
  templateUrl: './select-file-or-directories.component.html',
  styleUrls: ['./select-file-or-directories.component.scss'],
})
export class SelectFileOrDirectoriesComponent {
  @Input() formGroup!: FormGroup;

  @Input() activeStepIndex!: number;

  selectedFilesId: number[] = [];

  selectedFiles!: DeviceFile[];

  stripFilePath(text: string) {
    const trimmedText = text.trim();
    return trimmedText.replace(/^.*[\\/]/, '');
  }

  stripFileName(text: string) {
    const trimmedText = text.trim();
    return trimmedText.substring(0, trimmedText.lastIndexOf('/'));
  }

  getTimeDate(timeInMilliSeconds: number | null) {
    return new DateFormatPipe().transform(
      timeInMilliSeconds,
      'MMM d, y',
      false,
      false,
      false
    );
  }

  onClickFile(id: number) {
    if (this.selectedFilesId.includes(id)) {
      this.selectedFilesId = this.selectedFilesId.filter(
        fileId => fileId !== id
      );
    } else {
      this.selectedFilesId.push(id);
    }
  }

  selectAllFile() {
    if (this.formGroup.get('fileSize')?.value !== this.selectedFilesId.length) {
      this.selectedFilesId = this.formGroup
        .get('file')
        ?.value.map((file: DeviceFile) => file.id);
    } else {
      this.selectedFilesId = [];
    }
  }

  handleClick() {
    this.selectedFiles = this.formGroup
      .get('file')
      ?.value.filter((file: DeviceFile) =>
        this.selectedFilesId.includes(file.id)
      );
    this.formGroup.get('selectedFiles')?.setValue(this.selectedFiles);
  }

  isFolder(file: DeviceFile) {
    return file.filePath.endsWith('/') || file.filePath.endsWith('\\');
  }
}
