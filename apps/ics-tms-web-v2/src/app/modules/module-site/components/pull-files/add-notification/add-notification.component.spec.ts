import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BehaviorSubject, of, Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { UsersData, UsersResults } from '../../../models/users-data.model';
import {
  CommonResponseData,
  IcsUser,
  LoginResponse,
  UserGroup,
} from '../../../../../models/common';
import { NAME_ONLY } from '../../../../../constants/appConstants';
import { TeamData } from '../../../../module-settings/model/team-data.model';
import { AddNotificationComponent } from './add-notification.component';
import { SelectionEvent } from 'src/app/components/shared/ics-searchable-dropdown/ics-searchable-dropdown.component';

describe('AddNotificationComponent', () => {
  let component: AddNotificationComponent;
  let fixture: ComponentFixture<AddNotificationComponent>;
  let mockStore: any;
  let mockFormGroup: FormGroup;

  const mockUsersData: UsersData = {
    results: [
      {
        id: '1',
        email: '<EMAIL>',
        emailVerified: true,
        mfaConfigured: false,
        status: 1,
        fullName: 'John Doe',
        company: { id: 'comp1', name: 'Company 1', featureFlags: [] },
        roles: ['admin'],
      },
      {
        id: '2',
        email: '<EMAIL>',
        emailVerified: true,
        mfaConfigured: true,
        status: 1,
        fullName: 'Jane Smith',
        company: { id: 'comp2', name: 'Company 2', featureFlags: ['feature1'] },
        roles: ['user'],
      },
    ],
    resultsMetadata: { totalResults: 2, pageIndex: 0, pageSize: 10 },
  };

  const mockLoginResponse: LoginResponse = {
    fullName: 'Current User',
    email: '<EMAIL>',
    sub: 'current-user-id',
    roles: ['admin', 'user'],
  };

  const mockUserGroups: CommonResponseData<UserGroup> = {
    results: [
      { id: 'group1', name: 'Admin Group', usercount: 5 },
      { id: 'group2', name: 'User Group', usercount: 10 },
    ],
    resultsMetadata: { totalResults: 2, pageIndex: 0, pageSize: 10 },
  };

  const mockIcsUsers: CommonResponseData<IcsUser> = {
    results: [
      {
        id: '1',
        email: '<EMAIL>',
        emailVerified: true,
        mfaConfigured: false,
        status: 1,
        fullName: 'ICS User One',
        lastLocked: null,
        failedLoginAttempts: 0,
        persona: 'admin',
        company: { id: 'comp1', name: 'Company 1', featureFlags: [] },
        roles: ['admin'],
        userGroups: [{ id: 'group1', name: 'Admin Group' }],
        isAdmin: true,
      },
    ],
    resultsMetadata: { totalResults: 1, pageIndex: 0, pageSize: 10 },
  };

  const mockTeamData: TeamData = {
    id: 'team1',
    name: 'Test Team',
    users: [
      {
        id: '1',
        isAdmin: false,
        userGroupCount: 2,
      },
    ],
    sitegroups: [
      {
        id: 'sg1',
        name: 'Site Group 1',
      },
    ],
  };

  beforeEach(async () => {
    const storeSpy = jasmine.createSpyObj('Store', ['select', 'dispatch']);

    await TestBed.configureTestingModule({
      declarations: [AddNotificationComponent],
      imports: [ReactiveFormsModule],
      providers: [{ provide: Store, useValue: storeSpy }],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(AddNotificationComponent);
    component = fixture.componentInstance;
    mockStore = TestBed.inject(Store) as any;

    mockFormGroup = new FormGroup({
      selectedNotification: new FormControl([]),
    });
    component.formGroup = mockFormGroup;
    component.activeStepIndex = 0;

    component.usersData = undefined as any;
    component.currentUserData = undefined as any;
    component.teamsData = undefined as any;
    component.userGroups$.next({
      results: [],
      resultsMetadata: { totalResults: 0, pageIndex: 0, pageSize: 10 },
    });
    component.selectedUserGroups$.next([]);
    component.selectedUsers$.next([]);

    const selectorMap = new Map();
    let callOrder = 0;

    mockStore.select.and.callFake((selector: any) => {
      callOrder++;

      if (!selectorMap.has(selector)) {
        selectorMap.set(selector, callOrder);
      }

      const firstCall = selectorMap.get(selector);

      if (firstCall === 1) {
        return of(mockUsersData);
      }
      if (firstCall === 2) {
        return of(mockLoginResponse);
      }
      if (firstCall === 3) {
        return of(mockIcsUsers);
      }
      if (firstCall === 4) {
        return of(mockUserGroups);
      }

      return of(mockLoginResponse);
    });
  });

  afterEach(() => {
    if (component) {
      component.ngOnDestroy();
    }
    fixture.destroy();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.usersData).toBeUndefined();
      expect(component.currentUserData).toBeUndefined();
      expect(component.teamsData).toBeUndefined();
      expect(component.userGroups$.getValue().results.length).toBe(0);
      expect(component.selectedUserGroups$.getValue().length).toBe(0);
      expect(component.selectedUsers$.getValue().length).toBe(0);
    });

    it('should have required input properties', () => {
      expect(component.formGroup).toBeDefined();
      expect(component.activeStepIndex).toBe(0);
    });

    it('should have protected readonly constants', () => {
      expect(of).toBeDefined();
      expect(NAME_ONLY).toBeDefined();
    });

    it('should initialize BehaviorSubjects correctly', () => {
      expect(component.userGroups$).toBeInstanceOf(BehaviorSubject);
      expect(component.selectedUserGroups$).toBeInstanceOf(BehaviorSubject);
      expect(component.selectedUsers$).toBeInstanceOf(BehaviorSubject);
    });

    it('should have destroy$ Subject for cleanup', () => {
      expect(component['destroy$']).toBeInstanceOf(Subject);
    });
  });

  describe('ngOnInit', () => {
    it('should dispatch required actions', () => {
      mockStore.dispatch.calls.reset();

      component.ngOnInit();

      expect(mockStore.dispatch).toHaveBeenCalledTimes(3);
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        jasmine.objectContaining({ type: '[User Groups] Load User Groups' })
      );
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        jasmine.objectContaining({ type: '[Users] Load Users' })
      );
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        jasmine.objectContaining({
          type: '[All Users Teams] Load All Users Teams',
        })
      );
    });

    it('should call getAddNotificationData', () => {
      spyOn(component, 'getAddNotificationData' as any);
      component.ngOnInit();

      expect(component['getAddNotificationData']).toHaveBeenCalled();
    });
  });

  describe('ngOnDestroy', () => {
    it('should complete destroy$ subject', () => {
      spyOn(component['destroy$'], 'next');
      spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(component['destroy$'].next).toHaveBeenCalled();
      expect(component['destroy$'].complete).toHaveBeenCalled();
    });
  });

  describe('getAddNotificationData', () => {
    it('should subscribe to store selectors and update component data', done => {
      mockStore.select.calls.reset();

      component['getAddNotificationData']();

      setTimeout(() => {
        expect(mockStore.select).toHaveBeenCalledTimes(4);
        expect(component.usersData).toEqual(mockUsersData);
        expect(component.currentUserData).toEqual(mockLoginResponse);
        done();
      }, 10);
    });

    it('should add current user to selectedUsers and update form', done => {
      mockStore.select.calls.reset();
      component.selectedUsers = [];

      component['getAddNotificationData']();

      setTimeout(() => {
        expect(component.selectedUsers.length).toBe(1);
        expect(component.selectedUsers[0].id).toBe('current-user-id');
        expect(component.selectedUsers[0].email).toBe('<EMAIL>');
        done();
      }, 10);
    });

    it('should update userGroups$ BehaviorSubject', done => {
      const userGroupsSpy = spyOn(component.userGroups$, 'next');

      mockStore.select.calls.reset();

      component['getAddNotificationData']();

      setTimeout(() => {
        expect(userGroupsSpy).toHaveBeenCalledWith(mockUserGroups);
        done();
      }, 10);
    });
  });

  describe('updateUserGroups', () => {
    const mockUserGroup: UserGroup = {
      id: 'group1',
      name: 'Test Group',
      usercount: 3,
    };

    beforeEach(() => {
      component.selectedUserGroups$.next([]);
    });

    it('should add user group when action is not remove', () => {
      const selectionEvent: SelectionEvent = {
        item: mockUserGroup,
        action: 'add',
      };
      component.updateUserGroups(selectionEvent);

      expect(component.selectedUserGroups$.getValue()).toContain(mockUserGroup);
    });

    it('should remove user group when action is remove', () => {
      component.selectedUserGroups$.next([mockUserGroup]);
      const selectionEvent: SelectionEvent = {
        item: mockUserGroup,
        action: 'remove',
      };

      component.updateUserGroups(selectionEvent);

      expect(component.selectedUserGroups$.getValue()).not.toContain(
        mockUserGroup
      );
    });

    it('should handle multiple user groups', () => {
      const userGroup2: UserGroup = {
        id: 'group2',
        name: 'Test Group 2',
        usercount: 5,
      };

      component.updateUserGroups({ item: mockUserGroup, action: 'add' });
      component.updateUserGroups({ item: userGroup2, action: 'add' });

      expect(component.selectedUserGroups$.getValue().length).toBe(2);
      expect(component.selectedUserGroups$.getValue()).toContain(mockUserGroup);
      expect(component.selectedUserGroups$.getValue()).toContain(userGroup2);
    });

    it('should not add duplicate user groups', () => {
      component.selectedUserGroups$.next([mockUserGroup]);
      component.updateUserGroups({ item: mockUserGroup, action: 'add' });

      expect(component.selectedUserGroups$.getValue().length).toBe(2);
    });
  });

  describe('checkIfClearDisabled', () => {
    const mockUserGroup: UserGroup = {
      id: 'group1',
      name: 'Test Group',
      usercount: 1,
    };

    beforeEach(() => {
      component.userGroups$.next(mockUserGroups);
      component.userData = {
        id: '1',
        userGroups: [{ id: 'group1', name: 'Test Group' }],
      } as IcsUser;
    });

    it('should return false when not in edit mode', () => {
      component.isEditPeople = false;
      const result = component.checkIfClearDisabled(mockUserGroup);
      expect(result).toBe(false);
    });

    it('should return false when user group has more than one user', () => {
      component.isEditPeople = true;
      const groupWithMultipleUsers: UserGroup = {
        id: 'group2',
        name: 'Multi User Group',
        usercount: 5,
      };

      const result = component.checkIfClearDisabled(groupWithMultipleUsers);
      expect(result).toBe(false);
    });

    it('should return true when current user is the only user in group', () => {
      component.isEditPeople = true;
      component.userGroups$.next({
        results: [{ id: 'group1', name: 'Single User Group', usercount: 1 }],
        resultsMetadata: { totalResults: 1, pageIndex: 0, pageSize: 10 },
      });

      const result = component.checkIfClearDisabled(mockUserGroup);
      expect(result).toBe(true);
    });
  });

  describe('updateMembers', () => {
    const mockIcsUser: IcsUser = {
      id: '1',
      email: '<EMAIL>',
      emailVerified: true,
      mfaConfigured: false,
      status: 1,
      fullName: 'Test User',
      lastLocked: null,
      failedLoginAttempts: 0,
      persona: 'user',
      company: { id: 'comp1', name: 'Company 1', featureFlags: [] },
      roles: ['user'],
      userGroups: [],
    };

    beforeEach(() => {
      component.selectedUsers$.next([]);
      component.teamsData = mockTeamData;
    });

    it('should add user when action is not remove', () => {
      const selectionEvent: SelectionEvent = {
        item: mockIcsUser,
        action: 'add',
      };
      component.updateMembers(selectionEvent);

      expect(component.selectedUsers$.getValue()).toContain(
        jasmine.objectContaining({ id: '1' })
      );
    });

    it('should remove user when action is remove', () => {
      component.selectedUsers$.next([mockIcsUser]);
      const selectionEvent: SelectionEvent = {
        item: mockIcsUser,
        action: 'remove',
      };

      component.updateMembers(selectionEvent);

      expect(component.selectedUsers$.getValue()).not.toContain(
        jasmine.objectContaining({ id: '1' })
      );
    });

    it('should merge with existing team data when adding user', () => {
      component.teamsData = {
        users: [
          {
            id: '1',
            email: '<EMAIL>',
            fullName: 'ICS User One',
            isAdmin: false,
          },
        ],
      } as any;

      const selectionEvent: SelectionEvent = {
        item: mockIcsUser,
        action: 'add',
      };
      component.updateMembers(selectionEvent);

      const addedUser = component.selectedUsers$.getValue()[0];
      expect(addedUser.isAdmin).toBe(false);
    });

    it('should set isAdmin to true when undefined', () => {
      component.teamsData = {
        users: [],
      } as any;

      const userWithoutAdmin = { ...mockIcsUser };
      delete userWithoutAdmin.isAdmin;

      const selectionEvent: SelectionEvent = {
        item: userWithoutAdmin,
        action: 'add',
      };
      component.updateMembers(selectionEvent);

      const addedUser = component.selectedUsers$.getValue()[0];
      expect(addedUser.isAdmin).toBe(true);
    });
  });

  describe('onGetData', () => {
    it('should update selectedUsers and form control', () => {
      const testData = [mockIcsUsers.results[0]];
      component.onGetData(testData);

      expect(component.selectedUsers).toEqual(testData);
      expect(component.formGroup.get('selectedNotification')?.value).toEqual(
        testData
      );
    });

    it('should handle empty data', () => {
      component.onGetData([]);

      expect(component.selectedUsers).toEqual([]);
      expect(component.formGroup.get('selectedNotification')?.value).toEqual(
        []
      );
    });

    it('should handle null data', () => {
      component.onGetData(null);

      expect(component.selectedUsers).toBeNull();
      expect(component.formGroup.get('selectedNotification')?.value).toBeNull();
    });
  });

  describe('getStringInitials', () => {
    it('should return initials from full name', () => {
      const result = component.getStringInitials('John Doe Smith');
      expect(result).toBe('JDS');
    });

    it('should handle single name', () => {
      const result = component.getStringInitials('John');
      expect(result).toBe('J');
    });

    it('should handle empty string', () => {
      const result = component.getStringInitials('');
      expect(result).toBe('');
    });

    it('should handle null/undefined', () => {
      const result1 = component.getStringInitials(null as any);
      const result2 = component.getStringInitials(undefined as any);
      expect(result1).toBe('');
      expect(result2).toBe('');
    });

    it('should handle names with special characters', () => {
      const result = component.getStringInitials('Jean-Claude Van Damme');
      expect(result).toBe('JCVD');
    });

    it('should convert to uppercase', () => {
      const result = component.getStringInitials('john doe');
      expect(result).toBe('JD');
    });

    it('should handle names with numbers', () => {
      const result = component.getStringInitials('John2 Doe3');
      expect(result).toBe('JD');
    });
  });

  describe('removeUserAndAddToDD', () => {
    beforeEach(() => {
      component.selectedUsers = [...mockUsersData.results];
    });

    it('should remove user from selectedUsers', () => {
      const userToRemove = mockUsersData.results[0];
      component.removeUserAndAddToDD(userToRemove);

      expect(component.selectedUsers).not.toContain(userToRemove);
      expect(component.selectedUsers.length).toBe(1);
    });

    it('should update form control after removal', () => {
      const userToRemove = mockUsersData.results[0];
      component.removeUserAndAddToDD(userToRemove);

      expect(mockFormGroup.get('selectedNotification')?.value).toEqual(
        component.selectedUsers
      );
    });

    it('should handle removing non-existent user', () => {
      const nonExistentUser: UsersResults = {
        id: 'non-existent',
        email: '<EMAIL>',
        emailVerified: false,
        mfaConfigured: false,
        status: 0,
        fullName: 'Non Existent',
        company: { id: '', name: '', featureFlags: [] },
        roles: [],
      };

      const originalLength = component.selectedUsers.length;
      component.removeUserAndAddToDD(nonExistentUser);

      expect(component.selectedUsers.length).toBe(originalLength);
    });

    it('should handle empty selectedUsers array', () => {
      component.selectedUsers = [];
      const userToRemove = mockUsersData.results[0];

      expect(() => component.removeUserAndAddToDD(userToRemove)).not.toThrow();
      expect(component.selectedUsers).toEqual([]);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow of adding and removing users', () => {
      expect(component.selectedUsers).toEqual([]);

      component.onGetData(mockUsersData.results);
      expect(component.selectedUsers.length).toBe(2);

      component.removeUserAndAddToDD(mockUsersData.results[0]);
      expect(component.selectedUsers.length).toBe(1);
      expect(component.selectedUsers[0].id).toBe('2');
    });

    it('should handle user group selection workflow', () => {
      const userGroup1: UserGroup = {
        id: 'group1',
        name: 'Group 1',
        usercount: 3,
      };
      const userGroup2: UserGroup = {
        id: 'group2',
        name: 'Group 2',
        usercount: 5,
      };

      component.updateUserGroups({ item: userGroup1, action: 'add' });
      component.updateUserGroups({ item: userGroup2, action: 'add' });
      expect(component.selectedUserGroups$.getValue().length).toBe(2);

      component.updateUserGroups({ item: userGroup1, action: 'remove' });
      expect(component.selectedUserGroups$.getValue().length).toBe(1);
      expect(component.selectedUserGroups$.getValue()[0].id).toBe('group2');
    });

    it('should handle ICS user member workflow', () => {
      const icsUser: IcsUser = mockIcsUsers.results[0];
      component.teamsData = mockTeamData;

      component.updateMembers({ item: icsUser, action: 'add' });
      expect(component.selectedUsers$.getValue().length).toBe(1);

      component.updateMembers({ item: icsUser, action: 'remove' });
      expect(component.selectedUsers$.getValue().length).toBe(0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle malformed user data', () => {
      const malformedUser = { id: '1' } as UsersResults;
      expect(() => component.removeUserAndAddToDD(malformedUser)).not.toThrow();
    });

    it('should handle null form control', () => {
      mockFormGroup.removeControl('selectedNotification');
      expect(() => component.onGetData(mockUsersData.results)).not.toThrow();
    });

    it('should handle store selector errors gracefully', () => {
      mockStore.select.and.returnValue(of(null));
      expect(() => component['getAddNotificationData']()).not.toThrow();
    });

    it('should handle undefined team data in updateMembers', () => {
      component.teamsData = undefined as any;
      const icsUser: IcsUser = mockIcsUsers.results[0];

      expect(() =>
        component.updateMembers({ item: icsUser, action: 'add' })
      ).not.toThrow();
    });

    it('should handle very long full names in getStringInitials', () => {
      const longName = `${'A'.repeat(1000)} ${'B'.repeat(1000)}`;
      const result = component.getStringInitials(longName);
      expect(result).toBe('AB');
    });
  });
});
