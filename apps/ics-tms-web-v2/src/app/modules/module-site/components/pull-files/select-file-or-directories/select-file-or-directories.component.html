<form
  [ngClass]="{
    'd-visible': activeStepIndex === 1,
    'd-none': activeStepIndex !== 1,
  }"
>
  <div class="select-file-container">
    <div class="select-file-table-heading">
      <input
        type="checkbox"
        class="selectAllFileInput"
        [checked]="selectedFilesId.length === formGroup.get('fileSize')?.value"
        (click)="selectAllFile()"
        name="selectAllFiles"
        id="cbAll"
      />
      <div class="file-icon-col"></div>
      <div class="name-path-col">Name and Path</div>
      <div class="application-col">Application</div>
      <div class="last-snapshot-col">Last snapshot</div>
    </div>
    <div class="select-files-row">
      <div
        (click)="onClickFile(file.id)"
        [ngClass]="{ selectedFileBGBlue: selectedFilesId.includes(file.id) }"
        class="file-row"
        *ngFor="let file of formGroup.get('file')?.value"
      >
        <input
          type="checkbox"
          class="selectFileInput"
          [checked]="selectedFilesId.includes(file.id)"
          name="selectAllFiles"
          id="{{ file.id }}"
        />
        <div class="file-icon-col-row">
          <ng-container *ngIf="isFolder(file); else showFile">
            <i class="fa-solid fa-folder folder-icon"></i>
          </ng-container>
          <ng-template #showFile>
            <i class="fa-solid fa-file file-icon"></i>
          </ng-template>
          <i *ngIf="file.pullRequestQueued" class="fas fa-sync-alt"></i>
        </div>
        <div class="name-path-col-row">
          <div class="fileName">{{ stripFilePath(file.filePath) }}</div>
          <div class="filePath">{{ stripFileName(file.filePath) }}</div>
        </div>
        <div class="application-col-row">{{ file.applicationId }}</div>
        <div class="last-snapshot-col-row">
          {{ getTimeDate(file.lastPulled) }}
        </div>
      </div>
    </div>
  </div>
  <div class="select-file-or-directory-btn">
    <button
      (click)="handleClick()"
      matStepperNext
      [disabled]="selectedFilesId.length <= 0"
      class="btn-primary pull-from-button"
    >
      Continue
    </button>
  </div>
</form>

<div
  class="selectedFiles-pull-files"
  *ngIf="activeStepIndex !== 1 && selectedFilesId.length > 0"
>
  <div class="heading">Selected</div>
  <div class="file-names">
    <span *ngFor="let file of selectedFiles">
      {{ file.applicationId }} {{ file.filePath }}</span
    >
  </div>
</div>
