import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DateFormatPipe } from '../../../../../utils/date-format.pipe';
import { DeviceFile } from '../../../models/device-file.modal';
import { SelectFileOrDirectoriesComponent } from './select-file-or-directories.component';

describe('SelectFileOrDirectoriesComponent', () => {
  let component: SelectFileOrDirectoriesComponent;
  let fixture: ComponentFixture<SelectFileOrDirectoriesComponent>;
  let mockFormGroup: FormGroup;

  const mockDeviceFiles: DeviceFile[] = [
    {
      id: 1,
      filePath: '/path/to/file1.txt',
      contentType: 'text/plain',
      writeable: true,
      lastPulled: 1640995200000,
      applicationId: 'app1',
      changed: false,
      pullRequestQueued: false,
      displayProperties: [{ label: 'File 1', visibility: 'visible' }],
      icon: 'file-icon',
    },
    {
      id: 2,
      filePath: '/path/to/folder/',
      contentType: 'directory',
      writeable: true,
      lastPulled: 1640995200000,
      applicationId: 'app1',
      changed: true,
      pullRequestQueued: false,
      displayProperties: null,
      icon: 'folder-icon',
    },
    {
      id: 3,
      filePath: 'C:\\Windows\\System32\\file.exe',
      contentType: 'application/exe',
      writeable: false,
      lastPulled: null,
      applicationId: 'app2',
      changed: false,
      pullRequestQueued: true,
      displayProperties: [{ label: 'System File', visibility: 'hidden' }],
      icon: 'exe-icon',
    },
    {
      id: 4,
      filePath: '/another/folder\\',
      contentType: 'directory',
      writeable: true,
      lastPulled: 1640995200000,
      applicationId: 'app1',
      changed: false,
      pullRequestQueued: false,
      displayProperties: null,
      icon: 'folder-icon',
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SelectFileOrDirectoriesComponent, DateFormatPipe],
      imports: [ReactiveFormsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(SelectFileOrDirectoriesComponent);
    component = fixture.componentInstance;

    mockFormGroup = new FormGroup({
      file: new FormControl(mockDeviceFiles),
      fileSize: new FormControl(mockDeviceFiles.length),
      selectedFiles: new FormControl([]),
    });

    component.formGroup = mockFormGroup;
    component.activeStepIndex = 1;

    fixture.detectChanges();
  });

  afterEach(() => {
    fixture.destroy();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.selectedFilesId).toEqual([]);
      expect(component.selectedFiles).toBeUndefined();
    });

    it('should have required input properties', () => {
      expect(component.formGroup).toBeDefined();
      expect(component.activeStepIndex).toBe(1);
    });

    it('should initialize selectedFilesId as empty array', () => {
      const newComponent = new SelectFileOrDirectoriesComponent();
      expect(newComponent.selectedFilesId).toEqual([]);
    });
  });

  describe('stripFilePath method', () => {
    it('should extract filename from Unix-style path', () => {
      const result = component.stripFilePath('/path/to/file.txt');
      expect(result).toBe('file.txt');
    });

    it('should extract filename from Windows-style path', () => {
      const result = component.stripFilePath('C:\\Windows\\System32\\file.exe');
      expect(result).toBe('file.exe');
    });

    it('should handle path with only filename', () => {
      const result = component.stripFilePath('filename.txt');
      expect(result).toBe('filename.txt');
    });

    it('should handle empty string', () => {
      const result = component.stripFilePath('');
      expect(result).toBe('');
    });

    it('should handle string with only spaces', () => {
      const result = component.stripFilePath('   ');
      expect(result).toBe('');
    });

    it('should trim whitespace from input', () => {
      const result = component.stripFilePath('  /path/to/file.txt  ');
      expect(result).toBe('file.txt');
    });

    it('should handle path ending with slash', () => {
      const result = component.stripFilePath('/path/to/folder/');
      expect(result).toBe('');
    });

    it('should handle path ending with backslash', () => {
      const result = component.stripFilePath('C:\\path\\to\\folder\\');
      expect(result).toBe('');
    });

    it('should handle mixed path separators', () => {
      const result = component.stripFilePath('/path\\to/file.txt');
      expect(result).toBe('file.txt');
    });
  });

  describe('stripFileName method', () => {
    it('should extract directory path from Unix-style path', () => {
      const result = component.stripFileName('/path/to/file.txt');
      expect(result).toBe('/path/to');
    });

    it('should extract directory path from Windows-style path', () => {
      const result = component.stripFileName('C:\\Windows\\System32\\file.exe');
      expect(result).toBe('');
    });

    it('should handle path with no directory', () => {
      const result = component.stripFileName('filename.txt');
      expect(result).toBe('');
    });

    it('should handle empty string', () => {
      const result = component.stripFileName('');
      expect(result).toBe('');
    });

    it('should handle string with only spaces', () => {
      const result = component.stripFileName('   ');
      expect(result).toBe('');
    });

    it('should trim whitespace from input', () => {
      const result = component.stripFileName('  /path/to/file.txt  ');
      expect(result).toBe('/path/to');
    });

    it('should handle path ending with slash', () => {
      const result = component.stripFileName('/path/to/folder/');
      expect(result).toBe('/path/to/folder');
    });

    it('should handle root directory path', () => {
      const result = component.stripFileName('/file.txt');
      expect(result).toBe('');
    });

    it('should handle path with no forward slash', () => {
      const result = component.stripFileName('file');
      expect(result).toBe('');
    });
  });

  describe('getTimeDate method', () => {
    it('should format timestamp to date string', () => {
      const timestamp = 1640995200000;
      const result = component.getTimeDate(timestamp);
      expect(result).toContain('Jan');
      expect(result).toContain('6');
    });

    it('should handle null timestamp', () => {
      const result = component.getTimeDate(null);
      expect(result).toBe('');
    });

    it('should handle zero timestamp', () => {
      const result = component.getTimeDate(0);
      expect(result).toContain('Jan');
      expect(result).toContain('4');
    });

    it('should handle negative timestamp', () => {
      const result = component.getTimeDate(-86400000);
      expect(result).toContain('Dec');
      expect(result).toContain('3');
    });

    it('should handle large timestamp', () => {
      const timestamp = 4102444800000;
      const result = component.getTimeDate(timestamp);
      expect(result).toContain('Jan');
      expect(result).toContain('5');
    });
  });

  describe('onClickFile method', () => {
    beforeEach(() => {
      component.selectedFilesId = [];
    });

    it('should add file id to selectedFilesId when not already selected', () => {
      component.onClickFile(1);
      expect(component.selectedFilesId).toContain(1);
      expect(component.selectedFilesId.length).toBe(1);
    });

    it('should remove file id from selectedFilesId when already selected', () => {
      component.selectedFilesId = [1, 2, 3];
      component.onClickFile(2);
      expect(component.selectedFilesId).toEqual([1, 3]);
      expect(component.selectedFilesId.length).toBe(2);
    });

    it('should handle multiple file selections', () => {
      component.onClickFile(1);
      component.onClickFile(2);
      component.onClickFile(3);
      expect(component.selectedFilesId).toEqual([1, 2, 3]);
    });

    it('should handle selecting and deselecting the same file multiple times', () => {
      component.onClickFile(1);
      expect(component.selectedFilesId).toContain(1);

      component.onClickFile(1);
      expect(component.selectedFilesId).not.toContain(1);

      component.onClickFile(1);
      expect(component.selectedFilesId).toContain(1);
    });

    it('should handle file id that does not exist in current selection', () => {
      component.selectedFilesId = [1, 2];
      component.onClickFile(999);
      expect(component.selectedFilesId).toContain(999);
      expect(component.selectedFilesId.length).toBe(3);
    });

    it('should maintain order of selection', () => {
      component.onClickFile(3);
      component.onClickFile(1);
      component.onClickFile(2);
      expect(component.selectedFilesId).toEqual([3, 1, 2]);
    });
  });

  describe('selectAllFile method', () => {
    beforeEach(() => {
      component.selectedFilesId = [];
    });

    it('should select all files when none are selected', () => {
      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([1, 2, 3, 4]);
    });

    it('should select all files when some are selected', () => {
      component.selectedFilesId = [1, 2];
      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([1, 2, 3, 4]);
    });

    it('should deselect all files when all are selected', () => {
      component.selectedFilesId = [1, 2, 3, 4];
      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([]);
    });

    it('should handle empty file array', () => {
      mockFormGroup.get('file')?.setValue([]);
      mockFormGroup.get('fileSize')?.setValue(0);
      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([]);
    });

    it('should handle null file array', () => {
      mockFormGroup.get('file')?.setValue(null);
      expect(() => component.selectAllFile()).toThrow();
    });

    it('should handle undefined file array', () => {
      mockFormGroup.get('file')?.setValue(undefined);
      expect(() => component.selectAllFile()).toThrow();
    });

    it('should work when fileSize control is null', () => {
      mockFormGroup.removeControl('fileSize');
      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([1, 2, 3, 4]);
    });

    it('should work when file control is null', () => {
      mockFormGroup.removeControl('file');
      component.selectAllFile();
      expect(component.selectedFilesId).toBeUndefined();
    });
  });

  describe('handleClick method', () => {
    beforeEach(() => {
      component.selectedFilesId = [];
    });

    it('should filter and set selected files based on selectedFilesId', () => {
      component.selectedFilesId = [1, 3];
      component.handleClick();

      const selectedFiles = mockFormGroup.get('selectedFiles')?.value;
      expect(selectedFiles.length).toBe(2);
      expect(selectedFiles[0].id).toBe(1);
      expect(selectedFiles[1].id).toBe(3);
    });

    it('should set empty array when no files are selected', () => {
      component.selectedFilesId = [];
      component.handleClick();

      const selectedFiles = mockFormGroup.get('selectedFiles')?.value;
      expect(selectedFiles).toEqual([]);
    });

    it('should handle all files selected', () => {
      component.selectedFilesId = [1, 2, 3, 4];
      component.handleClick();

      const selectedFiles = mockFormGroup.get('selectedFiles')?.value;
      expect(selectedFiles.length).toBe(4);
      expect(selectedFiles.map((f: DeviceFile) => f.id)).toEqual([1, 2, 3, 4]);
    });

    it('should handle selectedFilesId with non-existent ids', () => {
      component.selectedFilesId = [1, 999, 3];
      component.handleClick();

      const selectedFiles = mockFormGroup.get('selectedFiles')?.value;
      expect(selectedFiles.length).toBe(2);
      expect(selectedFiles.map((f: DeviceFile) => f.id)).toEqual([1, 3]);
    });

    it('should update component selectedFiles property', () => {
      component.selectedFilesId = [2, 4];
      component.handleClick();

      expect(component.selectedFiles.length).toBe(2);
      expect(component.selectedFiles[0].id).toBe(2);
      expect(component.selectedFiles[1].id).toBe(4);
    });

    it('should handle null file array', () => {
      mockFormGroup.get('file')?.setValue(null);
      component.selectedFilesId = [1, 2];
      expect(() => component.handleClick()).toThrow();
    });

    it('should handle undefined file array', () => {
      mockFormGroup.get('file')?.setValue(undefined);
      component.selectedFilesId = [1, 2];
      expect(() => component.handleClick()).toThrow();
    });

    it('should handle when file control does not exist', () => {
      mockFormGroup.removeControl('file');
      component.selectedFilesId = [1, 2];
      component.handleClick();

      expect(component.selectedFiles).toBeUndefined();
    });

    it('should handle when selectedFiles control does not exist', () => {
      mockFormGroup.removeControl('selectedFiles');
      component.selectedFilesId = [1, 2];

      expect(() => component.handleClick()).not.toThrow();
    });
  });

  describe('isFolder method', () => {
    it('should return true for path ending with forward slash', () => {
      const file: DeviceFile = {
        id: 1,
        filePath: '/path/to/folder/',
        contentType: 'directory',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'folder-icon',
      };

      expect(component.isFolder(file)).toBe(true);
    });

    it('should return true for path ending with backslash', () => {
      const file: DeviceFile = {
        id: 1,
        filePath: 'C:\\path\\to\\folder\\',
        contentType: 'directory',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'folder-icon',
      };

      expect(component.isFolder(file)).toBe(true);
    });

    it('should return false for file path without trailing slash', () => {
      const file: DeviceFile = {
        id: 1,
        filePath: '/path/to/file.txt',
        contentType: 'text/plain',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'file-icon',
      };

      expect(component.isFolder(file)).toBe(false);
    });

    it('should return false for empty file path', () => {
      const file: DeviceFile = {
        id: 1,
        filePath: '',
        contentType: 'unknown',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'unknown-icon',
      };

      expect(component.isFolder(file)).toBe(false);
    });

    it('should return false for file path with slash in middle', () => {
      const file: DeviceFile = {
        id: 1,
        filePath: '/path/with/slash/file.txt',
        contentType: 'text/plain',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'file-icon',
      };

      expect(component.isFolder(file)).toBe(false);
    });

    it('should return true for root directory paths', () => {
      const unixRoot: DeviceFile = {
        id: 1,
        filePath: '/',
        contentType: 'directory',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'folder-icon',
      };

      const windowsRoot: DeviceFile = {
        id: 2,
        filePath: 'C:\\',
        contentType: 'directory',
        writeable: true,
        lastPulled: null,
        applicationId: 'app1',
        changed: false,
        pullRequestQueued: false,
        displayProperties: null,
        icon: 'folder-icon',
      };

      expect(component.isFolder(unixRoot)).toBe(true);
      expect(component.isFolder(windowsRoot)).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete file selection workflow', () => {
      expect(component.selectedFilesId).toEqual([]);

      component.onClickFile(1);
      component.onClickFile(3);
      expect(component.selectedFilesId).toEqual([1, 3]);

      component.handleClick();
      expect(component.selectedFiles.length).toBe(2);
      expect(mockFormGroup.get('selectedFiles')?.value.length).toBe(2);

      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([1, 2, 3, 4]);

      component.handleClick();
      expect(component.selectedFiles.length).toBe(4);
      expect(mockFormGroup.get('selectedFiles')?.value.length).toBe(4);

      component.selectAllFile();
      expect(component.selectedFilesId).toEqual([]);

      component.handleClick();
      expect(component.selectedFiles).toEqual([]);
      expect(mockFormGroup.get('selectedFiles')?.value).toEqual([]);
    });

    it('should handle mixed file and folder operations', () => {
      const files = mockFormGroup.get('file')?.value;

      expect(component.isFolder(files[1])).toBe(true);
      expect(component.isFolder(files[3])).toBe(true);
      expect(component.isFolder(files[0])).toBe(false);
      expect(component.isFolder(files[2])).toBe(false);

      component.onClickFile(1);
      component.onClickFile(2);
      component.onClickFile(4);

      component.handleClick();
      const { selectedFiles } = component;

      expect(selectedFiles.length).toBe(3);
      expect(component.isFolder(selectedFiles[0])).toBe(false);
      expect(component.isFolder(selectedFiles[1])).toBe(true);
      expect(component.isFolder(selectedFiles[2])).toBe(true);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle FormGroup with missing controls gracefully', () => {
      const emptyFormGroup = new FormGroup({});
      component.formGroup = emptyFormGroup;

      expect(() => component.selectAllFile()).not.toThrow();
      expect(() => component.handleClick()).not.toThrow();
    });

    it('should handle null FormGroup', () => {
      component.formGroup = null as any;

      expect(() => component.selectAllFile()).toThrow();
      expect(() => component.handleClick()).toThrow();
    });

    it('should handle undefined FormGroup', () => {
      component.formGroup = undefined as any;

      expect(() => component.selectAllFile()).toThrow();
      expect(() => component.handleClick()).toThrow();
    });

    it('should handle duplicate file IDs in selectedFilesId', () => {
      component.selectedFilesId = [1, 1, 2, 2, 3];
      component.handleClick();

      const { selectedFiles } = component;
      expect(selectedFiles.length).toBe(3);
      expect(selectedFiles.map((f: DeviceFile) => f.id)).toEqual([1, 2, 3]);
    });

    it('should handle very large file arrays', () => {
      const largeFileArray: DeviceFile[] = [];
      for (let i = 0; i < 1000; i++) {
        largeFileArray.push({
          id: i,
          filePath: `/path/to/file${i}.txt`,
          contentType: 'text/plain',
          writeable: true,
          lastPulled: Date.now(),
          applicationId: 'app1',
          changed: false,
          pullRequestQueued: false,
          displayProperties: null,
          icon: 'file-icon',
        });
      }

      mockFormGroup.get('file')?.setValue(largeFileArray);
      mockFormGroup.get('fileSize')?.setValue(largeFileArray.length);

      expect(() => component.selectAllFile()).not.toThrow();
      expect(component.selectedFilesId.length).toBe(1000);
    });
  });
});
