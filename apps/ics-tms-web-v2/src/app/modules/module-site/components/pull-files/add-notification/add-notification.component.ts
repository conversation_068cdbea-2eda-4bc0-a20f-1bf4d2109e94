import { Component, inject, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { BehaviorSubject, Observable, of, Subject, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';
import { UsersData } from '../../../models/users-data.model';
import { loadAllUsersData } from '../../../store/actions/users.actions';
import { UsersDataSelector } from '../../../store/selectors/users-data.selectors';
import { UsersResults } from '../../../../module-settings/model/users.model';
import { getLoginUserDetailsData } from '../../../../../store/selectors/globalStore.selectors';
import {
  CommonResponseData,
  IcsUser,
  LoginResponse,
  UserGroup,
} from '../../../../../models/common';
import { NAME_ONLY } from '../../../../../constants/appConstants';
import { TeamData } from '../../../../module-settings/model/team-data.model';
import {
  SearchableDropdownItem,
  SelectionEvent,
} from 'src/app/components/shared/ics-searchable-dropdown/ics-searchable-dropdown.component';
import { selectUserGroupsData } from 'src/app/store/selectors/user-groups.selector';
import { loadUserGroups } from 'src/app/store/actions/user-groups.actions';
import { selectUsersData } from 'src/app/store/selectors/users.selector';
import { loadUsers } from 'src/app/store/actions/users.actions';

@Component({
  selector: 'app-add-notification',
  templateUrl: './add-notification.component.html',
  styleUrls: ['./add-notification.component.scss'],
})
export class AddNotificationComponent implements OnInit {
  @Input() formGroup!: FormGroup;

  @Input() activeStepIndex!: number;

  usersData: UsersData = {} as UsersData;

  currentUserData: LoginResponse = {} as LoginResponse;

  protected readonly of = of;

  selectedUsers: UsersData['results'] = [];

  protected readonly NAME_ONLY = NAME_ONLY;

  userGroups$ = new BehaviorSubject<CommonResponseData<UserGroup>>(
    {} as CommonResponseData<UserGroup>
  );

  selectedUserGroups$ = new BehaviorSubject<UserGroup[]>([]);

  isEditPeople!: boolean;

  userData!: IcsUser;

  users$ = new Observable<CommonResponseData<IcsUser>>();

  selectedUsers$ = new BehaviorSubject<IcsUser[]>([]);

  teamsData!: TeamData;

  private destroy$ = new Subject<void>();

  store = inject(Store);

  ngOnInit() {
    this.getAddNotificationData();
    this.store.dispatch(loadUserGroups({}));
    this.store.dispatch(loadUsers({}));
    this.store.dispatch(loadAllUsersData({ pageIndex: 0, pageSize: -1 }));
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getAddNotificationData(): void {
    this.store
      .select(UsersDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.usersData = data;
      });

    this.store
      .select(getLoginUserDetailsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.currentUserData = data;
        if (this.currentUserData) {
          const currentUser: UsersResults = {
            id: this.currentUserData.sub || '',
            email: this.currentUserData.email || '',
            emailVerified: true,
            mfaConfigured: false,
            status: 1,
            fullName: this.currentUserData.fullName || '',
            company: {
              id: '',
              name: '',
              featureFlags: [],
            },
            roles: this.currentUserData.roles || [],
          };
          this.selectedUsers.push(currentUser);
          this.formGroup
            .get('selectedNotification')
            ?.setValue(this.selectedUsers);
        }
      });

    this.users$ = this.store.select(selectUsersData);

    this.store
      .select(selectUserGroupsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(userGroups => {
        this.userGroups$.next(userGroups);
      });
  }

  updateUserGroups({ item: userGroup, action }: SelectionEvent) {
    const typedUserGroup = userGroup as UserGroup;

    if (action === 'remove') {
      this.selectedUserGroups$.next(
        this.selectedUserGroups$
          .getValue()
          .filter(group => group.id !== typedUserGroup.id)
      );
      return;
    }
    this.selectedUserGroups$.next([
      ...this.selectedUserGroups$.getValue(),
      typedUserGroup,
    ]);
  }

  checkIfClearDisabled = (item: SearchableDropdownItem) => {
    if (!this.isEditPeople) return false;
    const userGroup = this.userGroups$
      .getValue()
      .results.find(group => group.id === (item as UserGroup)?.id);
    const onlyOneUser = (userGroup?.usercount ?? 0) <= 1;
    if (!onlyOneUser) return false;
    const isCurrentUserIsTheOnlyUser = this.userData?.userGroups?.some(
      group => group?.id === userGroup?.id
    );
    return Boolean(isCurrentUserIsTheOnlyUser);
  };

  updateMembers({ item: user, action }: SelectionEvent) {
    const typedUser = user as IcsUser;

    if (action === 'remove') {
      this.selectedUsers$.next(
        this.selectedUsers$
          .getValue()
          .filter(member => member.id !== typedUser.id)
      );
      return;
    }
    const existingUser = this.teamsData?.users.find(
      preExistingUser => preExistingUser.id === typedUser.id
    );
    const newUser = { ...typedUser, ...existingUser };
    this.selectedUsers$.next([
      ...this.selectedUsers$.getValue(),
      { ...newUser, ...(newUser?.isAdmin === undefined && { isAdmin: true }) },
    ]);
  }

  onGetData(data: any) {
    this.selectedUsers = data;
    this.formGroup.get('selectedNotification')?.setValue(this.selectedUsers);
  }

  getStringInitials(fullName: string): string {
    const initials = (fullName?.match(/\b\w/g) ?? []).map(char =>
      char.toUpperCase()
    );
    return initials.join('');
  }

  removeUserAndAddToDD(user: UsersResults) {
    this.selectedUsers = this.selectedUsers.filter(data => data.id !== user.id);
    this.formGroup.get('selectedNotification')?.setValue(this.selectedUsers);
  }
}
