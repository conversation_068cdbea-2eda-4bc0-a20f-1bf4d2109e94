@media (min-width: 99.2rem) {
  .ics-config-list-group .list-group-item .value {
    padding: 0 0 0 0 !important;
  }
}

.custom-select-wrapper {
  position: relative;
  display: inline-block;
}

.custom-select-wrapper select {
  width: 100%;
  padding-right: 1.8em; /* Reduce this value */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: none;
  border: 0.1rem solid #ccc;
  border-radius: 0.5rem;
  height: 2.5em;
  font-size: 1em;
  box-sizing: border-box;
}

/* Custom chevron arrow */
.custom-select-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0.6em; /* Reduce this value to move arrow closer to the edge */
  width: 0.8em;
  height: 0.8em;
  pointer-events: none;
  transform: translateY(-50%);
  background: url('data:image/svg+xml;utf8,<svg width="12" height="8" viewBox="0 0 12 8" xmlns="http://www.w3.org/2000/svg"><path d="M2 2l4 4 4-4" stroke="black" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>')
    no-repeat center center;
  background-size: contain;
}

.panel {
  background-color: #fff;
  border: 0.1rem solid rgba(0, 0, 0, 0.12);
  border-radius: 0.4rem;
  box-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.05);

  .panel-heading {
    background-color: transparent;
    border-bottom: 0.1rem solid rgba(0, 0, 0, 0.12);
    padding: 1.5rem;

    .panel-title {
      margin: 0.7rem 0;
      font-size: 2rem;
      color: rgba(0, 0, 0, 0.87);
    }

    .actions-container {
      display: flex;
      gap: 1rem;
      align-items: center;
    }
    box-sizing: border-box;

    &-title {
      font-size: 1.4rem; // smaller font size as original
      font-weight: bold;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50%;
      display: inline-block;
    }

    &__controls {
      display: flex;
      align-items: center;
      gap: 1rem; // space between select and button
      margin-left: auto; // push controls to right

      select.form-control {
        width: 70%;
        height: 3.4rem;
        padding: 0.6rem 1.2rem;
        font-size: 1.4rem;
        border-radius: 0.3rem;
        border: 0.1rem solid #ccc;
        color: #212121;
        appearance: auto;
        -webkit-appearance: auto;
        -moz-appearance: auto;
      }

      button {
        width: 6rem;
        height: 2.8rem;
        font-size: 1.2rem;
        padding: 0;
        line-height: 1.5;
        border-radius: 0.2rem;
        background-color: #027299;
        border-color: transparent;
        border-radius: 0.3rem;
        color: white;
        cursor: pointer;

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          background-color: #027299;
          border-color: transparent;
        }

        &.has-icon {
          padding-left: 3rem;
        }
      }
    }
  }
}

.list-group-item {
  padding-left: 15px;

  &::after {
    content: '';
    display: table;
    clear: both;
  }

  .name {
    float: left;
    width: 50%;
    padding-right: 1.5rem;
  }

  .value {
    float: left;
    width: 50%;

    input {
      width: 100%;
    }
  }
}
