<div class="panel panel-default bs-primary mb40 ics-device-config">
  <div class="panel-heading panel-heading-has-btn">
    <h3 class="panel-title">Network</h3>
    <div class="pull-right" style="display: flex; align-items: center">
      <div class="custom-select-wrapper" style="margin-right: 1rem">
        <select
          id="networkFunction"
          class="form-control"
          [(ngModel)]="selectedFunction"
        >
          <option value="" disabled hidden>Network Function</option>
          <option value="traceroute">Traceroute</option>
          <option value="url">URL</option>
        </select>
      </div>

      <button
        class="btn btn-primary btn-sm btn-animated display-inline-block"
        id="btnDeviceSaveConfig"
        [ngClass]="{ 'has-icon': saving.step2 || saving.step3 }"
        (click)="onUpdateClick()"
        [disabled]="
          (selectedFunction === 'traceroute' && !currentValue) ||
          (selectedFunction === 'url' && !urlValue)
        "
        style="width: 6rem"
      >
        <span>Update</span>
      </button>
    </div>
  </div>
  <div class="list-group ics-config-list-group">
    <div>
      <div class="list-group-item">
        <div [ngClass]="{ modified: isValueUpdated() }">
          <div (click)="updateValue()">
            <!-- Field Label -->
            <div class="name">
              <span>{{
                selectedFunction === 'traceroute'
                  ? 'Traceroute'
                  : selectedFunction === 'url'
                    ? 'URL'
                    : 'Traceroute'
              }}</span>
            </div>
            <!-- / Field Label -->

            <!-- Field Value -->
            <div
              *ngIf="selectedFunction === 'traceroute' || !selectedFunction"
              class="value"
            >
              <div>
                <input
                  (blur)="handleBlur($event)"
                  autofocus="autofocus"
                  type="text"
                  class="form-control"
                  placeholder="IP Address"
                  [(ngModel)]="currentValue"
                />
              </div>
            </div>
            <!-- / Field Value -->
            <div class="value" *ngIf="selectedFunction === 'url'">
              <div>
                <input
                  (blur)="handleUrl($event)"
                  class="form-control"
                  type="text"
                  placeholder="Enter URL"
                  [(ngModel)]="urlValue"
                  autofocus="autofocus"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
