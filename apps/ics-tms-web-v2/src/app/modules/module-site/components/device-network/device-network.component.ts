import { Component, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import { firstValueFrom, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { filter, find } from 'lodash';
import { DevicesService } from '../../services/devices.service';
import { FileUploadRequest } from '../../models/file-upload.model';
import { AuthService } from 'src/app/services/auth.service';
import { UsersService } from 'src/app/services/users.service';
import { UserGroupsService } from 'src/app/services/user-groups.service';

dayjs.extend(timezone);

@Component({
  selector: 'app-device-network',
  templateUrl: './device-network.component.html',
  styleUrls: ['./device-network.component.scss'],
})
export class DeviceNetworkComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  selectedFunction: string = 'traceroute';

  currentValue: string = '';

  urlValue: string = '';

  isEditing: boolean = false;

  oldValue: string = '';

  deviceId: string;

  deviceDetails: any;

  lastFetchedName: string = '';

  people: any[] = [];

  person: any;

  teams: any[] = [];

  saving = {
    step2: false,
    step3: false,
  };

  model: FileUploadRequest = {
    files: [
      {
        path: 'network-diagnostics/traceroute-test',
        applicationId: 'invenco.system',
      },
    ],
    name: '',
    devices: [],
    sites: [],
    siteTags: [],
    users: [],
    teams: [],
    additionalProperties: {
      userParams: {
        host: '',
      },
    },
  };

  private route = inject(ActivatedRoute);

  private devicesService = inject(DevicesService);

  private userGroupsService = inject(UserGroupsService);

  private usersService = inject(UsersService);

  constructor() {
    this.deviceId = this.route.snapshot.params['device_id'];
    this.model.devices = [{ id: this.deviceId }];
  }

  ngOnInit() {
    this.initializeData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeData() {
    const fetchNamePromise = this.devicesService
      .getdata(this.deviceId)
      .toPromise()
      .then(deviceDetails => {
        this.deviceDetails = deviceDetails;
        this.currentValue = this.deviceDetails?.ipAddress || '';
        this.oldValue = this.currentValue;
        this.currentValue = '';

        this.model.additionalProperties = {
          userParams: {
            host: this.currentValue,
          },
        };
        this.model.devices = [{ id: Number(this.deviceId) }];

        return this.devicesService
          .getNameByTemplate({
            timezone: dayjs.tz.guess(),
            deviceId: Number(this.deviceId),
            devices: [{ id: Number(this.deviceId) }],
            sites: [],
            siteTags: [],
          })
          .toPromise();
      })
      .then(nameResponse => {
        this.model.name = nameResponse?.name || '';
        this.lastFetchedName = this.model.name;
      })
      .catch(error => {
        console.error('Failed to initialize data:', error);
      });

    const fetchUsersPromise = firstValueFrom(this.usersService.getUsersData())
      .then((data: any) => {
        const myId = AuthService.getUser().sub;
        this.people = filter(data.results, o => o.id !== myId);
        this.person = find(data.results, ['id', myId]);
        if (this.person && this.model.users) {
          this.model.users.push(this.person);
        }
      })
      .catch((error: any) => {
        console.error('Failed to fetch users:', error);
      });

    interface Team {
      id: string | number;
      name: string;
      [key: string]: any;
    }

    const fetchTeamsPromise: Promise<void> = firstValueFrom(
      (this.userGroupsService as UserGroupsService).getAllUserGroups()
    )
      .then((data: Team[]) => {
        this.teams = data;
      })
      .catch((error: any) => {
        console.error('Failed to fetch user groups:', error);
      });

    Promise.all([fetchNamePromise, fetchUsersPromise, fetchTeamsPromise])
      .then(() => {})
      .catch(error => {
        console.error('Initialization failed:', error);
      });
  }

  isValueUpdated(): boolean {
    if (this.selectedFunction === 'traceroute') {
      return this.currentValue !== this.oldValue;
    }
    return !!this.urlValue;
  }

  updateValue(): void {
    this.isEditing = !this.isEditing;
  }

  handleBlur(event: FocusEvent): void {
    const target = event.target as HTMLInputElement;
    this.currentValue = target.value;
    this.isEditing = false;
  }

  handleUrl(event: FocusEvent): void {
    const target = event.target as HTMLInputElement;
    this.urlValue = target.value;
  }

  onUpdateClick(): void {
    if (this.selectedFunction === 'traceroute') {
      if (!this.currentValue) return;

      this.saving.step2 = true;
      this.model.additionalProperties = {
        userParams: {
          host: this.currentValue,
        },
      };
      this.model.files = [
        {
          path: 'network-diagnostics/traceroute-test',
          applicationId: 'invenco.system',
        },
      ];

      this.devicesService
        .requestFileUploadNetwork(this.model)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: _response => {
            this.saving.step2 = false;
            this.saving.step3 = true;
            this.oldValue = this.currentValue;
            this.currentValue = '';

            setTimeout(() => {
              this.saving.step3 = false;
            }, 1500);
          },
          error: error => {
            console.error('Failed to update network settings:', error);
            this.saving.step2 = false;
          },
        });
    } else {
      if (!this.urlValue) return;

      const urlModel: FileUploadRequest = {
        ...this.model,
        additionalProperties: {
          userParams: {
            url: this.urlValue,
          },
        },
        files: [
          {
            path: 'network-diagnostics/url-test',
            applicationId: 'invenco.system',
          },
        ],
      };

      this.devicesService
        .requestFileUploadNetwork(urlModel)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: _response => {
            this.urlValue = '';
          },
          error: error => {
            console.error('Failed to send URL test request:', error);
          },
        });
    }
  }
}
