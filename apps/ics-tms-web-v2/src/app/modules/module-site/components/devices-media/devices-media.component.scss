.card {
  border: none;

  .card-title {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      font-size: 2rem;
      font-weight: 400;
      margin: 0.7rem 0;
    }

    .icon {
      font-size: 1.5rem;
      color: gold;
      margin-right: 0.5rem;
    }
  }

  .card-body {
    padding: 1rem 1.5rem;

    .media-sub-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 1.3rem;
      color: var(--label-unknown);

      .time-history {
        font-size: 1.2rem;
        font-weight: 500;
        color: var(--label-unknown);
      }

      .media-search-input {
        input {
          height: 2.8rem;
          margin: 0;
          border: 0.1rem solid var(--color-border);
          border-radius: 0.3rem;
          padding: 0 1rem;
          font-size: 1rem;
          width: 180px;
        }
      }
    }

    .table {
      .media-table-head {
        font-size: 1.2rem;
        border-bottom: 0.2rem solid var(--divider-color) !important;
        min-width: 100% !important;
      }

      td {
        background-color: var(--color-bg-fa);
        padding: 0.8rem;
        text-align: left;
      }

      .col-1 {
        background-color: var(--color-black-shade-eight);
        width: 6%;
      }

      .col-2 {
        background-color: var(--color-black-shade-eight);
        width: 50%;
      }

      .col-3 {
        background-color: var(--color-black-shade-eight);
        width: 14%;
      }

      .col-4 {
        background-color: var(--color-black-shade-eight);
        width: 16%;
      }

      .text-muted {
        color: var(--md-white-30);
      }

      .text-highlight {
        background-color: var(--md-yellow-500);
        padding: 0.2rem;
      }
    }

    .no-data {
      justify-content: center;
      align-content: center;
      display: flex;
      font-size: 1.4rem;
      margin-top: 2rem;
      padding: 1rem 1.5rem !important;
      border-top: 0.2rem solid var(--divider-color) !important;
      color: var(--label-unknown);
    }
  }

  .playlist-section {
    display: flex;
    flex-direction: column;
    padding: 1rem;

    .title {
      font-size: 1.2rem;
      font-weight: 500;
      color: var(--label-unknown);
      margin-bottom: 1rem;
    }

    .section-left {
      display: flex;
      flex-direction: row;

      .thumbnail {
        flex: 1;
        padding: 0;
        margin: 0;
        border: none;
        max-width: 130px;
        max-height: 75px;

        .carousel {
          max-width: 130px;
          max-height: 75px;

          .image-wrapper img {
            width: 130px;
            height: 75px;
            max-height: 75px;
            border-radius: 1px;
          }
        }
      }

      .meta {
        flex: 2;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: 0.5rem;
        align-items: left;
        margin-left: 1.2rem;
        margin-top: 0;

        .field-main {
          font-size: 1.4rem;
          margin-right: 1.5rem;

          .link-unstyled {
            color: var(--color-black) !important;
            font-weight: 700;
          }

          .label {
            display: inline-block;
            padding: 0.3rem 0.3rem;
            border-radius: 0.3rem;
            font-size: 1.3rem;
            font-weight: 500;
            margin-left: 0.5rem;

            &.draft {
              background-color: var(--label-default);
              color: var(--color-white);
            }

            &.scheduled {
              background-color: var(--label-primary);
              color: var(--color-white);
            }

            &.live {
              background-color: var(--label-success);
              color: var(--color-white);
            }

            &.expired {
              background-color: gray;
              color: var(--color-white);
            }
          }
        }

        .field {
          color: var(--label-unknown);
        }
      }
    }
  }

  .history {
    margin-top: 2rem;

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      input {
        padding: 0.5rem;
        font-size: 1rem;
        width: 200px;
        border-radius: 4px;
        border: 1px solid var(--color-border);
      }
    }

    .history-table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        padding: 0.5rem;
        border-bottom: 1px solid #ccc;
        text-align: left;
      }

      td[colspan] {
        text-align: center;
        color: #666;
        padding: 1rem;
      }
    }
  }
}
