import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Store } from '@ngrx/store';
import { ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import dayjs from 'dayjs';
import { DevicesService } from '../../services/devices.service';
import { PlaylistsService } from '../../services/playlists.service';
import { loadDeviceMedia } from '../../store/actions/device-media.actions';
import { DeviceData } from '../../models/devices.interface';
import { DevicesMediaComponent } from './devices-media.component';
import { AuthService } from 'src/app/services/auth.service';
import { Company } from 'src/app/models/common';

describe('DevicesMediaComponent', () => {
  let component: DevicesMediaComponent;
  let fixture: ComponentFixture<DevicesMediaComponent>;
  let mockStore: any;
  let mockDevicesService: any;
  let mockPlaylistsService: any;

  const mockDeviceData: Partial<DeviceData> = {
    id: 'device-123',
    siteId: 'site-456',
    siteName: 'Test Site',
    lastRegistered: '2023-01-01T10:00:00Z',
    lastContact: '2023-01-01T10:00:00Z',
    name: 'Test Device',
    description: 'Test Device Description',
    serialNumber: 'SN123456',
    keyGroupRef: 'KG123',
    keyGroupId: 'KGI123',
    presence: 'online',
    status: 1,
    gatewayAddress: '***********',
    macAddress: '00:11:22:33:44:55',
    subnetMask: '*************',
    releaseVersion: '1.0.0',
    alarmRulesSettings: {
      suspended: false,
      suspendedByDeviceUntil: 0,
      suspendedFrom: 0,
      suspendedUntil: 0,
    },
    ipAddress: '*************',
    deviceType: {
      id: 'dt-123',
      name: 'Test Device Type',
    },
    promptSet: {
      id: 'ps-123',
      name: 'Test Prompt Set',
      version: '1.0',
    },
    ksn: 'KSN123',
    playlistId: 'playlist-789',
    lastSuccessfulRki: '2023-01-01T10:00:00Z',
    siteKeygroupId: 'SKG123',
    statusAlarmTs: '2023-01-01T10:00:00Z',
    oosConditions: [],
    statusStr: 'Online',
    configSchema: {} as any,
    configForm: {} as any,
    configData: {} as any,
  };

  const mockMediaData = [
    {
      name: 'Video 1',
      count: 5,
      totalPlayTime: 120000,
      lastPlayed: '2023-01-01T10:00:00Z',
    },
    {
      name: 'Video 2',
      count: 3,
      totalPlayTime: 90000,
      lastPlayed: '2023-01-02T15:30:00Z',
    },
  ];

  const mockPlaylistData = {
    id: 'playlist-789',
    name: 'Test Playlist',
    assets: [
      { thumbnailFileUrl: 'thumb1.jpg', length: 60 },
      { thumbnailFileUrl: 'thumb2.jpg', length: 90 },
    ],
    sites: [{ id: 'site-456', status: 2 }],
  };

  beforeEach(async () => {
    const storeSpy = jasmine.createSpyObj('Store', ['select', 'dispatch']);
    const devicesServiceSpy = jasmine.createSpyObj('DevicesService', [
      'getDeviceMediaData',
    ]);
    const playlistsServiceSpy = jasmine.createSpyObj('PlaylistsService', [
      'getPlaylist',
    ]);
    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [
      'snapshot',
    ]);

    await TestBed.configureTestingModule({
      declarations: [DevicesMediaComponent],
      imports: [FormsModule],
      providers: [
        { provide: Store, useValue: storeSpy },
        { provide: DevicesService, useValue: devicesServiceSpy },
        { provide: PlaylistsService, useValue: playlistsServiceSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(DevicesMediaComponent);
    component = fixture.componentInstance;
    mockStore = TestBed.inject(Store);
    mockDevicesService = TestBed.inject(DevicesService);
    mockPlaylistsService = TestBed.inject(PlaylistsService);
  });

  beforeEach(() => {
    mockStore.select.calls.reset();
    mockStore.dispatch.calls.reset();
    mockDevicesService.getDeviceMediaData.calls.reset();
    mockPlaylistsService.getPlaylist.calls.reset();

    if (component) {
      component['hasGetDeviceMediaDataInitialized'] = false;
      component.mediaData = [];
      component.filteredMedia = [];
      component.deviceId = '';
      component.siteId = '';
      component.data = undefined;
      component.device = undefined;
      component.mediaUTC = '';
      component.query = '';
    }

    mockStore.select.and.callFake((selector: any) => {
      const selectorStr = selector.toString();
      if (selectorStr.includes('devicesSelector')) {
        return of({
          devicesData: {
            devicesReducers: {
              data: mockDeviceData,
            },
          },
        });
      }
      if (selectorStr.includes('deviceMediaDataSelector')) {
        return of(mockMediaData);
      }
      return of(null);
    });

    mockDevicesService.getDeviceMediaData.and.returnValue(of(mockMediaData));
    mockPlaylistsService.getPlaylist.and.returnValue(of(mockPlaylistData));
  });

  afterEach(() => {
    if (component) {
      component.ngOnDestroy();
    }
    mockStore.select.calls.reset();
    mockStore.dispatch.calls.reset();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.displayedColumns).toEqual(jasmine.any(Array));
      expect(component.mediaData).toEqual([]);
      expect(component.filteredMedia).toEqual([]);
      expect(component.loading).toBe(true);
      expect(component.isLoadingMedia).toBe(true);
      expect(component.query).toBe('');
      expect(component.canViewUpdatePlaylist).toBe(true);
      expect(component.playlistSlide).toBe('0');
    });

    it('should call getDeviceMediaData on ngOnInit', () => {
      spyOn(component as any, 'getDeviceMediaData');
      component.ngOnInit();
      expect((component as any).getDeviceMediaData).toHaveBeenCalled();
    });

    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = spyOn(component['destroy$'], 'next');
      const completeSpy = spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Store Interactions', () => {
    it('should dispatch loadDeviceMedia action on ngOnInit', () => {
      component.deviceId = 'test-device-id';

      component.ngOnInit();

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        loadDeviceMedia({ deviceId: 'test-device-id' })
      );
    });

    it('should subscribe to devicesSelector and set device data', () => {
      component.data = mockDeviceData as any;
      component.device = mockDeviceData;
      component.deviceId = mockDeviceData.id!;
      component.siteId = mockDeviceData.siteId!;

      spyOn(component, 'initializeDeviceMediaComponent');

      component['hasGetDeviceMediaDataInitialized'] = false;

      component.initializeDeviceMediaComponent();

      expect(component.data).toEqual(jasmine.objectContaining(mockDeviceData));
      expect(component.device).toEqual(
        jasmine.objectContaining(mockDeviceData)
      );
      expect(component.deviceId).toBe('device-123');
      expect(component.siteId).toBe('site-456');
    });

    it('should subscribe to deviceMediaDataSelector and set media data', () => {
      component.mediaData = mockMediaData;
      component.mediaUTC = `(UTC${dayjs(mockMediaData[0].lastPlayed).format('Z')})`;

      expect(component.mediaData).toEqual(mockMediaData);
      expect(component.mediaUTC).toContain('UTC');
    });

    it('should subscribe to deviceMediaLoadingSelector and set loading state', () => {
      component.loading = true;

      expect(component.loading).toBe(true);
    });

    it('should handle empty media data without errors', () => {
      component.mediaData = [];
      component.mediaUTC = '';

      expect(component.mediaData).toEqual([]);
      expect(component.mediaUTC).toBe('');
    });

    it('should call store selectors on ngOnInit', () => {
      component.ngOnInit();

      expect(mockStore.select).toHaveBeenCalled();
    });
  });

  describe('initializeDeviceMediaComponent', () => {
    beforeEach(() => {
      component.deviceId = 'device-123';
      component.data = mockDeviceData as any;
    });

    it('should call getDeviceMediaData service and handle success', () => {
      component.initializeDeviceMediaComponent();

      expect(mockDevicesService.getDeviceMediaData).toHaveBeenCalledWith(
        'device-123'
      );
      expect(component.mediaData).toEqual(mockMediaData);
      expect(component.isLoadingMedia).toBe(false);
      expect(component['hasGetDeviceMediaDataInitialized']).toBe(true);
    });

    it('should handle getDeviceMediaData service error', () => {
      mockDevicesService.getDeviceMediaData.and.returnValue(
        throwError({ status: 500 })
      );

      component.initializeDeviceMediaComponent();

      expect(component.isLoadingMedia).toBe(false);
    });

    it('should return early if no playlistId', () => {
      component.data = { ...mockDeviceData, playlistId: undefined } as any;
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['PLAYLIST'],
      } as Company);

      component.initializeDeviceMediaComponent();

      expect(mockPlaylistsService.getPlaylist).not.toHaveBeenCalled();
    });

    it('should return early if no feature flags', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: [],
      } as Company);

      component.initializeDeviceMediaComponent();

      expect(mockPlaylistsService.getPlaylist).not.toHaveBeenCalled();
    });

    it('should return early if PLAYLIST feature flag not present', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['OTHER_FLAG'],
      } as Company);

      component.initializeDeviceMediaComponent();

      expect(mockPlaylistsService.getPlaylist).not.toHaveBeenCalled();
    });

    it('should set canViewUpdatePlaylist based on feature flags', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT'],
      } as Company);

      component.initializeDeviceMediaComponent();

      expect(component.canViewUpdatePlaylist).toBe(true);
    });

    it('should set canViewUpdatePlaylist to false if missing required flags', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['PLAYLIST'],
      } as Company);

      component.initializeDeviceMediaComponent();

      expect(component.canViewUpdatePlaylist).toBe(false);
    });

    it('should fetch and set playlist data successfully', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT'],
      } as Company);

      component.initializeDeviceMediaComponent();

      expect(mockPlaylistsService.getPlaylist).toHaveBeenCalledWith(
        'playlist-789'
      );
      expect(component.playlist).toEqual({
        name: 'Test Playlist',
        url: 'playlist/builder/playlist-789',
        thumbnails: ['thumb1.jpg', 'thumb2.jpg'],
        assetCount: 2,
        status: 2,
        length: 150000,
      });
    });

    it('should handle playlist 404 error', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT'],
      } as Company);
      mockPlaylistsService.getPlaylist.and.returnValue(
        throwError({ status: 404 })
      );

      component.initializeDeviceMediaComponent();

      expect(component.playlist).toBeUndefined();
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(() => {
      component.mediaData = [...mockMediaData];
      component.filteredMedia = [...mockMediaData];
    });

    it('should update query and filter media on onQueryChange', () => {
      spyOn(component, 'filterMediaStats');

      component.onQueryChange('Video 1');

      expect(component.query).toBe('Video 1');
      expect(component.filterMediaStats).toHaveBeenCalled();
    });

    it('should filter media by name', () => {
      component.query = 'Video 1';

      component.filterMediaStats();

      expect(component.filteredMedia).toEqual([mockMediaData[0]]);
    });

    it('should filter media by count', () => {
      component.query = '5';

      component.filterMediaStats();

      expect(component.filteredMedia).toEqual([mockMediaData[0]]);
    });

    it('should filter media by formatted play time', () => {
      component.query = '02:00';

      component.filterMediaStats();

      expect(component.filteredMedia).toEqual([mockMediaData[0]]);
    });

    it('should filter media by formatted last played date', () => {
      component.query = '2023';

      component.filterMediaStats();

      expect(component.filteredMedia.length).toBeGreaterThan(0);
      expect(component.filteredMedia.length).toBe(2);
    });

    it('should return all media when query is empty', () => {
      component.query = '';

      component.filterMediaStats();

      expect(component.filteredMedia).toEqual(mockMediaData);
    });

    it('should return empty array when mediaData is not an array', () => {
      component.mediaData = null as any;
      component.query = '';

      component.filterMediaStats();

      expect(component.filteredMedia).toEqual([]);
    });

    it('should return filtered media stats', () => {
      component.filteredMedia = [mockMediaData[0]];

      const result = component.filteredMediaStats();

      expect(result).toEqual([mockMediaData[0]]);
    });
  });

  describe('Utility Methods', () => {
    it('should highlight search text', () => {
      const result = component.highlight('Video Test', 'Test');

      expect(result).toBe("Video <span class='text-highlight'>Test</span>");
    });

    it('should return original text when search is empty', () => {
      const result = component.highlight('Video Test', '');

      expect(result).toBe('Video Test');
    });

    it('should return original text when text is empty', () => {
      const result = component.highlight('', 'Test');

      expect(result).toBe('');
    });

    it('should convert milliseconds to time duration', () => {
      expect(component.convertMSToTimeDuration(120000)).toBe('00:02:00');
      expect(component.convertMSToTimeDuration(3661000)).toBe('01:01:01');
      expect(component.convertMSToTimeDuration(0)).toBe('00:00:00');
    });

    it('should return empty string for null milliseconds', () => {
      expect(component.convertMSToTimeDuration(null as any)).toBe('');
    });

    it('should return correct playlist status class', () => {
      expect(component.playlistStatusClass(0)).toBe('draft');
      expect(component.playlistStatusClass(1)).toBe('scheduled');
      expect(component.playlistStatusClass(2)).toBe('live');
      expect(component.playlistStatusClass(3)).toBe('expired');
      expect(component.playlistStatusClass(99)).toBe('default');
    });

    it('should format last played date', () => {
      const result = component.formatLastPlayed('2023-01-01T10:00:00Z');

      expect(result).toContain('Jan 1, 2023 at');
    });

    it('should get column header with UTC for 5th column', () => {
      component.mediaUTC = '(UTC+05:30)';

      expect(component.getColumnHeader('Last Played', 4)).toBe(
        'Last Played(UTC+05:30)'
      );
      expect(component.getColumnHeader('Name', 0)).toBe('Name');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty media data array', () => {
      component.mediaData = [];
      component.query = 'test';

      component.filterMediaStats();

      expect(component.filteredMedia).toEqual([]);
    });

    it('should handle media data without lastPlayed property', () => {
      const mediaWithoutLastPlayed = [
        {
          name: 'Video 1',
          count: 5,
          totalPlayTime: 120000,
        },
      ];
      component.mediaData = mediaWithoutLastPlayed;

      component.ngOnInit();

      expect(() => component.filterMediaStats()).not.toThrow();
    });

    it('should handle store selector returning undefined', () => {
      mockStore.select.and.returnValue(of(undefined));

      component.ngOnInit();

      expect(component.mediaData).toEqual([]);
    });

    it('should not initialize twice', () => {
      component['hasGetDeviceMediaDataInitialized'] = true;
      spyOn(component, 'initializeDeviceMediaComponent');

      component.ngOnInit();

      expect(component.initializeDeviceMediaComponent).not.toHaveBeenCalled();
    });

    it('should handle getPlaylist error with status != 404', () => {
      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        name: 'Test Company',
        featureFlags: ['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT'],
      } as Company);
      mockPlaylistsService.getPlaylist.and.returnValue(
        throwError({ status: 500 })
      );

      component.initializeDeviceMediaComponent();
      expect(component.playlist).toBeUndefined();
    });

    it('should convert negative and undefined ms to time duration', () => {
      expect(component.convertMSToTimeDuration(-1000)).toBe('');
      expect(component.convertMSToTimeDuration(undefined as any)).toBe('');
    });

    it('should not highlight if text is not a string', () => {
      expect(component.highlight(null as any, 'Test')).toBeNull();
      expect(component.highlight(undefined as any, 'Test')).toBeUndefined();
    });

    it('should get column header for index other than 4 and non-string col', () => {
      expect(component.getColumnHeader('Other', 2)).toBe('Other');
      expect(component.getColumnHeader(undefined as any, 2)).toBeUndefined();
    });

    it('should filter mediaStats with whitespace query and undefined mediaData', () => {
      component.query = '   ';
      component.mediaData = undefined as any;
      component.filterMediaStats();
      expect(component.filteredMedia).toEqual([]);
    });

    it('should not call initializeDeviceMediaComponent if hasGetDeviceMediaDataInitialized is true', () => {
      const spy = spyOn(component, 'initializeDeviceMediaComponent');
      (component as any)['hasGetDeviceMediaDataInitialized'] = true;
      mockStore.select.and.returnValue({
        pipe: () => ({
          subscribe: (cb: any) =>
            cb({
              devicesData: {
                devicesReducers: {
                  data: mockDeviceData,
                },
              },
            }),
        }),
      });
      (component as any).getDeviceMediaData();
      expect(spy).not.toHaveBeenCalled();
    });

    it('should not set mediaUTC if lastPlayed is missing', () => {
      const data = [{ name: 'No LastPlayed', count: 1, totalPlayTime: 1 }];
      mockStore.select.and.callFake((selector: any) => {
        if (selector.toString().includes('deviceMediaDataSelector')) {
          return { pipe: () => ({ subscribe: (cb: any) => cb(data) }) };
        }
        if (selector.toString().includes('devicesSelector')) {
          return { pipe: () => ({ subscribe: () => {} }) };
        }
        return { pipe: () => ({ subscribe: () => {} }) };
      });
      (component as any).getDeviceMediaData();
      expect(component.mediaUTC).toBe('');
    });

    it('should subscribe to deviceMediaLoadingSelector', () => {
      (component as any).getDeviceMediaData();

      expect(mockStore.select).toHaveBeenCalled();

      expect(component.loading).toBeDefined();
    });

    it('should highlight non-string text gracefully', () => {
      expect(component.highlight(12345 as any, '2')).toBe(
        "1<span class='text-highlight'>2</span>345"
      );
    });

    it('should highlight regex special characters in search', () => {
      const result = component.highlight('foo.bar', '.');
      expect(result).toBe("foo<span class='text-highlight'>.</span>bar");
    });

    it('should getColumnHeader return undefined for undefined/null col', () => {
      expect(component.getColumnHeader(undefined as any, 1)).toBeUndefined();
      expect(component.getColumnHeader(null as any, 1)).toBeNull();
    });

    it('should set filteredMedia to [] if mediaData is not array', () => {
      component.mediaData = 'not-an-array' as any;
      component.query = '';
      component.filterMediaStats();
      expect(component.filteredMedia).toEqual([]);
    });

    it('should initialize component when devicesState has data and hasGetDeviceMediaDataInitialized is false', () => {
      component['hasGetDeviceMediaDataInitialized'] = false;
      const spy = spyOn(component, 'initializeDeviceMediaComponent');

      const devicesState = {
        devicesData: {
          devicesReducers: {
            data: { id: 'test-id', siteId: 'test-site' },
          },
        },
      };

      if (
        devicesState?.devicesData?.devicesReducers?.data?.id &&
        !component['hasGetDeviceMediaDataInitialized']
      ) {
        component.data = devicesState.devicesData.devicesReducers.data as any;
        component.device = component.data;
        component.deviceId = component.data?.id ?? '';
        component.siteId = component.data?.siteId ?? '';
        component.initializeDeviceMediaComponent();
      }

      expect(component.data).toEqual(
        jasmine.objectContaining({ id: 'test-id', siteId: 'test-site' })
      );
      expect(component.device).toEqual(
        jasmine.objectContaining({ id: 'test-id', siteId: 'test-site' })
      );
      expect(component.deviceId).toBe('test-id');
      expect(component.siteId).toBe('test-site');
      expect(spy).toHaveBeenCalled();
    });

    it('should set mediaUTC when mediaData has lastPlayed', () => {
      const dataWithLastPlayed = [
        { name: 'Video', lastPlayed: '2023-01-01T10:00:00Z' },
      ];

      component.mediaData = dataWithLastPlayed || [];
      if (
        component.mediaData &&
        component.mediaData.length > 0 &&
        component.mediaData[0]?.lastPlayed
      ) {
        component.mediaUTC = `(UTC${dayjs(component.mediaData[0].lastPlayed).format('Z')})`;
      }
      component.filterMediaStats();

      expect(component.mediaUTC).toContain('UTC');
      expect(component.mediaData).toEqual(dataWithLastPlayed);
    });

    it('should build playlist with assets mapping and reduction', () => {
      const playlistWithAssets = {
        id: 'playlist-123',
        name: 'Test Playlist',
        assets: [
          { thumbnailFileUrl: 'thumb1.jpg', length: 60 },
          { thumbnailFileUrl: 'thumb2.jpg', length: 90 },
          { thumbnailFileUrl: 'thumb3.jpg' },
        ],
        sites: [{ id: 'site-456', status: 2 }],
      };

      spyOn(AuthService, 'getCompany').and.returnValue({
        id: 'company-123',
        featureFlags: ['GSTV', 'PLAYLIST', 'PLAYLIST_MANAGEMENT'],
      } as any);

      mockPlaylistsService.getPlaylist.and.returnValue(of(playlistWithAssets));
      component.data = {
        playlistId: 'playlist-123',
        siteId: 'site-456',
      } as any;

      component.initializeDeviceMediaComponent();

      expect(component.playlist).toEqual({
        name: 'Test Playlist',
        url: 'playlist/builder/playlist-123',
        thumbnails: ['thumb1.jpg', 'thumb2.jpg', 'thumb3.jpg'],
        assetCount: 3,
        status: 2,
        length: 150000,
      });
    });
  });
});
