import { Component, inject, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { ActivatedRoute } from '@angular/router';
import dayjs from 'dayjs';
import { Subject, takeUntil } from 'rxjs';
import isEmpty from 'lodash/isEmpty';
import { loadDeviceMedia } from '../../store/actions/device-media.actions';

import {
  deviceMediaDataSelector,
  deviceMediaLoadingSelector,
} from '../../store/selectors/device-media.selectors';
import { MEDIA_TABLE_COLUMNS } from '../../constants/appConstants';
import { DeviceData } from '../../models/devices.interface';
import { devicesSelector } from '../../store/selectors/devices.selector';
import { PlaylistsService } from '../../services/playlists.service';
import { DevicesService } from '../../services/devices.service';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-devices-media',
  templateUrl: './devices-media.component.html',
  styleUrls: ['./devices-media.component.scss'],
})
export class DevicesMediaComponent implements OnInit {
  displayedColumns: string[] = MEDIA_TABLE_COLUMNS;

  data?: DeviceData;

  device: any;

  siteId: string = '';

  deviceId: string = '';

  mediaData: any[] = [];

  filteredMedia: any[] = [];

  loading = true;

  isLoadingMedia = true;

  query: string = '';

  playlist: any;

  canViewUpdatePlaylist = true;

  playlistSlide: string = '0';

  mediaUTC: string = '';

  store = inject(Store);

  route = inject(ActivatedRoute);

  devicesService = inject(DevicesService);

  playlistsService = inject(PlaylistsService);

  private destroy$ = new Subject<void>();

  private hasGetDeviceMediaDataInitialized = false;

  ngOnInit(): void {
    this.getDeviceMediaData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getDeviceMediaData(): void {
    this.store
      .select(devicesSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe((devicesState: any) => {
        if (
          devicesState?.devicesData?.devicesReducers?.data?.id &&
          !this.hasGetDeviceMediaDataInitialized
        ) {
          this.data = devicesState.devicesData.devicesReducers.data;
          this.device = this.data;
          this.deviceId = this.data?.id ?? '';
          this.siteId = this.data?.siteId ?? '';
          this.initializeDeviceMediaComponent();
        }
      });

    this.store.dispatch(loadDeviceMedia({ deviceId: this.deviceId }));

    this.store
      .select(deviceMediaDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.mediaData = data || [];
        if (
          this.mediaData &&
          this.mediaData.length > 0 &&
          this.mediaData[0]?.lastPlayed
        ) {
          this.mediaUTC = `(UTC${dayjs(this.mediaData[0].lastPlayed).format('Z')})`;
        }
        this.filterMediaStats();
      });

    this.store
      .select(deviceMediaLoadingSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(isLoading => {
        this.loading = isLoading;
      });
  }

  initializeDeviceMediaComponent() {
    this.isLoadingMedia = true;
    this.devicesService.getDeviceMediaData(this.deviceId).subscribe({
      next: (media: any) => {
        this.mediaData = media;
        this.isLoadingMedia = false;
        this.hasGetDeviceMediaDataInitialized = true;
      },
      error: () => {
        this.isLoadingMedia = false;
      },
    });

    const featureFlags = AuthService.getCompany()?.featureFlags || [];

    if (
      !this.data?.playlistId ||
      isEmpty(featureFlags) ||
      featureFlags.indexOf('PLAYLIST') < 0
    ) {
      return;
    }
    this.canViewUpdatePlaylist = [
      'GSTV',
      'PLAYLIST',
      'PLAYLIST_MANAGEMENT',
    ].every(f => featureFlags.includes(f));

    this.playlistsService.getPlaylist(this.data.playlistId).subscribe({
      next: (response: any) => {
        const site = response.sites?.find(
          (s: any) => s.id === this.data?.siteId
        );
        if (site) {
          this.playlist = {
            name: response.name,
            url: `playlist/builder/${response.id}`,
            thumbnails:
              response.assets?.map((a: any) => a.thumbnailFileUrl) || [],
            assetCount: response.assets?.length || 0,
            status: site.status,
            length:
              (response.assets?.reduce(
                (sum: number, a: any) => sum + (a.length || 0),
                0
              ) || 0) * 1000,
          };
        }
      },
      error: (err: any) => {
        if (err.status === 404) {
          this.playlist = undefined;
        }
      },
    });
  }

  onQueryChange(event: string): void {
    this.query = event;
    this.filterMediaStats();
  }

  filterMediaStats(): void {
    if (this.query?.trim() && this.mediaData.length) {
      const q = this.query.toLowerCase();

      this.filteredMedia = this.mediaData?.filter(
        media =>
          media?.name?.toLowerCase().includes(q) ||
          String(media.count).includes(q) ||
          this.convertMSToTimeDuration(media.totalPlayTime)
            .toLowerCase()
            .includes(q) ||
          (media.lastPlayed &&
            new Date(media.lastPlayed)
              .toLocaleString()
              .toLowerCase()
              .includes(q))
      );
    } else {
      this.filteredMedia = Array.isArray(this.mediaData)
        ? [...this.mediaData]
        : [];
    }
  }

  filteredMediaStats(): any[] {
    return this.filteredMedia;
  }

  highlight(text: any, search: string): string {
    if (!text || !search) return text;
    const textStr = typeof text !== 'string' ? String(text) : text;
    const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedSearch, 'gi');
    return textStr.replace(
      regex,
      (match: string) => `<span class='text-highlight'>${match}</span>`
    );
  }

  convertMSToTimeDuration(ms: number): string {
    if (ms === null || ms === undefined || ms < 0) return '';

    const sec = Math.floor((ms / 1000) % 60)
      .toString()
      .padStart(2, '0');
    const min = Math.floor((ms / 1000 / 60) % 60)
      .toString()
      .padStart(2, '0');
    const hr = Math.floor(ms / (1000 * 60 * 60))
      .toString()
      .padStart(2, '0');

    return `${hr}:${min}:${sec}`;
  }

  playlistStatusClass(status: number): string {
    const map: Record<number, string> = {
      0: 'draft',
      1: 'scheduled',
      2: 'live',
      3: 'expired',
    };
    return map[status] || 'default';
  }

  formatLastPlayed(date: string): string {
    return dayjs(date).format('MMM D, YYYY [at] h:mm a');
  }

  getColumnHeader(col: string, i: number): string {
    let colHeader = col;
    if (i === 4) {
      colHeader += this.mediaUTC;
    }
    return colHeader;
  }
}
