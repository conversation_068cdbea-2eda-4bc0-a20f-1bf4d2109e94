<div class="card">
  <div class="card-title d-flex justify-content-between align-items-center">
    <h2 class="m-0">Media</h2>
  </div>

  <hr class="p-0 m-0" />

  <!-- Playlist Section -->
  <section *ngIf="playlist" class="playlist-section">
    <div class="title">PLAYLIST</div>
    <div class="section-left">
      <div class="thumbnail">
        <ngb-carousel
          class="carousel"
          #carousel
          [interval]="2000"
          [pauseOnHover]="true"
          [pauseOnFocus]="true"
          [showNavigationIndicators]="false"
        >
          <ng-template
            ngbSlide
            *ngFor="let image of playlist.thumbnails; let i = index"
          >
            <div class="image-wrapper">
              <img
                [src]="image"
                alt="Playlist Thumbnail {{ i + 1 }}"
                class="w-100"
                style="object-fit: cover; max-height: 320px"
              />
            </div>
          </ng-template>
        </ngb-carousel>
      </div>

      <div class="meta">
        <div class="field-main">
          <a
            *ngIf="canViewUpdatePlaylist"
            [href]="playlist.url"
            target="_blank"
            class="link-unstyled"
            >{{ playlist.name }}</a
          >
          <span *ngIf="!canViewUpdatePlaylist">{{ playlist.name }}</span>
          <span
            class="label"
            [ngClass]="playlistStatusClass(playlist.status)"
            >{{ playlistStatusClass(playlist.status) | titlecase }}</span
          >
        </div>
        <div class="field">{{ convertMSToTimeDuration(playlist.length) }}</div>
        <div class="field">
          <ng-container *ngIf="playlist.assetCount === 1">1 video</ng-container>
          <ng-container *ngIf="playlist.assetCount !== 1"
            >{{ playlist.assetCount }} videos</ng-container
          >
        </div>
      </div>
    </div>
  </section>

  <hr *ngIf="playlist" class="p-0 m-0" />

  <!-- Playback History Section -->
  <div class="card-body">
    <div class="media-sub-title d-flex justify-content-between">
      <div class="time-history">24 HOURS PLAYBACK HISTORY</div>
      <div class="media-search-input">
        <input
          class="ics-input form-control"
          [(ngModel)]="query"
          (ngModelChange)="onQueryChange($event)"
          placeholder="Search by keyword"
          type="text"
        />
      </div>
    </div>

    <table class="table mb-0" aria-describedby="Device Media Table">
      <thead>
        <tr class="media-table-head">
          <th
            *ngFor="let col of displayedColumns; let i = index"
            [ngClass]="{
              'col-1': i === 0,
              'col-2': i === 1,
              'col-3': i === 2 || i === 3,
              'col-4': i === 4,
            }"
            scope="col"
          >
            {{ getColumnHeader(col, i) }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngIf="loading">
          <td [attr.colspan]="displayedColumns.length" class="text-center">
            <app-ics-loader></app-ics-loader>
          </td>
        </tr>

        <tr *ngFor="let media of filteredMediaStats() || []; let i = index">
          <td>{{ i + 1 }}</td>
          <td [innerHTML]="highlight(media.name, query)"></td>
          <td [innerHTML]="highlight(media.count, query)"></td>
          <td
            [innerHTML]="
              highlight(convertMSToTimeDuration(media.totalPlayTime), query)
            "
          ></td>
          <td
            [innerHTML]="highlight(formatLastPlayed(media.lastPlayed), query)"
          ></td>
        </tr>
      </tbody>
    </table>

    <div *ngIf="!loading || !mediaData" class="no-data m-0 p-2">
      No media playback history is available.
    </div>
  </div>
</div>
