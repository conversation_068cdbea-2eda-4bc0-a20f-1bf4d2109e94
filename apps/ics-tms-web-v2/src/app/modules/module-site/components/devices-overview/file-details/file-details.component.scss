.file-details-container {
  height: fit-content;
  box-shadow: 0 0.8rem 0.938rem rgba(0, 0, 0, 0.5);
  padding: 1.6rem 0.938rem;

  .title {
    padding: 1.6rem 0.938rem;
    font-size: 1.8rem;
    line-height: 1.42857143;
    margin: 0;
    font-weight: 500;
  }

  .file-details-data {
    margin-top: 2rem;
    box-shadow: 0 0.1rem 0.6rem rgba(0, 0, 0, 0.25);
    height: 70vh;

    textarea {
      width: 100%;
      height: 100%;
      resize: none;
      border: none;
      font-size: 1.4rem;
      padding: 0rem 0.978rem;

      &:focus {
        outline: none;
      }
    }
  }

  .button-area {
    padding-top: 0.938rem;
    display: flex;
    justify-content: flex-end;

    button {
      color: var(--color-primary);
      font-weight: 500;
      border: none;

      &:hover {
        color: var(--color-blue);
      }
    }
  }
}
