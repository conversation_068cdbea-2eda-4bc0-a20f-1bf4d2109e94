import { Component, inject, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-file-details',
  templateUrl: './file-details.component.html',
  styleUrls: ['./file-details.component.scss'],
})
export class FileDetailsComponent implements OnInit {
  activeModal = inject(NgbActiveModal);

  @Input() title!: string;

  @Input() content!: any;

  closeModal() {
    this.activeModal.close();
  }

  ngOnInit(): void {
    if (typeof this.content !== 'string') {
      this.content = JSON.stringify(this.content, null, 2);
    }
  }
}
