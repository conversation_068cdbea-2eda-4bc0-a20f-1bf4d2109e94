.card {
  border-radius: 0.3rem;
  border: none;

  .table > :not(caption) > * > * {
    background: none;
  }

  .mat-mdc-card-content {
    padding: 0;
  }

  .header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    .right-side {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    h3 {
      font-size: 2rem;
      margin: 0.7rem 0;
      font-weight: 400;
    }

    .pull-file-btn {
      .pull-file-icon {
        font-size: 1.2rem;
        margin-top: -0.2rem;
        height: fit-content;
        width: fit-content;
      }
    }

    .btn {
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
      transition: all ease-in 0.1s;
      border-radius: 0.3rem;

      div {
        display: flex;
        align-items: center;
        gap: 0.2rem;

        i {
          font-size: 1.2rem;
        }

        p {
          font-size: 1.2rem;
        }
      }

      p {
        margin: 0;
      }

      &:active {
        border-color: var(--color-blue);
        background-color: var(--color-blue);
      }
    }
  }

  .table {
    margin: 0;

    .disabled-rows {
      cursor: not-allowed;
      background-color: var(--list-group-item-hover);
    }

    .active-rows {
      cursor: pointer;

      &:hover {
        background-color: var(--list-group-item-hover);
      }
    }
  }

  .rows,
  .header-row {
    .cell {
      div {
        font-size: 1.4rem;
      }
    }

    .header-cell {
      div {
        font-size: 1.2rem;
        color: var(--color-black-shade-six);
      }
    }
  }

  .header-row > .header-cell,
  .rows > .cell {
    flex-grow: 2 !important;
  }

  .header-cell {
    font-weight: 700;
  }

  .header-row > .header-cell:nth-child(2),
  .rows > .cell:nth-child(2) {
    flex-grow: 3 !important;
  }

  .header-row > .header-cell:first-child,
  .rows > .cell:first-child {
    flex-grow: 1 !important;
  }

  .rows {
    background-color: var(--dropdown-border-color);

    .cell {
      font-size: 1.6rem;

      .folder-icon {
        color: var(--color-email-not-verified);
      }

      .file-icon {
        color: var(--color-black-shade-six);
      }
    }

    &:nth-child(even) {
      .cell {
        &:nth-child(2) {
          font-weight: 600;
        }
      }
    }
  }
}

.file-path-col {
  display: flex;
  flex-direction: column;

  span {
    font-weight: 700;
  }

  .normal-text {
    font-size: 1.3rem;
    font-weight: normal;
  }
}

.table-header {
  height: 38px !important;
  padding: 10px 15px !important;

  .overview-head {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--md-grey-650) !important;
    border-top: none;
    padding: 10px 15px;
  }
}

.fw700 {
  font-weight: 700;
}

.overview-row {
  color: var(--color-black-shade-one);
  background-color: var(--color-white);
  font-size: 1.4rem;

  td {
    &:first-child {
      position: relative;
    }

    color: var(--color-black-shade-one);
    background-color: var(--list-group-item-hover);
    padding: 1rem 1.5rem;
  }

  .application-id-col {
    font-size: 1.3rem;
  }
}

.folder-icon {
  color: var(--color-black-shade-two);
  font-size: 2.4rem;
  margin-left: 0.8rem;
}

.file-icon {
  color: var(--color-folder-icon);
  font-size: 2.4rem;
  margin-left: 0.8rem;
}

.fa-sync-alt {
  margin-left: 1.6rem;
  font-size: 1.4rem;
  color: var(--color-white);
  background: var(--label-info);
  padding: 0.2rem;
  border-radius: 50%;
  animation: icon-spin 2s infinite linear;
  position: absolute;
  top: 30%;
  left: 2rem;
}

@keyframes icon-spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.no-data {
  width: 100%;

  .no-data-found-overview {
    padding: 1rem 1.5rem;
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    font-size: 1.4rem;
    color: var(--color-black-shade-two);

    i {
      font-size: 7rem;
      color: var(--badge-bg-ng-select);
    }

    p {
      margin: 0;
    }
  }
}
