<div class="card">
  <!-- Header  -->
  <div class="header">
    <h3>Files</h3>
    <div class="right-side">
      <ng-container
        *ngIf="
          dataSource.length &&
          (mayEdit() || canPullFiles()) &&
          isDeviceOperationalOrOOS()
        "
      >
        <button (click)="onClickPull()" class="btn btn-primary pull-file-btn">
          <div>
            <mat-icon class="pull-file-icon">cloud_download</mat-icon>
            <p>Pull</p>
          </div>
        </button>
      </ng-container>

      <ng-container *ngIf="mayEdit()">
        <button (click)="onClickCopy()" class="btn btn-primary">
          <div>
            <i class="fa-regular fa-copy"></i>
            <p>Copy</p>
          </div>
        </button>
      </ng-container>
    </div>
  </div>

  <!-- Table  -->
  <section>
    <table class="table" aria-describedby="devices-overview">
      <thead class="table-header">
        <tr>
          <th
            class="overview-head"
            *ngFor="let col of displayedColumns"
            scope="col"
            [ngStyle]="{ width: col.width }"
          >
            {{ col.name }}
          </th>
        </tr>
      </thead>
      <tbody *ngIf="dataSource.length > 0">
        <tr
          (click)="openFileDetailsModal(item.filePath, item.id)"
          *ngFor="let item of activeFiles"
          class="overview-row active-rows"
        >
          <td>
            <i class="fa-solid {{ item.icon }}"></i>
            <i *ngIf="item.pullRequestQueued" class="fas fa-sync-alt"></i>
          </td>
          <td>
            <div class="file-path-col">
              <span [ngClass]="{ fw700: true }">{{
                stripFilePath(item.filePath)
              }}</span>
              <span class="normal-text">{{
                stripFileName(item.filePath)
              }}</span>
            </div>
          </td>
          <td class="application-id-col">{{ item['applicationId'] }}</td>
          <td>{{ item['lastPulled'] | dateFormat: 'MMM DD, YYYY' }}</td>
        </tr>

        <tr
          *ngFor="let item of disableFiles"
          class="overview-row disabled-rows"
        >
          <td>
            <i class="fa-solid {{ item.icon }}"></i>
            <i *ngIf="item.pullRequestQueued" class="fas fa-sync-alt"></i>
          </td>
          <td>
            <div class="file-path-col">
              <span [ngClass]="{ fw700: true }">{{
                stripFilePath(item.filePath)
              }}</span>
              <span class="normal-text">{{
                stripFileName(item.filePath)
              }}</span>
            </div>
          </td>
          <td class="application-id-col">{{ item['applicationId'] }}</td>
          <td>{{ item['lastPulled'] | dateFormat: 'MMM DD, YYYY' }}</td>
        </tr>
      </tbody>
    </table>

    <div class="no-data">
      <td
        *ngIf="loading && activeFiles.length < 1 && disableFiles.length < 1"
        colspan="4"
        class="no-data-found-overview"
      >
        <app-ics-loader></app-ics-loader>
      </td>

      <td
        *ngIf="!loading && dataSource.length == 0"
        colspan="4"
        class="no-data-found-overview"
      >
        <i class="fas fa-file-contract"></i>
        <p>No files found.</p>
      </td>
    </div>
  </section>
</div>
