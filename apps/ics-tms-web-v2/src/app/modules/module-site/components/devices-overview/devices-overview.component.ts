import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject, takeUntil } from 'rxjs';
import { loadDeviceFiles } from '../../store/actions/device-file.actions';
import {
  deviceFileDataSelector,
  deviceFileLoadingSelector,
} from '../../store/selectors/device-file.selectors';
import { DeviceFile } from '../../models/device-file.modal';
import { getBaseUrl } from '../../../../constants/api';
import { setHeaderName } from '../../../../store/actions/globalStore.actions';
import { DeviceData } from '../../models/devices.interface';
import { FileDetailsComponent } from './file-details/file-details.component';
import { AuthService } from 'src/app/services/auth.service';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';
import { ModalConstants } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-devices-overview',
  templateUrl: './devices-overview.component.html',
  styleUrls: ['./devices-overview.component.scss'],
})
export class DevicesOverviewComponent implements OnInit {
  @Input() deviceData$!: Observable<any>;

  @Output() activeURL = new EventEmitter<string>();

  private destroy$ = new Subject<void>();

  dataSource: DeviceFile[] = [];

  displayedColumns = [
    { name: '', width: '8%' },
    { name: 'Name and Path', width: '40%' },
    { name: 'Application', width: '26%' },
    { name: 'Last Snapshot', width: '26%' },
  ];

  siteId!: string;

  deviceId!: string;

  activeFiles: DeviceFile[] = [];

  disableFiles: DeviceFile[] = [];

  selectedTab = '';

  loading = true;

  deviceData!: DeviceData;

  private modalService = inject(NgbModal);

  private http = inject(HttpClient);

  private authService = inject(AuthService);

  store = inject(Store);

  route = inject(ActivatedRoute);

  ngOnInit(): void {
    this.getDeviceOverviewData();
    this.initializeOverviewRouteParams();
    this.store.dispatch(loadDeviceFiles({ deviceId: this.deviceId }));
    this.store.dispatch(loadDeviceTypes({}));
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeOverviewRouteParams(): void {
    this.route.params.subscribe(params => {
      this.siteId = params['site_id'];
      this.deviceId = params['device_id'];
    });
    this.route.url.subscribe(segments => {
      const lastSegment = segments[segments.length - 1].path;
      this.selectedTab = lastSegment;
    });
  }

  private getDeviceOverviewData(): void {
    this.store
      .select(deviceFileDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.dataSource = data;

        this.activeFiles = this.dataSource
          .filter(
            file =>
              !(file.filePath.endsWith('/') || file.filePath.endsWith('\\')) &&
              file.lastPulled
          )
          .map(file => {
            if (file.filePath.endsWith('/') || file.filePath.endsWith('\\')) {
              return { ...file, icon: 'fa-folder folder-icon ' };
            }
            return { ...file, icon: 'fa-file file-icon ' };
          });

        this.disableFiles = this.dataSource
          .filter(
            file =>
              file.filePath.endsWith('/') ||
              file.filePath.endsWith('\\') ||
              !file.lastPulled
          )
          .map(file => {
            if (file.filePath.endsWith('/') || file.filePath.endsWith('\\')) {
              return { ...file, icon: 'fa-folder folder-icon ' };
            }
            return { ...file, icon: 'fa-file file-icon ' };
          });
      });
    this.store
      .select(deviceFileLoadingSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.loading = loading;
      });

    this.deviceData$.subscribe(data => {
      this.deviceData = data.devicesData.devicesReducers.data;
    });
  }

  getFilePathLastName(filePath: string) {
    const cleanedPath = filePath.replace(/^\/|\/$/g, '');
    const splitPath = cleanedPath.split('/');
    return splitPath[splitPath.length - 1];
  }

  openFileDetailsModal(file: string, id: number) {
    this.http
      .get(`${getBaseUrl()}/devices/${this.deviceId}/files/${id}/content`)
      .subscribe(data => {
        const filePathName = this.getFilePathLastName(file);
        const modalRef = this.modalService.open(FileDetailsComponent, {
          centered: true,
          windowClass: ModalConstants.WINDOW_CLASS,
          container: ModalConstants.CONTAINER_SELECTOR,
        });
        modalRef.componentInstance.content = data;
        modalRef.componentInstance.title = filePathName;
      });
  }

  onClickCopy() {
    this.store.dispatch(setHeaderName({ name: 'Copy Files' }));
    this.activeURL.emit('copy-files');
  }

  stripFilePath(text: string) {
    const trimmedText = text.trim();
    return trimmedText.replace(/^.*[\\/]/, '');
  }

  stripFileName(text: string) {
    const trimmedText = text.trim();
    return trimmedText.substring(0, trimmedText.lastIndexOf('/'));
  }

  onClickPull() {
    this.store.dispatch(setHeaderName({ name: 'Pull Files' }));
    this.activeURL.emit('pull-files');
  }

  mayEdit() {
    return (
      AuthService.isAllowedAccess('WRITE_DEVICES') &&
      this.deviceData.presence !== 'OUT_OF_INSTANCE'
    );
  }

  canPullFiles() {
    return AuthService.isAllowedAccess('PULL_FILES');
  }

  isDeviceOperationalOrOOS() {
    return this.deviceData.status === 1 || this.deviceData.status === 2;
  }
}
