import {
  Component,
  On<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  SimpleChanges,
  Input,
  inject,
} from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DevicesService } from '../../services/devices.service';
import { ConfigData, ConfigResult } from '../../models/app-config.model';
import { DeviceData } from '../../models/devices.interface';

@Component({
  selector: 'app-device-app-config',
  templateUrl: './device-app-config.component.html',
  styleUrls: ['./device-app-config.component.scss'],
})
export class DeviceAppConfigComponent implements OnChanges, OnDestroy {
  // Subject for managing subscriptions
  private destroy$ = new Subject<void>();

  @Input() deviceDetails!: DeviceData;

  isLoadingConfig = false;

  selectedRow: number | null = null;

  configData!: ConfigData;

  private devicesService = inject(DevicesService);

  private router = inject(Router);

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['deviceDetails'] && this.deviceDetails) {
      this.loadConfigData();
    }
  }

  loadConfigData(): void {
    if (!this.deviceDetails?.id) {
      console.warn('Cannot load config data: Device details not available');
      return;
    }

    this.isLoadingConfig = true;

    this.devicesService
      .getAppConfigDetails(this.deviceDetails.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: response => {
          this.configData = response;
          this.isLoadingConfig = false;
        },
        error: error => {
          this.isLoadingConfig = false;
          console.error('Failed to load app config details:', error);
        },
      });
  }

  selectRow(index: number): void {
    this.selectedRow = index;
  }

  downloadFile(
    configFileContentId: string,
    configFileInstanceId: string,
    revisionName: string
  ): void {
    if (configFileContentId && configFileInstanceId && revisionName) {
      this.devicesService
        .GetDownloadConfigFile(configFileContentId, configFileInstanceId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: response => {
            if (response.url) {
              const a = document.createElement('a');
              a.href = response.url;
              a.download = revisionName;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }
          },
          error: () => {},
        });
    }
  }

  navigateToInstance(deployedConfigFileInstanceId: string): void {
    if (deployedConfigFileInstanceId) {
      this.router.navigate([
        '/remote/config-management/instance',
        deployedConfigFileInstanceId,
      ]);
    }
  }

  extractRevisionNumber(revisionName: string | null): string {
    return revisionName?.match(/\d+/)?.[0] ?? '';
  }

  getStatusStyle(status: string): { [key: string]: string } {
    const statusStyles: { [key: string]: { backgroundColor: string } } = {
      NeedsDeployment: { backgroundColor: '#fb8e89' },
      LocalDeployment: { backgroundColor: '#f9f99f' },
      LocalOverride: { backgroundColor: '#91d3ea' },
      Pending: { backgroundColor: '#f9f99f' },
      Deployed: { backgroundColor: '#4caf50' },
    };

    const normalizedValue = String(status).replace(/\s+/g, '');
    return statusStyles[normalizedValue] || {};
  }

  formatDate(dateValue: string | Date | null): string {
    if (!dateValue || Number.isNaN(new Date(dateValue).getTime())) {
      return '';
    }
    return new Date(dateValue).toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });
  }

  formatDeploymentType(result: ConfigResult): string {
    if (!result) {
      return '';
    }

    const hasFactoryHash =
      result.deviceCfactHash && result.deviceCfactHash !== '';
    const hasLocalHash =
      result.deviceCflocHash && result.deviceCflocHash !== '';
    const hasRemoteHash =
      result.deviceCfremHash && result.deviceCfremHash !== '';

    if (hasFactoryHash && !hasLocalHash && !hasRemoteHash) {
      return 'Factory';
    }
    if (hasLocalHash && !hasRemoteHash) {
      return 'Local';
    }
    if (hasRemoteHash) {
      return 'Remote';
    }
    return '';
  }

  trackByIndex(i: number): number {
    return i;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
