.ics-app-config {
  .ics-loader {
    position: relative;
    left: 50%;
    display: block;
    margin-left: -1.5rem;
    animation: icsLoader 0.65s infinite linear;
    border-top: 0.4rem solid rgba(0, 0, 0, 0.1);
    border-right: 0.4rem solid rgba(0, 0, 0, 0.1);
    border-bottom: 0.4rem solid rgba(0, 0, 0, 0.1);
    border-left: 0.4rem solid rgba(0, 0, 0, 0.35);
  }
  .panel {
    &.panel-default {
      border: 0.1rem solid #ddd;
      border-radius: 0.4rem;
      background-color: #fff;
    }

    .panel-heading {
      &.panel-heading-has-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2rem;
        border-bottom: 0.1rem solid #ddd;
        background-color: #f7f7f7;

        .panel-title {
          margin: 0;
          font-size: 1.25rem;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .text-center {
      &.mt20 {
        margin-top: 2rem;
      }
      &.pb15 {
        padding-bottom: 1.5rem;
      }
      &.text-grey-500 {
        color: #9e9e9e;
      }
    }

    .table-responsive {
      max-height: 40rem;
      overflow-y: auto;
      margin-top: 1rem;

      .app-config-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 1.2rem;

        thead {
          background-color: #f5f5f5;

          .app-config-header {
            th {
              padding: 1rem 1.2rem;
              font-weight: 600;
              color: #555;
              text-align: left;
              border-bottom: 0.2rem solid #ddd;
              white-space: nowrap;
            }
          }
        }

        tbody {
          .app-config-row {
            tr {
              border-bottom: 0.1rem solid #eee;

              &:hover {
                background-color: #fafafa;
              }
            }

            td {
              padding: 0.8rem 1.2rem;
              color: #444;
              vertical-align: middle;
              white-space: nowrap;

              &.app-name,
              &.file-name,
              &.instance-name,
              &.revision-name,
              &.assignment-level,
              &.assignment-status,
              &.hash,
              &.last-deployed,
              &.deployment-type {
                cursor: default;
              }

              &.file-name,
              &.instance-name {
                cursor: pointer;

                .link-style {
                  color: #3f51b5;
                  text-decoration: underline;
                  user-select: none;
                }

                &:hover .link-style {
                  color: #303f9f;
                }
              }

              .material-icons {
                vertical-align: middle;
                font-size: 1.3rem;
                margin-right: 0.4rem;
                color: #757575;
              }

              .status-badge {
                display: inline-block;
                padding: 0.2rem 0.8rem;
                border-radius: 1.2rem;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.03em;
              }
            }
          }
        }
      }
    }
  }
}
