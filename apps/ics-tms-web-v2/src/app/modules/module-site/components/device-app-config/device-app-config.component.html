<div class="panel panel-default bs-primary mb-40 ics-app-config">
  <div class="panel-heading panel-heading-has-btn">
    <h3 class="panel-title">AppConfig</h3>
  </div>

  <!-- Loading Spinner -->
  <div class="text-center mt20 pb15 text-grey-500">
    <div class="ics-loader" *ngIf="isLoadingConfig"></div>

    <!-- Message when there is no data -->
    <span
      *ngIf="
        !isLoadingConfig && (!configData || configData.results.length === 0)
      "
    >
      No Appconfiguration data found for this device.
    </span>
  </div>

  <div class="table-responsive" *ngIf="configData && configData.results.length">
    <table class="table app-config-table">
      <thead>
        <tr class="app-config-header">
          <th>APPLICATION</th>
          <th>FILE</th>
          <th>INSTANCE</th>
          <th>REVISION</th>
          <th>LEVEL</th>
          <th>STATUS</th>
          <th>ASSIGNED</th>
          <th>DEPLOYED</th>
          <th>ACTIVE</th>
          <th>LOCAL</th>
          <th>LAST DEPLOYED</th>
          <th>DEPLOYMENT TYPE</th>
        </tr>
      </thead>

      <tbody>
        <tr
          class="app-config-row"
          *ngFor="
            let result of configData.results;
            let i = index;
            trackBy: trackByIndex
          "
        >
          <td class="app-name" [title]="result.appName || ''">
            {{ result.appName || '' }}
          </td>

          <td
            class="file-name"
            [title]="'Download ' + (result.configFileName || '')"
            (click)="
              result.configFileName
                ? downloadFile(
                    result.configFileContentId,
                    result.configFileInstanceId,
                    result.revisionName
                  )
                : null
            "
            style="cursor: pointer"
          >
            <span class="link-style">{{ result.configFileName || '' }}</span>
          </td>

          <td
            class="instance-name"
            [title]="result.deviceConfigFileInstanceName || ''"
            (click)="
              result.deviceConfigFileInstanceName
                ? navigateToInstance(result.deployedConfigFileInstanceId)
                : null
            "
            style="cursor: pointer"
          >
            <span class="link-style">{{
              result.deviceConfigFileInstanceName || ''
            }}</span>
          </td>

          <td class="revision-name" [title]="result.revisionName || ''">
            {{ extractRevisionNumber(result.revisionName) }}
          </td>

          <td class="assignment-level" [title]="result.assignmentLevel || ''">
            <span [ngSwitch]="result.assignmentLevel.toUpperCase()">
              <i *ngSwitchCase="'COMPANY'" class="material-icons">language</i>
              <i *ngSwitchCase="'DEVICE'" class="material-icons"
                >local_gas_station</i
              >
              <i *ngSwitchCase="'SITE'" class="material-icons">location_on</i>
              <i *ngSwitchCase="'SITETAG'" class="material-icons"
                >local_offer</i
              >
              <i *ngSwitchDefault class="material-icons">help_outline</i>
            </span>
            <span>
              {{
                result.assignmentLevel
                  ? result.assignmentLevel.charAt(0).toUpperCase() +
                    result.assignmentLevel.slice(1).toLowerCase()
                  : ''
              }}
            </span>
          </td>

          <td class="assignment-status" [title]="result.assignmentStatus || ''">
            <span
              [ngStyle]="getStatusStyle(result.assignmentStatus)"
              class="status-badge"
            >
              {{ result.assignmentStatus }}
            </span>
          </td>

          <td class="hash" [title]="result.desiredRenditionHash || ''">
            {{
              result.desiredRenditionHash
                ? result.desiredRenditionHash.substring(0, 8)
                : ''
            }}
          </td>

          <td
            class="hash"
            [title]="
              result.deviceCfremHash || result.deployedRenditionHash || ''
            "
          >
            {{
              (
                result.deviceCfremHash ||
                result.deployedRenditionHash ||
                ''
              ).substring(0, 8)
            }}
          </td>

          <td class="hash" [title]="result.deviceCfactHash || ''">
            {{
              result.deviceCfactHash
                ? result.deviceCfactHash.substring(0, 8)
                : ''
            }}
          </td>

          <td class="hash" [title]="result.deviceCflocHash || ''">
            {{
              result.deviceCflocHash
                ? result.deviceCflocHash.substring(0, 8)
                : ''
            }}
          </td>

          <td class="last-deployed" [title]="formatDate(result.lastDeployAt)">
            {{ formatDate(result.lastDeployAt) }}
          </td>

          <td class="deployment-type" [title]="formatDeploymentType(result)">
            {{ formatDeploymentType(result) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
