<div class="history-card">
  <div class="history-card__header-spacer" *ngIf="isSticky"></div>

  <div class="history-card__header" [ngClass]="{ sticky: isSticky }">
    <h2 *ngIf="!isSticky" class="history-card__title">History</h2>
    <div
      class="history-card__controls"
      [ngClass]="{ 'history-card__controls--sticky': isSticky }"
    >
      <label class="history-card__advanced-label" for="customCheckbox">
        <input
          [(ngModel)]="isAdvancedMode"
          class="history-card__checkbox"
          id="customCheckbox"
          name="preference"
          type="checkbox"
          value="preference1"
        />
        <b class="history-card__advanced-text">Advanced View</b>
      </label>

      <div
        class="history-card__dropdown history-card__dropdown--filter"
        appClickOutside
        (clickOutside)="closeMenu()"
      >
        <button class="history-card__dropdown-button" (click)="toggleMenu()">
          <i class="fa fa-filter" aria-hidden="true"></i>
        </button>
        <div
          class="history-card__dropdown-menu history-card__dropdown-menu--left"
          *ngIf="isMenuOpened"
        >
          <app-filter-dropdown></app-filter-dropdown>
        </div>
      </div>

      <div
        class="history-card__dropdown history-card__dropdown--time"
        appClickOutside
        (clickOutside)="closeTimeMenu()"
      >
        <button
          class="history-card__dropdown-button"
          (click)="toggleTimeMenu()"
        >
          <i class="fa-regular fa-clock"></i>
        </button>
        <div
          class="history-card__dropdown-menu history-card__dropdown-menu--right"
          *ngIf="isTimeMenuOpened"
        >
          <app-time-dropdown></app-time-dropdown>
        </div>
      </div>

      <input
        (ngModelChange)="callHistoryUrlWithDebounce()"
        [(ngModel)]="timeService.historySearchInput"
        type="text"
        class="ics-input history-card__search-input"
        placeholder="Search by keyword"
      />
    </div>
  </div>

  <div
    *ngIf="historyData?.results && historyData.results.length > 0"
    class="history-card__list"
  >
    <div
      *ngFor="let history of historyData.results; trackBy: trackByUniqueId"
      class="history-card__item"
      [attr.data-unique-id]="history.uniqueId"
    >
      <div class="history-card__row">
        <div class="history-card__text">
          <i
            [ngClass]="{ 'history-card__icon--red': history.lv === 'fatal' }"
            aria-hidden="true"
            class="fa {{ getIcon(history.lv) }}"
          ></i>
          <span
            [innerHTML]="
              history.isFull
                ? (history.msg | extractMessage)
                : ((history.msg | extractMessage).substring(0, 501)
                  | highlightSearch: timeService.historySearchInput)
            "
          ></span>
          <span *ngIf="!history.isFull && history.msg.length > 501">...</span>
          <span
            (click)="onClickMoreOrLess(history.uniqueId)"
            *ngIf="history.msg.length > 501"
            class="history-card__more-less"
            >{{ history.isFull ? 'less' : 'more' }}</span
          >
          <div *ngIf="isAdvancedMode" class="history-card__advanced-info">
            <b>dv</b>
            <span
              [innerHTML]="
                history.dv
                  | extractMessage
                  | highlightSearch: timeService.historySearchInput
              "
            ></span>
          </div>

          <div *ngIf="isAdvancedMode" class="history-card__advanced-info">
            <b>fn</b>
            <span
              [innerHTML]="
                history.fn
                  | extractMessage
                  | highlightSearch: timeService.historySearchInput
              "
            ></span>
          </div>
        </div>
        <div class="history-card__timestamp">
          {{ history.ts | dateFormat: 'MMM DD [at] hh:mm a' }}
        </div>
      </div>
    </div>
  </div>

  <ng-container *ngIf="isLoadingCombined | async">
    <app-ics-loader></app-ics-loader>
  </ng-container>

  <ng-container
    *ngIf="
      !(isLoadingCombined | async) &&
      (!historyData?.results || historyData.results.length === 0)
    "
  >
    <div class="history-card__no-results">
      <span *ngIf="timeService.historySearchInput && timeService.getText()">
        No results matching that keyword were found for the last
        <b>{{ timeService.getText() }}.</b> Try updating your filters.
      </span>
      <span *ngIf="!timeService.historySearchInput && timeService.getText()">
        No results were found for the last
        <b>{{ timeService.getText() }}.</b> Try updating your filters.
      </span>
      <span *ngIf="!timeService.getText()">
        No results were found. Try updating your filters.
      </span>
    </div>
  </ng-container>
</div>

<a
  class="return-to-top"
  [ngClass]="{ active: isScrollable }"
  (click)="scrollToTop()"
  href="#"
>
  <i class="fa fa-chevron-circle-up"></i>
</a>
