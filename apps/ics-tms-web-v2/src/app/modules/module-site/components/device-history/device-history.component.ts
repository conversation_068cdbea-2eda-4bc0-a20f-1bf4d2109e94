import {
  Component,
  HostListener,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewEncapsulation,
  NgZone,
  inject,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { ActivatedRoute } from '@angular/router';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  of,
  timer,
} from 'rxjs';
import {
  takeUntil,
  switchMap,
  map,
  debounceTime,
  distinctUntilChanged,
  shareReplay,
  startWith,
  throttleTime,
} from 'rxjs/operators';

import { TimeService } from '../../services/time.service';
import { loadDeviceHistory } from '../../store/actions/device-history.actions';
import {
  deviceHistoryDataSelector,
  deviceHistoryLoadingSelector,
} from '../../store/selectors/device-history.selectors';
import { DeviceHistory } from '../../models/device-history.modal';
import { HistoryParams } from '../../models/history-params.modal';
import { FilterService } from '../../services/filter.service';
import { HeaderStateService } from '../../services/header-state.service';
import { APPEND, HISTORY_ICONS } from '../../constants/appConstants';

@Component({
  selector: 'app-device-history',
  templateUrl: './device-history.component.html',
  styleUrls: ['./device-history.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class DeviceHistoryComponent implements OnInit, OnDestroy {
  siteId!: string;

  deviceId!: string;

  isAdvancedMode = false;

  isSticky = false;

  isScrollable = false;

  isMenuOpened = false;

  isTimeMenuOpened = false;

  lastScrollTop: number = 0;

  historyData: DeviceHistory = {} as DeviceHistory;

  private uniqueId = 1;

  targetHeight = '';

  isLoading!: Observable<boolean>;

  private localLoading$ = new BehaviorSubject<boolean>(false);

  isLoadingCombined!: Observable<boolean>;

  private readonly scrollTrigger$ = new Subject<void>();

  private readonly searchInputSubject = new Subject<string>();

  private lastSearchQuery = '';

  private lastLoadTime = 0;

  private isCurrentlyLoading = false;

  private reachedEndOfData = false;

  showLocalLoader = false;

  private previousScrollPosition = 0;

  private loaderContainer: HTMLElement | null = null;

  private loadTimer: any = null;

  private searchTimer: any = null;

  private readonly API_CALL_DELAY = 5000;

  private readonly SCROLL_THRESHOLD = 10;

  private destroy$ = new Subject<void>();

  public timeService = inject(TimeService);

  private store = inject(Store);

  private route = inject(ActivatedRoute);

  public filterService = inject(FilterService);

  private ngZone = inject(NgZone);

  private headerStateService = inject(HeaderStateService);

  constructor() {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.siteId = params['site_id'];
      this.deviceId = params['device_id'];

      this.timeService.setDeviceId(this.deviceId);

      this.initializeTimeServiceAndLoadData();
    });
  }

  private initializeTimeServiceAndLoadData(): void {
    if (!this.timeService.initializeTimeService()) {
      this.loadDeviceHistory();
    }
  }

  ngOnInit(): void {
    this.setupDataSubscription();
    this.setupScrollHandler();
    this.setupSearchDebounce();
    this.isLoading = this.store.select(deviceHistoryLoadingSelector);

    this.isLoadingCombined = combineLatest([
      this.isLoading.pipe(startWith(false)),
      this.localLoading$.pipe(distinctUntilChanged()),
    ]).pipe(
      map(([storeLoading, localLoading]) => storeLoading || localLoading),
      shareReplay(1)
    );

    setTimeout(() => {
      this.loaderContainer = document.querySelector('.history-card');
    }, 0);

    this.timeService.timeFilterChanged$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.reachedEndOfData = false;
      });
  }

  private setupDataSubscription(): void {
    this.store
      .select(deviceHistoryDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        const newResults =
          data.results?.map(result => ({
            ...result,
            isFull: false,
            uniqueId: this.uniqueId++,
          })) ?? [];

        this.historyData = {
          ...data,
          results: newResults,
        };
      });
  }

  private setupScrollHandler(): void {
    this.scrollTrigger$
      .pipe(
        takeUntil(this.destroy$),
        throttleTime(1000),
        switchMap(() =>
          combineLatest([
            of(!!this.historyData?.resultsMetadata?.pageKey),
            this.isLoading,
          ])
        )
      )
      .subscribe(([hasMoreData, isLoading]) => {
        if (
          hasMoreData &&
          !isLoading &&
          !this.isCurrentlyLoading &&
          !this.reachedEndOfData
        ) {
          this.handleLoadMoreOnScroll();
        }
      });

    this.store
      .select(deviceHistoryLoadingSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(isLoading => {
        if (!isLoading && this.isCurrentlyLoading) {
          this.isCurrentlyLoading = false;
          this.showLocalLoader = false;
          this.localLoading$.next(false);

          this.restoreScrollPosition();
        } else if (isLoading) {
          this.isCurrentlyLoading = true;
        }
      });

    this.store
      .select((state: any) => state.deviceHistory?.error)
      .pipe(
        takeUntil(this.destroy$),
        switchMap(error => {
          if (error) {
            console.error('Error loading device history:', error);
            this.isCurrentlyLoading = false;
            this.showLocalLoader = false;
            this.localLoading$.next(false);
            this.cancelLoadTimer();

            return timer(0);
          }
          return of(null);
        })
      )
      .subscribe(() => {});

    this.store
      .select(deviceHistoryDataSelector)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data.results?.length && data.resultsMetadata) {
          if (!data.resultsMetadata.pageKey) {
            this.reachedEndOfData = true;
          } else {
            this.reachedEndOfData = false;
          }
        }

        if (this.reachedEndOfData) {
          this.showLocalLoader = false;
          this.localLoading$.next(false);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.cancelLoadTimer();
    this.cancelSearchTimer();
    this.localLoading$.next(false);
    this.showLocalLoader = false;
  }

  private loadDeviceHistory(): void {
    const params: HistoryParams = {
      advanced: true,
      'levels[]': this.filterService.getFilterLevels(),
      pageMinSize: 25,
      start: this.getOneHourBeforeTimeInUTC(),
    };

    this.store.dispatch(
      loadDeviceHistory({
        historyParamsData: params,
        deviceId: this.deviceId,
        replace: true,
      })
    );
  }

  private getOneHourBeforeTimeInUTC(): string {
    const date = new Date();
    date.setHours(date.getHours() - 1);
    return date.toISOString();
  }

  toggleMenu(): void {
    this.isMenuOpened = !this.isMenuOpened;
    if (this.isMenuOpened) {
      this.isTimeMenuOpened = false;
    }
  }

  clickedOutside(): void {
    this.isMenuOpened = false;
  }

  toggleTimeMenu(): void {
    this.isTimeMenuOpened = !this.isTimeMenuOpened;
    if (this.isTimeMenuOpened) {
      this.isMenuOpened = false;
    }
  }

  clickedOutsideTimeMenu(): void {
    this.isTimeMenuOpened = false;
  }

  callHistoryUrlWithDebounce(): void {
    this.localLoading$.next(true);
    this.showLocalLoader = true;
    this.isCurrentlyLoading = true;

    this.ngZone.run(() => {});

    const emptyHistory: DeviceHistory = {
      results: [],
      resultsMetadata: {} as any,
    };

    this.historyData = emptyHistory;

    const currentSearchQuery = this.timeService.historySearchInput;

    this.searchInputSubject.next(currentSearchQuery);
  }

  private setupSearchDebounce(): void {
    this.searchInputSubject
      .pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((_searchValue: string) => {
        this.cancelSearchTimer();
        const emptyHistory: DeviceHistory = {
          results: [],
          resultsMetadata: {} as any,
        };
        this.historyData = emptyHistory;
        this.localLoading$.next(true);
        this.showLocalLoader = true;
        this.isCurrentlyLoading = true;
        this.ngZone.run(() => {});
        this.searchTimer = setTimeout(() => {
          this.ngZone.run(() => {
            this.reachedEndOfData = false;
            this.timeService.callHistoryURL();
            this.lastSearchQuery = this.timeService.historySearchInput;
          });
        }, this.API_CALL_DELAY);
      });
  }

  private cancelSearchTimer(): void {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  }

  closeMenu(): void {
    this.isMenuOpened = false;
  }

  closeTimeMenu(): void {
    this.isTimeMenuOpened = false;
  }

  onClickMoreOrLess(id: number): void {
    const newResults =
      this.historyData.results?.map(result => {
        if (result.uniqueId === id) {
          return {
            ...result,
            isFull: !result.isFull,
          };
        }
        return result;
      }) ?? [];

    this.historyData = {
      ...this.historyData,
      results: newResults,
    };
  }

  getIcon(lv: string): string {
    return HISTORY_ICONS[lv] || 'fa-info-circle';
  }

  private isScrollingUp = false;

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(_event: Event): void {
    const currentScrollPosition =
      document.documentElement.scrollTop || window.pageYOffset;

    this.isScrollingUp = currentScrollPosition < this.lastScrollTop;

    this.handleStickyAndScrollable();

    const target = document.documentElement;
    const isAtBottom =
      target.offsetHeight + target.scrollTop + this.SCROLL_THRESHOLD >=
      target.scrollHeight;

    if (isAtBottom && !this.isScrollingUp) {
      this.scrollTrigger$.next();
    }
  }

  private handleStickyAndScrollable(): void {
    const scrollTop = document.documentElement.scrollTop || window.pageYOffset;
    const scrollThreshold = 250;
    const deviceHeader = document.querySelector('.devices-header');

    const deviceHeaderHeight = deviceHeader
      ? deviceHeader.getBoundingClientRect().height
      : 51;

    if (document.documentElement) {
      (document.documentElement as HTMLElement).style.setProperty(
        '--device-header-height',
        `${deviceHeaderHeight}px`
      );
    }

    const previousStickyState = this.isSticky;
    if (scrollTop > scrollThreshold) {
      this.isSticky = true;
      this.isScrollable = true;
    } else {
      this.isSticky = false;
      this.isScrollable = false;
    }

    this.lastScrollTop = scrollTop;

    if (previousStickyState !== this.isSticky) {
      this.headerStateService.setHistoryHeaderSticky(this.isSticky);

      this.ngZone.run(() => {});
    }
  }

  private handleLoadMoreOnScroll(): void {
    if (this.isCurrentlyLoading || this.reachedEndOfData) {
      return;
    }

    const now = Date.now();
    if (now - this.lastLoadTime < 1000) {
      return;
    }

    if (
      !this.historyData.resultsMetadata ||
      !this.historyData.resultsMetadata.pageKey
    ) {
      this.reachedEndOfData = true;
      return;
    }

    this.cancelLoadTimer();

    this.isCurrentlyLoading = true;
    this.localLoading$.next(true);
    this.showLocalLoader = true;

    this.ngZone.run(() => {});

    this.lastScrollTop =
      document.documentElement.scrollTop <= 0
        ? 0
        : document.documentElement.scrollTop;

    this.ensureLoaderVisibility();

    this.loadTimer = setTimeout(() => {
      this.ngZone.run(() => {
        this.loadMoreHistory();
      });
    }, this.API_CALL_DELAY);
  }

  private loadMoreHistory(): void {
    try {
      this.lastLoadTime = Date.now();

      const currentScrollHeight = document.documentElement.scrollHeight;
      this.targetHeight = `${currentScrollHeight}px`;

      this.timeService.callHistoryURL(APPEND);
    } catch (error) {
      this.isCurrentlyLoading = false;
      this.showLocalLoader = false;
      this.localLoading$.next(false);
    }
  }

  scrollToTop(): void {
    document.documentElement.scrollTo({ top: 0, behavior: 'smooth' });
  }

  trackByUniqueId(index: number, item: any): number {
    return item.uniqueId;
  }

  private ensureLoaderVisibility(): void {
    if (this.isScrollingUp) {
      return;
    }

    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        if (this.loaderContainer) {
          const loaderElement =
            this.loaderContainer.querySelector('app-ics-loader');

          if (loaderElement) {
            const rect = loaderElement.getBoundingClientRect();
            const isVisible =
              rect.top >= 0 &&
              rect.left >= 0 &&
              rect.bottom <=
                (window.innerHeight || document.documentElement.clientHeight) &&
              rect.right <=
                (window.innerWidth || document.documentElement.clientWidth);

            const scrollPosition =
              window.pageYOffset || document.documentElement.scrollTop;
            const { scrollHeight } = document.documentElement;
            const { clientHeight } = document.documentElement;
            const isNearBottom =
              scrollPosition + clientHeight > scrollHeight - 300;

            if (!isVisible && isNearBottom && !this.isScrollingUp) {
              const viewportHeight =
                window.innerHeight || document.documentElement.clientHeight;
              const targetScrollPosition =
                window.scrollY + rect.top - viewportHeight * 0.7;

              window.scrollTo({
                top: targetScrollPosition,
                behavior: 'smooth',
              });
            }
          }
        }
      }, 0);
    });
  }

  private restoreScrollPosition(): void {
    if (this.lastScrollTop > 0) {
      this.ngZone.run(() => {
        requestAnimationFrame(() => {
          const currentScrollTop =
            document.documentElement.scrollTop || window.pageYOffset;

          if (currentScrollTop >= this.lastScrollTop) {
            window.scrollTo({
              top: this.lastScrollTop,
              behavior: 'auto',
            });
          }

          this.lastScrollTop = 0;
        });
      });
    }
  }

  private cancelLoadTimer(): void {
    if (this.loadTimer) {
      clearTimeout(this.loadTimer);
      this.loadTimer = null;
    }
  }
}
