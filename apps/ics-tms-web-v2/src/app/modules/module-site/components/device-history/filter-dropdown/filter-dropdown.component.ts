import { Component, inject } from '@angular/core';
import { FilterService } from '../../../services/filter.service';
import { TimeService } from '../../../services/time.service';

@Component({
  selector: 'app-filter-dropdown',
  templateUrl: './filter-dropdown.component.html',
  styleUrls: ['./filter-dropdown.component.scss'],
})
export class FilterDropdownComponent {
  filterMap = new Map<string, boolean>();

  trace = false;

  debug = false;

  info = false;

  notice = false;

  warn = false;

  error = false;

  critical = false;

  fatal = false;

  device_id!: string;

  public filterService = inject(FilterService);

  private timeService = inject(TimeService);

  constructor() {
    this.filterMap = this.filterService.map;
    this.device_id = this.timeService.getDeviceId();
  }

  onTrace() {
    this.filterService.map.set('trace', !this.filterService.map.get('trace'));
    this.timeService.callHistoryURL();
  }

  onDebug() {
    this.filterService.map.set('debug', !this.filterService.map.get('debug'));
    this.timeService.callHistoryURL();
  }

  onInfo() {
    this.filterService.map.set('info', !this.filterService.map.get('info'));
    this.timeService.callHistoryURL();
  }

  onNotice() {
    this.filterService.map.set('notice', !this.filterService.map.get('notice'));
    this.timeService.callHistoryURL();
  }

  onWarn() {
    this.filterService.map.set('warn', !this.filterService.map.get('warn'));
    this.timeService.callHistoryURL();
  }

  onError() {
    this.filterService.map.set('error', !this.filterService.map.get('error'));
    this.timeService.callHistoryURL();
  }

  onCritical() {
    this.filterService.map.set(
      'critical',
      !this.filterService.map.get('critical')
    );
    this.timeService.callHistoryURL();
  }

  onFatal() {
    this.filterService.map.set('fatal', !this.filterService.map.get('fatal'));
    this.timeService.callHistoryURL();
  }
}
