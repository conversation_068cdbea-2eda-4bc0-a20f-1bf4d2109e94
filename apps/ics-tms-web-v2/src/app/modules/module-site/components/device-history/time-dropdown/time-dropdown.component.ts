import {
  Component,
  OnInit,
  <PERSON><PERSON><PERSON>roy,
  ViewEncapsulation,
  ChangeDetectorRef,
  ViewChild,
  ElementRef,
  AfterViewInit,
  inject,
} from '@angular/core';
import { TimeService } from '../../../services/time.service';

@Component({
  selector: 'app-time-dropdown',
  templateUrl: './time-dropdown.component.html',
  styleUrls: ['./time-dropdown.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class TimeDropdownComponent implements OnInit, AfterViewInit, OnDestroy {
  private subscriptions: Array<{ unsubscribe: () => void }> = [];

  deviceId!: string;

  isSticky = false;

  customFromTimeOption: { time: string; category: string } | null = null;

  @ViewChild('fromSelect') fromSelectRef!: ElementRef<HTMLSelectElement>;

  @ViewChild('toSelect') toSelectRef!: ElementRef<HTMLSelectElement>;

  public timeService = inject(TimeService);

  private cdr = inject(ChangeDetectorRef);

  constructor() {
    this.deviceId = this.timeService.getDeviceId();
  }

  ngOnInit() {
    this.timeService.initializeTimeService();

    if (this.timeService.selectOption1 && this.timeService.selectOption1.time) {
      this.customFromTimeOption = { ...this.timeService.selectOption1 };
    }

    const customTimeSubscription = this.timeService.customTimeAdded$.subscribe(
      customTime => {
        this.customFromTimeOption = customTime;

        this.cdr.detectChanges();

        setTimeout(() => {
          this.updateSelectElements();
        }, 0);
      }
    );
    this.subscriptions.push(customTimeSubscription);

    const timeFilterSubscription =
      this.timeService.timeFilterChanged$.subscribe(() => {
        setTimeout(() => {
          this.cdr.detectChanges();

          this.updateSelectElements();
        }, 0);
      });
    this.subscriptions.push(timeFilterSubscription);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateSelectElements();

      setTimeout(() => {
        this.updateSelectElements();
      }, 300);
    }, 100);
  }

  private updateSelectElements() {
    if (!this.fromSelectRef || !this.toSelectRef) {
      return;
    }

    const fromOptions =
      this.fromSelectRef.nativeElement.querySelectorAll('option');
    const fromValue = this.timeService.selectOption1;

    if (this.timeService.isButtonClicked && this.customFromTimeOption) {
      const isStandardTime = this.isStandardTimeOption(
        this.customFromTimeOption.time
      );

      let customOptionExists = false;

      fromOptions.forEach((optionElem: HTMLOptionElement) => {
        try {
          const optionValue = JSON.parse(optionElem.value || '{}');
          if (
            this.customFromTimeOption &&
            optionValue.time === this.customFromTimeOption.time &&
            optionValue.category === this.customFromTimeOption.category
          ) {
            customOptionExists = true;
            if (this.fromSelectRef && this.fromSelectRef.nativeElement) {
              this.fromSelectRef.nativeElement.value = optionElem.value;
            }
          }
        } catch (e) {
          // Swallow error, do nothing
        }
      });

      if (!customOptionExists && this.customFromTimeOption) {
        if (isStandardTime) {
          fromOptions.forEach((optionElem: HTMLOptionElement) => {
            try {
              const optionValue = JSON.parse(optionElem.value || '{}');
              if (
                optionValue.time === this.customFromTimeOption?.time &&
                optionValue.category === this.customFromTimeOption?.category
              ) {
                if (this.fromSelectRef && this.fromSelectRef.nativeElement) {
                  this.fromSelectRef.nativeElement.value = optionElem.value;
                }
                customOptionExists = true;
              }
            } catch (e) {
              // Swallow error, do nothing
            }
          });
        }

        if (!customOptionExists) {
          const customTime = this.customFromTimeOption;
          setTimeout(() => {
            this.timeService.selectOption1 = {
              time: customTime.time,
              category: customTime.category,
            };
            this.cdr.detectChanges();
          }, 0);
        }
      }

      return;
    }

    if (
      this.customFromTimeOption &&
      this.customFromTimeOption.time === fromValue.time &&
      this.customFromTimeOption.category === fromValue.category
    ) {
      const matchValue = JSON.stringify({
        time: this.customFromTimeOption.time,
        category: this.customFromTimeOption.category,
      });
      this.fromSelectRef.nativeElement.value = matchValue;
    }

    let fromOptionFound = false;
    fromOptions.forEach((optionElem: HTMLOptionElement) => {
      try {
        const optionValue = JSON.parse(optionElem.value || '{}');
        if (
          optionValue.time === fromValue.time &&
          optionValue.category === fromValue.category
        ) {
          const localOption = optionElem.cloneNode(true) as HTMLOptionElement;
          localOption.selected = true;
          fromOptionFound = true;
        }
      } catch (e) {
        // Swallow error, do nothing
      }
    });

    if (!fromOptionFound) {
      const sameCategory = Array.from(fromOptions).filter(
        (optionElem: HTMLOptionElement) => {
          try {
            const optionValue = JSON.parse(optionElem.value || '{}');
            return optionValue.category === fromValue.category;
          } catch (e) {
            return false;
          }
        }
      );
      if (sameCategory.length > 0) {
        const firstOption = sameCategory[0] as HTMLOptionElement;
        firstOption.selected = true;
      }
    }

    const toOptions = this.toSelectRef.nativeElement.querySelectorAll('option');
    const toValue = this.timeService.selectOption2;

    let toOptionFound = false;
    toOptions.forEach((optionElem: HTMLOptionElement) => {
      try {
        const optionValue = JSON.parse(optionElem.value || '{}');
        if (
          optionValue.time === toValue.time &&
          optionValue.category === toValue.category
        ) {
          const localOption = optionElem.cloneNode(true) as HTMLOptionElement;
          localOption.selected = true;
          toOptionFound = true;
        }
      } catch (e) {
        // Swallow error, do nothing
      }
    });

    if (!toOptionFound) {
      if (toValue.time === 'Now') {
        const nowOptions = Array.from(toOptions).filter(
          (optionElem: HTMLOptionElement) => {
            try {
              const optionValue = JSON.parse(optionElem.value || '{}');
              return optionValue.time === 'Now';
            } catch (e) {
              return false;
            }
          }
        );
        if (nowOptions.length > 0) {
          const nowOption = nowOptions[0] as HTMLOptionElement;
          nowOption.selected = true;
        }
      }
    }

    this.cdr.detectChanges();
  }

  shouldDisableOption(time: string, day: string): boolean {
    if (
      day === 'Yesterday' &&
      this.timeService.selectOption1.category === 'Today'
    ) {
      return true;
    }

    return false;
  }

  compareSelectOptions(option1: any, option2: any): boolean {
    if (!option1 || !option2) {
      return false;
    }

    const timeMatch = option1.time === option2.time;
    const categoryMatch = option1.category === option2.category;
    const result = timeMatch && categoryMatch;

    return result;
  }

  private timeToMinutes(timeStr: string): number {
    if (timeStr === 'Now') {
      const now = new Date();
      return now.getHours() * 60 + now.getMinutes();
    }

    const match = timeStr.match(/(\d+):(\d+)\s*(AM|PM)/);
    if (!match) return 0;

    let hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    const isPM = match[3] === 'PM';

    if (isPM && hours < 12) hours += 12;
    if (!isPM && hours === 12) hours = 0;

    return hours * 60 + minutes;
  }

  isTimeBefore(time1: string, time2: string): boolean {
    const minutes1 = this.timeToMinutes(time1);
    const minutes2 = this.timeToMinutes(time2);
    return minutes1 < minutes2;
  }

  isTimeAfter(time1: string, time2: string): boolean {
    const minutes1 = this.timeToMinutes(time1);
    const minutes2 = this.timeToMinutes(time2);
    return minutes1 > minutes2;
  }

  isTimeBetween(time: string, start: string, end: string): boolean {
    const timeMinutes = this.timeToMinutes(time);
    const startMinutes = this.timeToMinutes(start);
    const endMinutes = this.timeToMinutes(end);
    return timeMinutes >= startMinutes && timeMinutes < endMinutes;
  }

  /**
   * Checks if a time string is one of the standard time options (3-hour intervals)
   * @param time The time string to check (e.g., "8:20 AM")
   * @returns True if the time is a standard option, false otherwise
   */
  isStandardTimeOption(time: string): boolean {
    const standardTimes = [
      '12:00 AM',
      '3:00 AM',
      '6:00 AM',
      '9:00 AM',
      '12:00 PM',
      '3:00 PM',
      '6:00 PM',
      '9:00 PM',
      'Now',
    ];
    return standardTimes.includes(time);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
  }
}
