<div class="filter-dropdown">
  <div
    [ngClass]="{ 'filter-background': filterService.map.get('trace') }"
    class="dropdown-items"
    (click)="onTrace()"
  >
    <span id="icon-trace">
      <i class="fa fa-search" aria-hidden="true"></i>
    </span>
    <span class="label-text">Trace</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('debug') }"
    class="dropdown-items"
    (click)="onDebug()"
  >
    <span id="icon-bug">
      <i class="fa fa-bug" aria-hidden="true"></i>
    </span>
    <span class="label-text">Debug</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('info') }"
    class="dropdown-items"
    (click)="onInfo()"
  >
    <span id="icon-info">
      <i class="fa fa-info-circle" aria-hidden="true"></i>
    </span>
    <span class="label-text">Info</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('notice') }"
    class="dropdown-items"
    (click)="onNotice()"
  >
    <span id="icon-horn">
      <i class="fa fa-bullhorn" aria-hidden="true"></i>
    </span>
    <span class="label-text">Notice</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('warn') }"
    class="dropdown-items"
    (click)="onWarn()"
  >
    <span class="icon-orange">
      <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
    </span>
    <span class="label-text">Warn</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('error') }"
    class="dropdown-items"
    (click)="onError()"
  >
    <span class="icon-red">
      <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
    </span>
    <span class="label-text">Error</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('critical') }"
    class="dropdown-items"
    (click)="onCritical()"
  >
    <span class="icon-orange">
      <i class="fa fa-fire" aria-hidden="true"></i>
    </span>
    <span class="label-text">Critical</span>
  </div>

  <div
    [ngClass]="{ 'filter-background': filterService.map.get('fatal') }"
    class="dropdown-items"
    (click)="onFatal()"
  >
    <span class="icon-red">
      <i class="fa fa-fire" aria-hidden="true"></i>
    </span>
    <span class="label-text">Fatal</span>
  </div>
</div>
