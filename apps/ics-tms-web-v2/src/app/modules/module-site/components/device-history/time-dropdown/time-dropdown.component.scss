.time-container {
  width: 24rem;
  height: fit-content;
  border-radius: 0.3rem;
  box-shadow:
    0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
    0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
    0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
}

.upper-container {
  padding: 0.8rem 1rem 0.9rem;

  b {
    font-size: 1.3rem;
  }

  .button-group-history {
    border: 0.1rem solid var(--dropdown-border-hover);
    border-radius: 0.3rem;
    display: flex;
    background-color: var(--dropdown-by-default);
    justify-content: space-around;
    width: 100%;

    button {
      flex: 1;
      font-size: 1.3rem;
      font-weight: 700;
      width: 20%;
      border: none;
      padding: 0.4rem 0;
      text-align: center;
      background-color: var(--dropdown-by-default);

      &:not(:last-child) {
        border-right: 0.1rem solid var(--dropdown-border-hover);
      }

      &:hover {
        background-color: var(--dropdown-hover);
      }
    }
  }
}

.button-active-bg {
  border-color: var(--placeholder-text-color) !important;
  background-color: var(--dropdown-bg-selected) !important;
}

.lower-container {
  padding: 0.8rem 1rem 0.2rem 0.9rem;
}

.from-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding-bottom: 0.7rem;

  b {
    flex: 1;
    font-size: 1.3rem;
    margin-bottom: 0.4rem;
  }
  .from-input {
    display: inline-block;
    border: 0.1rem solid var(--color-border);
  }
  .to-input {
    border: 0.1rem solid var(--color-border);
  }

  .history-input-label {
    position: absolute;
    padding: 0.4rem 0.4rem 0.2rem 0.4rem;
    right: 3rem;
    border-radius: 0.25em;
    white-space: nowrap;
    line-height: 1;
    font-size: 1.3rem;
    background: #e0e0e0;
  }
}

.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
  position: initial !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup {
  padding: 0;
  color: var(--color-black);
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  padding: 0;
}

select {
  font-size: 1.4rem;
  width: 100%;
  border-radius: 0.3rem;
  flex: 6;
  height: 2.8rem;
}

select:focus {
  border-color: var(--color-primary-shade-two);
  outline: 0;
  box-shadow: inset 0 0 0 0.1rem var(--color-primary-shade-two);
}
