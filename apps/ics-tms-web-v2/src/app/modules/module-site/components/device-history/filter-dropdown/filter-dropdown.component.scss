.filter-dropdown {
  cursor: pointer;
  width: 14rem;
  box-shadow:
    0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
    0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
    0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
}

.dropdown-items {
  padding: 0.4rem 0.8rem 0.6rem;
  display: flex;
  align-items: center;
  justify-content: space-between;

  i {
    font-size: 1.4rem;
  }

  .label-text {
    font-size: 1.4rem;
  }
}

.dropdown-items:hover {
  background-color: var(--color-bg-default);
}

#icon-trace {
  color: var(--color-white-shade-four);
}

#icon-bug {
  color: var(--label-success);
}

#icon-info {
  color: var(--label-info);
}

#icon-horn {
  color: var(--label-warning-shade-one);
}

.icon-orange {
  color: var(--label-warning-shade-two);
}

.icon-red {
  color: var(--color-bg-red);
}

.mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
  line-height: normal;
  padding: 0.6rem 1.6rem !important;
}

.mat-button-toggle-standalone.mat-button-toggle-appearance-standard,
.mat-button-toggle-group-appearance-standard {
  border: solid 0.1rem var(--color-border);
  border-radius: 0.6rem;
}

.filter-background {
  background-color: var(--color-email-not-verified);
  color: var(--color-white);
}

.filter-background:hover {
  background-color: var(--color-email-not-verified);
  color: var(--color-white);
}
