<div class="time-container">
  <div class="upper-container">
    <b>Show last</b>
    <br />
    <div class="button-group-history">
      <button
        [ngClass]="{
          'button-active-bg': timeService.getButtonClicked() === '1 hour',
        }"
        class="button-history"
        (click)="timeService.setButtonClicked('1 hour', true)"
      >
        1h
      </button>
      <button
        [ngClass]="{
          'button-active-bg': timeService.getButtonClicked() == '3 hour',
        }"
        class="button-history"
        (click)="timeService.setButtonClicked('3 hour', true)"
      >
        3h
      </button>
      <button
        [ngClass]="{
          'button-active-bg': timeService.getButtonClicked() == '6 hour',
        }"
        class="button-history"
        (click)="timeService.setButtonClicked('6 hour', true)"
      >
        6h
      </button>
      <button
        [ngClass]="{
          'button-active-bg': timeService.getButtonClicked() == '12 hour',
        }"
        class="button-history"
        (click)="timeService.setButtonClicked('12 hour', true)"
      >
        12h
      </button>
      <button
        [ngClass]="{
          'button-active-bg': timeService.getButtonClicked() == '24 hour',
        }"
        class="button-history"
        (click)="timeService.setButtonClicked('24 hour', true)"
      >
        24h
      </button>
    </div>
  </div>
  <mat-divider></mat-divider>
  <div class="lower-container">
    <div class="from-row">
      <b>From</b>
      <select
        [(ngModel)]="timeService.selectOption1"
        (ngModelChange)="timeService.setIsButtonClicked(false)"
        class="from-input"
        #fromSelect
        [compareWith]="compareSelectOptions"
      >
        <!-- Yesterday options -->
        <optgroup label="Yesterday">
          <option [ngValue]="{ time: '12:00 AM', category: 'Yesterday' }">
            12:00 AM
          </option>
          <option [ngValue]="{ time: '3:00 AM', category: 'Yesterday' }">
            3:00 AM
          </option>
          <option [ngValue]="{ time: '6:00 AM', category: 'Yesterday' }">
            6:00 AM
          </option>
          <option [ngValue]="{ time: '9:00 AM', category: 'Yesterday' }">
            9:00 AM
          </option>
          <option [ngValue]="{ time: '12:00 PM', category: 'Yesterday' }">
            12:00 PM
          </option>
          <option [ngValue]="{ time: '3:00 PM', category: 'Yesterday' }">
            3:00 PM
          </option>
          <option [ngValue]="{ time: '6:00 PM', category: 'Yesterday' }">
            6:00 PM
          </option>
          <option [ngValue]="{ time: '9:00 PM', category: 'Yesterday' }">
            9:00 PM
          </option>
          <!-- Custom Yesterday time option if available -->
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Yesterday' &&
              !isStandardTimeOption(customFromTimeOption.time)
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
        </optgroup>

        <!-- Today options -->
        <optgroup label="Today">
          <option [ngValue]="{ time: '12:00 AM', category: 'Today' }">
            12:00 AM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '12:00 AM', '3:00 AM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '3:00 AM', category: 'Today' }">
            3:00 AM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '3:00 AM', '6:00 AM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '6:00 AM', category: 'Today' }">
            6:00 AM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '6:00 AM', '9:00 AM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '9:00 AM', category: 'Today' }">
            9:00 AM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '9:00 AM', '12:00 PM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '12:00 PM', category: 'Today' }">
            12:00 PM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '12:00 PM', '3:00 PM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '3:00 PM', category: 'Today' }">
            3:00 PM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '3:00 PM', '6:00 PM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '6:00 PM', category: 'Today' }">
            6:00 PM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeBetween(customFromTimeOption.time, '6:00 PM', '9:00 PM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
          <option [ngValue]="{ time: '9:00 PM', category: 'Today' }">
            9:00 PM
          </option>
          <option
            *ngIf="
              customFromTimeOption &&
              customFromTimeOption.category === 'Today' &&
              !isStandardTimeOption(customFromTimeOption.time) &&
              isTimeAfter(customFromTimeOption.time, '9:00 PM')
            "
            [ngValue]="customFromTimeOption"
          >
            {{ customFromTimeOption.time }}
          </option>
        </optgroup>
      </select>
      <div class="history-input-label">
        {{ timeService.selectOption1.category }}
      </div>
    </div>
    <div class="from-row">
      <b>To</b>
      <select
        [(ngModel)]="timeService.selectOption2"
        (ngModelChange)="timeService.setIsButtonClicked(false)"
        class="to-input"
        #toSelect
        [compareWith]="compareSelectOptions"
      >
        <!-- Yesterday options -->
        <optgroup label="Yesterday">
          <option
            [ngValue]="{ time: '12:00 AM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('12:00 AM', 'Yesterday')"
          >
            12:00 AM
          </option>
          <option
            [ngValue]="{ time: '3:00 AM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('3:00 AM', 'Yesterday')"
          >
            3:00 AM
          </option>
          <option
            [ngValue]="{ time: '6:00 AM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('6:00 AM', 'Yesterday')"
          >
            6:00 AM
          </option>
          <option
            [ngValue]="{ time: '9:00 AM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('9:00 AM', 'Yesterday')"
          >
            9:00 AM
          </option>
          <option
            [ngValue]="{ time: '12:00 PM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('12:00 PM', 'Yesterday')"
          >
            12:00 PM
          </option>
          <option
            [ngValue]="{ time: '3:00 PM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('3:00 PM', 'Yesterday')"
          >
            3:00 PM
          </option>
          <option
            [ngValue]="{ time: '6:00 PM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('6:00 PM', 'Yesterday')"
          >
            6:00 PM
          </option>
          <option
            [ngValue]="{ time: '9:00 PM', category: 'Yesterday' }"
            [disabled]="shouldDisableOption('9:00 PM', 'Yesterday')"
          >
            9:00 PM
          </option>
        </optgroup>

        <!-- Today options -->
        <optgroup label="Today">
          <option
            [ngValue]="{ time: '12:00 AM', category: 'Today' }"
            [disabled]="shouldDisableOption('12:00 AM', 'Today')"
          >
            12:00 AM
          </option>
          <option
            [ngValue]="{ time: '3:00 AM', category: 'Today' }"
            [disabled]="shouldDisableOption('3:00 AM', 'Today')"
          >
            3:00 AM
          </option>
          <option
            [ngValue]="{ time: '6:00 AM', category: 'Today' }"
            [disabled]="shouldDisableOption('6:00 AM', 'Today')"
          >
            6:00 AM
          </option>
          <option
            [ngValue]="{ time: '9:00 AM', category: 'Today' }"
            [disabled]="shouldDisableOption('9:00 AM', 'Today')"
          >
            9:00 AM
          </option>
          <option
            [ngValue]="{ time: '12:00 PM', category: 'Today' }"
            [disabled]="shouldDisableOption('12:00 PM', 'Today')"
          >
            12:00 PM
          </option>
          <option
            [ngValue]="{ time: '3:00 PM', category: 'Today' }"
            [disabled]="shouldDisableOption('3:00 PM', 'Today')"
          >
            3:00 PM
          </option>
          <option
            [ngValue]="{ time: '6:00 PM', category: 'Today' }"
            [disabled]="shouldDisableOption('6:00 PM', 'Today')"
          >
            6:00 PM
          </option>
          <option
            [ngValue]="{ time: '9:00 PM', category: 'Today' }"
            [disabled]="shouldDisableOption('9:00 PM', 'Today')"
          >
            9:00 PM
          </option>
          <!-- Add Now option -->
          <option [ngValue]="{ time: 'Now', category: 'Today' }">Now</option>
        </optgroup>
      </select>
      <div class="history-input-label">
        {{ timeService.selectOption2.category }}
      </div>
    </div>
  </div>
  <!-- Debug info -->
  <div
    style="
      background-color: #f8f9fa;
      padding: 0.5rem;
      margin-bottom: 1rem;
      font-size: 1rem;
      display: none;
    "
  >
    <strong>Debug Info:</strong><br />
    From: {{ timeService.selectOption1 | json }}<br />
    To: {{ timeService.selectOption2 | json }}<br />
    Button clicked: {{ timeService.isButtonClicked }}<br />
    Button time: {{ timeService.buttonClickedTime }}
  </div>
</div>
