.history-card {
  border: none;
  background-color: var(--divider-color);

  &__header-spacer {
    height: 5.9rem;
  }

  &__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);
    background-color: var(--divider-color);
    transition: all 0.3s ease;

    &.sticky {
      position: fixed;
      top: var(--device-header-height, 5.1rem);
      left: 0;
      right: 0;
      width: 100%;
      box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.1);
      z-index: 1000;
    }

    & .history-card__title {
      margin: 0.7rem 0;
      font-size: 2rem;
      font-weight: 400;
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    gap: 1rem;

    &--sticky {
      justify-content: flex-end;
      position: fixed;
      width: 100%;
      left: 0;
      padding: 1.5rem;
      top: calc(var(--device-header-height, 5.1rem) + 4.9rem);
      background: var(--color-primary);
      color: var(--color-white);
      z-index: 999;

      > button {
        opacity: 0.8;
        color: var(--color-white);

        &:hover {
          color: var(--color-white);
        }
      }

      > i,
      > button > i {
        color: rgba(255, 255, 255, 0.7);
      }

      .history-card__search-input {
        width: 12%;
      }

      .history-card__advanced-text {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    label.history-card__advanced-label {
      font-size: 1.4rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 700;
      cursor: pointer;
      white-space: nowrap;
      line-height: 1;
      vertical-align: middle;
      margin: 0;
    }

    .history-card__checkbox {
      width: auto;
      height: 1.25rem;
      vertical-align: middle;
      margin: 0;
      cursor: pointer;
    }

    .history-card__advanced-text {
      font-weight: 700;
    }

    .history-card__search-input {
      margin-bottom: 0;
      vertical-align: middle;
      font-size: 1.4rem;
      padding: 0.3rem 0.5rem;
      border: 0.1rem solid var(--color-border);
      border-radius: 0.3rem;
    }
  }

  &__dropdown {
    position: relative;

    &--filter {
      margin-left: 0.5rem;

      .history-card__controls--sticky & {
        button {
          background-color: transparent;
          color: var(--color-white);

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--color-white);
          }
        }
      }
    }

    &--time {
      margin-left: 0.5rem;

      .history-card__controls--sticky & {
        button {
          background-color: transparent;
          color: var(--color-white);

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--color-white);
          }
        }
      }
    }

    &-button {
      font-size: 1.6rem;
      font-weight: 500;
      background-color: var(--color-white);
      color: var(--logo-color-font);
      border: none;
      border-radius: 0.3rem;
      vertical-align: middle;
      padding: 0.3rem 0.6rem;
      cursor: pointer;

      &:hover {
        background-color: rgba(0, 0, 0, 0.26);
        color: var(--color-black);
      }
    }

    &-menu {
      position: absolute;
      z-index: 1010;
      background-color: var(--color-white) !important;
      width: max-content;
      top: 3.2rem;

      &--left {
        left: -12rem;
      }

      &--right {
        left: 1rem;
      }

      .history-card__controls--sticky & {
        background-color: var(--color-white) !important;
        color: var(--color-black);

        * {
          color: inherit;
        }

        .button-group-history button {
          background-color: var(--dropdown-by-default);
          color: var(--color-black);

          &:hover {
            background-color: var(--dropdown-hover);
          }

          &.button-active-bg {
            background-color: var(--dropdown-bg-selected) !important;
          }
        }

        select {
          color: var(--color-black);
          background-color: var(--color-white);
          border-color: var(--color-border);
        }
      }
    }
  }

  &__no-results {
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-history);
    font-size: 1.4rem;
    padding: 1rem 1.5rem;

    span {
      padding: 0.6rem 0;
    }
  }

  &__list {
    padding: 1rem 1.5rem;
  }

  &__item {
    &:nth-child(odd) {
      background-color: var(--color-bg-fa);
    }
  }

  &__row {
    display: flex;
    padding: 0.6rem 0;
    border-bottom: 0.1rem solid var(--color-border);
  }

  &__text {
    color: var(--color-black-shade-two);
    font-size: 1.4rem;
    line-height: 1.43;
    width: 75%;

    .fa-info-circle {
      color: var(--label-info);
    }

    span {
      margin-left: 0.8rem;
    }

    ::ng-deep .highlight-letter {
      background-color: #ffff00 !important;
      font-weight: bold !important;
      padding: 0 0.2rem !important;
      border-radius: 0.2rem !important;
      display: inline-block !important;
      color: #000 !important;
      box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.3) !important;
      text-shadow: none !important;
    }

    .fa-search {
      color: var(--color-white-shade-four);
    }

    .fa-bug {
      color: var(--label-success);
    }

    .fa-bullhorn {
      color: var(--label-warning-shade-one);
    }

    .fa-exclamation-circle,
    .fa-fire {
      color: var(--label-warning-shade-two);
    }

    .icon-red {
      color: var(--color-bg-red);
    }
  }

  &__icon--red {
    color: var(--color-bg-red);
  }

  &__more-less {
    cursor: pointer;
    color: var(--color-primary);

    &:hover {
      color: var(--color-blue);
      text-decoration: underline;
    }
  }

  &__advanced-info {
    padding: 0.6rem 0;
  }

  &__timestamp {
    flex: 2;
    font-size: 1.4rem;
  }
}

.return-to-top {
  position: fixed;
  right: 2rem;
  bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  text-decoration: none;
  opacity: 0;
  visibility: hidden;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;

  &.active {
    opacity: 1;
    visibility: visible;

    i {
      opacity: 1;
    }
  }

  &:hover i {
    color: rgba(0, 0, 0, 0.5);
  }

  i {
    font-size: 4.5rem;
    position: relative;
    top: 0;
    left: 0;
    margin: 0;
    transition: all 0.3s ease;
    opacity: 0;
    color: rgba(0, 0, 0, 0.2);
  }
}
