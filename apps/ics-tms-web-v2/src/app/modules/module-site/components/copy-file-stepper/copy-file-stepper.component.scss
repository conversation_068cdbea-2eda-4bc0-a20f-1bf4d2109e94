.copy-files-container {
  margin-left: 25%;
  width: 50%;
  top: -8.5rem;
  position: relative;

  .stepper-area-copy-files {
    box-shadow:
      0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
      0 0.1rem 0.1rem 0 rgba(0, 0, 0, 0.14),
      0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
    background-color: var(--color-white);
    border-radius: 0.3rem;
    padding: 2.4rem;
    margin-bottom: 2rem;

    h3 {
      font-size: 1.8rem;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      font-weight: 500;
      line-height: 1.1;
      border-bottom: 0.1rem solid var(--color-border);
    }

    .stepper-btn-container {
      padding-left: 4rem;
      margin-top: 2rem;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 2rem;

      .copy-files-btn {
        padding: 0.6rem 1.2rem;
        border: none;
        border-radius: 0.3rem;
        color: var(--color-white);
        font-weight: 500;
        font-size: 1.4rem;

        &:disabled {
          opacity: 0.65;
          cursor: not-allowed;
          box-shadow: none;

          &:hover {
            background-color: var(--logo-color-one);
          }
        }
      }

      .copy-file-cancel-btn {
        border-color: var(--btn-cancel-color);
        background-color: var(--btn-cancel-color);
        font-weight: 500;
        font-size: 1.4rem;
        padding: 0.6rem 1.2rem;
        color: var(--color-black);
      }
    }
  }

  .mat-vertical-stepper-header {
    padding: 0 !important;
  }

  .mat-vertical-content-container {
    margin-left: 1.2rem;
  }

  .stepper .mat-vertical-stepper-content {
    margin-bottom: 2rem;
    padding-top: 1.5rem;
  }
}
