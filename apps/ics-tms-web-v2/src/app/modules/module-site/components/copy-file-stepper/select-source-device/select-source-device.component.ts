import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { debounceTime, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { DevicesService } from '../../../services/devices.service';
import { DevicesResponse } from '../../../../module-devices/model/devices.model';
import { getApiConstants } from '../../../constants/api';
import { ID } from '../../../constants/appConstants';
import { Device } from 'src/app/models/ics-stepper-template.model';

@Component({
  selector: 'app-select-source-device',
  templateUrl: './select-source-device.component.html',
  styleUrls: ['./select-source-device.component.scss'],
})
export class SelectSourceDeviceComponent {
  @Input() formGroup!: FormGroup;

  @Input() activeStepIndex!: number;

  @Input() device_id!: string;

  sourceDeviceQuery = '';

  inputSubject = new Subject<string>();

  deviceExists!: Device;

  deviceData: DevicesResponse = {} as DevicesResponse;

  isDeviceDataLoading = true;

  constructor(
    private deviceService: DevicesService,
    private http: HttpClient
  ) {
    this.inputSubject.pipe(debounceTime(900)).subscribe(query => {
      this.deviceService.getSourceDevice(query).subscribe({
        next: data => {
          this.isDeviceDataLoading = false;
          this.deviceData = data;
          this.formGroup.controls['device'].setValue(this.deviceData.results);
          this.deviceExists = this.validateDeviceId(data.results, query);
        },
      });
    });
  }

  validateDeviceId(devices: any, query: string) {
    const isPresent = devices.find(
      (device: Device) =>
        device['serialNumber'].toLowerCase() === query.toLowerCase()
    );
    return isPresent;
  }

  onQueryChange(event: string) {
    this.isDeviceDataLoading = true;
    this.inputSubject.next(event);
    if (event.length < 1) {
      this.deviceData.results = [];
    }
  }

  onClickContinue() {
    this.getFiles();
  }

  getFiles() {
    const targetFileDeviceId = this.deviceData.results[0].id;
    this.http
      .get(
        getApiConstants().device.copyFiles.getFiles.replace(ID, this.device_id)
      )
      .subscribe(data => {
        this.formGroup.controls['this_device_file'].setValue(data);
      });
    this.http
      .get(
        getApiConstants().device.copyFiles.getFiles.replace(
          ID,
          targetFileDeviceId.toString()
        )
      )
      .subscribe(data => {
        this.formGroup.controls['target_file'].setValue(data);
      });
  }
}
