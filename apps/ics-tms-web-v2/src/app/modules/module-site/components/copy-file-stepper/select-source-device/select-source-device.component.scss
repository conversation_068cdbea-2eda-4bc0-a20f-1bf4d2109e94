.select-source-device-container {
  display: flex;
  flex-direction: column;

  .input-block {
    .source-device-input {
      width: 90%;
    }
  }

  .invalid-serial-error {
    color: var(--color-error-text);
    font-size: 1.4rem;
    line-height: 1.3;
    margin: 0 0 1rem;
    text-rendering: optimizelegibility;
  }

  .deviceDetails {
    display: flex;
    gap: 1rem;
    color: var(--color-black);
    margin-bottom: 2.5rem;

    .titles {
      width: 25%;
      font-size: 1.4rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
      align-items: flex-end;

      p {
        margin: 0;
      }
    }

    .values {
      width: 75%;
      display: flex;
      font-size: 1.4rem;
      flex-direction: column;
      gap: 1rem;
      font-weight: 700;

      p {
        margin: 0;
      }
    }
  }

  .select-source-btn-container {
    padding-top: 1.6rem;

    .select-source-btn {
      width: fit-content;
      border-radius: 0.3rem;
      border: none;
      font-size: 1.4rem;
      color: var(--color-white);
      transition: all ease-in 0.1s;
      font-weight: 500;
      padding: 0.6rem 1.25em;
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);

      &:disabled {
        opacity: 0.65;
        cursor: not-allowed;
        box-shadow: none;

        &:hover {
          box-shadow: none;
          background-color: var(--color-primary);
        }
      }
    }
  }
}
