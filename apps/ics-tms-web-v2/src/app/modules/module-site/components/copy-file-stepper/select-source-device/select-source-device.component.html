<div
  class="select-source-device-container"
  [ngClass]="{
    'd-none': activeStepIndex !== 0,
  }"
>
  <div class="input-block">
    <input
      (ngModelChange)="onQueryChange($event)"
      class="ics-input source-device-input"
      [(ngModel)]="sourceDeviceQuery"
      placeholder="Enter the serial number of the source device"
    />
    <div
      class="invalid-serial-error"
      *ngIf="!isDeviceDataLoading && !deviceExists; else deviceDetails"
    >
      Invalid serial number
    </div>

    <ng-template #deviceDetails>
      <div *ngIf="deviceExists" class="deviceDetails">
        <div class="titles">
          <p>Device Name</p>
          <p>Device Type</p>
          <p>Site</p>
        </div>
        <div class="values">
          <p>{{ deviceExists.name }}</p>
          <p>{{ deviceExists.deviceType.name }}</p>
          <p>{{ deviceExists['siteName'] }}</p>
        </div>
      </div>
    </ng-template>
  </div>

  <div class="select-source-btn-container">
    <button
      matStepperNext
      [disabled]="!deviceExists"
      (click)="onClickContinue()"
      class="btn-primary select-source-btn"
    >
      Continue
    </button>
  </div>
</div>
