<div class="copy-files-container">
  <div class="stepper-area-copy-files">
    <h3>Copy files</h3>

    <mat-stepper
      orientation="vertical"
      [linear]="true"
      #stepper
      (selectionChange)="onStepChange($event)"
      class="stepper"
    >
      <ng-template matStepperIcon="edit">
        <mat-icon class="check-circle">check_circle</mat-icon>
      </ng-template>

      <mat-step [stepControl]="sourceDeviceForm">
        <ng-template matStepLabel>Select Source Device </ng-template>
        <app-select-source-device
          [device_id]="device_id"
          [formGroup]="sourceDeviceForm"
          [activeStepIndex]="activeStepIndex"
        >
        </app-select-source-device>
      </mat-step>

      <mat-step [stepControl]="fileDirectory">
        <ng-template matStepLabel>Select Files/Directories</ng-template>

        <app-select-file-directories
          [activeStepIndex]="activeStepIndex"
          [formGroup]="fileDirectory"
        >
        </app-select-file-directories>
      </mat-step>
    </mat-stepper>

    <div class="stepper-btn-container">
      <button [disabled]="true" class="btn-primary copy-files-btn">
        Copy Files
      </button>
      <button
        (click)="onClickCancel()"
        class="btn btn-cancel copy-file-cancel-btn"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
