import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { DEVICE_OVERVIEW } from 'src/app/constants/appConstants';
import { setHeaderName } from 'src/app/store/actions/globalStore.actions';

@Component({
  selector: 'app-copy-file-stepper',
  templateUrl: './copy-file-stepper.component.html',
  styleUrls: ['./copy-file-stepper.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CopyFileStepperComponent {
  activeStepIndex = 0;

  parentForm: FormGroup;

  @Input() device_id!: string;

  @Input() site_id!: string;

  @Output() selectedTab = new EventEmitter<string>();

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private store: Store
  ) {
    this.parentForm = this.formBuilder.group({
      sourceDeviceForm: this.formBuilder.group({
        device: [[], Validators.required],
        this_device_file: [[]],
        target_file: [[]],
      }),
      fileDirectory: this.formBuilder.group({
        file: [[], Validators.required],
      }),
    });
  }

  onStepChange(event: StepperSelectionEvent) {
    this.activeStepIndex = event.selectedIndex;
  }

  get sourceDeviceForm() {
    return this.parentForm.get('sourceDeviceForm') as FormGroup;
  }

  get fileDirectory() {
    return this.parentForm.get('fileDirectory') as FormGroup;
  }

  onClickCancel() {
    this.selectedTab.emit('overview');
    this.store.dispatch(setHeaderName({ name: DEVICE_OVERVIEW }));
  }
}
