.ics-config-list-group {
  padding: 1rem 1.5rem;
}

.ics-config-list-group .list-group-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-top: 0.5rem;
  margin-bottom: 0.7rem;
  color: var(--md-grey-500);
}

.list-group-item {
  position: relative;
  display: block;
  margin-bottom: 0;
  border: 0.1rem solid #d6d6d6;
  background-color: var(--white);
  padding: 0 0 0 0;
}

.select-wrapper {
  display: block;
  border: 0.1rem solid #d6d6d6 !important;
  padding-left: 0.6rem;
  transition: border-color 0.3s ease;
  outline: none;
}

.select-wrapper:focus-within {
  border-color: rgba(68, 138, 255, 0.75) !important;
  color: var(--black) !important;
}

.select-wrapper:focus,
.select-wrapper:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}

.ics-config-list-group .list-group-item .name {
  font-weight: bold;
  cursor: default;
  word-break: break-all;
}
@media (min-width: 99.2rem) {
  .col-md-6 {
    width: 50%;
  }
}
@media (min-width: 99.2rem) {
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left;
  }
}
.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
  position: relative;
  min-height: 0.1rem;
  padding-right: 1rem;
  padding-left: 1rem;
}

.ics-config-list-group .list-group-item .value {
  color: var(--md-grey-700);
}

@media (min-width: 99.2rem) {
  .col-md-6 {
    width: 50%;
  }
}
@media (min-width: 99.2rem) {
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left;
  }
}
@media (min-width: 99.2rem) {
  .ics-config-list-group .list-group-item .value {
    padding: 0.6rem 3rem 0.6rem 1rem;
  }
}
a,
area,
button,
[role='button'],
input,
label,
select,
summary,
textarea {
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}
[role='button'] {
  cursor: pointer;
}

.bootstrap-iso .list-group {
  all: unset !important;
}

.ics-config-list-group .list-group-item:not(.modified) .icon.edited {
  display: none;
}

.ics-config-list-group .list-group-item:not(.modified) .new .value:hover {
  background-color: var(--md-grey-100);
}

.ics-config-list-group .list-group-item.modified .icon.edited {
  color: var(--color-device-config-icon);
  display: inline-block;
  position: absolute;
  left: -1.5rem;
  font-size: 1.6rem;
  top: 0.6rem;
}
.ics-config-list-group .list-group-item.modified .icon:not(.edited) {
  display: none;
}
.ics-config-list-group .list-group-item.modified .new .value {
  background-color: rgba(76, 175, 80, 0.15);
}
.ics-config-list-group .list-group-item.modified .new .value div {
  color: var(--color-device-config-icon);
  font-weight: bold;
}
.ics-config-list-group .list-group-item.modified .old {
  display: block;
}
.ics-config-list-group .list-group-item.modified .old .value {
  background-color: rgba(0, 0, 0, 0.04);
}
.ics-config-list-group .list-group-item.modified .old .value div {
  color: var(--md-red-a400);
  font-weight: bold;
}
.ics-config-list-group .list-group-item .value.editing {
  background-color: var(--md-grey-320);
  border: 0.1rem solid var(--md-amber-100);
  cursor: text;
}
.ics-config-list-group .list-group-item .memory .memory-outer {
  float: left;
  border-radius: 0.3rem;
  height: 1rem;
  -webkit-width: calc(100% - 20rem);
  width: calc(100% - 20rem);
  background-color: var(--md-grey-100);
  overflow: hidden;
  margin-top: 0.5rem;
}
.ics-config-list-group .list-group-item .memory .memory-inner {
  float: left;
  height: 1rem;
  background-color: var(--md-grey-300);
}
.ics-config-list-group .list-group-item .memory .text {
  padding-left: 1rem;
}
