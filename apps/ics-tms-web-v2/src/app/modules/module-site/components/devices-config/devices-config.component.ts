import { DatePipe } from '@angular/common';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import {
  isEqual,
  isNaN,
  isBoolean,
  includes,
  invert,
  get,
  filter as _filter,
  toLower,
  find,
  forEach,
} from 'lodash';
import {
  Component,
  OnInit,
  OnDestroy,
  ElementRef,
  ViewChild,
  inject,
} from '@angular/core';
import { Store, select } from '@ngrx/store';
import { Subject, Subscription } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  NON_OPT,
  DeviceConfigBlacklist,
  DeviceConfigFieldCondition,
  DeviceConfigMap,
  DeviceConfigPrinterModelBlacklist,
  terminalLocationConfigKey,
  terminalLocationValues,
  InternalPrinterParams,
  PrinterlessDeviceModels,
} from '../../models/device-config.config';
import {
  devicesSelector,
  isLoadingSelector,
} from '../../store/selectors/devices.selector';
import * as devicesActions from '../../store/actions/devices.actions';
import {
  CONFIG_FORM,
  ConfigInputData,
  ConfigDataPayload,
} from '../../models/config-data.model';
import { DevicesService } from '../../services/devices.service';
import {
  CONFIG_UPDATE,
  CONFIG_UPDATED,
  CONFIG_UPDATING,
} from '../../constants/appConstants';
import { TIME_ZONE_OPTIONS } from '../../constants/timezone';
import { AuthService } from 'src/app/services/auth.service';
import {
  ModalConfirmationComponent,
  ModalButton,
} from 'src/app/components/shared/ics-stepper-template/modal-confirmation/modal-confirmation.component';
import { ToastService } from 'src/app/services/toast.service';

dayjs.extend(utc);
dayjs.extend(timezone);

const PRT_MODEL_CFG = 'cfg.prt-model';
const PRT_PAPER_WIDTH_CFG = 'cfg.prt-paper-width';

@Component({
  selector: 'app-devices-config',
  templateUrl: './devices-config.component.html',
  styleUrls: ['./devices-config.component.scss'],
})
export class DevicesConfigComponent implements OnInit, OnDestroy {
  compareById = (a: any, b: any) => a && b && a.id === b.id;

  checkInputType(field: any) {
    let ret = field.type;
    if (field.type === 'string' || field.type === 'datetime') {
      if (field.options && field.options.enum) {
        ret = 'select';
      } else {
        ret = 'text';
      }
    } else if (field.type === 'boolean') {
      ret = 'toggle';
    } else if (field.type === 'number') {
      ret = 'number';
    } else if (field.type === 'dropdown') {
      ret = 'dropdown';
    }
    return ret;
  }

  configFields: any[] = [];

  device: any = null;

  data: any = null;

  result: any = null;

  updatedFields: any[] = [];

  btnUpdateLabel = 'Update';

  updating = { step1: false, step2: false };

  // Subject for handling unsubscribe on component destroy
  private destroy$ = new Subject<void>();

  // Track subscriptions that don't support takeUntil
  private subscriptions: Subscription[] = [];

  // Inject dependencies
  private datePipe = inject(DatePipe);

  private store = inject(Store<any>);

  private route = inject(ActivatedRoute);

  private deviceService = inject(DevicesService);

  private authService = inject(AuthService);

  private modalService = inject(NgbModal);

  private toastService = inject(ToastService);

  timezoneOptions = TIME_ZONE_OPTIONS;

  isAppFFEnabled_ADA = this.authService.getMyAppFeatureFlags()['ADA'];

  ADAFlag = this.isAppFFEnabled_ADA;

  isAuxiliarySetupCollapsed = false;

  searchQuery: string = '';

  @ViewChild('inputElement') inputElementRef!: ElementRef<
    HTMLInputElement | HTMLSelectElement
  >;

  ngAfterViewChecked() {
    if (this.inputElementRef) {
      this.inputElementRef.nativeElement.focus();
    }
  }

  checkMediaData(fields: any[]): boolean {
    return fields.some(
      field => field.label === 'Dual Display Enabled' && field.value === true
    );
  }

  checkMediaFields(field: any): boolean {
    return (
      field.label !== 'Dual Display Enabled' &&
      field.label !== 'Secondary Display Brightness' &&
      field.label !== 'GSTV Screen' &&
      field.label !== 'Audio Output Channel'
    );
  }

  blur(e: any) {
    if (e.keyCode !== 13) return;
    const { target } = e;
    target.blur();
  }

  isEqual(x: any, y: any) {
    const xVal = x || null;
    const yVal = y || null;
    return isEqual(xVal, yVal);
  }

  convertToType(fType: string, fData: any, format?: string) {
    const fmt = format || 'MMM D, YYYY [at] h:mm a (UTCZ)';
    let retValue = null;
    const nullValue = fData === 'na' ? fData : null;
    if (fType === 'datetime') {
      if (this.checkNullOrEmpty(fData) !== null) {
        if (fmt.includes('UTCZ') || fmt.includes('Z') || fmt.includes('z')) {
          retValue = dayjs(fData)
            .tz(dayjs.tz.guess())
            .format(fmt.replace('UTCZ', 'z'));
        } else {
          retValue = dayjs(fData).format(fmt);
        }
      } else {
        retValue = null;
      }
    } else if (fType === 'number') {
      if (this.checkNullOrEmpty(fData) !== null) {
        if (!isNaN(parseFloat(fData))) {
          retValue = parseFloat(fData);
        } else {
          retValue = nullValue;
        }
      } else {
        retValue = null;
      }
    } else if (fType === 'boolean') {
      retValue = isBoolean(fData) ? fData : fData === '1' || fData === 'true';
    } else {
      retValue = this.checkNullOrEmpty(fData) !== null ? fData : null;
    }
    return retValue;
  }

  getDatetimeFormat(config: any) {
    let datetimeFormat = '';
    if (config.type === 'datetime' && config.options) {
      datetimeFormat = config.options.format;
    }
    return datetimeFormat;
  }

  isG7(deviceType: string) {
    return includes(
      ['G7-100', 'G6-300', 'G6-400', 'G6-500', 'G7-100-8', 'G7-100-15'],
      deviceType
    );
  }

  isG6500() {
    return this.device.deviceType.id === 'G6-500';
  }

  isControllerDevice(deviceType: string) {
    return includes(['EDGE'], deviceType);
  }

  isDeviceTypeSupported(deviceType: string) {
    return includes(
      [
        'G6-200',
        'G7-100',
        'G6-300',
        'G6-400',
        'G6-500',
        'G7-100-8',
        'G7-100-15',
      ],
      deviceType
    );
  }

  mapPrtModelLabel(fdata: any, prop = 'value') {
    const value = (fdata[prop] || '').toUpperCase();
    let newValue = value;
    if (
      value === 'NIPPON' &&
      this.device.configData &&
      Object.prototype.hasOwnProperty.call(
        Object(this.device.configData),
        'cfg.prt-paper-width'
      ) &&
      this.device.configData['cfg.prt-paper-width']
    ) {
      if (Number(this.device.configData['cfg.prt-paper-width'][prop]) > 2.2) {
        newValue = 'Nippon 3”';
      } else if (
        Number(this.device.configData['cfg.prt-paper-width'][prop]) <= 2.2
      ) {
        newValue = 'Nippon 2”';
      }
    } else {
      newValue =
        invert(DeviceConfigMap['cfg.prt-model'].values)[value] || value;
      if (
        newValue === 'Nippon 2”' &&
        (!this.device.configData ||
          !Object.prototype.hasOwnProperty.call(
            Object(this.device.configData),
            'cfg.prt-paper-width'
          ))
      ) {
        newValue = 'NIPPON';
      }
    }
    Object.assign(fdata, { [prop]: newValue });
  }

  mapPrtSerialPortTypeLabel(fdata: any, prop = 'value') {
    const value = (fdata[prop] || '').toUpperCase();
    let newValue = value;
    newValue =
      invert(DeviceConfigMap['cfg.prt-port-type'].values)[value] || value;
    Object.assign(fdata, { [prop]: newValue });
  }

  checkNullOrEmpty(value: any) {
    return includes([null, undefined, 'null'], value) ? null : '';
  }

  getConfigFields() {
    if (!this.device?.configForm?.elements || !this.device?.configSchema) {
      return [];
    }

    if (
      this.device.configForm &&
      this.device.configForm.elements &&
      this.device.configSchema
    ) {
      let isAuxiliary = false;
      let nonPrinterParam = true;
      let groupedFormFields = this.device.configForm.elements.map((el: any) => {
        const fields = el.elements.reduce((a: any[], c: any) => {
          const fname = c.scope.substring(c.scope.indexOf('/', 2) + 1);
          if (
            !this.isG7(this.device.deviceType.id) &&
            !this.isControllerDevice(this.device.deviceType.id) &&
            fname === 'g7opt.apc-system-freeram.gauge.last'
          ) {
            return a;
          }
          if (
            (this.isG7(this.device.deviceType.id) ||
              this.isControllerDevice(this.device.deviceType.id)) &&
            fname === 'g6opt.ca-dyn-freespace-ram.gauge.mean'
          ) {
            return a;
          }
          let fdata = null;
          if (
            this.device.configSchema[fname] &&
            this.checkAccesibility(fname) &&
            !this.checkIfblacklisted(
              DeviceConfigBlacklist,
              this.device.deviceType.id,
              fname,
              this.device.releaseVersion
            ) &&
            this.checkAdaConfig(fname)
          ) {
            fdata = this.device.configData[fname] || {};
            if (fname === 'db.target.device_type') {
              fdata.value =
                this.device.deviceType.name +
                (this.device.deviceType.screenSize
                  ? ` <strong>(${this.device.deviceType.screenSize}")</strong>`
                  : '');
            }
            if (fname === 'bfc.device-build') {
              if (this.device.configData[fname]) {
                fdata.option = this.device.configData[fname].all;
                fdata.value = this.device.configData[fname].value
                  ? this.device.configData[fname].value
                  : fdata.option[0];
              } else {
                fdata.option = [];
                fdata.value = '';
              }
            }
            if (fname === 'g6opt.env-kernel-type') {
              fdata.value = this.device.config.terminalType;
            }
            if (fname === 'cfg.net-terminal-rank' && fdata.value === 'aux') {
              isAuxiliary = true;
            }
            if (
              fname === 'cfg.net-controller-backup' ||
              fname === 'cfg.tamper-analog' ||
              fname === 'cfg.prt-auto-cut'
            ) {
              fdata.value = fdata.value === '1';
            }
            if (
              this.device.configSchema[fname].type === 'dropdown' &&
              this.device.configSchema[fname].options &&
              Array.isArray(this.device.configSchema[fname].options.enum) &&
              this.device.configSchema[fname].options.enum.length > 0 &&
              (fdata.value === '' ||
                fdata.value === null ||
                typeof fdata.value === 'undefined')
            ) {
              fdata.value =
                this.device.configSchema[fname].options.enum[0].value;
            }
            if (
              fname === 'cfg.prt-model' &&
              (fdata.value === '' ||
                fdata.value === null ||
                fdata.value === undefined) &&
              this.device.configData['g7opt.apc-printer-model']
            ) {
              fdata.value =
                this.device.configData['g7opt.apc-printer-model'].value;
            }
            if (fname === 'cfg.prt-model') {
              this.mapPrtModelLabel(fdata, 'value');
              if (this.checkNullOrEmpty(fdata.pending) !== null) {
                this.mapPrtModelLabel(fdata, 'pending');
              }
            }
            if (fname === 'cfg.prt-port-type') {
              this.mapPrtSerialPortTypeLabel(fdata, 'value');
              if (this.checkNullOrEmpty(fdata.pending) !== null) {
                this.mapPrtSerialPortTypeLabel(fdata, 'pending');
              }
            }
            const boolValue =
              this.device.configSchema[fname].type === 'boolean' ? false : null;
            const typedValue = this.convertToType(
              this.device.configSchema[fname].type,
              fdata.value
            );
            const typedPending = this.convertToType(
              this.device.configSchema[fname].type,
              fdata.pending
            );
            const fobj: any = {
              isEditing: false,
              invalid: false,
              order: c.order,
              name: fname.toString(),
              label: this.device.configSchema[fname].label || fname.toString(),
              placeholder: this.device.configSchema[fname].label,
              timestamp: fdata && fdata.timestamp ? fdata.timestamp : null,
              value:
                fdata && this.checkNullOrEmpty(fdata.value) !== null
                  ? typedValue
                  : boolValue,
              originalValue:
                fdata && this.checkNullOrEmpty(fdata.value) !== null
                  ? typedValue
                  : boolValue,
              pending:
                fdata && this.checkNullOrEmpty(fdata.pending) !== null
                  ? typedPending
                  : null,
              originalPending:
                fdata && this.checkNullOrEmpty(fdata.pending) !== null
                  ? typedPending
                  : null,
              nonIPrinter: nonPrinterParam,
            };
            fobj.isUpdating = fobj.pending !== null;

            if (fname === 'bfc.device-build') {
              fobj.options = fdata.option;
            }

            if (
              fname === 'cfg.prt-model' &&
              get(this.device, ['configSchema', fname, 'options', 'enum'])
                .length > 0
            ) {
              this.device.configSchema[fname].options.enum = _filter(
                this.device.configSchema[fname].options.enum,
                (e: any) =>
                  !includes(
                    DeviceConfigPrinterModelBlacklist[
                      this.device.deviceType.id
                    ] || [],
                    e
                  )
              );
              if (
                fdata.value === 'Internal' ||
                fdata.pending === 'Internal' ||
                fdata.pending === 'INTERNAL'
              ) {
                nonPrinterParam = true;
              }
              const isBlacklist = this.isPrinterPortTypeBlacklist(
                this.device.deviceType['id'],
                this.device.releaseVersion
              );
              const { options } = this.device.configSchema[fname];
              if (isBlacklist) {
                const customPrinterConfigLabel = 'Custom printer';
                options.enum = options.enum.filter(
                  (option: any) => option !== customPrinterConfigLabel
                );
              }
            }
            if (includes(InternalPrinterParams, fname)) {
              fobj.nonIPrinter = nonPrinterParam;
            }
            if (fname !== 'g7opt.apc-printer-model') {
              a.push({ ...this.device.configSchema[fname], ...fobj });
            }
          }
          return a;
        }, []);
        return {
          category: el.label,
          fields,
        };
      });
      if (this.device.deviceType.id !== 'EDGE') {
        if (
          this.device.configData['g7opt.sys-contactless-module'] &&
          ['scc-default', 'scc-config'].indexOf(
            this.device.configData['g7opt.sys-contactless-module'].value
          ) > -1
        ) {
          if (
            this.device.configData['g7opt.scc-channel-status'] &&
            this.device.configData['g7opt.scc-channel-status'].value !== 'up'
          ) {
            groupedFormFields[0].fields.splice(6, 1);
          }
          if (
            this.device.configData['g7opt.sys-securelink-upc-scc'] &&
            this.device.configData['g7opt.sys-securelink-upc-scc'].value !==
              'up'
          ) {
            groupedFormFields[0].fields.splice(6, 1);
          }
        } else {
          groupedFormFields[0].fields.splice(6, 1);
        }
      }
      if (
        (this.isAppFFEnabled_ADA && isAuxiliary) ||
        PrinterlessDeviceModels.includes(this.device.deviceType.name)
      ) {
        groupedFormFields = groupedFormFields.filter(
          (g: any) => g.category !== 'Printer'
        );
      }
      this.checkAndRemoveFields(groupedFormFields);
      return groupedFormFields;
    }
    return [];
  }

  checkAndRemoveFields(groupedData: any[]) {
    const fieldsToRemove = [
      'Secondary Display Brightness',
      'GSTV Screen',
      'Audio Output Channel',
    ];
    const isG6500 = this.isG6500();
    const isIPT =
      this.device.config && this.device.config.subPlatform === 'ipt';
    const isDisplayModeUnavailable =
      !this.device.configData['g7opt.display-mode-sdc'] ||
      (this.device.configData['g7opt.display-mode-sdc'] &&
        this.device.configData['g7opt.display-mode-sdc'].value ===
          'unavailable');
    const isSubPlatformInvalid =
      !this.device.config ||
      !this.device.config.subPlatform ||
      this.device.config.subPlatform === '';
    if (!isG6500 || isSubPlatformInvalid || (isG6500 && isIPT)) {
      fieldsToRemove.push(
        'Dual Display Enabled',
        'Secondary Display Resolution'
      );
    }
    if (isDisplayModeUnavailable) {
      fieldsToRemove.push('Secondary Display Resolution');
    }
    const dualDisplayCategory = groupedData.find(
      category => category.category === 'Dual Display'
    );
    const detailsCategory = groupedData.find(
      category => category.category === 'Details'
    );
    if (dualDisplayCategory) {
      const dualDisplayField = dualDisplayCategory.fields.find(
        (field: any) => field.label === 'Dual Display Enabled'
      );
      const secondaryDisplayBrightness = dualDisplayCategory.fields.find(
        (field: any) => field.label === 'Secondary Display Brightness'
      );
      if (secondaryDisplayBrightness) {
        secondaryDisplayBrightness.type = 'number';
        secondaryDisplayBrightness.options = { minimum: 0, maximum: 100 };
      }
      if (
        dualDisplayField &&
        (dualDisplayField.value === false ||
          isSubPlatformInvalid ||
          isDisplayModeUnavailable ||
          (this.device.config &&
            this.device.config.subPlatform &&
            this.device.config.subPlatform !== 'opt'))
      ) {
        dualDisplayCategory.fields = dualDisplayCategory.fields.filter(
          (field: any) => !fieldsToRemove.includes(field.label)
        );
        if (isIPT && isG6500) {
          dualDisplayCategory.fields = dualDisplayCategory.fields.filter(
            (field: any) => !fieldsToRemove.includes(field.label)
          );
        }
        if (dualDisplayField.value === false) {
          const additionalFieldsToRemove2 = ['Secondary Display Resolution'];
          dualDisplayCategory.fields = dualDisplayCategory.fields.filter(
            (field: any) => !additionalFieldsToRemove2.includes(field.label)
          );
        }
      }
    }
    if (detailsCategory) {
      const secondaryDisplay = detailsCategory.fields.find(
        (field: any) => field.label === 'Secondary Display Status'
      );
      const udcSerial = detailsCategory.fields.find(
        (field: any) => field.label === 'UDC Serial'
      );
      const additionalFieldsToRemoveDetails = [
        'SDC Serial',
        'UDC Serial',
        'UDC Version',
      ];
      const screenSpecifications = detailsCategory.fields.find(
        (field: any) => field.label === 'Supported Screens'
      );
      const type = detailsCategory.fields.find(
        (field: any) => field.label === 'Type'
      );
      if (
        screenSpecifications &&
        screenSpecifications.value !== null &&
        screenSpecifications.value !== '' &&
        isG6500
      ) {
        screenSpecifications.value = screenSpecifications.value.replace(
          /'/g,
          '"'
        );
        if (
          screenSpecifications.value.includes('{') &&
          screenSpecifications.value.includes('}')
        ) {
          try {
            const specifications = JSON.parse(screenSpecifications.value);
            if (
              Object.prototype.hasOwnProperty.call(
                Object(specifications),
                'numOfSupportedScreens'
              )
            ) {
              const { numOfSupportedScreens } = specifications;
              if (typeof numOfSupportedScreens === 'number') {
                screenSpecifications.value = numOfSupportedScreens;
              } else {
                screenSpecifications.value = '';
              }
            }
          } catch (error) {
            screenSpecifications.value = '';
          }
        }
      }
      if (isG6500 && type && !isSubPlatformInvalid) {
        type.value = `${type.value} <strong>(${this.device.config.subPlatform.toUpperCase()})</strong>`;
      }
      if (!isG6500 || (isG6500 && isIPT) || isSubPlatformInvalid) {
        additionalFieldsToRemoveDetails.push(
          'Secondary Display Status',
          'Supported Screens'
        );
        detailsCategory.fields = detailsCategory.fields.filter(
          (field: any) => !additionalFieldsToRemoveDetails.includes(field.label)
        );
      }
      if (
        secondaryDisplay &&
        secondaryDisplay.value !== null &&
        secondaryDisplay.value !== '' &&
        isG6500
      ) {
        switch (secondaryDisplay.value) {
          case 'unavailable':
            detailsCategory.fields = detailsCategory.fields.filter(
              (field: any) =>
                !additionalFieldsToRemoveDetails.includes(field.label)
            );
            break;
          case 'available':
            detailsCategory.fields = detailsCategory.fields.filter(
              (field: any) => field.label !== 'SDC Serial'
            );
            break;
          default: {
            const additionalFieldsToRemove3 = [
              ...additionalFieldsToRemoveDetails,
            ];
            additionalFieldsToRemove3.splice(0, 1);
            detailsCategory.fields = detailsCategory.fields.filter(
              (field: any) => !additionalFieldsToRemove3.includes(field.label)
            );
            break;
          }
        }
        const status =
          secondaryDisplay.value === 'unavailable'
            ? '(Unavailable)'
            : '(Available)';
        if (udcSerial) udcSerial.value += ` ${status}`;
      } else if (isG6500) {
        detailsCategory.fields = detailsCategory.fields.filter(
          (field: any) => !additionalFieldsToRemoveDetails.includes(field.label)
        );
      }
      if (isDisplayModeUnavailable) {
        detailsCategory.fields = detailsCategory.fields.filter(
          (field: any) =>
            field.label !== 'Supported Screens' &&
            field.label !== 'Secondary Display Resolution'
        );
      }
      if (dualDisplayCategory) {
        const dualDisplayField = dualDisplayCategory.fields.find(
          (field: any) => field.label === 'Dual Display Enabled'
        );
        if (dualDisplayField && dualDisplayField.value === false) {
          const additionalFieldsToRemove = [
            'Secondary Display Status',
            'Supported Screens',
          ];
          detailsCategory.fields = detailsCategory.fields.filter(
            (field: any) => !additionalFieldsToRemove.includes(field.label)
          );
        }
      }
    }
  }

  isPrinterPortTypeBlacklist(deviceType: string, deviceRelease: string) {
    const deviceTypes = ['G7-100', 'G6-300'];
    const regexString =
      /^(R[0-2]\.\d+\.\d+[a-z]?|R3\.[0-1]\.\d+[a-z]?|R3\.2\.[0-2][0-3]?[a-z]?)$/;
    return deviceTypes.includes(deviceType) && regexString.test(deviceRelease);
  }

  checkAccesibility(name: string) {
    return !(
      name === 'cfg.lcd-accessibility-mode' &&
      !Object.prototype.hasOwnProperty.call(
        Object(this.device.config),
        'accessibilityMode'
      )
    );
  }

  checkAdaConfig(fname: string) {
    return !(
      fname === 'g7opt.sys-aux-keypad' &&
      (!this.device.config ||
        !Object.prototype.hasOwnProperty.call(
          Object(this.device.config),
          'adaPuck'
        ))
    );
  }

  checkIfblacklisted(
    blacklist: any,
    deviceType: string,
    name: string,
    deviceRelease: string
  ) {
    if (deviceRelease) {
      const releaseNumber = parseInt(deviceRelease.replace(/[^\d]/g, ''), 10);
      const isPrinterPortTypeBlacklist =
        this.isPrinterPortTypeBlacklist(deviceType, deviceRelease) &&
        name === 'cfg.prt-port-type';
      if (isPrinterPortTypeBlacklist) {
        return true;
      }
      if (
        Object.prototype.hasOwnProperty.call(DeviceConfigMap, name) &&
        Object.prototype.hasOwnProperty.call(
          DeviceConfigMap[name],
          deviceType
        ) &&
        DeviceConfigMap[name][deviceType].allowedReleaseNumber !== undefined &&
        releaseNumber < DeviceConfigMap[name][deviceType].allowedReleaseNumber
      ) {
        return true;
      }
    }
    return Object.prototype.hasOwnProperty.call(blacklist, deviceType)
      ? blacklist[deviceType].includes(name)
      : false;
  }

  checkInputFieldMessage(field: any) {
    if (this.isAppFFEnabled_ADA) {
      const fieldCondition = DeviceConfigFieldCondition[field.name];
      if (fieldCondition) {
        const message = fieldCondition(field, this.device);
        if (message) {
          Object.assign(field, { message });
          return true;
        }
      }
    }
    return false;
  }

  formHasChanges() {
    const updatedFields: any[] = [];
    let isInternalPrinter = false;
    this.configFields.forEach((g: any) => {
      g.fields.forEach((f: any) => {
        if (
          f.name === 'cfg.prt-model' &&
          (f.value === 'Internal' ||
            f.pending === 'Internal' ||
            f.pending === 'INTERNAL')
        ) {
          isInternalPrinter = true;
        }
      });
    });
    this.configFields.forEach((g: any) => {
      g.fields.forEach((f: any) => {
        if (includes(InternalPrinterParams, f.name)) {
          Object.assign(f, { nonIPrinter: isInternalPrinter });
        }
        if (
          f.name === 'bfc.device-build' &&
          !f.readonly &&
          !isEqual(f.value?.id, f.originalValue?.id)
        ) {
          updatedFields.push(f);
        }
        if (
          f.name !== 'bfc.device-build' &&
          !f.readonly &&
          (!isEqual(f.value, f.originalValue) ||
            !isEqual(f.pending, f.originalPending))
        ) {
          updatedFields.push(f);
        }
      });
    });
    this.updatedFields = updatedFields;
    return this.updatedFields.length > 0;
  }

  filteredConfigs(searchQuery: string) {
    if (!this.configFields) {
      return;
    }

    const configFields = this.getConfigFields().map((g: any) => {
      if (!g?.fields) return g;
      g.fields.forEach((f: any) => {
        if (this.updatedFields.length > 0) {
          this.updatedFields.forEach((uf: any) => {
            if (f.name === uf.name) {
              Object.assign(f, {
                value: uf.value,
                originalValue: uf.originalValue,
                pending: uf.pending,
                originalPending: uf.originalPending,
              });
            }
          });
        }
      });
      return g;
    });
    if (!searchQuery) {
      this.configFields = configFields;
      return;
    }
    const filteredConfigs = configFields.map((g: any) => {
      const fields = g.fields.filter((f: any) => {
        const q = searchQuery.toLowerCase();
        const label = f.label.toLowerCase();
        const value = JSON.stringify(f.value).toLowerCase();
        const pending = JSON.stringify(f.pending).toLowerCase();
        return (
          label.indexOf(q) > -1 ||
          value.indexOf(q) > -1 ||
          pending.indexOf(q) > -1
        );
      });
      return {
        category: g.category,
        fields,
      };
    });
    this.configFields = filteredConfigs;
  }

  get filteredNetwork() {
    if (!this.query) return this.network;
    return _filter(this.network, item => {
      const q = toLower(this.query);
      const name = toLower(item.name);
      const value = toLower(JSON.stringify(item.value));
      return name.indexOf(q) > -1 || value.indexOf(q) > -1;
    });
  }

  get filteredOther() {
    if (!this.query) return this.other;
    return _filter(this.other, item => {
      const q = toLower(this.query);
      const name = toLower(item.name);
      const value = toLower(JSON.stringify(item.value));
      return name.indexOf(q) > -1 || value.indexOf(q) > -1;
    });
  }

  saving = { step1: true, step2: false, step3: false };

  memoryBarWidth = 0;

  deviceAvailableRAM = 0;

  deploymentType = 'maintenance-window';

  network: any[] = [];

  other: any[] = [];

  details: any[] = [];

  deviceTypes: any[] = [];

  query: string = '';

  isOPT(device: any) {
    return !(NON_OPT.indexOf(device.deviceType?.id) > -1);
  }

  getConfigItems() {
    if (!this.device) return;
    this.network = [
      { name: 'Device IP', value: this.device.ipAddress },
      { name: 'Gateway Address', value: this.device.gatewayAddress },
      { name: 'Netmask', value: this.device.subnetMask },
    ];
    this.other = [
      {
        name: 'Registered Date',
        value: this.device.lastRegistered
          ? dayjs(this.device.lastRegistered)
              .tz(dayjs.tz.guess())
              .format('MMM D, YYYY [at] h:mm a (z)')
          : null,
      },
      { name: 'RKI Keygroup', value: this.device.keyGroupRef },
    ];
    Object.keys(this.device.config || {}).forEach(key => {
      if (key === 'dns') {
        const dns = this.device.config.dns && this.device.config.dns.split(';');
        this.network.push({
          name: 'DNS1',
          value: (dns && dns.length >= 1 && dns[0]) || null,
        });
        this.network.push({
          name: 'DNS2',
          value: (dns && dns.length >= 2 && dns[1]) || null,
        });
      } else {
        const details = this.getConfigDetails(key, this.device.config[key]);
        if (details && details.name && details.category) {
          if (typeof (this as any)[details.category] === 'undefined') {
            (this as any)[details.category] = [];
          }
          (this as any)[details.category].push({
            name: details.name,
            value: details.value || null,
          });
        }
      }
    });
  }

  getConfigDetails(property: string, value: any) {
    const configDetails: any = {
      ipAddress: { name: 'Device IP', category: 'network', value },
      gatewayAddress: { name: 'Gateway Address', category: 'network', value },
      subnetMask: { name: 'Netmask', category: 'network', value },
      isUnicast: { name: 'Media Sync Mode', category: 'network', value },
      controllerIp: { name: 'Controller IP', category: 'network', value },
      controllerPort: { name: 'Controller Port', category: 'network', value },
      configurationServiceIp: {
        name: 'Configuration Service IP',
        category: 'network',
        value,
      },
      configurationServicePort: {
        name: 'Configuration Service Port',
        category: 'network',
        value,
      },
      dhcpEnabled: { name: 'DHCP', category: 'network', value },
      logServer: { name: 'Syslog Server IP', category: 'network', value },
      ntpServer: { name: 'NTP Server', category: 'network', value },
      ntpPool: { name: 'NTP Pool', category: 'network', value },
      printerType: { name: 'Printer', category: 'other', value },
      lastRegistered: {
        name: 'Registered Date',
        category: 'other',
        value: value
          ? dayjs(value)
              .tz(dayjs.tz.guess())
              .format('MMM D, YYYY [at] h:mm a (z)')
          : null,
      },
      keyGroupRef: { name: 'RKI Keygroup', category: 'other', value },
      pkiExpiry: {
        name: 'RKI Keygroup Expiry',
        category: 'other',
        value: value
          ? dayjs(value)
              .tz(dayjs.tz.guess())
              .format('MMM D, YYYY [at] h:mm a (z)')
          : null,
      },
      pciRebootTime: {
        name: 'PCI Reboot Time',
        category: 'other',
        value: value ? dayjs(value, 'hhmm').format('h:mm a') : null,
      },
      masterVolume: {
        name: 'Master Volume',
        category: 'other',
        value: value ? `${value}%` : null,
      },
      timeZone: { name: 'Timezone', category: 'other', value },
      memory: { name: 'Memory', category: 'other', value },
    };
    return (
      (typeof configDetails[property] !== 'undefined' &&
        configDetails[property]) ||
      null
    );
  }

  getDeviceAvailableRAM() {
    const ram = this.other && this.other.find(item => item.name === 'Memory');
    if (ram && ram.value) {
      this.deviceService
        .getDeviceAvailableMemory(this.device.deviceType.id)
        .subscribe((data: any) => {
          this.deviceAvailableRAM = data.mbytes;
          const usedRam = data.mbytes - this.getDeviceFreeRam(ram.value);
          const percentage = (usedRam / this.deviceAvailableRAM) * 100;
          this.memoryBarWidth = percentage;
        });
    }
  }

  getDeviceFreeRam(val: number) {
    return Math.round(val / 1024);
  }

  updateDetails() {
    if (!this.device || !this.device.deviceType) {
      console.warn('Device or device type is not available');
      return;
    }

    const mayEdit =
      this.authService &&
      AuthService.isAllowedAccess('WRITE_DEVICES') &&
      this.device.presence !== 'OUT_OF_INSTANCE';

    this.details = [
      {
        name: 'Name',
        value: this.device.name,
        originalValue: this.device.name,
        editable: mayEdit,
        isEditing: false,
        input: {
          type: 'text',
          placeholder: 'Enter a name for this device',
          minlength: 1,
          maxlength: 100,
        },
      },
      {
        name: 'Type',
        value: find(this.deviceTypes, { id: this.device.deviceType.id }),
        originalValue: find(this.deviceTypes, {
          id: this.device.deviceType.id,
        }),
        editable: false,
        isEditing: false,
        input: {
          type: 'select',
          options: this.deviceTypes,
        },
      },
      {
        name: 'Terminal ID',
        value: this.device.config.terminalId,
        editable: false,
      },
      {
        name: 'Model',
        value: this.device.config.terminalType,
        editable: false,
      },
      {
        name: 'MAC Address',
        value: this.device.macAddress,
        editable: false,
      },
      {
        name: 'Notes',
        value: this.device.description,
        originalValue: this.device.description,
        editable: mayEdit,
        isEditing: false,
        input: {
          type: 'text',
          placeholder: 'Enter any notes about this device',
          minlength: 0,
          maxlength: 1000,
        },
      },
    ];

    if (this.device.deviceType.id === 'G7-100') {
      this.details.push(
        {
          name: 'Serial (UPC)',
          value: this.device.serialNumber,
          editable: false,
        },
        {
          name: 'Serial (SDC)',
          value: this.device.config.sdcSerialNumber,
          editable: false,
        },
        {
          name: 'Serial (APC)',
          value: this.device.config.apcSerialNumber,
          editable: false,
        }
      );
    } else {
      this.details.push({
        name: 'Serial',
        value: this.device.serialNumber,
        editable: false,
      });
    }
  }

  filteredDetails() {
    if (!this.query) return this.details;
    return _filter(this.details, detail => {
      const q = toLower(this.query);
      const name = toLower(detail.name);
      const value = toLower(JSON.stringify(detail.value));
      return name.indexOf(q) > -1 || value.indexOf(q) > -1;
    });
  }

  hasChangedDetails() {
    const editedFields = _filter(
      this.details,
      detail =>
        detail.editable && !this.isEqual(detail.value, detail.originalValue)
    );
    if (!editedFields.length) return false;

    forEach(editedFields, detail => {
      Object.assign(detail, { invalid: !(detail.input.type === 'select') });
      if (detail.invalid && detail.input.type === 'text') {
        if (detail.value.length >= detail.input.minlength) {
          Object.assign(detail, { invalid: false });
        }
      }
    });

    const invalidFields = _filter(editedFields, { invalid: true });
    return !invalidFields.length;
  }

  updateParentDevice(update: any) {
    if (this.device) {
      this.device.name = update.name;
      this.device.deviceType = update.deviceType;
      this.device.description = update.description;

      this.store.dispatch(
        devicesActions.updateDeviceProperties({ data: this.device })
      );
    }
  }

  // Initialize site and device IDs
  siteId!: string;

  deviceId!: string;

  updateType = CONFIG_UPDATE;

  inputFocused = false;

  onBlur() {
    this.inputFocused = false;
    this.focusedIndexI = -1;
    this.focusedIndexJ = -1;
  }

  focusedIndexI = -1;

  focusedIndexJ = -1;

  onInputFocus(indexI: number, indexJ: number) {
    this.inputFocused = true;
    this.focusedIndexI = indexI;
    this.focusedIndexJ = indexJ;
  }

  // Initialize observables using inject pattern
  devicesData$ = this.store.pipe(select(devicesSelector));

  isLoading$ = this.store.pipe(select(isLoadingSelector));

  // Initialize config form
  configForm: CONFIG_FORM = {
    form_data: [],
  };

  modalPrompt(options: {
    title: string;
    textContent: string;
    buttons: ModalButton[];
  }): Promise<void> {
    const modalRef = this.modalService.open(ModalConfirmationComponent, {
      backdrop: 'static',
      keyboard: false,
      centered: true,
      container: '#ng-modal-container',
      windowClass: 'common-menu-popup-modal in',
      size: 'sm',
    });
    modalRef.componentInstance.title = options.title;
    modalRef.componentInstance.textContent = options.textContent;
    modalRef.componentInstance.buttons = options.buttons;

    return modalRef.result;
  }

  ngOnInit(): void {
    // Subscribe to route params to get site_id and device_id
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.siteId = params['site_id'];
      this.deviceId = params['device_id'];

      // Dispatch action to get device data
      this.store.dispatch(
        devicesActions.getData({ siteId: this.siteId, deviceId: this.deviceId })
      );
    });

    // Subscribe to device data changes
    this.devicesData$
      .pipe(
        takeUntil(this.destroy$),
        filter((data: any) => {
          const isValid = !!data && !!data.devicesData?.devicesReducers?.data;
          if (!isValid) {
            console.warn('Invalid or incomplete device data received');
          }
          return isValid;
        })
      )
      .subscribe((data: any) => {
        this.result = data.devicesData.devicesReducers.data;
        this.data = this.result;
        this.device = this.result;
        if (this.device && this.device.deviceType) {
          this.initializeConfigFields();

          this.updateDetails();
          this.getConfigItems();
          this.getDeviceAvailableRAM();

          this.ADAFlag =
            this.isG7(this.device.deviceType.id) && this.isAppFFEnabled_ADA;
        }
      });
  }

  private initializeConfigFields(): void {
    if (this.device?.configForm?.elements && this.device?.configSchema) {
      this.configForm.form_data = this.data?.configForm?.elements?.map(
        (element: any) => ({
          groupName: element.label,
          configInputData: element.elements.map((item: any) => {
            const lastEle = item.scope.split('/').pop();
            return {
              scope: lastEle,
              label: this.data.configSchema[lastEle]?.label ?? null,
              readable: this.data.configSchema[lastEle]?.readonly ?? null,
              type: this.data.configSchema[lastEle]?.type ?? null,
              enumOptions:
                this.data.configSchema[lastEle]?.options?.enum ?? null,
              value: this.data.configData[lastEle]?.value ?? null,
              originalValue: this.data.configData[lastEle]?.value ?? null,
            };
          }),
        })
      );

      this.configFields = this.getConfigFields();
    }
  }

  getDateAndTime(time: any, format: string) {
    return this.datePipe.transform(time, format);
  }

  inputChangedByUser = false;

  handleInputChange(
    value: string | number | Object | null | boolean,
    originalValue: string | number | boolean | Object | null
  ) {
    if (value !== originalValue) {
      this.inputChangedByUser = true;
    } else {
      this.inputChangedByUser = false;
    }
  }

  isUpdateValid() {
    return !this.configForm.form_data.some(formData =>
      formData.configInputData.some(
        inputData => inputData.value !== inputData.originalValue
      )
    );
  }

  handleUpdate() {
    this.updateType = CONFIG_UPDATING;

    const updatedConfigObjects: ConfigInputData[] =
      this.configForm.form_data.reduce(
        (result: ConfigInputData[], formData) => {
          const changedData = formData.configInputData.filter(
            inputData => inputData.value !== inputData.originalValue
          );
          if (changedData.length > 0) {
            result.push(...changedData);
          }
          return result;
        },
        []
      );

    let payload: ConfigDataPayload = {
      config: {},
    };

    updatedConfigObjects.forEach(configObject => {
      const { scope } = configObject;
      if (scope && scope.includes('cfg')) {
        payload.config = { ...payload.config, [scope]: configObject.value };
      } else {
        const key = scope.split('.').pop();
        payload = { ...payload, [String(key)]: configObject.value };
      }
    });

    if (Object.keys(payload.config).length === 0) {
      this.updateType = CONFIG_UPDATED;
      const subscription = this.deviceService
        .updateConfig(payload, this.deviceId)
        .subscribe(() => {
          this.updateType = CONFIG_UPDATE;
          this.store.dispatch(
            devicesActions.getData({
              siteId: this.siteId,
              deviceId: this.deviceId,
            })
          );
        });

      // Add subscription to be cleaned up on destroy
      this.subscriptions.push(subscription);
    }
  }

  convertNullToEmpty(type: string, value: any): any {
    let retValue: any = '';
    switch (type) {
      case 'datetime':
        retValue = value ? dayjs(value) : '';
        break;
      case 'number':
        retValue = value !== null && value !== undefined ? Number(value) : '';
        break;
      case 'boolean':
        retValue = value !== null && value !== undefined ? value : '';
        break;
      case 'dropdown':
        retValue =
          value !== null && value !== undefined
            ? { id: value.id, name: value.name }
            : '';
        break;
      default:
        retValue = value || '';
        break;
    }
    return retValue;
  }

  deviceUpdate(device: any) {
    this.deviceService.updateDevice(device).subscribe({
      next: _response => {
        this.btnUpdateLabel = 'Updated';
        this.updating = { step1: false, step2: true };

        this.deviceService.getDevice(device.id).subscribe({
          next: deviceDetails => {
            this.device = deviceDetails;
            if (
              !Object.prototype.hasOwnProperty.call(
                deviceDetails.configData,
                terminalLocationConfigKey
              )
            ) {
              Object.assign(deviceDetails.configData, {
                [terminalLocationConfigKey]: {
                  value: terminalLocationValues.Forecourt,
                  timestamp: new Date().toISOString(),
                },
              });
            }
            this.device.configData = deviceDetails.configData;
            this.configFields = this.getConfigFields();

            this.updateParentDevice(this.device);
          },
          error: _err => {
            console.error('Failed to get device details');
          },
        });

        setTimeout(() => {
          this.btnUpdateLabel = 'Update';
          this.updating = { step1: false, step2: false };
        }, 800);
      },
      error: _err => {
        this.toastService.show({
          message: 'An error was encountered while updating the device.',
          classname: 'bg-danger text-light',
          delay: 6000,
          type: 'error',
        });
      },
    });
  }

  updateConfigs() {
    if (this.updatedFields.length === 0) return;

    const needsReboot = this.updatedFields.filter(
      f => f.target.toLowerCase() !== 'local'
    );
    let configUpdate: Record<string, any> = {};

    this.updatedFields.forEach(f => {
      if (f.value !== f.originalValue && f.pending === f.originalPending) {
        configUpdate[f.name] = this.convertNullToEmpty(f.type, f.value);
      } else if (
        f.value === f.originalValue &&
        f.pending !== f.originalPending
      ) {
        configUpdate[f.name] = this.convertNullToEmpty(f.type, f.pending);
      }
    });

    if (Object.keys(configUpdate).length === 0) return;

    const name = Object.prototype.hasOwnProperty.call(
      configUpdate,
      'db.target.name'
    )
      ? configUpdate['db.target.name']
      : this.device.name;
    const description = Object.prototype.hasOwnProperty.call(
      configUpdate,
      'db.target.description'
    )
      ? configUpdate['db.target.description']
      : this.device.description;
    const terminalLocation = Object.prototype.hasOwnProperty.call(
      configUpdate,
      terminalLocationConfigKey
    )
      ? configUpdate[terminalLocationConfigKey]
      : null;

    Object.keys(configUpdate).forEach(key => {
      if (key.startsWith('db.target') || key === terminalLocationConfigKey) {
        delete configUpdate[key];
      }
    });

    configUpdate = this.mapConfigValue(configUpdate);

    const deviceData: any = {
      id: this.device.id,
      configUpdate,
    };
    if (name) deviceData.name = name;
    if (description) deviceData.description = description;
    if (
      terminalLocation &&
      Object.values(terminalLocationValues).includes(terminalLocation)
    ) {
      deviceData.terminalLocation = terminalLocation;
    }

    this.btnUpdateLabel = 'Updating';
    this.updating = { step1: true, step2: false };

    if (needsReboot.length > 0) {
      this.modalPrompt({
        title: 'Update Config Confirmation',
        textContent:
          'The config change(s) may require a device reboot. Do you wish to continue?',
        buttons: [
          {
            label: 'No',
            cancel: true,
            class: 'btn-default btn-link-default btn-wide-modal mr7',
          },
          {
            label: 'Yes',
            primary: true,
            class: 'btn-box-shadow btn-primary btn-wide-modal',
          },
        ],
      })
        .then(() => {
          this.deviceUpdate(deviceData);
        })
        .catch(() => {
          this.btnUpdateLabel = 'Update';
          this.updating = { step1: false, step2: false };
        });
    } else {
      this.deviceUpdate(deviceData);
    }
  }

  mapConfigValue(configUpdate: Record<string, any>): Record<string, any> {
    const cu = { ...configUpdate };

    Object.keys(cu).forEach(key => {
      if (
        DeviceConfigMap[key] &&
        DeviceConfigMap[key].values &&
        ![null, undefined].includes(
          DeviceConfigMap[key].values[configUpdate[key]]
        )
      ) {
        cu[key] = DeviceConfigMap[key].values[configUpdate[key]];

        if (key === PRT_MODEL_CFG && configUpdate[key] === 'Nippon 2”') {
          cu[PRT_PAPER_WIDTH_CFG] = 2.2;
        } else if (key === PRT_MODEL_CFG && configUpdate[key] === 'Nippon 3”') {
          cu[PRT_PAPER_WIDTH_CFG] = 3.15;
        }
      }

      if (
        key === 'cfg.net-controller-backup' ||
        key === 'cfg.tamper-analog' ||
        key === 'cfg.prt-auto-cut'
      ) {
        cu[key] = configUpdate[key] === true ? 1 : 0;
      }
    });

    return cu;
  }

  hasChanged() {
    const editedFields = _filter(
      this.details,
      detail =>
        detail.editable && !this.isEqual(detail.value, detail.originalValue)
    );
    if (!editedFields.length) return false;
    editedFields.forEach(detail => {
      Object.assign(detail, { invalid: !(detail.input.type === 'select') });
      if (detail.invalid && detail.input.type === 'text') {
        if (detail.value.length >= detail.input.minlength) {
          Object.assign(detail, { invalid: false });
        }
      }
    });
    const invalidFields = _filter(editedFields, { invalid: true });
    return !invalidFields.length;
  }

  startEditing(field: any) {
    const updatedField = { ...field, isEditing: true };
    Object.assign(field, updatedField);
  }

  stopEditing(field: any) {
    const updatedField = { ...field, isEditing: false };
    Object.assign(field, updatedField);
  }

  updateDevice() {
    this.saving = {
      step1: false,
      step2: true,
      step3: false,
    };

    const device = {
      id: this.device.id,
      name: find(this.details, { name: 'Name' }).value,
      description: find(this.details, { name: 'Notes' }).value,
      deploymentType: this.deploymentType,
    };

    const subscription = this.deviceService.updateDevice(device).subscribe(
      response => {
        this.saving = {
          step1: false,
          step2: false,
          step3: true,
        };
        this.device = response;
        this.updateDetails();
        this.updateParentDevice(response);
        setTimeout(() => {
          this.saving = {
            step1: true,
            step2: false,
            step3: false,
          };
        }, 800);
      },
      () => {
        this.saving = {
          step1: true,
          step2: false,
          step3: false,
        };
      }
    );

    // Add subscription to be cleaned up on destroy
    this.subscriptions.push(subscription);
  }

  /**
   * Cleanup resources when component is destroyed
   */
  ngOnDestroy(): void {
    // Complete the destroy subject to notify all takeUntil operators
    this.destroy$.next();
    this.destroy$.complete();

    // Unsubscribe from all subscriptions that don't support takeUntil
    this.subscriptions.forEach(subscription => {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    });
  }
}
