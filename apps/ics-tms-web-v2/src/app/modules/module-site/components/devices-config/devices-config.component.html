<div>
  <div
    *ngIf="isOPT(device)"
    class="panel panel-default bs-primary mb40 ics-device-config"
  >
    <div class="panel-heading panel-heading-has-btn">
      <h3 class="panel-title">Config</h3>
      <div class="pull-right">
        <div class="form-group display-inline-block mr5 mb0">
          <input
            type="text"
            [(ngModel)]="searchQuery"
            (ngModelChange)="filteredConfigs(searchQuery)"
            class="form-control"
            id="inputConfigSearchQuery"
            placeholder="Search by keyword"
          />
        </div>
        <button
          class="btn btn-primary btn-sm btn-animated display-inline-block pull-right"
          id="btnUpdateConfig"
          [disabled]="!formHasChanges() || updating.step1 || updating.step2"
          [ngClass]="{ 'has-icon': updating.step1 || updating.step2 }"
          (click)="updateConfigs()"
          style="margin-top: 0.2rem"
        >
          <span>{{ btnUpdateLabel }}</span>
          <span *ngIf="updating.step1"
            ><i class="fa fa-circle-o-notch fa-spin fa-fw"></i
          ></span>
          <span *ngIf="updating.step2"
            ><i class="fa fa-check" aria-hidden="true"></i
          ></span>
        </button>
      </div>
    </div>
    <ng-container *ngFor="let group of configFields">
      <div
        class="list-group ics-config-list-group"
        *ngIf="group?.fields?.length > 0"
      >
        <div
          *ngIf="group.category !== 'Auxiliary Setup'"
          class="list-group-title"
        >
          {{ group.category.toUpperCase() }}
        </div>
        <div
          *ngIf="
            group.category === 'Auxiliary Setup' &&
            isG7(device.deviceType.id) &&
            ADAFlag
          "
          class="list-group-title"
        >
          <button
            type="button"
            class="btn btn-link ph0 no-focus"
            (click)="isAuxiliarySetupCollapsed = !isAuxiliarySetupCollapsed"
          >
            <span
              class="icon gicon-arrow_drop_right icon-rotate display-inline-block"
              [ngClass]="{ 'rotate-down-90': isAuxiliarySetupCollapsed }"
            ></span>
          </button>
          {{ group.category.toUpperCase() }}
        </div>
        <div
          [ngClass]="{
            collapse:
              !isAuxiliarySetupCollapsed &&
              group.category === 'Auxiliary Setup',
          }"
        >
          <div
            *ngFor="let field of group.fields"
            class="list-group-item"
            [ngSwitch]="checkInputType(field) === 'memory'"
            [ngClass]="{
              modified:
                !field.readonly &&
                (!isEqual(field.value, field.originalValue) ||
                  field.isUpdating) &&
                checkInputType(field) !== 'dropdown',
            }"
          >
            <!-- Readonly fields -->
            <div *ngIf="field.readonly">
              <div *ngSwitchCase="false">
                <div class="col-md-6 name">
                  <span
                    [innerHTML]="field.label | highlight: searchQuery"
                  ></span>
                </div>
                <div
                  class="col-md-6 value"
                  *ngIf="checkInputType(field) === 'toggle'"
                >
                  <label class="ics-checkbox-switch mb0 no-focus">
                    <span
                      class="switch-icon media-object"
                      [ngClass]="{
                        'is-checked': field.value,
                        'is-disabled': true,
                      }"
                    ></span>
                  </label>
                </div>
                <div
                  class="col-md-6 value"
                  *ngIf="
                    checkInputType(field) !== 'toggle' &&
                    checkInputType(field) !== 'dropdown'
                  "
                >
                  <div *ngIf="field.type === 'number'">
                    <span
                      [innerHTML]="field.value ?? '-' | highlight: searchQuery"
                    ></span>
                  </div>
                  <div *ngIf="field.type !== 'number'">
                    <span
                      [innerHTML]="field.value || '-' | highlight: searchQuery"
                    ></span>
                  </div>
                  <span
                    *ngIf="
                      field.name === 'db.target.key_group_ref' &&
                      isDeviceTypeSupported(device.deviceType.id)
                    "
                  >
                    <span
                      *ngIf="
                        device.siteKeygroupId !== null &&
                        device.siteKeygroupId !== device.keyGroupId &&
                        !device.inFlight
                      "
                      title="Device key group does not match the site key group"
                    >
                      <i
                        class="fa fa-exclamation-circle text-muted"
                        aria-hidden="true"
                      ></i>
                    </span>
                    <span
                      *ngIf="
                        device.siteKeygroupId !== null && !!device.inFlight
                      "
                      title="An RKI process is in-flight for this device"
                    >
                      <i
                        class="fa fa-cog fa-spin text-muted"
                        aria-hidden="true"
                      ></i>
                    </span>
                  </span>
                </div>
              </div>
            </div>
            <!-- Editable fields -->

            <div *ngIf="!field.readonly">
              <!-- Old Value -->
              <div class="old">
                <div
                  class="col-md-6"
                  *ngIf="checkInputType(field) !== 'dropdown'"
                ></div>

                <div
                  class="col-md-6 value"
                  *ngIf="checkInputType(field) !== 'dropdown'"
                  [ngSwitch]="checkInputType(field) === 'toggle'"
                >
                  <span class="icon edited">-</span>

                  <span
                    *ngSwitchCase="false"
                    [innerHTML]="
                      field.originalValue || '&nbsp;' | highlight: searchQuery
                    "
                  ></span>

                  <label
                    *ngSwitchCase="true"
                    class="ics-checkbox-switch mb0 no-focus"
                  >
                    <span
                      class="switch-icon media-object"
                      [ngClass]="{
                        'is-checked': field.originalValue,
                        'is-disabled': true,
                      }"
                    ></span>
                  </label>
                </div>
              </div>
              <!-- / Old Value -->

              <!-- New Value -->
              <div class="new">
                <!-- Field Label -->
                <div class="col-md-6 name" [ngSwitch]="!!field.timestamp">
                  <span
                    *ngSwitchCase="false"
                    [innerHTML]="field.label | highlight: searchQuery"
                  ></span>
                  <span
                    *ngSwitchCase="true"
                    [innerHTML]="field.label | highlight: searchQuery"
                  ></span>
                </div>
                <!-- / Field Label -->

                <!-- Field Value -->
                <div>
                  <select
                    *ngIf="
                      !field.isUpdating &&
                      checkInputType(field) === 'dropdown' &&
                      field.name !== 'bfc.device-build'
                    "
                    style="width: 50%"
                    class="select-wrapper"
                    id="selectConfig-{{ field.name }}"
                    [(ngModel)]="field.value"
                    (keypress)="blur($event)"
                    (blur)="field.isEditing = false"
                    autofocus
                  >
                    <option value="" disabled>Select an option</option>
                    <option
                      *ngFor="let option of field.options.enum"
                      [value]="option.value"
                    >
                      {{ option.text }}
                    </option>
                  </select>
                </div>

                <div
                  *ngIf="
                    !field.isUpdating && checkInputType(field) === 'toggle'
                  "
                  class="col-md-6 value"
                >
                  <div>
                    <span class="icon edited">+</span>
                    <div class="icon">
                      <i class="fa fa-pencil" aria-hidden="true"></i>
                    </div>
                    <label class="ics-checkbox-switch mb0 no-focus">
                      <input
                        type="checkbox"
                        [(ngModel)]="field.value"
                        (change)="field.isEditing = true"
                        hidden
                      />
                      <span
                        class="switch-icon media-object"
                        [ngClass]="{ 'is-checked': field.value }"
                      ></span>
                    </label>
                  </div>
                </div>

                <div
                  class="col-md-6 value"
                  *ngIf="
                    field.isUpdating &&
                    checkInputType(field) === 'toggle' &&
                    checkInputType(field) !== 'dropdown'
                  "
                >
                  <div>
                    <span class="icon edited">+</span>
                    <div class="icon">
                      <i class="fa fa-pencil" aria-hidden="true"></i>
                    </div>

                    <label
                      class="ics-checkbox-switch mb0 no-focus"
                      (click)="field.pending = field.pending ? false : true"
                    >
                      <span
                        class="switch-icon media-object"
                        [ngClass]="{ 'is-checked': field.pending }"
                      ></span>
                    </label>
                  </div>
                </div>

                <div
                  class="col-md-6 value"
                  *ngIf="
                    !field.isUpdating &&
                    checkInputType(field) !== 'toggle' &&
                    checkInputType(field) !== 'dropdown'
                  "
                  (click)="startEditing(field)"
                >
                  <div *ngIf="!field.isEditing">
                    <span class="icon edited">+</span>
                    <div class="icon">
                      <i class="fa fa-pencil" aria-hidden="true"></i>
                    </div>
                    <span
                      *ngIf="field.type === 'number'"
                      [innerHTML]="
                        checkNullOrEmpty(field.value) !== null
                          ? field.value
                          : ('-' | highlight: searchQuery)
                      "
                    ></span>
                    <span
                      *ngIf="field.type !== 'number'"
                      [innerHTML]="field.value || '-' | highlight: searchQuery"
                    ></span>
                  </div>

                  <input
                    *ngIf="checkInputType(field) === 'text' && field.isEditing"
                    (blur)="field.isEditing = false"
                    autofocus
                    type="text"
                    #inputElement
                    id="inputConfig-{{ field.name }}"
                    class="form-control"
                    [placeholder]="field.placeholder"
                    (keypress)="blur($event)"
                    [(ngModel)]="field.value"
                  />

                  <input
                    *ngIf="
                      checkInputType(field) === 'number' && field.isEditing
                    "
                    (blur)="field.isEditing = false"
                    autofocus
                    type="number"
                    #inputElement
                    id="numberConfig-{{ field.name }}"
                    class="form-control"
                    [placeholder]="field.placeholder"
                    (keypress)="blur($event)"
                    [(ngModel)]="field.value"
                    [min]="field.options.minimum"
                    [max]="field.options.maximum"
                    [step]="field.options.step || 1"
                  />

                  <select
                    *ngIf="
                      checkInputType(field) === 'select' &&
                      field.isEditing &&
                      field.name !== 'cfg.rtc-time-zone'
                    "
                    autofocus
                    style="width: 100%"
                    class="select-wrapper"
                    #inputElement
                    id="selectConfig-{{ field.name }}"
                    [(ngModel)]="field.value"
                    (keypress)="blur($event)"
                    (blur)="field.isEditing = false"
                  >
                    <option
                      *ngFor="let option of field.options.enum"
                      [value]="option"
                    >
                      {{ option }}
                    </option>
                  </select>

                  <select
                    *ngIf="
                      checkInputType(field) === 'select' &&
                      field.isEditing &&
                      field.name === 'cfg.rtc-time-zone'
                    "
                    autofocus
                    style="width: 100%"
                    class="select-wrapper"
                    #inputElement
                    id="selectConfig-{{ field.name }}"
                    [(ngModel)]="field.value"
                    (keypress)="blur($event)"
                    (blur)="field.isEditing = false"
                  >
                    <option
                      *ngFor="let option of timezoneOptions"
                      [value]="option"
                    >
                      {{ option }}
                    </option>
                  </select>
                </div>

                <div
                  class="col-md-6 value"
                  *ngIf="
                    field.isUpdating &&
                    checkInputType(field) !== 'toggle' &&
                    checkInputType(field) !== 'dropdown'
                  "
                  (click)="field.isEditing = true"
                >
                  <div *ngIf="!field.isEditing">
                    <span class="icon edited">+</span>
                    <div class="icon">
                      <i class="fa fa-pencil" aria-hidden="true"></i>
                    </div>
                    <span
                      [innerHTML]="
                        field.pending || '-' | highlight: searchQuery
                      "
                    ></span>
                  </div>

                  <input
                    *ngIf="checkInputType(field) === 'text' && field.isEditing"
                    (blur)="field.isEditing = false"
                    autofocus
                    type="text"
                    #inputElement
                    id="inputConfig-{{ field.name }}"
                    class="form-control"
                    [placeholder]="field.placeholder"
                    (keypress)="blur($event)"
                    [(ngModel)]="field.pending"
                  />

                  <input
                    *ngIf="
                      checkInputType(field) === 'number' && field.isEditing
                    "
                    (blur)="field.isEditing = false"
                    autofocus
                    type="number"
                    #inputElement
                    id="numberConfig-{{ field.name }}"
                    class="form-control"
                    [placeholder]="field.placeholder"
                    (keypress)="blur($event)"
                    [(ngModel)]="field.pending"
                    [min]="field.options.minimum"
                    [max]="field.options.maximum"
                    [step]="field.options.step || 1"
                  />

                  <select
                    *ngIf="
                      checkInputType(field) === 'select' &&
                      field.isEditing &&
                      field.name !== 'cfg.rtc-time-zone'
                    "
                    autofocus
                    style="width: 100%"
                    class="select-wrapper"
                    #inputElement
                    id="selectConfig-{{ field.name }}"
                    [(ngModel)]="field.pending"
                    (keypress)="blur($event)"
                    (blur)="field.isEditing = false"
                  >
                    <option
                      *ngFor="let option of field.options.enum"
                      [value]="option"
                    >
                      {{ option }}
                    </option>
                  </select>

                  <select
                    *ngIf="
                      checkInputType(field) === 'select' &&
                      field.isEditing &&
                      field.name === 'cfg.rtc-time-zone'
                    "
                    autofocus
                    style="width: 100%"
                    class="select-wrapper"
                    #inputElement
                    id="selectConfig-{{ field.name }}"
                    [(ngModel)]="field.pending"
                    (keypress)="blur($event)"
                    (blur)="field.isEditing = false"
                  >
                    <option
                      *ngFor="let option of timezoneOptions"
                      [value]="option"
                    >
                      {{ option }}
                    </option>
                  </select>
                </div>

                <div
                  class="col-md-6 value"
                  *ngIf="
                    !field.isUpdating &&
                    checkInputType(field) === 'dropdown' &&
                    field.name === 'bfc.device-build'
                  "
                  (click)="field.isEditing = true"
                >
                  <select
                    *ngIf="field.value !== null"
                    id="selectConfig-{{ field.value.name }}"
                    style="width: 50%"
                    class="select-wrapper"
                    [(ngModel)]="field.value"
                    (blur)="field.isEditing = false"
                    aria-invalid="false"
                    [compareWith]="compareById"
                  >
                    <option
                      *ngFor="let option of field.options"
                      [ngValue]="option"
                    >
                      {{ option.name }}
                    </option>
                  </select>
                </div>
                <!-- / Field Value -->
              </div>
              <!-- / New Value -->

              <!-- Error / Warning Messages -->
              <div
                *ngIf="checkInputFieldMessage(field)"
                [ngClass]="field.message.type"
              >
                <div class="col-md-6"></div>
                <div class="col-md-6 text">
                  {{ field.message.text }}
                </div>
              </div>
              <!-- / Error / Warning Message -->
            </div>

            <!-- Memory Bar -->
            <div *ngSwitchCase="true" class="memory">
              <div class="col-md-6 name">
                <span [innerHTML]="field.label | highlight: searchQuery"></span>
              </div>
              <div class="col-md-6 value" *ngIf="!field.value">
                <span [innerHTML]="'-' | highlight: searchQuery"></span>
              </div>
              <div class="col-md-6 value clearfix" *ngIf="field.value">
                <div class="memory-outer clearfix">
                  <div
                    class="memory-inner"
                    [ngStyle]="{ width: memoryBarWidth + '%' }"
                  ></div>
                </div>
                <span class="text">
                  <span
                    [innerHTML]="
                      getDeviceFreeRam(field.value) +
                        ' MB free of ' +
                        deviceAvailableRAM +
                        ' MB' | highlight: searchQuery
                    "
                  ></span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <div
      class="text-center mt20 pb15 text-grey-500"
      *ngIf="!configFields.length && (searchQuery || '').length > 0"
    >
      No results found for that keyword. Try searching for something else.
    </div>
    <div
      class="text-center mt20 pb15 text-grey-500"
      *ngIf="!configFields.length && !(searchQuery || '').length"
    >
      No available device configuration(s).
    </div>
  </div>

  <div
    *ngIf="!isOPT(device)"
    class="panel panel-default bs-primary mb40 ics-device-config"
  >
    <div class="panel-heading panel-heading-has-btn">
      <h3 class="panel-title">Config</h3>
      <div class="pull-right">
        <div class="form-group display-inline-block mr5 mb0">
          <input
            type="text"
            [(ngModel)]="query"
            class="form-control"
            id="inputConfigSearch"
            placeholder="Search by keyword"
          />
        </div>
        <button
          class="btn btn-primary btn-sm btn-animated display-inline-block pull-right"
          id="btnDeviceSaveConfig"
          [disabled]="!hasChanged() || saving.step2 || saving.step3"
          [ngClass]="{ 'has-icon': saving.step2 || saving.step3 }"
          (click)="updateDevice()"
          style="width: 6rem; margin-top: 0.2rem"
        >
          <span *ngIf="saving.step1">Update</span>
          <span *ngIf="saving.step2"
            ><i class="fa fa-circle-o-notch fa-spin fa-fw"></i
          ></span>
          <span *ngIf="saving.step3"
            ><i class="fa fa-check" aria-hidden="true"></i
          ></span>
        </button>
      </div>
    </div>
    <!-- Details -->
    <div
      class="list-group ics-config-list-group"
      *ngIf="filteredDetails().length > 0"
    >
      <div class="list-group-title">DETAILS</div>
      <div class="list-group-item" *ngFor="let detail of filteredDetails()">
        <div *ngIf="!detail.editable">
          <div class="col-md-6 name">{{ detail.name }}</div>
          <div class="col-md-6 value">
            {{ detail.value?.name || detail.value || '-' }}
          </div>
        </div>
        <div
          [ngClass]="{ modified: !isEqual(detail.value, detail.originalValue) }"
          *ngIf="detail.editable"
        >
          <div class="new">
            <div class="col-md-6 name">{{ detail.name }}</div>
            <div class="col-md-6 value" (click)="detail.isEditing = true">
              <div *ngIf="!detail.isEditing">
                <span class="icon edited">+</span>
                <div class="icon">
                  <i class="fa fa-pencil" aria-hidden="true"></i>
                </div>
                <span *ngIf="detail.input.type === 'select'">{{
                  detail.value?.name
                }}</span>
                <span *ngIf="detail.input.type !== 'select'">{{
                  detail.value || '-'
                }}</span>
              </div>
              <input
                *ngIf="detail.input.type === 'text' && detail.isEditing"
                (blur)="detail.isEditing = false"
                type="text"
                class="form-control"
                [(ngModel)]="detail.value"
                [minlength]="detail.input.minlength"
                [maxlength]="detail.input.maxlength"
                [placeholder]="detail.input.placeholder"
              />
              <select
                *ngIf="detail.input.type === 'select' && detail.isEditing"
                style="width: 50%"
                #inputElement
                class="select-wrapper"
                [(ngModel)]="detail.value"
              >
                <option
                  *ngFor="let option of detail.input.options"
                  [ngValue]="option"
                >
                  {{ option.name }}
                </option>
              </select>
            </div>
          </div>
          <div
            class="error"
            *ngIf="detail.input.type === 'text' && detail.invalid"
          >
            <div class="col-md-6"></div>
            <div class="col-md-6 text">
              {{ detail.name + ' has to be at least ' }}
              {{ detail.input.minlength }} character(s) long
            </div>
          </div>
          <div class="old">
            <div class="col-md-6"></div>
            <div class="col-md-6 value">
              <span class="icon edited">-</span>
              <span *ngIf="detail.input.type === 'select'">{{
                detail.originalValue?.name
              }}</span>
              <span *ngIf="detail.input.type !== 'select'">{{
                detail.originalValue || '&nbsp;'
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Network -->
    <div
      class="list-group ics-config-list-group"
      *ngIf="filteredNetwork.length > 0"
    >
      <div class="list-group-title">NETWORK</div>
      <div class="list-group-item" *ngFor="let item of filteredNetwork">
        <div class="col-md-6 name">
          <span [innerHTML]="item.name | highlight: searchQuery"></span>
        </div>
        <div class="col-md-6 value">
          <span [innerHTML]="item.value || '-' | highlight: searchQuery"></span>
        </div>
      </div>
    </div>
    <!-- Others -->
    <div
      class="list-group ics-config-list-group"
      *ngIf="filteredOther.length > 0"
    >
      <div class="list-group-title">OTHERS</div>
      <div
        class="list-group-item"
        *ngFor="let item of filteredOther"
        [ngSwitch]="item.name !== 'Memory'"
      >
        <div *ngSwitchCase="true">
          <div class="col-md-6 name">
            <span [innerHTML]="item.name | highlight: searchQuery"></span>
          </div>
          <div class="col-md-6 value">
            <span
              [innerHTML]="item.value || '-' | highlight: searchQuery"
            ></span>
            <span
              *ngIf="
                [
                  'G6-200',
                  'G7-100',
                  'G6-300',
                  'G6-400',
                  'G6-500',
                  'G7-100-8',
                  'G7-100-15',
                ].indexOf(device.deviceType.id) > -1
              "
            >
              <span
                *ngIf="
                  device.siteKeygroupId !== null &&
                  item.name === 'RKI Keygroup' &&
                  device.siteKeygroupId !== device.keyGroupId &&
                  !device.inFlight
                "
                title="Device key group does not match the site key group"
              >
                <i
                  class="fa fa-exclamation-circle text-muted"
                  aria-hidden="true"
                ></i>
              </span>
              <span
                *ngIf="
                  device.siteKeygroupId !== null &&
                  item.name === 'RKI Keygroup' &&
                  device.siteKeygroupId !== device.keyGroupId &&
                  !!device.inFlight
                "
                title="An RKI process is in-flight for this device"
              >
                <i class="fa fa-cog fa-spin text-muted" aria-hidden="true"></i>
              </span>
            </span>
          </div>
        </div>
        <div *ngSwitchCase="false" class="memory">
          <div class="col-md-6 name">
            <span [innerHTML]="item.name | highlight: searchQuery"></span>
          </div>
          <div class="col-md-6 value" *ngIf="!item.value">
            <span [innerHTML]="'-' | highlight: searchQuery"></span>
          </div>
          <div class="col-md-6 value clearfix" *ngIf="item.value">
            <div class="memory-outer clearfix">
              <div
                class="memory-inner"
                [ngStyle]="{ width: memoryBarWidth + '%' }"
              ></div>
            </div>
            <span class="text">
              <span
                [innerHTML]="
                  getDeviceFreeRam(item.value) +
                    ' MB free of ' +
                    deviceAvailableRAM +
                    ' MB' | highlight: searchQuery
                "
              ></span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="text-center mt20 pb15 text-grey-500"
      *ngIf="
        !filteredDetails().length &&
        !filteredNetwork.length &&
        !filteredOther.length
      "
    >
      No results found for that keyword. Try searching for something else.
    </div>
  </div>
</div>
