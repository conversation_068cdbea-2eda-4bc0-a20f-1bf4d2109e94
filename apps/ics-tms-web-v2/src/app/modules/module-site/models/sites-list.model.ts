import { ThemePalette } from '@angular/material/core';

export interface Owner {
  id: string;
  name: string;
}

export interface Tag {
  id: number;
  name: string;
  siteCount?: number;
}

export interface Counts {
  [key: string]: number;
  totalDevices: number;
  operationalDevices: number;
  inactiveDevices: number;
  unknownDevices: number;
  oosDevices: number;
}
export interface NameAndLocation {
  name: string;
  location: string;
}
export interface Details {
  id: string;
  name: string;
  formattedAddress: string;
  status: string;
  company: string;
  counts: Counts;
  tags: Tag[];
  created: string;
}

export interface Result {
  id: string;
  name: string;
  formattedAddress: string;
  status: number;
  visible: boolean;
  owner: Owner;
  created: string;
  tags: Tag[];
  counts: Counts;
  statusStr: string;
  company: string;
  details: Details;
}

export interface HealthData {
  siteStatus: {
    TOTAL: number;
    NORMAL: number;
    WARNING: number;
    CRITICAL: number;
    UNKNOWN: number;
    INACTIVE: number;
    [key: string]: number;
  };
  deviceHealth: {
    OPERATIONAL: number;
    OUT_OF_SERVICE: number;
    UNKNOWN: number;
    INACTIVE: number;
    [key: string]: number;
  };
  siteEvents: {
    NEW_SITE: number;
    [key: string]: number;
  };
  oosCategory: {
    [key: string]: number;
  };
  oosCondition: {
    [key: string]: number;
  };
}

export interface OOSNode {
  name: string;
  children?: OOSNode[];
}

export interface Task {
  name: string;
  completed: boolean;
  color: ThemePalette;
  subtasks?: Task[];
}

export interface ExampleFlatNode {
  expandable: boolean;
  name: string;
  level: number;
  completed: boolean;
  color: ThemePalette;
}
export interface Filter {
  [key: string]: boolean;
  newSitesCheckbox: boolean;
  normalCheckbox: boolean;
  warningCheckbox: boolean;
  criticalCheckbox: boolean;
  unknownCheckbox: boolean;
  inactiveCheckbox: boolean;
  operationalCheckbox: boolean;
  operationalDevicesCheckbox: boolean;
  unknownDevicesCheckbox: boolean;
  inactiveDevicesCheckbox: boolean;
  outOfServiceDevicesCheckbox: boolean;
}
export interface SiteGroup {
  id: string;
  name: string;
  owner: Owner;
  siteCount: number;
}
export interface KeyGroup {
  id: string;
  name: string;
  ref: string;
  owner: Owner;
}
export interface ExternalTypes {
  allowedExternalReferenceTypes: string[];
}
export interface ResultsMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface Sites {
  results: Result[];
  resultsMetadata: ResultsMetadata;
}
export interface SitesListDataState {
  sites: Sites;
  healthData: HealthData;
  loading: boolean;
}
export interface AddSiteFormDataState {
  tags: Tag[];
  sitegroups: SiteGroup[];
  keygroups: KeyGroup[];
  externalTypes: ExternalTypes;
  siteNameResult?: boolean;
}

export interface AppState {
  sitesListData: SitesListDataState;
}

export interface SitesParams {
  autoPoll?: boolean;
  isCSV: boolean;
  order?: string;
  pageIndex: number;
  pageSize: number;
  showHiddenSites?: boolean;
  'oosFilter[]'?: string[];
  'statuses[]'?: string[];
  'deviceStatuses[]'?: string[];
  'siteEvents[]'?: string[];
}
