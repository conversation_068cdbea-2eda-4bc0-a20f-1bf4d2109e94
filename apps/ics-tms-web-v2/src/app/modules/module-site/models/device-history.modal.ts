interface Result {
  ap: string;
  ds: string;
  ts: number;
  dt: string;
  dv: string;
  dn: string;
  lv: string;
  mt: string;
  v: string;
  k: string;
  fn: string;
  msg: string;
  isFull?: boolean;
  uniqueId: number;
}

interface ResultsMetadata {
  pageMinSize: number;
  pageKey: {
    deviceId: string;
    ts_SK: string;
  };
}

export interface DeviceHistory {
  results: Result[];
  resultsMetadata: ResultsMetadata;
  noMoreData?: boolean; // Flag to indicate when there's no more data to load
}
