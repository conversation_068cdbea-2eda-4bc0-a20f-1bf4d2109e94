interface Company {
  id: string;
  name: string;
  featureFlags: string[];
}

export interface UsersResults {
  id: string;
  email: string;
  emailVerified: boolean;
  mfaConfigured: boolean;
  status: number;
  fullName: string;
  company: Company;
  roles: string[];
}

export interface UsersData {
  results: UsersResults[];
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
}
