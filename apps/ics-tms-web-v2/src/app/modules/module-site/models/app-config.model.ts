export interface ConfigResult {
  appName: string;
  configFileName: string;
  configFileContentId: string;
  configFileInstanceId: string;
  deviceConfigFileInstanceName: string;
  deployedConfigFileInstanceId: string;
  revisionName: string;
  assignmentLevel: string;
  assignmentStatus: string;
  desiredRenditionHash: string;
  deviceCfremHash: string;
  deployedRenditionHash: string;
  deviceCfactHash: string;
  deviceCflocHash: string;
  lastDeployAt: string | Date;
  mwFrame?: string;
  scheduledAt?: string | Date;
}

export interface ConfigData {
  results: ConfigResult[];
}
