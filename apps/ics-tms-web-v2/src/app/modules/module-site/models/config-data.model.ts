interface ConfigData {
  groupName: string;
  configInputData: ConfigInputData[];
}

export interface ConfigInputData {
  scope: string;
  label: string;
  readable: boolean;
  type: string;
  enumOptions: string[] | null;
  value: string | number | boolean | null | Object;
  originalValue: string | number | boolean | null | Object;
}

export interface CONFIG_FORM {
  form_data: ConfigData[];
}

export interface ConfigDataPayload {
  config: {
    [key: string]: any;
  };
  [key: string]: any;
}
