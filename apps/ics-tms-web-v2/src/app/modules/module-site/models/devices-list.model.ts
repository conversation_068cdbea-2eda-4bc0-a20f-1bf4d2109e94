export interface devicesListData {
  id: string;
  siteId: string;
  siteName: string;
  lastRegistered: string;
  lastContact: string;
  name: string;
  description: string;
  serialNumber: string;
  keyGroupRef: string;
  keyGroupId: string;
  presence: string;
  status: number;
  gatewayAddress: string;
  macAddress: string;
  subnetMask: string;
  releaseVersion: string;
  label?: string;
  auxInfo: {
    type: string;
    status: string;
  };
  alarmRulesSettings: {
    suspended: boolean;
    suspendedByDeviceUntil: number;
    suspendedFrom: number;
    suspendedUntil: number;
  };
  ipAddress: string;
  deviceType: {
    id?: string;
    name?: string;
  };
  ksn: string;
  statusAlarmTs: string;
  statusStr: string;
}

export interface DevicesList {
  results: devicesListData[];
  resultsMetadata: {
    pageIndex: number;
    pageSize: number;
    totalResults: number;
  };
}
