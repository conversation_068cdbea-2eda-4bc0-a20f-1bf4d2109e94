interface Hour {
  openAt: number;
  closeAt: number;
}

interface Owner {
  id: string;
  name: string;
}

interface Tags {
  id: string;
  name: string;
}

interface ExternalReferences {
  referenceId: string;
  referenceType: string;
}

export interface DeviceSite {
  id: string;
  name: string;
  address: string;
  contactPhone: string | null;
  contactEmail: string;
  latitude: string;
  longitude: string;
  formattedAddress: string;
  referenceId: string;
  status: number;
  timezoneId: string;
  visible: boolean;
  hours: Hour[];
  created: string; // You might want to use a Date type here if you handle dates in your code.
  owner: Owner;
  keyGroup: string | null;
  isDefault: boolean;
  suppressOffhoursAlarm: boolean;
  tags: Tags[];
  externalReferences: ExternalReferences[];
  allowedExternalReferenceTypes: string[];
}
