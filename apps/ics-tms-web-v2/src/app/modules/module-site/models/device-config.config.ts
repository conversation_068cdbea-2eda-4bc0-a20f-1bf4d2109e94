export const NON_OPT = ['G6-100', 'vOPT', 'IPT'];

export const InternalPrinterParams = [
  'cfg.prt-model',
  'cfg.prt-port-type',
  'cfg.prt-auto-cut',
  'cfg.prt-paper-width',
];

export const PrinterlessDeviceModels = ['EDGE'];

export const terminalLocationConfigKey = 'cfg.net-terminal-location';

export const terminalLocationValues = {
  Forecourt: 'Forecourt',
  Backcourt: 'Backcourt',
};

export const DeviceConfigBlacklist: { [key: string]: string[] } = {
  'G7-100-15': [],
  'G7-100-8': [],
  'G6-200': [
    'cfg.prt-port-type',
    'cfg.aud-outchannel',
    'cfg.prt-model',
    'cfg.prt-margin-x',
    'cfg.prt-margin-y',
    'cfg.prt-cut-offset',
    'g7opt.sdc-hw-serialnumber',
    'g7opt.apc-hw-serialnumber',
    'cfg.lcd-brightness',
    'cfg.prt-font-size',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.prt-paper-low-threshold',
    'cfg.prt-auto-cut',
    'cfg.net-controller-backup',
    'cfg.net-controller-ip2',
    'cfg.net-controller-port2',
    'cfg.net-controller-con-retry-attempts',
    'cfg.net-controller-con-retry-interval',
    'cfg.tamper-analog',
    'cfg.net-eth-speed',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
  'G6-300': [
    'cfg.tamper-analog',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
  'G6-400': [
    'cfg.prt-port-type',
    'cfg.tamper-analog',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
  'G6-500': [
    'cfg.prt-port-type',
    'cfg.tamper-analog',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
  'G7-100': [
    'cfg.prt-font-size',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.prt-paper-low-threshold',
    'cfg.prt-auto-cut',
    'cfg.net-terminal-rank',
    'cfg.net-icsagent-bind-ip',
    'cfg.net-aux-ip',
    'cfg.mgt-device-type',
  ],
  EDGE: [
    'cfg.prt-port-type',
    'db.target.serial_number',
    'g7opt.sdc-hw-serialnumber',
    'g7opt.scc-hw-serialnumber',
    'cfg.aud-outchannel',
    'cfg.aud-volume-master',
    'cfg.net-terminal-rank',
    'cfg.net-icsagent-bind-ip',
    'cfg.net-aux-ip',
    'cfg.prt-margin-x',
    'cfg.prt-margin-y',
    'cfg.prt-paper-width',
    'cfg.prt-model',
    'db.target.key_group_ref',
    'cfg.prt-cut-offset',
    'cfg.prt-paper-low-threshold',
    'cfg.prt-font-size',
    'cfg.prt-auto-cut',
    'cfg.lcd-brightness',
    'cfg.tamper-analog',
    'client.invenco-icp-peer-cast-mode',
  ],
  OMNIA: [
    'cfg.prt-port-type',
    'cfg.tamper-analog',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
  'WIN-SERVER': [
    'cfg.prt-port-type',
    'cfg.tamper-analog',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
  'LINUX-SERVER': [
    'cfg.prt-port-type',
    'cfg.tamper-analog',
    'cfg.prt-paper-full-length',
    'cfg.prt-paper-full-diameter',
    'cfg.prt-paper-core-diameter',
    'cfg.mgt-device-type',
    'g7opt.scc-hw-serialnumber',
  ],
};

export const DeviceConfigPrinterModelBlacklist: { [key: string]: string[] } = {
  'G7-100-15': [],
  'G7-100-8': [],
  'G6-200': [],
  'G6-300': ['Nippon 3”', 'Nippon 2”', 'Hengstler', 'Zebra', 'Citizen/Axiom'], // ICS-7861:  the options should be: Internal None Auto
  'G6-400': ['Nippon 3”', 'Nippon 2”', 'Hengstler', 'Zebra', 'Citizen/Axiom'],
  'G6-500': ['Nippon 3”', 'Nippon 2”', 'Hengstler', 'Zebra', 'Citizen/Axiom'],
  'G7-100': [],
  OMNIA: ['Nippon 3”', 'Nippon 2”', 'Hengstler', 'Zebra', 'Citizen/Axiom'],
  'WIN-SERVER': [
    'Nippon 3”',
    'Nippon 2”',
    'Hengstler',
    'Zebra',
    'Citizen/Axiom',
  ],
  'LINUX-SERVER': [
    'Nippon 3”',
    'Nippon 2”',
    'Hengstler',
    'Zebra',
    'Citizen/Axiom',
  ],
};

export const DeviceConfigMap: { [key: string]: any } = {
  'cfg.prt-model': {
    values: {
      'Nippon 3”': 'NIPPON',
      'Nippon 2”': 'NIPPON',
      Hengstler: 'HENGSTLER',
      Zebra: 'ZEBRA',
      'Citizen/Axiom': 'CIT_AX',
      Internal: 'INTERNAL_PRINTER',
      'Auto-detect': '', // ICS-7770: if cfg.prt-model is auto, the value should be an empty string
      'Custom printer': 'CUSTOM_TG',
      None: 'NONE',
    },
  },
  'cfg.prt-port-type': {
    values: {
      'User controlled': 'TTY',
      'Internal printer': 'RAW',
    },
  },
  'cfg.lcd-brightness': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-eth-speed': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    EDGE: {
      allowedReleaseNumber: 1,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-controller-backup': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    EDGE: {
      allowedReleaseNumber: 1,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-controller-ip2': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    EDGE: {
      allowedReleaseNumber: 1,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-controller-port2': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    EDGE: {
      allowedReleaseNumber: 1,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-controller-con-retry-attempts': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    EDGE: {
      allowedReleaseNumber: 1,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-controller-con-retry-interval': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    EDGE: {
      allowedReleaseNumber: 1,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.tamper-analog': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.prt-font-size': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.prt-auto-cut': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.prt-paper-full-length': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.prt-paper-full-diameter': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.prt-paper-core-diameter': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.prt-paper-low-threshold': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-terminal-rank': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-icsagent-bind-ip': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
  'cfg.net-aux-ip': {
    'G6-200': {
      allowedReleaseNumber: 326,
    },
    'G6-300': {
      allowedReleaseNumber: 326,
    },
    'G6-400': {
      allowedReleaseNumber: 326,
    },
    'G6-500': {
      allowedReleaseNumber: 326,
    },
    'G7-100': {
      allowedReleaseNumber: 326,
    },
    OMNIA: {
      allowedReleaseNumber: 326,
    },
    'WIN-SERVER': {
      allowedReleaseNumber: 326,
    },
    'LINUX-SERVER': {
      allowedReleaseNumber: 326,
    },
  },
};

export interface DeviceConfigCondition {
  type: 'warning' | 'error';
  text: string;
}

export const DeviceConfigFieldCondition: {
  [key: string]: (field: any, device: any) => DeviceConfigCondition | null;
} = {
  'g7opt.sys-aux-keypad': (field, device) => {
    if (field.value === true && !device.config.adaPuck) {
      return {
        type: 'warning',
        text: 'ADA Keypad is not connected',
      };
    }
    return null;
  },
};
