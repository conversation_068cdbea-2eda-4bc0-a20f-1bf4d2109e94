export interface FileUploadFile {
  path: string;
  applicationId: string;
}

export interface FilePath {
  filePath: string;
  applicationId: string;
}

export interface FileUploadUser {
  id: string | number;
}

export interface FileUploadRequest {
  name: string;
  files: FileUploadFile[];
  devices: Array<{ id: string | number }>;
  sites: any[];
  siteTags: any[];
  users?: FileUploadUser[];
  teams?: FileUploadUser[];
  additionalProperties?: {
    userParams?: {
      host?: string;
      url?: string;
    };
  };
  startDate?: string;
  endDate?: string;
  filePaths?: FilePath[];
}
