export interface DevicesInterface {
  data: DeviceData;
  error: string | null;
  isLoading: boolean;
}

export interface DeviceData {
  id: string;
  siteId: string;
  siteName: string;
  lastRegistered: string;
  lastContact: string;
  name: string;
  description: string;
  serialNumber: string;
  keyGroupRef: string;
  keyGroupId: string;
  presence: string;
  status: number;
  gatewayAddress: string;
  macAddress: string;
  subnetMask: string;
  releaseVersion: string;
  alarmRulesSettings: {
    suspended: boolean;
    suspendedByDeviceUntil: number;
    suspendedFrom: number;
    suspendedUntil: number;
  };
  ipAddress: string;
  deviceType: {
    id?: string;
    name?: string;
  };
  promptSet: {
    id: string;
    name: string;
    version: string;
  };
  ksn: string;
  playlistId: string;
  lastSuccessfulRki: string;
  siteKeygroupId: string;
  statusAlarmTs: string;
  oosConditions: OOSConditions[];
  statusStr: string;
  config?: Config;
  configSchema: ConfigSchema;
  configForm: ConfigForm;
  configData: ConfigData;
  inFlight: boolean;
  auxDevice: any;
  mainDevice: any;
}

interface OOSConditions {
  category: string;
  condition: string;
}

// CONFIG
interface Config {
  dateTime?: string;
  timeZone?: string;
  temperature?: number;
  isMaster?: boolean;
  terminalId?: string;
  terminalType?: string;
}

// CONFIG SCHEMA
interface ConfigSchema {
  [key: string]: {
    label: string;
    readonly: boolean;
    target?: string;
    type: string;
    format: string;
    options?: {
      enum?: string[];
      multi?: boolean;
    };
  };
}

// CONFIG FORM
interface ConfigForm {
  type: string;
  elements: ConfigFormGroup[];
}
interface ConfigFormGroup {
  order: number;
  type: string;
  label: string;
  elements: ConfigFormElement[];
}
interface ConfigFormElement {
  order: number;
  type: string;
  scope?: string;
}

// CONFIG DATA
interface ConfigData {
  [key: string]: ConfigDataProperty;
}
interface ConfigDataProperty {
  value: string | null;
  timestamp: string;
}
