interface Owner {
  id: string;
  name: string;
}

interface Tag {
  id: number;
  name: string;
}

interface Counts {
  totalDevices: number;
  operationalDevices: number;
  inactiveDevices: number;
  unknownDevices: number;
  oosDevices: number;
}

export interface DeviceListResults {
  id: string;
  name: string;
  formattedAddress: string;
  status: number;
  visible: boolean;
  owner: Owner;
  created: string;
  tags: Tag[];
  criticalAlarmTs: string | null;
  counts: Counts;
  statusStr: string;
}

interface ResultsMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface DeviceList {
  results: DeviceListResults[];
  resultsMetadata: ResultsMetadata;
}
