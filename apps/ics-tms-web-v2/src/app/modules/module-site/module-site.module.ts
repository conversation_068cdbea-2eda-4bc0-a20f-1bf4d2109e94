import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatTreeModule } from '@angular/material/tree';
import { MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCardModule } from '@angular/material/card';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { NgSelectModule } from '@ng-select/ng-select';
import {
  NgbA<PERSON>rdionBody,
  NgbA<PERSON>rdionButton,
  NgbA<PERSON>rdionCollapse,
  NgbAccordionDirective,
  NgbAccordionHeader,
  NgbAccordionItem,
  NgbDatepickerModule,
  NgbDropdownModule,
  NgbPopoverModule,
  NgbProgressbarModule,
  NgbTimepickerModule,
  NgbTooltipModule,
  NgbCarouselModule,
} from '@ng-bootstrap/ng-bootstrap';
import { MaterialModule } from '../material.module';
import { SharedCommonModules } from '../common.module';
import { ModuleSiteComponent } from './module-site.component';
import { DeviceHistoryComponent } from './components/device-history/device-history.component';
import { FilterDropdownComponent } from './components/device-history/filter-dropdown/filter-dropdown.component';
import { TimeDropdownComponent } from './components/device-history/time-dropdown/time-dropdown.component';
import { DeviceJobComponent } from './components/device-job/device-job.component';
import { DevicesConfigComponent } from './components/devices-config/devices-config.component';
import { DevicesMediaComponent } from './components/devices-media/devices-media.component';
import { DevicesOverviewComponent } from './components/devices-overview/devices-overview.component';
import { DevicesVersionsComponent } from './components/devices-versions/devices-versions.component';
import { DevicesComponent } from './components/devices/devices.component';
import { DevicesService } from './services/devices.service';
import { devicesReducers } from './store/reducers/devices.reducer';
import { versionsReducers } from './store/reducers/devices-versions.reducer';
import { DVersionsEffect } from './store/effects/devices-versions.effects';
import { DevicesEffect } from './store/effects/devices.effect';
import { ModuleSiteRoutingModule } from './module-site-routing.module';
import { deviceAlarmReducer } from './store/reducers/device-alarm.reducers';
import { DeviceAlarmEffects } from './store/effects/device-alarm.effects';
import { EditDeviceInfoComponent } from './components/device-option-modals/edit-device-info/edit-device-info.component';
import { MoveDeviceOptionsComponent } from './components/device-option-modals/move-device-options/move-device-options.component';
import { RebootDeviceComponent } from './components/device-option-modals/reboot-device/reboot-device.component';
import { RecommissionDeviceComponent } from './components/device-option-modals/recommission-device/recommission-device.component';
import { DeleteDeviceComponent } from './components/device-option-modals/delete-device/delete-device.component';
import { deviceListReducer } from './store/reducers/device-list.reducers';
import { DeviceListEffects } from './store/effects/device-list.effects';
import { deviceSiteReducer } from './store/reducers/device-site.reducers';
import { DeviceSiteEffects } from './store/effects/device-site.effects';
import { alarmRulesReducer } from './store/reducers/alarm-rules.reducers';
import { AlarmRulesEffects } from './store/effects/alarm-rules.effects';
import { selfReducer } from './store/reducers/self.reducers';
import { SelfDataEffects } from './store/effects/self.effects';
import { deviceFileReducer } from './store/reducers/device-file.reducers';
import { DeviceFileEffects } from './store/effects/device-file.effects';
import { FileDetailsComponent } from './components/devices-overview/file-details/file-details.component';
import { deviceHistoryReducer } from './store/reducers/device-history.reducers';
import { DeviceHistoryEffects } from './store/effects/device-history.effects';
import { deviceMediaReducer } from './store/reducers/device-media.reducers';
import { DeviceMediaEffects } from './store/effects/device-media.effects';
import { CopyFileStepperComponent } from './components/copy-file-stepper/copy-file-stepper.component';
import { SelectSourceDeviceComponent } from './components/copy-file-stepper/select-source-device/select-source-device.component';
import { SelectFileDirectoriesComponent } from './components/copy-file-stepper/select-file-directories/select-file-directories.component';
import { ChallengeResponseComponent } from './components/device-option-modals/challenge-response/challenge-response.component';
import { ChallengeResponseVerifyMfaComponent } from './components/device-option-modals/challenge-response/challenge-response-verify-mfa/challenge-response-verify-mfa.component';
import { ClickOutsideDirective } from './directives/click-outside.directive';
import { SwapDeviceComponent } from './components/device-option-modals/swap-device/swap-device.component';
import { DeploymentTypeModalComponent } from './components/device-option-modals/move-device-options/deployment-type-modal/deployment-type-modal.component';
import { PullFilesComponent } from './components/pull-files/pull-files.component';
import { PullFromComponent } from './components/pull-files/pull-from/pull-from.component';
import { SelectFileOrDirectoriesComponent } from './components/pull-files/select-file-or-directories/select-file-or-directories.component';
import { AddNotificationComponent } from './components/pull-files/add-notification/add-notification.component';
import { UsersDataReducer } from './store/reducers/users.reducers';
import { UsersDataEffects } from './store/effects/users.effects';
import { HighlightSearchPipe } from './pipes/highlight-search.pipe';
import { HighlightPipe } from './pipes/highlight.pipe';
import { ExtractMessagePipe } from './pipes/extract-message.pipe';
import { DeviceAppConfigComponent } from './components/device-app-config/device-app-config.component';
import { DeviceNetworkComponent } from './components/device-network/device-network.component';
import { addSiteFormDataReducer } from './store/reducers/add-site-form.reducer';
import { AddSiteFormDataEffects } from './store/effects/add-site-form.effects';
import { AddSiteFormComponent } from './components/add-site-form/add-site-form.component';
import { StoreHoursComponent } from './components/add-site-form/store-hours/store-hours.component';
import { ExternalReferenceComponent } from './components/add-site-form/external-reference/external-reference.component';
import { AddSiteMfaComponent } from './components/add-site-form/mfa/add-site-mfa.component';
import { SharedModuleModule } from 'src/app/shared-module/shared-module.module';

@NgModule({
  declarations: [
    ModuleSiteComponent,
    DevicesComponent,
    DevicesConfigComponent,
    DeviceHistoryComponent,
    TimeDropdownComponent,
    FilterDropdownComponent,
    DevicesOverviewComponent,
    DevicesMediaComponent,
    DevicesVersionsComponent,
    DeviceJobComponent,
    EditDeviceInfoComponent,
    MoveDeviceOptionsComponent,
    RebootDeviceComponent,
    RecommissionDeviceComponent,
    DeleteDeviceComponent,
    FileDetailsComponent,
    CopyFileStepperComponent,
    SelectSourceDeviceComponent,
    SelectFileDirectoriesComponent,
    ChallengeResponseComponent,
    ChallengeResponseVerifyMfaComponent,
    ClickOutsideDirective,
    SwapDeviceComponent,
    DeploymentTypeModalComponent,
    PullFilesComponent,
    PullFromComponent,
    SelectFileOrDirectoriesComponent,
    AddNotificationComponent,
    HighlightSearchPipe,
    HighlightPipe,
    ExtractMessagePipe,
    DeviceAppConfigComponent,
    DeviceNetworkComponent,
    AddSiteFormComponent,
    StoreHoursComponent,
    ExternalReferenceComponent,
    AddSiteMfaComponent,
  ],
  imports: [
    CommonModule,
    ModuleSiteRoutingModule,
    MaterialModule,
    FormsModule,
    NgbCarouselModule,
    MatButtonModule,
    MatTreeModule,
    MatTableModule,
    MatInputModule,
    MatCheckboxModule,
    MatOptionModule,
    NgbProgressbarModule,
    MatSelectModule,
    MatAutocompleteModule,
    ReactiveFormsModule,
    MatCardModule,
    MaterialModule,
    SharedCommonModules,
    CommonModule,
    NgbPopoverModule,
    StoreModule.forFeature('devicesData', { devicesReducers }),
    StoreModule.forFeature('deviceAlarmData', deviceAlarmReducer),
    StoreModule.forFeature('deviceListData', deviceListReducer),
    StoreModule.forFeature('deviceSiteData', deviceSiteReducer),
    StoreModule.forFeature('deviceAlarmRulesData', alarmRulesReducer),
    StoreModule.forFeature('deviceSelfData', selfReducer),
    StoreModule.forFeature('deviceFileData', deviceFileReducer),
    StoreModule.forFeature('deviceVersionData', versionsReducers),
    StoreModule.forFeature('deviceHistoryData', deviceHistoryReducer),
    StoreModule.forFeature('deviceMediaData', deviceMediaReducer),
    StoreModule.forFeature('usersData', UsersDataReducer),
    StoreModule.forFeature('addSiteFormData', addSiteFormDataReducer),
    EffectsModule.forFeature([AddSiteFormDataEffects]),
    EffectsModule.forFeature([
      DevicesEffect,
      DVersionsEffect,
      DeviceAlarmEffects,
      DeviceListEffects,
      DeviceSiteEffects,
      AlarmRulesEffects,
      SelfDataEffects,
      DeviceFileEffects,
      DeviceHistoryEffects,
      DeviceMediaEffects,
      UsersDataEffects,
    ]),
    NgSelectModule,
    NgbTooltipModule,
    NgbDropdownModule,
    NgbDatepickerModule,
    NgbTimepickerModule,
    SharedModuleModule,
    NgbAccordionItem,
    NgbAccordionHeader,
    NgbAccordionCollapse,
    NgbAccordionBody,
    NgbAccordionButton,
    NgbAccordionDirective,
  ],
  providers: [DevicesService],
})
export class ModuleSiteModule {}
