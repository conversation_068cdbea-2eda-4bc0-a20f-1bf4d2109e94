import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject, Injectable } from '@angular/core';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { of } from 'rxjs';
import {
  loadDeviceVersionsData,
  loadDeviceVersionsDataFailure,
  loadDeviceVersionsDataSuccess,
} from '../actions/devices-version.actions';
import { DevicesService } from '../../services/devices.service';

@Injectable()
export class DVersionsEffect {
  service = inject(DevicesService);

  actions$ = inject(Actions);

  getData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceVersionsData),
      mergeMap(actions =>
        this.service.getDeviceVersions(actions.deviceId).pipe(
          map(data =>
            loadDeviceVersionsDataSuccess({ deviceVersionsData: data })
          ),
          catchError(() =>
            of(loadDeviceVersionsDataFailure({ error: 'error' }))
          )
        )
      )
    )
  );
}
