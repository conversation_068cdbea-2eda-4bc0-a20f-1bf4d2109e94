import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadSelfData,
  loadSelfDataFailure,
  loadSelfDataSuccess,
} from '../actions/self.actions';

@Injectable()
export class SelfDataEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getSelfData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadSelfData),
      switchMap(() =>
        this.devicesService.getSelfData().pipe(
          map(data => loadSelfDataSuccess({ selfData: data })),
          catchError(error => of(loadSelfDataFailure({ error })))
        )
      )
    )
  );
}
