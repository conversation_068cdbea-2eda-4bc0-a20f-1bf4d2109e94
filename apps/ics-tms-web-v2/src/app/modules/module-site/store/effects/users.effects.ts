import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadAllUsersData,
  loadAllUsersDataFailure,
  loadAllUsersDataSuccess,
} from '../actions/users.actions';

@Injectable()
export class UsersDataEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getUsersData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadAllUsersData),
      switchMap(actions =>
        this.devicesService
          .getALlUsersData(actions.pageIndex, actions.pageSize)
          .pipe(
            map(data => loadAllUsersDataSuccess({ users: data })),
            catchError(error => of(loadAllUsersDataFailure({ error })))
          )
      )
    )
  );
}
