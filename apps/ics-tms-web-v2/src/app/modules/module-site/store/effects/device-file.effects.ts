import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import {
  loadDeviceFiles,
  loadDeviceFilesFailure,
  loadDeviceFilesSuccess,
} from '../actions/device-file.actions';
import { DevicesService } from '../../services/devices.service';

@Injectable()
export class DeviceFileEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getDeviceFileData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceFiles),
      switchMap(actions =>
        this.devicesService.getDeviceOverviewFiles(actions.deviceId).pipe(
          map(data => loadDeviceFilesSuccess({ deviceFilesData: data })),
          catchError(error => of(loadDeviceFilesFailure({ error })))
        )
      )
    )
  );
}
