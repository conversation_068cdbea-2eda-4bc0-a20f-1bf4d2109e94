import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject, Injectable } from '@angular/core';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { of } from 'rxjs';
import * as devicesActions from '../actions/devices.actions';
import { DevicesService } from '../../services/devices.service';

@Injectable()
export class DevicesEffect {
  actions$ = inject(Actions);

  service = inject(DevicesService);

  getData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(devicesActions.getData),
      mergeMap(actions =>
        this.service.getdata(actions.deviceId).pipe(
          map(data => devicesActions.getDataSuccess({ data })),
          catchError(() => of({ type: '[Devices Component] GetData error' }))
        )
      )
    )
  );
}
