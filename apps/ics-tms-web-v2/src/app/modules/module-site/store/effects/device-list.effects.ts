import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadDeviceList,
  loadDeviceListFailure,
  loadDeviceListSuccess,
} from '../actions/device-list.actions';

@Injectable()
export class DeviceListEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getDeviceListData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceList),
      switchMap(() =>
        this.devicesService.getDeviceList().pipe(
          map(data => loadDeviceListSuccess({ deviceListData: data })),
          catchError(error => of(loadDeviceListFailure({ error })))
        )
      )
    )
  );
}
