import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadDeviceSiteData,
  loadDeviceSiteFailure,
  loadDeviceSiteSuccess,
} from '../actions/device-site.actions';

@Injectable()
export class DeviceSiteEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getDeviceSiteData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceSiteData),
      switchMap(action =>
        this.devicesService.getDeviceSite(action.siteId).pipe(
          map(data => loadDeviceSiteSuccess({ deviceSiteData: data })),
          catchError(error => of(loadDeviceSiteFailure({ error })))
        )
      )
    )
  );
}
