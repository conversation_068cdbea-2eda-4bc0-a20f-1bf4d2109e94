import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadDeviceHistory,
  loadDeviceHistoryFailure,
  loadDeviceHistorySuccess,
} from '../actions/device-history.actions';

@Injectable()
export class DeviceHistoryEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getDeviceHistoryData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceHistory),
      switchMap(actions =>
        this.devicesService
          .getHistoryData(actions.historyParamsData, actions.deviceId)
          .pipe(
            map(data =>
              loadDeviceHistorySuccess({
                historyData: data,
                replace: actions.replace,
              })
            ),
            catchError(error => of(loadDeviceHistoryFailure({ error })))
          )
      )
    )
  );
}
