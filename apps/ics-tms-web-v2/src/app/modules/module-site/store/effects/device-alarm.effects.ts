import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DeviceAlarmService } from '../../services/device-alarm.service';
import {
  loadDeviceAlarmData,
  loadDeviceAlarmDataFailure,
  loadDeviceAlarmDataSuccess,
} from '../actions/device-alarm.actions';

@Injectable()
export class DeviceAlarmEffects {
  actions$ = inject(Actions);

  deviceAlarmService = inject(DeviceAlarmService);

  getDeviceAlarmData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceAlarmData),
      switchMap(actions =>
        this.deviceAlarmService.getDeviceAlarmData(actions.deviceId).pipe(
          map(data => loadDeviceAlarmDataSuccess({ deviceAlarmData: data })),
          catchError(error => of(loadDeviceAlarmDataFailure({ error })))
        )
      )
    )
  );
}
