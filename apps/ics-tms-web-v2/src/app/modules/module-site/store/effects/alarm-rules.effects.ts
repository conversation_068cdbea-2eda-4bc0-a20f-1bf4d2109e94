import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadAlarmRules,
  loadAlarmRulesFailure,
  loadAlarmRulesSuccess,
} from '../actions/alarm-rules.actions';

@Injectable()
export class AlarmRulesEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getAlarmRulesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadAlarmRules),
      switchMap(actions =>
        this.devicesService.getAlarmRules(actions.siteId).pipe(
          map(data => loadAlarmRulesSuccess({ alarmRulesData: data })),
          catchError(error => of(loadAlarmRulesFailure({ error })))
        )
      )
    )
  );
}
