import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { DevicesService } from '../../services/devices.service';
import {
  loadDeviceMedia,
  loadDeviceMediaFailure,
  loadDeviceMediaSuccess,
} from '../actions/device-media.actions';

@Injectable()
export class DeviceMediaEffects {
  actions$ = inject(Actions);

  devicesService = inject(DevicesService);

  getDeviceMediaData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadDeviceMedia),
      switchMap(actions =>
        this.devicesService.getDeviceMediaData(actions.deviceId).pipe(
          map(data => loadDeviceMediaSuccess({ deviceMediaData: data })),
          catchError(error => of(loadDeviceMediaFailure({ error })))
        )
      )
    )
  );
}
