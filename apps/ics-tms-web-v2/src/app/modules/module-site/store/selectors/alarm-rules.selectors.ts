import { createFeatureSelector, createSelector } from '@ngrx/store';
import { AlarmRulesState } from '../reducers/alarm-rules.reducers';

export const selectAlarmRulesState = createFeatureSelector<AlarmRulesState>(
  'deviceAlarmRulesData'
);

export const selectAlarmRulesData = createSelector(
  selectAlarmRulesState,
  state => state.data
);

export const selectAlarmRulesLoading = createSelector(
  selectAlarmRulesState,
  state => state.isLoading
);

export const selectAlarmRulesError = createSelector(
  selectAlarmRulesState,
  state => state.error
);
