import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DeviceFileState } from '../reducers/device-file.reducers';

export const selectDeviceFileState =
  createFeatureSelector<DeviceFileState>('deviceFileData');

export const deviceFileDataSelector = createSelector(
  selectDeviceFileState,
  state => state.data
);

export const deviceFileLoadingSelector = createSelector(
  selectDeviceFileState,
  state => state.loading
);

export const deviceFileErrorSelector = createSelector(
  selectDeviceFileState,
  state => state.error
);
