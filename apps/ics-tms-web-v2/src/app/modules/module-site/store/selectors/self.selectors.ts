import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SelfState } from '../reducers/self.reducers';

export const deviceSelfStateSelector =
  createFeatureSelector<SelfState>('deviceSelfData');

export const selfDataSelector = createSelector(
  deviceSelfStateSelector,
  state => state.data
);

export const selfLoadingSelector = createSelector(
  deviceSelfStateSelector,
  state => state.isLoading
);

export const selfErrorSelector = createSelector(
  deviceSelfStateSelector,
  state => state.error
);
