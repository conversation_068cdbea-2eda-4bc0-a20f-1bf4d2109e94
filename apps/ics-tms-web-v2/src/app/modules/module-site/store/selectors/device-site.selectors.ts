import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DeviceSiteState } from '../reducers/device-site.reducers';

export const selectDeviceSiteState =
  createFeatureSelector<DeviceSiteState>('deviceSiteData');

export const deviceSiteSelector = createSelector(
  selectDeviceSiteState,
  state => state.data
);

export const deviceSiteLoading = createSelector(
  selectDeviceSiteState,
  state => state.isLoading
);

export const deviceSiteError = createSelector(
  selectDeviceSiteState,
  state => state.error
);
