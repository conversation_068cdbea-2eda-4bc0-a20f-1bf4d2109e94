import { createFeatureSelector, createSelector } from '@ngrx/store';
import { UserDataState } from '../reducers/users.reducers';

export const usersStateSelector =
  createFeatureSelector<UserDataState>('usersData');

export const UsersDataSelector = createSelector(
  usersStateSelector,
  state => state.data
);

export const UsersLoadingSelector = createSelector(
  usersStateSelector,
  state => state.loaded
);

export const UsersErrorSelector = createSelector(
  usersStateSelector,
  state => state.error
);
