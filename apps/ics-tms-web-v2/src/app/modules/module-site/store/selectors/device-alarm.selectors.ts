import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DeviceAlarmState } from '../../models/device-alarm.modal';

export const selectDeviceAlarmState =
  createFeatureSelector<DeviceAlarmState>('deviceAlarmData');

export const deviceAlarmSelector = createSelector(
  selectDeviceAlarmState,
  state => state.data
);

export const deviceAlarmLoadingSelector = createSelector(
  selectDeviceAlarmState,
  state => state.isLoading
);

export const deviceAlarmErrorSelector = createSelector(
  selectDeviceAlarmState,
  state => state.error
);
