import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DVersionsState } from '../reducers/devices-versions.reducer';

export const devicesVersionsSelector =
  createFeatureSelector<DVersionsState>('deviceVersionData');

export const deviceVersionDataSelector = createSelector(
  devicesVersionsSelector,
  state => state.data
);

export const deviceVersionsLoadingSelector = createSelector(
  devicesVersionsSelector,
  state => state.isLoading
);

export const deviceVersionsErrorSelector = createSelector(
  devicesVersionsSelector,
  state => state.error
);
