import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DeviceHistoryState } from '../reducers/device-history.reducers';

export const selectDeviceHistoryState =
  createFeatureSelector<DeviceHistoryState>('deviceHistoryData');

export const deviceHistoryDataSelector = createSelector(
  selectDeviceHistoryState,
  state => state.data
);

export const deviceHistoryLoadingSelector = createSelector(
  selectDeviceHistoryState,
  state => state.loading
);

export const deviceHistoryErrorSelector = createSelector(
  selectDeviceHistoryState,
  state => state.error
);

export const deviceHistoryMetaDataSelector = createSelector(
  selectDeviceHistoryState,
  state => state.historyMetaData
);
