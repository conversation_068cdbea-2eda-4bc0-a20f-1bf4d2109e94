import { createSelector } from '@ngrx/store';
import { DevicesInterface } from '../../models/devices.interface';

export const selectFeature = (state: DevicesInterface) => state;

export const devicesSelector = createSelector(selectFeature, state => state);

export const isLoadingSelector = createSelector(
  selectFeature,
  state => state.isLoading
);

export const devicesDataSelector = createSelector(
  selectFeature,
  state => state.data
);
