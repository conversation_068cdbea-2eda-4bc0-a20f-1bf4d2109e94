import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DeviceListState } from '../reducers/device-list.reducers';

export const selectDeviceListState =
  createFeatureSelector<DeviceListState>('deviceListData');

export const deviceListSelector = createSelector(
  selectDeviceListState,
  state => state.data
);

export const deviceListLoadingSelector = createSelector(
  selectDeviceListState,
  state => state.isLoading
);

export const deviceListErrorSelector = createSelector(
  selectDeviceListState,
  state => state.error
);
