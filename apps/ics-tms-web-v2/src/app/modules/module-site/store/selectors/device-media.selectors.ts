import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DeviceMediaState } from '../reducers/device-media.reducers';

export const selectDeviceMediaState =
  createFeatureSelector<DeviceMediaState>('deviceMediaData');

export const deviceMediaDataSelector = createSelector(
  selectDeviceMediaState,
  state => state.data
);

export const deviceMediaLoadingSelector = createSelector(
  selectDeviceMediaState,
  state => state.loading
);

export const deviceMediaErrorSelector = createSelector(
  selectDeviceMediaState,
  state => state.error
);
