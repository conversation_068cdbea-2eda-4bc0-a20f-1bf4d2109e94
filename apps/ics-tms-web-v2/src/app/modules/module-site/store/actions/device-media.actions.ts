import { createAction, props } from '@ngrx/store';

export const loadDeviceMedia = createAction(
  '[DeviceMedia] Load DeviceMedia',
  props<{ deviceId: string }>()
);

export const loadDeviceMediaSuccess = createAction(
  '[DeviceMedia] Load DeviceMedia Success',
  props<{ deviceMediaData: any }>()
);

export const loadDeviceMediaFailure = createAction(
  '[DeviceMedia] Load DeviceMedia Failure',
  props<{ error: any }>()
);
