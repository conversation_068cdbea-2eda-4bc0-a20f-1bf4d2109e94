import { createAction, props } from '@ngrx/store';
import { DeviceSite } from '../../models/device-site-data.modal';

export const loadDeviceSiteData = createAction(
  '[Device Site] Load Device Site',
  props<{ siteId: string }>()
);

export const loadDeviceSiteSuccess = createAction(
  '[Device Site] Load Device Site Success',
  props<{ deviceSiteData: DeviceSite }>()
);

export const loadDeviceSiteFailure = createAction(
  '[Device Site] Load Device Site Failure',
  props<{ error: string }>()
);
