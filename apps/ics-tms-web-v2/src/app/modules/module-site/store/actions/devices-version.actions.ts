import { createAction, props } from '@ngrx/store';
import { DeviceVersions } from '../../models/device-versions.modal';

export const loadDeviceVersionsData = createAction(
  '[Devices Version] GetData',
  props<{ deviceId: string }>()
);
export const loadDeviceVersionsDataSuccess = createAction(
  '[Devices Version] Get Data success',
  props<{ deviceVersionsData: DeviceVersions }>()
);

export const loadDeviceVersionsDataFailure = createAction(
  '[Devices Version] Get Data Failure',
  props<{ error: any }>()
);
