import { createAction, props } from '@ngrx/store';
import { DeviceFile } from '../../models/device-file.modal';

export const loadDeviceFiles = createAction(
  '[Device Files] Load Device Files',
  props<{ deviceId: string }>()
);

export const loadDeviceFilesSuccess = createAction(
  '[Device Files] Load Device Files Success',
  props<{ deviceFilesData: DeviceFile[] }>()
);

export const loadDeviceFilesFailure = createAction(
  '[Device Files] Load Device Files Failure',
  props<{ error: string }>()
);
