import { createAction, props } from '@ngrx/store';
import { UsersData } from '../../models/users-data.model';

export const loadAllUsersData = createAction(
  '[All Users Teams] Load All Users Teams',
  props<{ pageIndex: string | number; pageSize: string | number }>()
);

export const loadAllUsersDataSuccess = createAction(
  '[All Users Teams] Load All Users Teams Success',
  props<{ users: UsersData }>()
);

export const loadAllUsersDataFailure = createAction(
  '[All Users Teams] Load All Users Teams Failure',
  props<{ error: string }>()
);
