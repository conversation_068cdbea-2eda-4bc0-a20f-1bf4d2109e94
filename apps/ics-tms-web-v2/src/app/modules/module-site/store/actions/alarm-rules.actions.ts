import { createAction, props } from '@ngrx/store';
import { AlarmRules } from '../../models/alarm-rules.modal';

export const loadAlarmRules = createAction(
  '[Alarm Rules] Load Alarm Rules',
  props<{ siteId: string }>()
);

export const loadAlarmRulesSuccess = createAction(
  '[Alarm Rules] Load Alarm Rules Success',
  props<{ alarmRulesData: AlarmRules[] }>()
);

export const loadAlarmRulesFailure = createAction(
  '[Alarm Rules] Load Alarm Rules Failure',
  props<{ error: string }>()
);
