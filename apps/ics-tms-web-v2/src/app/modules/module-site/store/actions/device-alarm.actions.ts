import { createAction, props } from '@ngrx/store';
import { DeviceAlarm } from '../../models/device-alarm.modal';

export const loadDeviceAlarmData = createAction(
  '[Device Overview] GetAlarmData',
  props<{ deviceId: string }>()
);

export const loadDeviceAlarmDataSuccess = createAction(
  '[Device Overview] GetAlarmData Success',
  props<{ deviceAlarmData: DeviceAlarm[] }>()
);

export const loadDeviceAlarmDataFailure = createAction(
  '[Device Overview] GetData Failure',
  props<{ error: true }>()
);
