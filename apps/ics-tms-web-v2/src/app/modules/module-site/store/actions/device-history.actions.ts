import { createAction, props } from '@ngrx/store';
import { DeviceHistory } from '../../models/device-history.modal';
import { HistoryParams } from '../../models/history-params.modal';

export const loadDeviceHistory = createAction(
  '[DeviceHistory] Load DeviceHistory',
  props<{
    historyParamsData: HistoryParams;
    deviceId: string;
    replace?: boolean;
  }>()
);

export const loadDeviceHistorySuccess = createAction(
  '[DeviceHistory] Load DeviceHistory Success',
  props<{ historyData: DeviceHistory; replace?: boolean }>()
);

export const loadDeviceHistoryFailure = createAction(
  '[DeviceHistory] Load DeviceHistory Failure',
  props<{ error: any }>()
);

export const appendDeviceHistory = createAction(
  '[DeviceHistory] Append To DeviceHistory',
  props<{ historyData: DeviceHistory }>()
);
