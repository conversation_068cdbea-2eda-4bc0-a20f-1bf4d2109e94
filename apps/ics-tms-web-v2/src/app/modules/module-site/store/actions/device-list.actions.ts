import { createAction, props } from '@ngrx/store';
import { DeviceList } from '../../models/device-list.modal';

export const loadDeviceList = createAction('[Device List] Load Device List');

export const loadDeviceListSuccess = createAction(
  '[Device List] Load Device List Success',
  props<{ deviceListData: DeviceList }>()
);

export const loadDeviceListFailure = createAction(
  '[Device List] Load Device List Failure',
  props<{ error: string }>()
);
