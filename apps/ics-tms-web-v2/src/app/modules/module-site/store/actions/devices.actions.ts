import { createAction, props } from '@ngrx/store';
import { DeviceData } from '../../models/devices.interface';

export const getData = createAction(
  '[Devices Component] GetData',
  props<{ siteId: string; deviceId: string }>()
);
export const getDataSuccess = createAction(
  '[Devices Component] Get Data success',
  props<{ data: DeviceData }>()
);

// New action for updating device properties after config changes
export const updateDeviceProperties = createAction(
  '[Devices Config Component] Update Device Properties',
  props<{ data: DeviceData }>()
);
