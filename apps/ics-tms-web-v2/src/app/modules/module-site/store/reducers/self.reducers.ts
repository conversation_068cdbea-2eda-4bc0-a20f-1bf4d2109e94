import { createReducer, on } from '@ngrx/store';
import { SelfData } from '../../models/self.modal';
import {
  loadSelfData,
  loadSelfDataFailure,
  loadSelfDataSuccess,
} from '../actions/self.actions';

export interface SelfState {
  data: SelfData;
  isLoading: boolean | null;
  error: boolean | null;
}

export const initialState: SelfState = {
  data: {} as SelfData,
  isLoading: null,
  error: null,
};

export const selfReducer = createReducer(
  initialState,

  on(loadSelfData, state => ({
    ...state,
    isLoading: true,
    error: null,
  })),

  on(loadSelfDataSuccess, (state, { selfData }) => ({
    ...state,
    data: selfData,
    isLoading: false,
    error: null,
  })),

  on(loadSelfDataFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
