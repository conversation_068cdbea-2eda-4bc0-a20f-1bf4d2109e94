import { createReducer, on } from '@ngrx/store';
import * as versionActions from '../actions/devices-version.actions';
import {
  loadDeviceVersionsData,
  loadDeviceVersionsDataSuccess,
} from '../actions/devices-version.actions';
import { DeviceVersions } from '../../models/device-versions.modal';

export interface DVersionsState {
  data: DeviceVersions;
  error: string | null;
  isLoading: boolean;
}
export const initialState: DVersionsState = {
  data: {} as DeviceVersions,
  error: null,
  isLoading: false,
};

export const versionsReducers = createReducer(
  initialState,
  on(loadDeviceVersionsData, state => ({
    ...state,
    isLoading: true,
  })),

  on(loadDeviceVersionsDataSuccess, (state, { deviceVersionsData }) => ({
    ...state,
    data: deviceVersionsData,
    loading: false,
    error: null,
  })),

  on(versionActions.loadDeviceVersionsDataFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
