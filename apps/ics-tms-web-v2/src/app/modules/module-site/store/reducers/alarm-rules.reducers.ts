import { createReducer, on } from '@ngrx/store';
import { AlarmRules } from '../../models/alarm-rules.modal';
import {
  loadAlarmRules,
  loadAlarmRulesFailure,
  loadAlarmRulesSuccess,
} from '../actions/alarm-rules.actions';

export interface AlarmRulesState {
  data: AlarmRules[];
  error: string | null;
  isLoading: false;
}

export const initialState: AlarmRulesState = {
  data: [],
  error: null,
  isLoading: false,
};

export const alarmRulesReducer = createReducer(
  initialState,
  on(loadAlarmRules, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadAlarmRulesSuccess, (state, { alarmRulesData }) => ({
    ...state,
    data: alarmRulesData,
    loading: false,
    error: null,
  })),

  on(loadAlarmRulesFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
