import { createReducer, on } from '@ngrx/store';
import { DeviceHistory } from '../../models/device-history.modal';
import {
  appendDeviceHistory,
  loadDeviceHistory,
  loadDeviceHistoryFailure,
  loadDeviceHistorySuccess,
} from '../actions/device-history.actions';

export interface DeviceHistoryState {
  data: DeviceHistory;
  historyMetaData: {
    deviceId: string;
    ts_SK: string;
  };
  error: any;
  loading: boolean;
}

export const initialState: DeviceHistoryState = {
  data: {} as DeviceHistory,
  error: null,
  historyMetaData: {
    deviceId: '',
    ts_SK: '',
  },
  loading: false,
};

export const deviceHistoryReducer = createReducer(
  initialState,

  on(loadDeviceHistory, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadDeviceHistorySuccess, (state, { historyData, replace = true }) => {
    const seenKeys = new Set<string>();

    if (state.data.results?.length) {
      state.data.results.forEach(item => {
        const key = `${item.ts}_${item.msg.substring(0, 50)}`;
        seenKeys.add(key);
      });
    }

    const newUniqueResults = historyData.results.filter(item => {
      const key = `${item.ts}_${item.msg.substring(0, 50)}`;

      if (seenKeys.has(key)) {
        return false;
      }
      seenKeys.add(key);
      return true;
    });

    const updatedResults =
      replace || !state.data.results?.length
        ? historyData.results
        : [...state.data.results, ...newUniqueResults];

    const hasNewResults = newUniqueResults.length > 0;

    return {
      ...state,
      data: {
        ...historyData,
        results: updatedResults,
        noMoreData: !hasNewResults && !replace,
      },
      historyMetaData: historyData.resultsMetadata.pageKey,
      loading: false,
      error: null,
    };
  }),

  on(appendDeviceHistory, (state, { historyData }) => ({
    ...state,
    data: {
      results: [...state.data.results, ...historyData.results],
      resultsMetadata: historyData.resultsMetadata,
    },
    loading: false,
    error: null,
  })),

  on(loadDeviceHistoryFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  }))
);
