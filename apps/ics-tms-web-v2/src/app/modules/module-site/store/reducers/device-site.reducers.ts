import { createReducer, on } from '@ngrx/store';
import { DeviceSite } from '../../models/device-site-data.modal';
import {
  loadDeviceSiteData,
  loadDeviceSiteFailure,
  loadDeviceSiteSuccess,
} from '../actions/device-site.actions';

export interface DeviceSiteState {
  data: DeviceSite;
  error: string | null;
  isLoading: boolean;
}

export const initialState: DeviceSiteState = {
  data: {} as DeviceSite,
  error: '',
  isLoading: false,
};

export const deviceSiteReducer = createReducer(
  initialState,
  on(loadDeviceSiteData, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadDeviceSiteSuccess, (state, { deviceSiteData }) => ({
    ...state,
    data: deviceSiteData,
    loading: false,
    error: null,
  })),

  on(loadDeviceSiteFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
