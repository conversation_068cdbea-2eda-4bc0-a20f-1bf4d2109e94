import { createReducer, on } from '@ngrx/store';
import { DeviceList } from '../../models/device-list.modal';
import {
  loadDeviceList,
  loadDeviceListFailure,
  loadDeviceListSuccess,
} from '../actions/device-list.actions';

export interface DeviceListState {
  data: DeviceList;
  error: string | null;
  isLoading: boolean;
}

export const initialState: DeviceListState = {
  data: {
    results: [],
    resultsMetadata: {
      totalResults: 0,
      pageIndex: 0,
      pageSize: 0,
    },
  },
  error: '',
  isLoading: false,
};

export const deviceListReducer = createReducer(
  initialState,
  on(loadDeviceList, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadDeviceListSuccess, (state, { deviceListData }) => ({
    ...state,
    data: deviceListData,
    loading: false,
    error: null,
  })),

  on(loadDeviceListFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
