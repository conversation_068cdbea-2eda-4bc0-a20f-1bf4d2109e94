import { createReducer, on } from '@ngrx/store';
import { DeviceFile } from '../../models/device-file.modal';
import {
  loadDeviceFiles,
  loadDeviceFilesFailure,
  loadDeviceFilesSuccess,
} from '../actions/device-file.actions';

export interface DeviceFileState {
  data: DeviceFile[];
  loading: boolean;
  error: string | null;
}

const initialState: DeviceFileState = {
  data: [],
  loading: false,
  error: null,
};

export const deviceFileReducer = createReducer(
  initialState,
  on(loadDeviceFiles, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadDeviceFilesSuccess, (state, { deviceFilesData }) => ({
    ...state,
    data: deviceFilesData,
    loading: false,
    error: null,
  })),

  on(loadDeviceFilesFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
