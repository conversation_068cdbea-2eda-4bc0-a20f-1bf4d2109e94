import { createReducer, on } from '@ngrx/store';
import {
  loadDeviceMedia,
  loadDeviceMediaFailure,
  loadDeviceMediaSuccess,
} from '../actions/device-media.actions';

export interface DeviceMediaState {
  data: any;
  loading: boolean;
  error: any;
}

const initialState: DeviceMediaState = {
  data: {},
  loading: false,
  error: null,
};

export const deviceMediaReducer = createReducer(
  initialState,

  on(loadDeviceMedia, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadDeviceMediaSuccess, (state, { deviceMediaData }) => ({
    ...state,
    data: deviceMediaData,
    loading: false,
    error: null,
  })),

  on(loadDeviceMediaFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
