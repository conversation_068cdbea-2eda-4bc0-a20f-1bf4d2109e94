import { createReducer, on } from '@ngrx/store';
import {
  loadDeviceAlarmData,
  loadDeviceAlarmDataFailure,
  loadDeviceAlarmDataSuccess,
} from '../actions/device-alarm.actions';
import { DeviceAlarmState } from '../../models/device-alarm.modal';

const initialState: DeviceAlarmState = {
  data: [],
  error: null,
  isLoading: false,
};

export const deviceAlarmReducer = createReducer(
  initialState,
  on(loadDeviceAlarmData, state => ({
    ...state,
    loading: true,
    error: null,
  })),

  on(loadDeviceAlarmDataSuccess, (state, { deviceAlarmData }) => ({
    ...state,
    data: deviceAlarmData,
    loading: false,
    error: null,
  })),

  on(loadDeviceAlarmDataFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
