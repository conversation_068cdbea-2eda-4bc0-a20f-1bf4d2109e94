import { createReducer, on } from '@ngrx/store';
import { UsersData } from '../../models/users-data.model';
import {
  loadAllUsersData,
  loadAllUsersDataFailure,
  loadAllUsersDataSuccess,
} from '../actions/users.actions';

export interface UserDataState {
  data: UsersData;
  loaded: boolean;
  error: string;
}

export const initialState: UserDataState = {
  data: {} as UsersData,
  loaded: false,
  error: '',
};

export const UsersDataReducer = createReducer(
  initialState,
  on(loadAllUsersData, state => ({
    ...state,
    loaded: false,
  })),

  on(loadAllUsersDataSuccess, (state, { users }) => ({
    ...state,
    data: users,
    loaded: true,
    error: '',
  })),

  on(loadAllUsersDataFailure, (state, { error }) => ({
    ...state,
    loaded: false,
    error,
  }))
);
