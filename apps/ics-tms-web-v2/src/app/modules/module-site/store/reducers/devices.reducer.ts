import { createReducer, on } from '@ngrx/store';
import { DeviceData, DevicesInterface } from '../../models/devices.interface';
import * as devicesActions from '../actions/devices.actions';

export const initialState: DevicesInterface = {
  data: {} as DeviceData,
  error: null,
  isLoading: false,
};
export const devicesReducers = createReducer(
  initialState,
  on(devicesActions.getDataSuccess, (state, action) => ({
    ...state,
    isLoading: false,
    data: action.data,
  })),
  on(devicesActions.getData, state => ({ ...state, isLoading: true })),
  // Handle the new updateDeviceProperties action
  on(devicesActions.updateDeviceProperties, (state, action) => ({
    ...state,
    // Update only the device data, preserving loading state
    data: {
      ...state.data,
      // Ensure specific fields are updated correctly
      name: action.data.name || state.data.name,
      description: action.data.description || state.data.description,
      configData: action.data.configData || state.data.configData,
    },
  }))
);
