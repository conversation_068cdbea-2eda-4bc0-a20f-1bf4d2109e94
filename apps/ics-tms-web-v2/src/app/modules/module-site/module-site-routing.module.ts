import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModuleSiteComponent } from './module-site.component';
import { DevicesComponent } from './components/devices/devices.component';
import { DevicesOverviewComponent } from './components/devices-overview/devices-overview.component';
import { CopyFileStepperComponent } from './components/copy-file-stepper/copy-file-stepper.component';
import { ChallengeResponseComponent } from './components/device-option-modals/challenge-response/challenge-response.component';
import { PullFilesComponent } from './components/pull-files/pull-files.component';
import { DeviceAppConfigComponent } from './components/device-app-config/device-app-config.component';
import { DeviceNetworkComponent } from './components/device-network/device-network.component';
import { DevicesConfigComponent } from './components/devices-config/devices-config.component';
import { DevicesMediaComponent } from './components/devices-media/devices-media.component';
import { DevicesVersionsComponent } from './components/devices-versions/devices-versions.component';
import { DeviceHistoryComponent } from './components/device-history/device-history.component';
import { DeviceJobComponent } from './components/device-job/device-job.component';
import { AddSiteFormComponent } from './components/add-site-form/add-site-form.component';
import { authGuard } from 'src/app/auth.guard';

const routes: Routes = [
  { path: '', component: ModuleSiteComponent },
  {
    path: 'add',
    component: AddSiteFormComponent,
    canActivate: [authGuard],
    data: { navTitle: 'Asset Management' },
  },
  {
    path: ':site_id/:device_id',
    component: DevicesComponent,
    children: [
      {
        path: 'overview',
        component: DevicesOverviewComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Device Overview',
        },
      },
      {
        path: 'device-config',
        component: DevicesConfigComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Device Config',
        },
      },
      {
        path: 'media',
        component: DevicesMediaComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Device Media',
        },
      },
      {
        path: 'versions',
        component: DevicesVersionsComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Device Versions',
        },
      },
      {
        path: 'history',
        component: DeviceHistoryComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Device History',
        },
      },
      {
        path: 'jobs',
        component: DeviceJobComponent,
        data: { css: 'view-is-full view-site-detail', navTitle: 'Device Jobs' },
      },
      {
        path: 'app-config',
        component: DeviceAppConfigComponent,
        data: { css: 'view-is-full view-site-detail', navTitle: 'App Config' },
      },
      {
        path: 'network',
        component: DeviceNetworkComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Device Network',
        },
      },
      {
        path: 'copy-files',
        component: CopyFileStepperComponent,
        data: { css: 'view-is-full view-site-detail', navTitle: 'Copy Files' },
      },
      {
        path: 'challenge-response',
        component: ChallengeResponseComponent,
        data: {
          css: 'view-is-full view-site-detail',
          navTitle: 'Challenge Response',
        },
      },
      {
        path: 'pull-files',
        component: PullFilesComponent,
        data: { css: 'view-is-full view-site-detail', navTitle: 'Pull Files' },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModuleSiteRoutingModule {}
