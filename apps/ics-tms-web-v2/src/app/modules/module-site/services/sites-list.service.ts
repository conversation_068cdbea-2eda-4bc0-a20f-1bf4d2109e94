import { Injectable } from '@angular/core';
import { Task } from '../models/sites-list.model';
import { CHECKBOX_KEY, TREE_DATA, mp } from '../constants/filter-bar-data';

@Injectable({
  providedIn: 'root',
})
export class SitesListDataService {
  getTreeData(): Task[] {
    return TREE_DATA;
  }

  getMap(): Map<string, string> {
    return mp;
  }

  getStatusClass(statusStr: string): string {
    switch (statusStr) {
      case 'UNKNOWN':
        return 'unknown';
      case 'WARNING':
        return 'warning';
      case 'NORMAL':
        return 'normal';
      case 'CRITICAL':
        return 'critical';
      case 'INACTIVE':
        return 'inactive';
      default:
        return '';
    }
  }

  getCheckboxes() {
    const checkboxes: { [key: string]: boolean } = {};
    CHECKBOX_KEY.forEach(key => {
      checkboxes[key] = false;
    });
    return checkboxes;
  }
}
