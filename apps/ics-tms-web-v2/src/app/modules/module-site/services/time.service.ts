import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { HttpParams } from '@angular/common/http';
import { Subject } from 'rxjs';
import { DateFormatPipe } from '../../../utils/date-format.pipe';
import { loadDeviceHistory } from '../store/actions/device-history.actions';
import { deviceHistoryMetaDataSelector } from '../store/selectors/device-history.selectors';
import { APPEND } from '../constants/appConstants';
import { FilterService } from './filter.service';

@Injectable({
  providedIn: 'root',
})
export class TimeService {
  selectOption1: { time: string; category: string } = {
    time: '',
    category: '',
  };

  selectOption2: { time: string; category: string } = {
    time: '11:30  AM',
    category: 'TODAY',
  };

  device_id!: string;

  setDeviceId(deviceId: string) {
    this.device_id = deviceId;
  }

  getDeviceId() {
    return this.device_id;
  }

  historyMetaData!: { deviceId: string; ts_SK: string };

  timeFilterChanged$ = new Subject<void>();

  customTimeAdded$ = new Subject<{ time: string; category: string }>();

  constructor(
    private store: Store,
    private filterService: FilterService
  ) {
    this.store.select(deviceHistoryMetaDataSelector).subscribe(data => {
      if (data) {
        this.historyMetaData = data;
      }
    });

    // Don't initialize in constructor - wait for device ID to be set
  }

  buttonClickedTime = '1 hour';

  isButtonClicked = true;

  displayText = this.buttonClickedTime;

  historySearchInput = '';

  initializeTimeService() {
    // Check if we have a valid device ID
    if (!this.device_id) {
      console.warn(
        'Cannot initialize time service: Device ID is not available'
      );
      return false;
    }

    const hasValidSelections =
      this.selectOption1 &&
      this.selectOption1.time &&
      this.selectOption1.category &&
      this.selectOption2 &&
      this.selectOption2.time &&
      this.selectOption2.category;

    if (hasValidSelections && this.initialSetupDone) {
      return true;
    }

    this.setButtonClicked('1 hour', true);
    return true;
  }

  private initialSetupDone = false;

  setButtonClicked(value: string, forceUpdate: boolean = false) {
    if (!forceUpdate && this.initialSetupDone && value === '1 hour') {
      return;
    }

    this.isButtonClicked = true;
    this.buttonClickedTime = value;
    this.displayText = value;

    let hours = 1;
    if (value === '1 hour') hours = 1;
    else if (value === '3 hour') hours = 3;
    else if (value === '6 hour') hours = 6;
    else if (value === '12 hour') hours = 12;
    else if (value === '24 hour') hours = 24;

    const now = new Date();
    const fromDate = new Date(now);
    fromDate.setHours(fromDate.getHours() - hours);

    const minutes = fromDate.getMinutes();
    let roundedMinutes = Math.round(minutes / 5) * 5;

    if (roundedMinutes === 60) {
      fromDate.setHours(fromDate.getHours() + 1);
      roundedMinutes = 0;
      fromDate.setMinutes(0);
    } else {
      fromDate.setMinutes(roundedMinutes);
    }

    const fromHours = fromDate.getHours();
    const fromMinutes = fromDate.getMinutes();
    const fromAmPm = fromHours >= 12 ? 'PM' : 'AM';
    const formattedFromHours = fromHours % 12 === 0 ? 12 : fromHours % 12;

    const formattedFromTime = `${formattedFromHours}:${fromMinutes < 10 ? `0${fromMinutes}` : fromMinutes} ${fromAmPm}`;

    const fromDay =
      fromDate.getDate() === now.getDate() ? 'Today' : 'Yesterday';

    const customTimeOption = {
      time: formattedFromTime,
      category: fromDay,
    };

    // Update the selectOption1 with the calculated time
    this.selectOption1 = {
      time: formattedFromTime,
      category: fromDay,
    };

    this.selectOption2 = {
      time: 'Now',
      category: 'Today',
    };

    this.initialSetupDone = true;

    // Notify components about the custom time option
    this.customTimeAdded$.next(customTimeOption);

    // Trigger time filter change event
    this.timeFilterChanged$.next();

    // Call history URL with the new time parameters
    this.callHistoryURL();
  }

  getButtonClicked() {
    return this.buttonClickedTime;
  }

  getText() {
    if (
      this.selectOption1.time !== '' &&
      this.selectOption2.time !== '' &&
      !this.isButtonClicked
    ) {
      const opt1 = this.selectOption1.category;
      const opt2 = this.selectOption2.category;

      let opt1Date: string | null = '';
      let opt2Date: string | null = '';

      const currentDate1: Date = new Date();
      const currentDate2: Date = new Date();

      const customDateFormat = new DateFormatPipe();

      if (opt1 === 'Today') {
        opt1Date = customDateFormat.transform(currentDate1, 'MMM,dd');
      } else {
        opt1Date = customDateFormat.transform(
          currentDate1.setDate(currentDate1.getDate() - 1),
          'MMM,dd'
        );
      }

      if (opt2 === 'Today') {
        opt2Date = customDateFormat.transform(currentDate2, 'MMM,dd');
      } else {
        opt2Date = customDateFormat.transform(
          currentDate2.setDate(currentDate2.getDate() - 1),
          'MMM,dd'
        );
      }

      this.displayText =
        `${opt1Date} ${this.selectOption1.time} to ` +
        ` ${opt2Date} ${this.selectOption2.time}`;
    } else {
      this.displayText = this.buttonClickedTime;
    }

    return this.displayText;
  }

  setIsButtonClicked(boolean: boolean) {
    this.isButtonClicked = boolean;
    this.timeFilterChanged$.next();
    this.callHistoryURL();
  }

  callHistoryURL(type?: string) {
    // Ensure we have a valid device ID before making API calls
    if (!this.device_id) {
      console.error('Cannot load history: Device ID is not available');
      return;
    }

    // Validate that device_id is a valid number or numeric string
    if (Number.isNaN(Number(this.device_id))) {
      console.error(
        'Cannot load history: Device ID is not a valid number',
        this.device_id
      );
      return;
    }

    let startTimeInUTC = '';
    let endTimeInUTC = '';

    const { isButtonClicked } = this;

    if (isButtonClicked) {
      const displayText = this.getText();
      startTimeInUTC = this.getStartTimeOfButtonClicked(displayText);
      endTimeInUTC = this.getEndTimeOfButtonClickedInUTC(displayText);
    } else {
      const fromTime = this.selectOption1.time;
      const fromCategory = this.selectOption1.category;
      const toTime = this.selectOption2.time;
      const toCategory = this.selectOption2.category;

      startTimeInUTC = this.getStartTimeOfInput(fromTime, fromCategory);
      endTimeInUTC = this.getEndTimeOfInput(toTime, toCategory);
    }

    const baseParams: Record<string, any> = {
      advanced: true,
      end: endTimeInUTC,
      'levels[]': this.filterService.getFilterLevels(),
      pageMinSize: 25,
      start: startTimeInUTC,
    };

    if (this.historySearchInput) {
      baseParams['q'] = this.historySearchInput;
    }

    if (type === APPEND) {
      let params = new HttpParams()
        .set('pageKey[deviceId]', this.historyMetaData.deviceId)
        .set('pageKey[ts_SK]', this.historyMetaData.ts_SK);

      Object.entries(baseParams).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(val => {
            params = params.append(key, val);
          });
        } else {
          params = params.set(key, value);
        }
      });

      const paramsObject: Record<string, any> = {};
      params.keys().forEach(key => {
        if (key.endsWith('[]')) {
          paramsObject[key] = params.getAll(key);
        } else {
          paramsObject[key] = params.get(key);
        }
      });

      this.store.dispatch(
        loadDeviceHistory({
          historyParamsData: paramsObject,
          deviceId: this.device_id, // Use the validated device ID
          replace: false,
        })
      );
    } else {
      this.store.dispatch(
        loadDeviceHistory({
          historyParamsData: baseParams,
          deviceId: this.device_id, // Use the validated device ID
          replace: true,
        })
      );
    }
  }

  private getStartTimeOfButtonClicked(displayText: string) {
    const currentDate: Date = new Date();
    let hours = 1;
    if (displayText === '1 hour') {
      hours = 1;
    } else if (displayText === '3 hour') {
      hours = 3;
    } else if (displayText === '6 hour') {
      hours = 6;
    } else if (displayText === '12 hour') {
      hours = 12;
    } else {
      hours = 24;
    }
    currentDate.setHours(currentDate.getHours() - hours);
    const filteredStartDate = currentDate.toISOString();
    return filteredStartDate;
  }

  private getEndTimeOfButtonClickedInUTC(_displayText: string) {
    const currentDate: Date = new Date();
    const filteredEndDate = currentDate.toISOString();

    return filteredEndDate;
  }

  private getStartTimeOfInput(fromTime: string, fromCategory: string) {
    const time = fromTime;

    let filteredStartTime: string | number;

    // Handle 'Now' special case
    if (fromTime === 'Now') {
      const date = new Date();
      return date.toISOString();
    }

    const [hoursStr, minutesStr, period]: any = time.split(/[:\s]+/);
    let hours = parseInt(hoursStr, 10);
    const minutes = parseInt(minutesStr, 10);

    // Convert to 24-hour format
    if (period && period.trim() === 'PM' && hours < 12) {
      hours += 12;
    } else if (period && period.trim() === 'AM' && hours === 12) {
      hours = 0;
    }

    if (fromCategory === 'Today') {
      const date = new Date();
      date.setDate(date.getDate());

      date.setHours(hours, minutes, 0, 0);

      filteredStartTime = date.toISOString();
    } else {
      const date = new Date();
      date.setDate(date.getDate() - 1);

      date.setHours(hours, minutes, 0, 0);

      filteredStartTime = date.toISOString();
    }

    return filteredStartTime;
  }

  private getEndTimeOfInput(toTime: string, toCategory: string) {
    const time = toTime;

    let filteredEndTime: string | number;

    // Handle 'Now' special case
    if (toTime === 'Now') {
      const date = new Date();
      return date.toISOString();
    }

    const [hoursStr, minutesStr, period]: any = time.split(/[:\s]+/);
    let hours = parseInt(hoursStr, 10);
    const minutes = parseInt(minutesStr, 10);

    // Convert to 24-hour format
    if (period && period.trim() === 'PM' && hours < 12) {
      hours += 12;
    } else if (period && period.trim() === 'AM' && hours === 12) {
      hours = 0;
    }

    if (toCategory === 'Today') {
      const date = new Date();
      date.setDate(date.getDate());

      date.setHours(hours, minutes, 0, 0);

      filteredEndTime = date.toISOString();
    } else {
      const date = new Date();
      date.setDate(date.getDate() - 1);

      date.setHours(hours, minutes, 0, 0);

      filteredEndTime = date.toISOString();
    }

    return filteredEndTime;
  }
}
