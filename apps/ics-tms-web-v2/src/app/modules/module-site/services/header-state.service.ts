import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class HeaderStateService {
  private historyHeaderStickySubject = new BehaviorSubject<boolean>(false);

  public historyHeaderSticky$: Observable<boolean> =
    this.historyHeaderStickySubject.asObservable();

  setHistoryHeaderSticky(isSticky: boolean): void {
    this.historyHeaderStickySubject.next(isSticky);
  }
}
