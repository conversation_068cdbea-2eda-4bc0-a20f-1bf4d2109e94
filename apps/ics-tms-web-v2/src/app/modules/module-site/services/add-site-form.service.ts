import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpContext, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { LocationResponse, SitesPayload } from '../models/add-sites.model';
import { getApiConstants } from '../constants/api';
import { SitesResults } from 'src/app/models/sites.model';
import { skipAuth } from 'src/app/interceptors/auth.interceptor';

@Injectable({
  providedIn: 'root',
})
export class AddSiteFormService {
  http = inject(HttpClient);

  getLocation(params: {}): Observable<LocationResponse> {
    const context = new HttpContext().set(skipAuth(), true);
    const httpParams = new HttpParams({ fromObject: params });

    return this.http.get<LocationResponse>(
      getApiConstants().addSiteForm.getLocation,
      { context, params: httpParams }
    );
  }

  postSite(payload: SitesPayload): Observable<SitesResults> {
    return this.http.post<SitesResults>(
      getApiConstants().addSiteForm.getSites,
      payload
    );
  }
}
