import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DeviceAlarm } from '../models/device-alarm.modal';
import { getBaseUrl } from '../../../constants/api';

@Injectable({
  providedIn: 'root',
})
export class DeviceAlarmService {
  http = inject(HttpClient);

  getDeviceAlarmData(deviceId: string): Observable<DeviceAlarm[]> {
    const params = {
      autoPoll: true,
    };
    return this.http.get<DeviceAlarm[]>(
      `${getBaseUrl()}/devices/${deviceId}/alarms`,
      { params }
    );
    //   /devices/69340/alarms?autoPoll=true
  }
}
