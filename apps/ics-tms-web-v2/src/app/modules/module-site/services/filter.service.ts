import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FilterService {
  map = new Map([
    ['trace', false],
    ['debug', false],
    ['info', false],
    ['notice', true],
    ['warn', true],
    ['error', true],
    ['critical', true],
    ['fatal', true],
  ]);

  getFilterLevels() {
    return Array.from(this.map.entries())
      .filter(([_, value]) => value)
      .map(([key]) => key);
  }
}
