import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { DeviceList } from '../models/device-list.modal';
import { DeviceSite } from '../models/device-site-data.modal';
import { DeviceTypes } from '../models/device-types.modal';
import { AlarmRules } from '../models/alarm-rules.modal';
import { SelfData } from '../models/self.modal';
import { ConfigData } from '../models/app-config.model';
import { getBaseUrl } from '../../../constants/api';
import { DeviceFile } from '../models/device-file.modal';
import { DeviceVersions } from '../models/device-versions.modal';
import { DeviceHistory } from '../models/device-history.modal';
import { DevicesResponse } from '../../module-devices/model/devices.model';
import { getApiConstants } from '../constants/api';
import { ID } from '../constants/appConstants';
import { DeviceData } from '../models/devices.interface';
import { DevicesList } from '../models/devices-list.model';
import { UsersData } from '../models/users-data.model';
import { FileUploadRequest } from '../models/file-upload.model';

@Injectable()
export class DevicesService {
  // Add Device
  addDevice(deviceData: any, siteId: string): Observable<any> {
    // You may need to adjust the payload structure to match your backend
    const url = `${getBaseUrl()}/devices`;
    const data = {
      deviceType: deviceData.deviceType?.id || deviceData.deviceType,
      serialNumber: deviceData.serial,
      siteId,
      description: deviceData.description,
      name: deviceData.name,
    };
    return this.http.post(url, data);
  }

  // Get Device by Serial Number
  getDeviceBySerialNumber(
    serialNumber: string,
    serviceRecipientId?: string
  ): Observable<any> {
    let url = `${getBaseUrl()}/devices/serialnumber/${serialNumber}`;
    if (serviceRecipientId) {
      url += `?serviceRecipientId=${serviceRecipientId}`;
    }
    return this.http.get(url);
  }

  // Get Device State History
  getDeviceStateHistory(
    deviceId: string,
    q?: string,
    levels?: string[],
    start?: string,
    end?: string,
    key?: string,
    size?: number
  ): Observable<any> {
    const params: any = {
      q,
      start,
      end,
      pageMinSize: size,
      advanced: true,
    };
    if (levels) params['levels[]'] = levels;
    if (key) params['pageKey'] = key;
    return this.http.get(`${getBaseUrl()}/devices/${deviceId}/states/history`, {
      params,
    });
  }

  // Get Device Versions (already present as getDeviceVersions)

  // Bulk Move To Company
  bulkMoveToCompany(companyDeviceList: any): Observable<any> {
    return this.http.post(
      `${getBaseUrl()}/devices/movetocompany`,
      companyDeviceList
    );
  }

  // Bulk Move To Site
  bulkMoveToSite(
    siteId: string,
    deviceSerials: string[],
    scheduledDateTime?: string,
    deploymentType: string = 'maintenance-window'
  ): Observable<any> {
    const devices = deviceSerials.map(serialNumber => ({ serialNumber }));
    const data: any = { siteId, devices };
    if (deploymentType) data.deploymentType = deploymentType;
    if (deploymentType === 'schedule' && scheduledDateTime) {
      data.scheduledDateTime = scheduledDateTime;
    }
    return this.http.post(`${getBaseUrl()}/devices/movetosite`, data);
  }

  // Bulk Recommission
  doBulkRecommission(payload: {
    dryRun: boolean;
    devices: any[];
    sites: any[];
    siteGroups: any[];
    tags: any[];
  }): Observable<any> {
    return this.http.post(`${getBaseUrl()}/devices/recommission`, payload);
  }

  // Bulk Reboot
  doBulkReboot(payload: {
    dryRun: boolean;
    devices: any[];
    sites: any[];
    siteGroups: any[];
    tags: any[];
    schedule?: any;
  }): Observable<any> {
    return this.http.post(`${getBaseUrl()}/devices/reboot`, payload);
  }

  // Bulk Merchant Reset
  doBulkMerchantReset(userInfo: any): Observable<any> {
    return this.http.post(
      `${getBaseUrl()}/entities/internal/devices/mfa/jobs`,
      userInfo
    );
  }

  // Copy Config Files
  copyConfigFiles(
    sourceDeviceId: string,
    destinationDeviceId: string
  ): Observable<any> {
    return this.http.put(
      `${getBaseUrl()}/devices/${destinationDeviceId}/configurationfiles/copy`,
      { sourceTargetId: sourceDeviceId }
    );
  }

  // Delete Config File
  deleteConfigFile(file: any, deviceId: string): Observable<any> {
    return this.http.delete(
      `${getBaseUrl()}/devices/${deviceId}/configurationfiles/${file.configFilename}`
    );
  }

  // Delete Device
  deleteDevice(deviceId: string): Observable<any> {
    return this.http.delete(`${getBaseUrl()}/devices/${deviceId}`);
  }

  // Download Binary File
  downloadBinaryFile(deviceId: string, fileId: string): Observable<Blob> {
    return this.http
      .get<ArrayBuffer>(
        `${getBaseUrl()}/devices/${deviceId}/files/${fileId}/content`,
        {
          headers: { accept: 'application/octet-stream' },
          responseType: 'arraybuffer' as 'json',
        }
      )
      .pipe(map((data: ArrayBuffer) => new Blob([data])));
  }

  // Get Last Transacted On
  getLastTransactedOn(deviceIds: string[]): Observable<any> {
    const payload = {
      filters: {
        devices: { operator: 'in', value: deviceIds },
        tenant: false,
        returnThresholdWithZero: false,
      },
    };
    return this.http.post(
      `${getBaseUrl()}/device-monitoring/transaction-time-threshold`,
      payload
    );
  }

  // Get Config File Content
  getConfigFileContent(deviceId: string, fileName: string): Observable<any> {
    return this.http.get(
      `${getBaseUrl()}/devices/${deviceId}/configurationfiles/${fileName}/content`,
      { responseType: 'text' as 'json' }
    );
  }

  // Get Config Files List
  getConfigFilesList(deviceId: string): Observable<any> {
    return this.http.get(
      `${getBaseUrl()}/devices/${deviceId}/configurationfiles`
    );
  }

  // Get Device
  getDevice(deviceId: string, type?: string): Observable<any> {
    const params: any = type === 'autoPoll' ? { autoPoll: true } : {};
    return this.http.get(`${getBaseUrl()}/devices/${deviceId}`, { params });
  }

  // Get Device Alarms
  getDeviceAlarms(deviceId: string, type?: string): Observable<any> {
    const params: any = type === 'autoPoll' ? { autoPoll: true } : {};
    return this.http.get(`${getBaseUrl()}/devices/${deviceId}/alarms`, {
      params,
    });
  }

  // Get Device Types (already present as getDeviceTypes)

  // Get Devices By Company
  getDevicesByCompany(
    company: { id: string },
    pageSize?: number
  ): Observable<any> {
    const params: any = {
      companyId: company.id,
      pageSize: pageSize || 1000000,
    };
    return this.http.get(`${getBaseUrl()}/devices`, { params });
  }

  // Get File Content
  getFileContent(deviceId: string, fileId: string): Observable<any> {
    return this.http.get(
      `${getBaseUrl()}/devices/${deviceId}/files/${fileId}/content`,
      { responseType: 'text' as 'json' }
    );
  }

  // Get Uploadable Files
  getUploadableFiles(deviceId: string, autoPoll?: boolean): Observable<any> {
    const params: any = {};
    if (autoPoll) params.autoPoll = true;
    return this.http.get(`${getBaseUrl()}/devices/${deviceId}/files`, {
      params,
    });
  }

  // Copy Device Files
  copyDeviceFiles(deviceID: string, data: any): Observable<any> {
    return this.http.put(
      `${getBaseUrl()}/devices/${deviceID}/files/copy`,
      data
    );
  }

  // Reboot (delegated to jobsService in original, here just a stub)
  reboot(deviceId: string): Observable<any> {
    // Implement as needed, or inject jobsService if available
    return this.http.post(`${getBaseUrl()}/devices/${deviceId}/reboot`, {});
  }

  // Recommission
  recommission(deviceId: string): Observable<any> {
    return this.http.put(
      `${getBaseUrl()}/devices/${deviceId}/recommission`,
      {}
    );
  }

  // Save Config File
  saveConfigFile(file: any, deviceId: string): Observable<any> {
    return this.http.post(
      `${getBaseUrl()}/devices/${deviceId}/configurationfiles/${file.configFilename}/content`,
      file.content,
      { headers: { 'Content-Type': 'text/plain' } }
    );
  }

  // Save Config File CAgent
  saveConfigFileCAgent(file: any, deviceId: string): Observable<any> {
    return this.http.post(
      `${getBaseUrl()}/devices/${deviceId}/files/${file.id}/save`,
      file.content,
      { headers: { 'Content-Type': 'text/plain' } }
    );
  }

  // Swap Device
  swapDevice(oldDevice: any, newDevice: any): Observable<any> {
    return this.http.post(`${getBaseUrl()}/devices/${oldDevice.id}/swap`, {
      oldDeviceName: oldDevice.name,
      newDevice,
    });
  }

  // Update Device
  updateDevice(deviceData: any): Observable<any> {
    const url = `${getBaseUrl()}/devices/${deviceData.id}`;
    const data: any = {
      siteId: deviceData.siteId,
      description: deviceData.description,
      name: deviceData.name,
      configUpdate: deviceData.configUpdate,
      deploymentType: deviceData.deploymentType,
      serialNumber: deviceData.serialNumber,
      terminalLocation: deviceData.terminalLocation,
    };
    if (deviceData.deviceType && deviceData.deviceType.id) {
      data.deviceType = deviceData.deviceType.id;
    }
    if (deviceData.deploymentType === 'schedule') {
      data.scheduledDateTime = deviceData.scheduledDateTime;
    }
    return this.http.put(url, data);
  }

  // Challenge Response
  challengeResponse(data: any): Observable<any> {
    return this.http.post(
      `${getBaseUrl()}/devices/${data.device.id}/challenge-response`,
      {
        challenge: data.challenge,
        requestedBy: data.requestedBy,
        mfaCode: data.mfa,
        serialNumber: data.device.serialNumber,
        operation: data.operation,
      }
    );
  }

  // Get Rollout Details By Device
  getRolloutDetailsByDevice(
    deviceID: string,
    rolloutID: string
  ): Observable<any> {
    return this.http.get(
      `${getBaseUrl()}/devices/${deviceID}/rollouts/${rolloutID}`
    );
  }

  // Update Alarm Rules
  updateAlarmRules(data: any): Observable<any> {
    return this.http.put(`${getBaseUrl()}/devices/alarmrules`, data);
  }

  // Get Alarm Rules For Site
  getAlarmRulesForSite(siteID: string): Observable<any> {
    return this.http.get(`${getBaseUrl()}/sites/${siteID}/devices/alarmrules`);
  }

  // Get Bulk Operations
  getBulkOperations(
    pageIndex: number,
    pageSize: number,
    autoPoll?: boolean,
    excludeRestrictedDeviceTypes?: boolean
  ): Observable<any> {
    const params: any = { pageIndex, pageSize, autoPoll };
    if (excludeRestrictedDeviceTypes !== undefined)
      params.excludeRestrictedDeviceTypes = excludeRestrictedDeviceTypes;
    return this.http.get(`${getBaseUrl()}/devices/bulkoperations/summary`, {
      params,
    });
  }

  // Get Bulk Operation History
  getBulkOperationHistory(id: string): Observable<any> {
    return this.http.get(
      `${getBaseUrl()}/devices/bulkoperations/${id}/history`
    );
  }

  // Cancel Bulk Operation
  cancelBulkOperation(id: string): Observable<any> {
    return this.http.post(`${getBaseUrl()}/jobs/${id}/bulkcancel`, {});
  }

  // Delete Bulk Operation
  deleteBulkOperation(id: string): Observable<any> {
    return this.http.post(
      `${getBaseUrl()}/devices/bulkoperations/${id}/delete`,
      {}
    );
  }

  // Get Device Available Memory
  getDeviceAvailableMemory(deviceType?: string): Observable<any> {
    const params: any = deviceType ? { deviceType } : {};
    return this.http.get(`${getBaseUrl()}/device-available-memory`, { params });
  }

  // Is Sequoia Device
  isSequoiaDevice(deviceType: string): boolean {
    return (
      deviceType.includes('G7-100') ||
      deviceType === 'G6-300' ||
      deviceType === 'G6-400' ||
      deviceType === 'G6-500'
    );
  }

  // Check RKI
  checkRKI(
    devices: string[] | string,
    site?: string,
    isSerialNum?: boolean,
    companyRef?: string,
    keyGroupRef?: string
  ): Observable<any> {
    let queryString = '';
    if (site) {
      queryString += `site=${site}`;
    } else if (keyGroupRef) {
      queryString += `keyGroupRef=${keyGroupRef}`;
    }
    if (Array.isArray(devices)) {
      devices.forEach(elem => {
        queryString += isSerialNum
          ? `&deviceSerials=${elem}`
          : `&devices=${elem}`;
      });
    } else {
      queryString += isSerialNum
        ? `&deviceSerials=${devices}`
        : `&devices=${devices}`;
    }
    if (companyRef) {
      queryString += `&companyRef=${companyRef}`;
    }
    return this.http.get(`${getBaseUrl()}/devices/check-rki?${queryString}`);
  }

  // Sync Keygroup To Site
  syncKeygroupToSite(siteId: string, devices: string[]): Observable<any> {
    return this.http.post(`${getBaseUrl()}/devices/sync-keygroup-to-site`, {
      siteId,
      devices,
    });
  }

  // Get Price Change Filter Values (stub, implement as needed)
  getPriceChangeFilterValues(): Observable<any> {
    // Implement as needed, stub for now
    return new Observable(_observer => {
      _observer.next({
        results: [],
        resultsMetadata: { totalResults: 0, pageIndex: 0, pageSize: 0 },
      });
      _observer.complete();
    });
  }

  // Get Maintenance Window
  getMaintenanceWindow(): Observable<any> {
    return this.http.get(`${getBaseUrl()}/renditions/maintenance-window`);
  }

  // Generate No Status Tooltip (utility, not HTTP)
  generateNoStatusTooltip(): string {
    // Implement as needed, stub for now
    return '';
  }

  private http = inject(HttpClient);

  getdata(deviceId: string): Observable<DeviceData> {
    return this.http.get<DeviceData>(`${getBaseUrl()}/devices/${deviceId}`);
  }

  private fileUploadRequestUrl = `${getBaseUrl()}/fileuploadrequests`;

  requestFileUploadNetwork(data: FileUploadRequest): Observable<any> {
    const fileUploadModel = {
      name: data.name,
      files: data.files,
      devices: data.devices,
      sites: data.sites,
      siteTags: data.siteTags,
      users: (data.users || [])
        .map(u => (u && u.id ? { id: u.id } : null))
        .filter(Boolean),
      teams: (data.teams || [])
        .map(u => (u && u.id ? { id: u.id } : null))
        .filter(Boolean),
      additionalProperties: data.additionalProperties,
    };

    return this.http.post(this.fileUploadRequestUrl, fileUploadModel).pipe(
      map(result => result),
      catchError(error => {
        console.error('Failed to upload file:', error);
        throw error;
      })
    );
  }

  getPackageUrl(id: string): Observable<{ name: string; packageUrl: string }> {
    return this.http.get<{ name: string; packageUrl: string }>(
      `${this.fileUploadRequestUrl}/${id}`
    );
  }

  downloadPackage(id: string): Observable<Blob> {
    return this.http
      .get<ArrayBuffer>(`${this.fileUploadRequestUrl}/${id}/package`, {
        headers: {
          accept: 'application/octet-stream',
        },
        responseType: 'arraybuffer' as 'json',
      })
      .pipe(
        map(
          (data: ArrayBuffer) => new Blob([data], { type: 'application/zip' })
        )
      );
  }

  requestFileUpload(data: FileUploadRequest): Observable<any> {
    const fileUploadModel: any = {
      name: data.name,
      files: data.files, // Use files directly
      devices: data.devices,
      sites: data.sites,
      siteTags: data.siteTags,
      users: (data.users || [])
        .map(u => (u && u.id ? { id: u.id } : null))
        .filter(Boolean),
      teams: (data.teams || [])
        .map(u => (u && u.id ? { id: u.id } : null))
        .filter(Boolean),
      additionalProperties: data.additionalProperties, // Add this line
      // Remove startDate and endDate unless required
    };

    return this.http.post(this.fileUploadRequestUrl, fileUploadModel);
  }

  getDeviceOverviewFiles(deviceId: string): Observable<DeviceFile[]> {
    // return this.http.get<DeviceFile[]>("http://localhost:3001/FILE_DATA");
    return this.http.get<DeviceFile[]>(
      getApiConstants().device.overview.getFiles.replace(ID, deviceId)
    );
  }

  getDeviceVersions(deviceId: string): Observable<DeviceVersions> {
    return this.http.get<DeviceVersions>(
      getApiConstants().device.versions.getVersions.replace(ID, deviceId)
    );
  }

  getDeviceList(): Observable<DeviceList> {
    const params = {
      autoPoll: false,
      isCSV: false,
      order: 'status-desc',
      pageIndex: 0,
      pageSize: 20,
    };
    return this.http.get<DeviceList>(getApiConstants().device.getList, {
      params,
    });
  }

  getDeviceSite(siteId: string): Observable<DeviceSite> {
    return this.http.get<DeviceSite>(
      getApiConstants().device.getSite.replace(ID, siteId)
    );
  }

  getDeviceTypes(): Observable<DeviceTypes[]> {
    return this.http.get<DeviceTypes[]>(
      getApiConstants().device.getDeviceTypes
    );
  }

  getAlarmRules(siteId: string): Observable<AlarmRules[]> {
    return this.http.get<AlarmRules[]>(
      getApiConstants().device.getAlarmRules.replace(ID, siteId)
    );
  }

  getSelfData(): Observable<SelfData> {
    return this.http.get<SelfData>(getApiConstants().device.getSelf);
  }

  getHistoryData(params: any, deviceId: string): Observable<DeviceHistory> {
    return this.http.get<DeviceHistory>(
      getApiConstants().device.history.getHistory.replace(ID, deviceId),
      { params }
    );
  }

  getDeviceMediaData(deviceId: string): Observable<any> {
    return this.http.get<any>(
      getApiConstants().device.media.getMedia.replace(ID, deviceId)
    );
  }

  getSourceDevice(searchQuery: string | number): Observable<DevicesResponse> {
    const params = {
      pageIndex: 0,
      pageSize: 10000,
      searchFilter: '',
      showHiddenDevices: true,
    };

    if (searchQuery) {
      params.searchFilter = searchQuery.toString();
    }

    return this.http.get<DevicesResponse>(
      getApiConstants().device.copyFiles.getSourceDevice,
      { params }
    );
  }

  getDevicesList(
    deviceType: string,
    searchQuery: string
  ): Observable<DevicesList> {
    const params = {
      deviceType,
      pageIndex: 0,
      pageSize: 10,
      searchFilter: searchQuery,
      showHiddenDevices: true,
    };

    return this.http.get<DevicesList>(
      getApiConstants().device.swap.getDevicesList,
      { params }
    );
  }

  getALlUsersData(
    pageIndex: string | number,
    pageSize: string | number
  ): Observable<UsersData> {
    const params = {
      allRoles: false,
      pageIndex,
      pageSize,
      pending: true,
    };
    return this.http.get<UsersData>(
      getApiConstants().device.pullFiles.getAllUsers,
      { params }
    );
  }

  //
  updateConfig(
    params: { config: { [key: string]: any }; [key: string]: any },
    deviceId: string
  ) {
    return this.http.put(
      getApiConstants().device.updateConfig.replace(ID, deviceId),
      params
    );
  }

  getAppConfigDetails(deviceId: string): Observable<ConfigData> {
    const url = `${getBaseUrl()}/renditions/devices/config-files?filters[deviceId][$eq]=${deviceId}&onlyMeta=false`;
    return this.http.get<ConfigData>(url);
  }

  GetDownloadConfigFile(
    configFileContentId: string,
    configFileInstanceId: string
  ): Observable<{ url: string }> {
    return this.http.get<{ url: string }>(
      `${getBaseUrl()}/config-files/${configFileContentId}/instances/${configFileInstanceId}/download`
    );
  }

  getNameByTemplate(payload: {
    timezone: string;
    deviceId: number;
    devices: Array<{ id: number }>;
    sites: any[];
    siteTags: any[];
  }): Observable<{ name: string }> {
    return this.http.post<{ name: string }>(
      `${getBaseUrl()}/fileuploadrequests/zipfilename`,
      payload
    );
  }

  uploadConfigFile(
    deviceId: string,
    file: File,
    metadata: any
  ): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('metadata', JSON.stringify(metadata));

    return this.http.post(
      `${getBaseUrl()}/devices/${deviceId}/files/upload`,
      formData
    );
  }
}
