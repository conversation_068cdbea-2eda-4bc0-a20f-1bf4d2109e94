import { Task } from '../models/sites-list.model';

export const TREE_DATA: Task[] = [
  {
    name: 'Out of Service',
    completed: false,
    color: 'primary',
    subtasks: [
      {
        name: 'Security',
        completed: false,
        color: 'primary',
        subtasks: [
          { name: 'Secure Channel Lost', completed: false, color: 'primary' },
          {
            name: 'Component Certificate Mismatch',
            completed: false,
            color: 'primary',
          },
        ],
      },
      {
        name: 'OTP Tampered',
        completed: false,
        color: 'accent',
        subtasks: [
          { name: 'SDC Tampered (removal)', completed: false, color: 'accent' },
          {
            name: 'SDC Tampered (destructive)',
            completed: false,
            color: 'accent',
          },
          { name: 'UPC Tampered (removal)', completed: false, color: 'accent' },
          {
            name: 'UPC Tampered (destructive)',
            completed: false,
            color: 'accent',
          },
        ],
      },
      {
        name: 'OTP in Safe Mode',
        completed: false,
        color: 'warn',
        subtasks: [
          { name: 'SDC Safe Mode', completed: false, color: 'warn' },
          { name: 'UPC Safe Mode', completed: false, color: 'warn' },
          { name: 'APC Safe Mode', completed: false, color: 'warn' },
        ],
      },
      {
        name: 'Component Disconnected',
        completed: false,
        color: 'primary',
        subtasks: [
          { name: 'SDC Disconnected', completed: false, color: 'primary' },
          { name: 'UPC Disconnected', completed: false, color: 'primary' },
        ],
      },
      {
        name: 'Site Integration',
        completed: false,
        color: 'accent',
        subtasks: [
          { name: 'POS Disconnected', completed: false, color: 'accent' },
          { name: 'POS Error', completed: false, color: 'accent' },
        ],
      },
    ],
  },
];
export const keys = [
  'New Sites',
  'Normal',
  'Warning',
  'Critical',
  'Unknown',
  'Inactive',
  'Operational',
  'Operational Devices',
  'Unknown Devices',
  'Inactive Devices',
];
export const CHECKBOX_KEY = [
  'NEW_SITES',
  'NORMAL',
  'WARNING',
  'CRITICAL',
  'UNKNOWN',
  'INACTIVE',
  'OPERATIONAL',
  'OPERATIONAL DEVICES',
  'UNKNOWN DEVICES',
  'INACTIVE DEVICES',
];
export const mp = new Map<string, string>();

mp.set('Out of Service', 'cancel');
mp.set('Security', 'no_encryption');
mp.set('OTP Tampered', 'pan_tool');
mp.set('OTP in Safe Mode', 'security');
mp.set('Component Disconnected', 'power');
mp.set('Site Integration', 'swap_calls');

export const SiteEvents = ['New Sites'];
export const SiteStatus = [
  'Normal',
  'Warning',
  'Critical',
  'Unknown',
  'Inactive',
];
export const DeviceSatus = [
  'Operational',
  'Out of Service',
  'Unknown',
  'Inactive',
];

export interface FiltersArray {
  siteEvents?: string[];
  siteStatus?: string[];
  deviceStatus?: string[];
  outOfService?: string[];
}

export const PARENT_HAVE_CHILD: { [key: string]: number } = {
  Security: 2,
  'OTP Tampered': 4,
  'OTP in Safe Mode': 3,
  'Component Disconnected': 2,
  'Site Integration': 2,
};

export const PARENT_HAVE_CHILD_INITIAL: { [key: string]: number } = {
  Security: 0,
  'OTP Tampered': 0,
  'OTP in Safe Mode': 0,
  'Component Disconnected': 0,
  'Site Integration': 0,
};
