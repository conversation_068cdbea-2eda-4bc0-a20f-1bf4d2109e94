import { ID } from './appConstants';
import { getBaseUrl } from 'src/app/constants/api';

export const getApiConstants = () => ({
  sites: {
    getSitesList: `${getBaseUrl()}/sites/list`,
  },
  addSiteForm: {
    getSiteName: `${getBaseUrl()}/acceptable/sitename`,
    getExternalType: `${getBaseUrl()}/sites/external-types`,
    getKeyGroups: `${getBaseUrl()}/rki/keygroups?serviceRecipientId={COMPANY_ID}`,
    getLocation: `https://maps.googleapis.com/maps/api/timezone/json`,
    getSites: `${getBaseUrl()}/sites`,
  },
  device: {
    getList: `${getBaseUrl()}/sites/list`,
    getSite: `${getBaseUrl()}/sites/${ID}`,
    getDeviceTypes: `${getBaseUrl()}/devicetypes`,
    getAlarmRules: `${getBaseUrl()}/sites/${ID}/devices/alarmrules`,
    getSelf: `${getBaseUrl()}/self`,
    updateConfig: `${getBaseUrl()}/devices/${ID}`,
    overview: {
      getFiles: `${getBaseUrl()}/devices/${ID}/files`,
    },
    media: {
      getMedia: `${getBaseUrl()}/devices/${ID}/media/stats`,
    },
    versions: {
      getVersions: `${getBaseUrl()}/devices/${ID}/versions`,
    },
    history: {
      getHistory: `${getBaseUrl()}/devices/${ID}/states/history`,
    },
    jobs: {
      getJobsData: `${getBaseUrl()}/jobs`,
      getJobsDetails: `${getBaseUrl()}/jobs/${ID}/history`,
    },
    copyFiles: {
      getSourceDevice: `${getBaseUrl()}/devices`,
      getFiles: `${getBaseUrl()}/devices/${ID}/files`,
    },
    challengeResponse: {
      postRequest: `${getBaseUrl()}/devices/${ID}/challenge-response`,
    },
    swap: {
      getDevicesList: `${getBaseUrl()}/devices`,
      postSwapDevices: `${getBaseUrl()}/devices/${ID}/swap`,
    },
    move: {
      getRkiCheck: `${getBaseUrl()}/devices//check-rki`,
      putMoveDevice: `${getBaseUrl()}/devices/${ID}`,
    },
    recommission: {
      putRecommission: `${getBaseUrl()}/devices/${ID}/recommission`,
    },
    reboot: {
      postReboot: `${getBaseUrl()}/jobs`,
    },
    deleteDevice: {
      deleteDevice: `${getBaseUrl()}/devices/${ID}`,
    },
    pullFiles: {
      getAllUsers: `${getBaseUrl()}/users`,
      postFileUploadRequests: `${getBaseUrl()}/fileuploadrequests`,
    },
  },
});

export const SITE = 'SITE';
