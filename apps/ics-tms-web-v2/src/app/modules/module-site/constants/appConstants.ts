export const ERROR_INVALID_DUPLICATE_SITE_NAME =
  'This site name is invalid or already registered';

export const EMAIL_REGEX =
  // eslint-disable-next-line no-useless-escape
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
export const DEVICE_TYPES = [
  'G6-200',
  'G7-100',
  'G6-300',
  'G6-400',
  'G7-100-8',
  'G7-100-15',
];

export interface SelectResponseType {
  code: string;
  text: string;
}

export const ID = 'ID';
export const JOBS = 'JOBS';

export const ALLOWED_DEVICE_TYPES = [
  'G6-100',
  'G6-200',
  'G6-300',
  'G6-400',
  'G7-100',
  'C1-100',
  'FUELPOS',
  'E1-100-EPS',
  'vOPT',
  'IPT',
  'EDGE',
  'OMNIA',
];

export const JOB_DATE_RANGES = [
  'Today',
  'Yesterday',
  'Last 7 Days',
  'Last 14 Days',
  'Last 30 Days',
  'Last Month',
  'This Month',
  'Next Month',
  'Next Week',
  'Tomorrow',
  'This Year',
  'Last 365 Days',
  'Custom',
];

export const TYPE_REBOOT = 'sys.reboot';
export const TYPE_DOWNLOAD = 'sys.download';
export const TYPE_INSTALL = 'sys.install';
export const TYPE_UPLOAD = 'sys.upload';
export const TYPE_CONFIGURE = 'sys.configure';
export const TYPE_CONFIG_FILE_UPDATE = 'sys.configfile-update';

export const DATE_FORMAT: Intl.DateTimeFormatOptions = {
  year: 'numeric',
  month: 'short',
  day: '2-digit',
  hour: 'numeric',
  minute: '2-digit',
  timeZone: 'Asia/Kolkata',
  hour12: true,
};

export const FULL_PAGE_ITEMS = [
  'copy-files',
  'pull-files',
  'challenge-response',
];

export const STORE_HOURS_OPTIONS = [
  'Custom',
  '24/7',
  '6AM - 10PM',
  '8AM - 9PM',
  '9AM - 9PM',
  '5AM - 11PM',
];

export const WEEK = ['S', 'M', 'T', 'W', 'Th', 'F', 'S'];

interface StoreHoursList {
  [key: string]: { openAt: string; closeAt: string }[];
}

export const STORE_HOURS_LIST: StoreHoursList = {
  Custom: [
    { openAt: '08:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '08:00 AM', closeAt: '11:59 PM' },
  ],
  '24/7': [
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
    { openAt: '00:00 AM', closeAt: '11:59 PM' },
  ],
  '6AM - 10PM': [
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
    { openAt: '06:00 AM', closeAt: '10:00 PM' },
  ],
  '8AM - 9PM': [
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
    { openAt: '08:00 AM', closeAt: '09:00 PM' },
  ],
  '9AM - 9PM': [
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
    { openAt: '09:00 AM', closeAt: '09:00 PM' },
  ],
  '5AM - 11PM': [
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
    { openAt: '05:00 AM', closeAt: '11:00 PM' },
  ],
};

export const INDEX_ARRAY = [0, 1, 2, 3, 4, 5];
export const NONE = 'none';
export const CUSTOM = 'Custom';
export const OPEN = 'open';
export const CLOSE = 'close';
export const AM = 'AM';
export const PM = 'PM';
export const MAP_KEY = 'AIzaSyCcxr5U5st7OjX3ex4McQx0_PBQQR9JlBU';
export const DOWNLOAD_TOOLTIP = 'Download CSV file based from sites list';
export const SOME_SITES_VISIBLE = 'Some sites are hidden';
export const ALL_SITES_VISIBLE = 'All sites are visible';
export const VISIBLE_TOOLTIP =
  'This will hide the site and its devices from the dashboard, sites list and devices list.\nYou will see them in your search result.';
export const HOURS_TOOLTIP =
  'Calculated alarms/alerts will not be triggered during non-operating hours.';

export const DEVICE_ACTIVITY: { [key: string]: string } = {
  device_offline: 'is offline.',
  paper_low: 'has low printer paper.',
  paper_out: 'is out of paper.',
  printer_jam: 'has a printer jam.',
  sdc_destructive_tamper: 'SDC experienced destructive tamper.',
  upc_destructive_tamper: 'UPC experienced destructive tamper.',
  sdc_removal_tamper: 'SDC experienced removal tamper.',
  upc_removal_tamper: 'UPC experienced removal tamper.',
  destructive_tamper: 'experienced destructive tamper.',
  low_memory: 'has low memory.',
  excessive_bad_card_reads: 'experienced excessive bad card reads.',
  excessive_reboots: 'experienced excessive reboots.',
  site_devices_offline: 'Excessive devices are offline.',
  version_downgrade: 'software has been downgraded.',
  device_unreachable: 'is unreachable.',
  device_out_of_service: 'is out-of-service.',
  site_critical: 'Site is in critical state.',
};

export const MEDIA_TABLE_COLUMNS = [
  '#',
  'VIDEO',
  '# TIMES PLAYED',
  'TOTAL DURATION',
  'LAST PLAYED',
];

export const TIME_PLAYED = '# TIMES PLAYED';

export const TOTAL_DURATION = 'TOTAL DURATION';

export const LAST_PLAYED = 'LAST PLAYED';

export const DEVICE_OFFLINE = 'device_offline';

export const UNSUPPORTED_MEDIA_DEVICES = [
  'G6-100',
  'C1-100',
  'FUELPOS',
  'E1-100-EPS',
  'EDGE',
];

export const UNSUPPORTED_VERSION_DEVICES = [
  'G6-100',
  'C1-100',
  'FUELPOS',
  'E1-100-EPS',
];

export const UNSUPPORTED_CONFIG_DEVICES = ['C1-100', 'FUELPOS', 'E1-100-EPS'];

export const CURRENT_DEVICE = 'currentDevice';
export const CURRENT_SITE = 'currentSite';
export const SITE_TAGS = 'siteTags';

export const HISTORY_ICONS: { [key: string]: string } = {
  trace: 'fa-search',
  debug: 'fa-bug',
  info: 'fa-info-circle',
  notice: 'fa-bullhorn',
  warn: 'fa-exclamation-circle',
  error: 'fa-exclamation-circle',
  critical: 'fa-fire',
  fatal: 'fa-fire',
};
export const NEW = 'New';
export const APPEND = 'append';
export const CONFIG_UPDATE = 'update';
export const CONFIG_UPDATING = 'updating';
export const CONFIG_UPDATED = 'updated';
export const EXTERNAL_REFERENCE_PLACEHOLDER = 'Select External Refference Type';
