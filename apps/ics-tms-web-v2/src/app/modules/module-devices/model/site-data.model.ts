interface Owner {
  id: string;
  name: string;
}

interface Tag {
  id: number;
  name: string;
}

interface SiteGroup {
  id: string;
  name: string;
}
export interface SiteResults {
  address?: string;
  contactEmail?: string;
  contactPhone?: string;
  formattedAddress?: string;
  id?: string;
  latitude?: number;
  longitude?: number;
  name?: string;
  referenceId?: string;
  status?: number;
  timezoneId?: string;
  visible?: boolean;
  owner?: Owner;
  isDefault?: boolean;
  keyGroup?: string | null;
  tags?: Tag[];
  siteGroups?: SiteGroup[];
  statusStr?: string;
}

export interface SiteData {
  results: SiteResults[];
  resultsMetadata?: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
}

export interface DevicesParams {
  isCSV?: boolean;
  pageIndex: number;
  pageSize: number;
  showHiddenDevices?: boolean;
  'oosFilter[]'?: string[];
  'statuses[]'?: string[];
}

export interface SitesParam {
  autoPoll: boolean;
  order: string;
  pageIndex: number;
  pageSize: number;
  showHiddenSites: boolean;
}
