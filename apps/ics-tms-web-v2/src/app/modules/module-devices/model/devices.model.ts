import { ThemePalette } from '@angular/material/core';

export interface AlarmRulesSettings {
  suspendedByDeviceUntil: string | null;
  suspendedFrom: string | null;
  suspendedUntil: string | null;
  suspended: boolean;
}

export interface DeviceType {
  id: string;
  name: string;
  screenSize: number | null;
}

export interface AuxInfo {
  type: string | null;
  status: string | null;
}

export interface Devices {
  id: number;
  siteId: string;
  siteName: string;
  lastRegistered: string;
  lastContact: string;
  lastTransactionOn?: string;
  name: string;
  description: string;
  serialNumber: string;
  keyGroupRef: string | null;
  presence: string;
  status: number;
  gatewayAddress: string;
  macAddress: string;
  subnetMask: string;
  releaseVersion: string;
  alarmRulesSettings: AlarmRulesSettings;
  ipAddress: string;
  deviceType: DeviceType;
  ksn: string | null;
  auxInfo: AuxInfo;
  statusAlarmTs: string;
  siteAddress: string;
  oosConditions: unknown;
  statusStr: string;
}

export interface DevicesResponse {
  resultsMetadata: {
    pageIndex: number;
    pageSize: number;
    totalResults: number;
  };
  results: Devices[];
}

export interface Task {
  name: string;
  completed: boolean;
  color: ThemePalette;
  subtasks?: Task[];
}

export interface ExampleFlatNode {
  expandable: boolean;
  name: string;
  level: number;
  completed: boolean;
  color: ThemePalette;
}
