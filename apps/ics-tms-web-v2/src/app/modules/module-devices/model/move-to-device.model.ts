export interface CheckRKIResponse {
  deviceType: unknown[];
  devicesToRKI: unknown[];
}

export interface CheckRKIParams {
  site: string | null;
  deviceSerials: string[];
}

export interface Device {
  serialNumber: string;
}

export interface MoveToSitesPayload {
  siteId: string;
  devices: Device[];
  deploymentType: string | null;
}

export interface MoveToCompany {
  serialNumber: string;
  companyRef: string;
}

export interface SiteEntity {
  siteId: string;
  siteName: string;
  siteTags: string[];
  totalDevices: number;
  formattedAddress: string;
}
