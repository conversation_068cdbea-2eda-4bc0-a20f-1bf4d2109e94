import {
  Component,
  ElementRef,
  EventEmitter,
  Output,
  inject,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { MoveToCompany } from '../../../model/move-to-device.model';
import { MoveToDeviceService } from '../../../services/move-to-devices.service';

@Component({
  selector: 'app-move-to-company',
  templateUrl: './move-to-company.component.html',
  styleUrls: ['./move-to-company.component.scss'],
})
export class MoveToCompanyComponent {
  @Output() getBackToStart = new EventEmitter<string>();

  activeModal = inject(NgbActiveModal);

  multiLineText = '';

  CheckRKIParams!: MoveToCompany;

  ErrorMessage = '';

  isMoveToDeviceClicked = false;

  elementRef = inject(ElementRef);

  moveToDeviceService = inject(MoveToDeviceService);

  getNoOfWords(): number {
    const substrings = this.multiLineText.split(/[,\n]/);
    const filteredSubstrings = substrings.filter(
      substring => substring.trim().length > 0
    );
    return filteredSubstrings.length;
  }

  goBackToMoveDevice() {
    this.getBackToStart.emit('none');
  }

  adjustTextAreaHeight() {
    const textArea = this.elementRef.nativeElement.querySelector('textarea');
    if (textArea) {
      textArea.style.height = 'auto';
      if (textArea.scrollHeight < 54) {
        textArea.style.height = '54px';
      } else {
        textArea.style.height = `${textArea.scrollHeight}px`;
      }
    }
  }

  OnMoveToDevices() {
    try {
      const serialEntries = this.multiLineText
        .split(/[\n,]+/)
        .filter(entry => entry.trim() !== '');
      const seenSerialNumbers = new Set();

      const resultArray = serialEntries.map(entry => {
        const [serialNumber, companyRef] = entry
          .split('||')
          .map(part => part.trim());
        if (!serialNumber) {
          throw new Error(`Device's serial number is required`);
        }
        if (!companyRef) {
          throw new Error(`Company reference is required`);
        }
        if (seenSerialNumbers.has(serialNumber)) {
          throw new Error(
            `Duplication of serial number ${serialNumber} detected.`
          );
        }

        seenSerialNumbers.add(serialNumber);
        return { serialNumber, companyRef };
      });

      resultArray.forEach(entry => {
        const checkRKIParams: MoveToCompany = {
          serialNumber: entry['serialNumber'],
          companyRef: entry['companyRef'],
        };

        this.moveToDeviceService.getCheckRKI(checkRKIParams).subscribe();
      });

      this.moveToDeviceService.postMoveToCompany(resultArray).subscribe(
        _ => {
          this.activeModal.close('Close click');
        },
        error => {
          this.ErrorMessage = error.error.message;
        }
      );
    } catch (error: any) {
      this.ErrorMessage = error.message;
    }
  }

  adjustOverflow() {
    const textArea = document.querySelector(
      '.text-area'
    ) as HTMLTextAreaElement;
    if (
      textArea.scrollHeight >
      parseInt(
        window.getComputedStyle(textArea).getPropertyValue('max-height'),
        10
      )
    ) {
      textArea.style.overflowY = 'auto';
    } else {
      textArea.style.overflowY = 'hidden';
    }
  }
}
