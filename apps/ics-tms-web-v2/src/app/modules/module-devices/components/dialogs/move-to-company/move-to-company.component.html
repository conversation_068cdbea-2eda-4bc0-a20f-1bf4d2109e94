<div class="move-to-company-container">
  <div class="heading-move-company">
    <h4>Move Devices</h4>
  </div>

  <div class="main-body-move-company">
    <div
      class="error-message"
      *ngIf="ErrorMessage.length > 0"
      [innerHTML]="ErrorMessage"
    ></div>
    <p class="bolder-text">Add device serial numbers and customer codes</p>
    <p class="text-style-company">
      Use the Double pipe <code class="pipe">||</code> to separate serial number
      and customer code
    </p>
    <p *ngIf="getNoOfWords() < 1" class="light-bold-text">
      Your list can be separated by new lines or commas.
    </p>
    <p *ngIf="getNoOfWords() >= 1" class="light-bold-text">
      {{ getNoOfWords() }} device
    </p>
    <textarea
      (input)="adjustTextAreaHeight()"
      [(ngModel)]="multiLineText"
      class="text-area"
      name="name"
      placeholder="Enter serial numbers here"
      data-bs-toggle="tooltip"
      data-bs-placement="bottom"
      (input)="adjustOverflow()"
      [title]="'Please fill in this field.'"
    ></textarea>
  </div>

  <div class="footer-btn-move-sites">
    <div class="left-buttons">
      <button (click)="goBackToMoveDevice()" class="btn my-back-btn">
        Back to Start
      </button>
    </div>
    <div class="right-buttons">
      <button
        (click)="activeModal.close('Close click')"
        class="cancel-btn-site-groups"
      >
        Cancel
      </button>

      <button
        class="btn-move-devices"
        [disabled]="getNoOfWords() < 1"
        (click)="OnMoveToDevices()"
      >
        Move Devices
      </button>
    </div>
  </div>
</div>
