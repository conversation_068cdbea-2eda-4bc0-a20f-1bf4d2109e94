import { Component, inject, ViewEncapsulation } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { SitesParam } from '../../../model/site-data.model';
import { loadConsumers } from 'src/app/store/actions/consumers.actions';

@Component({
  selector: 'app-move-devices',
  templateUrl: './move-devices.component.html',
  styleUrls: ['./move-devices.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MoveDevicesComponent {
  sitesParams: SitesParam;

  moveTo = 'none';

  activeModal = inject(NgbActiveModal);

  constructor(private store: Store) {
    this.sitesParams = {
      autoPoll: false,
      order: 'name-asc',
      pageIndex: 1,
      pageSize: 20,
      showHiddenSites: true,
    };
    store.dispatch(loadConsumers());
  }

  openMoveToSitesModal() {
    this.moveTo = 'sites';
  }

  openMoveToCompany() {
    this.moveTo = 'company';
  }

  handleBackToStart(event: string) {
    this.moveTo = event;
  }
}
