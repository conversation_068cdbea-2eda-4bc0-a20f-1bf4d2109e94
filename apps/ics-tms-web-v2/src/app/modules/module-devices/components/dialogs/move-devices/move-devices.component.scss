.modal-content {
  padding: 0 !important;
  border-radius: 0.5rem !important;
  top: 1rem;
}

.move-devices-modal {
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .more-devices-heading {
    padding: 1rem 1.5rem;

    h4 {
      font-size: 1.8rem;
      font-weight: 500;
      margin: 0;
    }

    border-bottom: 0.1rem solid var(--color-border);
  }

  .more-devices-body {
    padding: 2rem 1.5rem;
    display: flex;
    justify-content: space-between;
    gap: 1.6rem;
    border-bottom: 0.1rem solid var(--color-border);

    .move-to-block {
      border: 0.1rem solid var(--dropdown-border-hover);
      border-radius: 0.3rem;
      background-color: var(--dropdown-by-default);
      width: 100%;
      height: fit-content;
      transition: all ease-in 0.1s;
      cursor: pointer;

      &:hover {
        background-color: var(--dropdown-hover);
      }

      &:active {
        background-color: var(--btn-bg-hover);
        border-color: var(--placeholder-text-color);
      }

      .inner-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 2.6rem 1rem;
        gap: 1rem;

        .icon-inner-block {
          font-size: 4rem;
          width: 4rem;
          height: 4rem;
          color: var(--color-primary);
        }

        p {
          font-size: 1.4rem;
          font-weight: 500;
          margin: 0;
        }
      }
    }
  }

  .footer-btn-dialog {
    display: flex;
    justify-content: flex-end;
    padding: 1rem 1.5rem;

    button {
      color: var(--color-primary);
      font-weight: 500;
      background-color: var(--color-white);
      border: none;
      font-size: 1.4rem;
      padding: 0.6rem 1.2rem;
      transition: all ease-in 0.1s;

      &:hover {
        color: var(--color-blue);
      }
    }
  }
}
