<div class="move-to-sites-container">
  <div class="heading-move-sites">
    <h4>Move Devices</h4>
  </div>

  <div
    class="main-body-move-sites"
    *ngIf="!isMoveToDeviceClicked && !isDeployImmediatelyClicked"
  >
    <div
      class="error-message"
      *ngIf="errorMessage.length > 0"
      [innerHTML]="errorMessage"
    ></div>

    <div class="upper-container">
      <p class="bolder-text">
        Select the site you want to move the devices to.
      </p>

      <input
        type="text"
        placeholder="Select a site"
        class="ics-input"
        [formControl]="searchControl"
      />

      <div
        #listContainer
        class="list-container"
        tabindex="0"
        (keydown)="handleKeyDown($event)"
        (mousemove)="cancelKeyboardNavigation()"
        *ngIf="siteData?.length"
      >
        <div
          *ngFor="let site of siteData; let i = index"
          (click)="selectSite(site)"
          (keydown)="handleKeyDown($event)"
          #listItem
          class="list-item"
          [class.highlighted]="i === highlightedIndex"
          (mouseenter)="onMouseEnter(i)"
        >
          {{ site.siteName }}
        </div>
      </div>
    </div>

    <div class="bottom-container">
      <p class="bolder-text">Add device serial numbers</p>
      <p *ngIf="getNoOfWords() < 1" class="light-bold-text">
        Your list can be separated by new lines or commas.
      </p>
      <p *ngIf="getNoOfWords() >= 1" class="light-bold-text">
        {{ getNoOfWords() }} device
      </p>
      <textarea
        (input)="adjustTextAreaHeight()"
        [(ngModel)]="multiLineText"
        class="text-area"
        name="name"
        placeholder="Enter serial numbers here"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        (input)="adjustOverflow()"
        [title]="'Please fill in this field.'"
      ></textarea>
    </div>
  </div>

  <div
    class="main-body-move-sites"
    *ngIf="isMoveToDeviceClicked && !isDeployImmediatelyClicked"
  >
    <h4 class="message-heading">These changes may affect configurations</h4>
    <p class="message">
      Any affected configurations for these devices will automatically deploy
      during the maintenance window for each site (daily)
    </p>
  </div>

  <div class="main-body-move-sites" *ngIf="isDeployImmediatelyClicked">
    <h4 class="message-heading">Are you sure?</h4>
    <p class="message">
      Deploying configurations will reboot any affected device(s), which may
      cause temporary disruption to service at site.
    </p>
  </div>

  <div class="footer-btn-move-sites">
    <div class="left-buttons">
      <button (click)="goBackToMoveDevice()" class="btn my-back-btn">
        Back to Start
      </button>
    </div>
    <div class="right-buttons">
      <button
        (click)="activeModal.close('Close click')"
        class="cancel-btn-site-groups"
      >
        Cancel
      </button>
      <button
        [disabled]="getNoOfWords() < 1 || selectedSite === null"
        class="btn-move-devices"
        (click)="onMoveDevices()"
        *ngIf="!isMoveToDeviceClicked && !isDeployImmediatelyClicked"
      >
        Move Devices
      </button>
      <button
        class="immediately-deploy btn btn-primary"
        *ngIf="isMoveToDeviceClicked && !isDeployImmediatelyClicked"
        (click)="onDeployImmediately()"
      >
        Deploy Immediately
      </button>
      <button
        class="confirm btn btn-primary"
        *ngIf="isMoveToDeviceClicked && !isDeployImmediatelyClicked"
        (click)="onConfirm()"
      >
        Confirm
      </button>
      <button
        class="waiting btn btn-primary"
        *ngIf="isDeployImmediatelyClicked"
        (click)="onConfirm()"
      >
        Wait for maintenance window
      </button>
      <button
        class="deploy btn btn-primary"
        *ngIf="isDeployImmediatelyClicked"
        (click)="onDeploy()"
      >
        Deploy
      </button>
    </div>
  </div>
</div>
