.move-to-company-container {
  width: auto;
  height: fit-content;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .heading-move-company {
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    h4 {
      font-size: 1.8rem;
      font-weight: 500;
      margin: 0;
    }
  }

  .main-body-move-company {
    padding: 1rem 1.5rem;
    display: flex;
    flex-direction: column;
    border-bottom: 0.1rem solid var(--color-border);

    .bolder-text {
      font-size: 1.4rem;
      font-weight: 700;
      margin-bottom: 0;
    }

    .text-style-company {
      color: var(--logo-color-font);
      font-weight: 600;
      font-size: 1.3rem;
      margin: 0;

      code {
        font-size: 90%;
        padding: 0.008rem 0.016rem;
        border-radius: 0.012rem;
        color: var(--pipe-color);
        background-color: var(--pipe-color-bg);
      }
    }

    .light-bold-text {
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 0.8rem;
      color: var(--logo-color-font);
    }

    .text-area {
      width: 100%;
      border-radius: 0.3rem;
      padding: 0.6rem 1.2rem;
      resize: none;
      height: 5.4rem;
      max-height: 30rem;
      border: 0.1rem solid var(--color-border);
      margin-bottom: 1.5rem;
      transition:
        border-color ease-in-out 0.15s,
        box-shadow ease-in-out 0.15s;
      font-size: 1.4rem;

      &:focus {
        box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
        border-color: rgba(68, 138, 255, 0.75) !important;
        outline: 0;
      }

      &::-webkit-scrollbar {
        width: 0.8rem;
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 5em;
        background-color: transparent;
      }

      &:hover::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.26);
      }
    }
  }

  .footer-btn-move-sites {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;

    .left-buttons {
      .my-back-btn {
        border: 0.1rem solid var(--dropdown-border-hover);
        background-color: var(--dropdown-by-default) !important;
        transition: all ease-in 0.1s;
        padding: 0.6rem 1.2rem;
        color: var(--color-black);
        line-height: normal;
        font-size: 1.4rem;

        &:hover {
          background-color: var(--dropdown-hover) !important;
        }

        &:active {
          background-color: var(--btn-bg-hover) !important;
          border-color: var(--placeholder-text-color);
        }
      }
    }

    .right-buttons {
      .cancel-btn-site-groups {
        color: var(--color-primary);
        font-weight: 500;
        background-color: var(--color-white);
        border: none;
        font-size: 1.4rem;
        padding: 0.6rem 1.2rem;
        transition: all ease-in 0.1s;
        margin-right: 1.6rem;

        &:hover {
          color: var(--color-blue);
        }
      }

      .btn-move-devices {
        border: 0.1rem solid var(--color-primary);
        box-shadow:
          0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
          0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
        transition: all ease-in 0.1s;
        font-size: 1.4rem;
        font-weight: 500;
        line-height: normal;
        border-radius: 0.3rem;
        touch-action: manipulation;
        background-color: var(--color-primary);
        color: var(--color-white);
        padding: 0.6rem 1.2rem;

        &:hover {
          border-color: var(--color-blue);
          background-color: var(--color-blue);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

.error-message {
  margin-bottom: 2rem;
  border: 0.1rem solid transparent;
  border-radius: 0.3rem;
  border-color: var(--color-bg-red);
  background-color: var(--alert-msg-bg);
  color: var(--color-black);
  padding: 1rem 1.5rem;
  font-size: 1.4rem;
}
