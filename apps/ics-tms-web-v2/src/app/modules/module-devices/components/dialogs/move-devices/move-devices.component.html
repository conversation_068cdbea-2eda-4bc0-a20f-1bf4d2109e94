<div class="move-devices-modal" *ngIf="moveTo === 'none'">
  <div class="more-devices-heading">
    <h4>Move Devices</h4>
  </div>

  <div class="more-devices-body">
    <div (click)="openMoveToSitesModal()" class="move-to-block">
      <div class="inner-block">
        <mat-icon class="icon-inner-block">pin_drop</mat-icon>
        <p>Move to Site</p>
      </div>
    </div>
    <div (click)="openMoveToCompany()" class="move-to-block">
      <div class="inner-block">
        <mat-icon class="icon-inner-block">business</mat-icon>
        <p>Move to Company</p>
      </div>
    </div>
  </div>

  <div class="footer-btn-dialog">
    <button (click)="activeModal.close('Close click')">Cancel</button>
  </div>
</div>

<ng-container *ngIf="moveTo === 'sites'">
  <app-move-to-site
    (getBackToStart)="handleBackToStart($event)"
  ></app-move-to-site>
</ng-container>

<ng-container *ngIf="moveTo === 'company'">
  <app-move-to-company
    (getBackToStart)="handleBackToStart($event)"
  ></app-move-to-company>
</ng-container>
