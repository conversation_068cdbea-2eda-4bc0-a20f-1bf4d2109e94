.move-to-sites-container {
  width: auto;
  height: fit-content;
  box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.5);

  .item-name-text {
    font-size: 1.4rem;
  }

  .item-owner-name-text {
    font-size: 1.2rem;
  }

  .heading-move-sites {
    padding: 1rem 1.5rem;
    border-bottom: 0.1rem solid var(--color-border);

    h4 {
      font-size: 1.8rem;
      font-weight: 500;
      margin: 0;
    }
  }

  .main-body-move-sites {
    padding: 1rem 1.5rem;
    display: flex;
    flex-direction: column;
    border-bottom: 0.1rem solid var(--color-border);

    .message-heading {
      font-weight: 500;
      font-size: 1.8rem;
      margin: 1rem 0;
    }

    .message {
      font-size: 1.4rem;
    }

    .upper-container {
      margin-bottom: 0.938rem;

      .bolder-text {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 0.8rem;
      }

      input {
        width: 100%;
      }

      .ng-select.ng-select-single .ng-select-container {
        background-color: var(--dropdown-by-default);
        cursor: pointer;
        transition:
          border-color ease-in-out 0.15s,
          box-shadow ease-in-out 0.15s;
        font-size: 1.4rem;

        &:hover {
          box-shadow: none;
        }
      }

      .ng-select.ng-select-single .ng-select-container .ng-value-container {
        cursor: pointer;
      }

      .ng-select .ng-clear-wrapper {
        display: none;
      }

      .ng-dropdown-panel
        .ng-dropdown-panel-items
        .ng-option.ng-option-selected {
        color: var(--color-white);
        font-weight: 700;
        background-color: var(--color-primary);
      }

      .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
        color: var(--color-white);
        font-weight: 700;
        background-color: var(--color-primary);
      }

      .ng-select .ng-select-container .ng-value-container .ng-input > input {
        cursor: pointer;
      }

      .ng-select.ng-select-opened > .ng-select-container {
        background-color: var(--color-white);
        box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
        border-color: rgba(68, 138, 255, 0.75) !important;
        outline: 0;
      }

      .ng-select.ng-select-single .ng-select-container:hover {
        box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
      }

      .ng-select.ng-select-focused:not(.ng-select-opened)
        > .ng-select-container {
        border-color: rgba(68, 138, 255, 0.75) !important;
        box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
      }
    }

    .bottom-container {
      .bolder-text {
        font-size: 1.4rem;
        font-weight: 700;
        margin: 0;
      }

      .light-bold-text {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.8rem;
        color: var(--color-text);
      }

      .text-area {
        width: 100%;
        border-radius: 0.3rem;
        padding: 0.6rem 1.2rem;
        resize: none;
        height: 5.4rem;
        max-height: 30rem;
        border: 0.1rem solid var(--color-border);
        margin-bottom: 1.5rem;
        transition:
          border-color ease-in-out 0.15s,
          box-shadow ease-in-out 0.15s;
        font-size: 1.4rem;

        &:focus {
          box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
          border-color: rgba(68, 138, 255, 0.75) !important;
          outline: 0;
        }

        &::-webkit-scrollbar {
          width: 0.8rem;
          background-color: transparent;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 5em;
          background-color: transparent;
        }

        &:hover::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.26);
        }
      }
    }
  }

  .footer-btn-move-sites {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;

    .left-buttons {
      .my-back-btn {
        border: 0.1rem solid var(--dropdown-border-hover);
        background-color: var(--dropdown-by-default) !important;
        transition: all ease-in 0.1s;
        padding: 0.6rem 1.2rem;
        color: var(--color-black);
        line-height: normal;
        font-size: 1.4rem;

        &:hover {
          background-color: var(--dropdown-hover) !important;
        }

        &:active {
          background-color: var(--btn-bg-hover) !important;
          border-color: var(--placeholder-text-color);
        }
      }
    }

    .right-buttons {
      display: flex;
      align-items: center;
      gap: 1rem;

      .cancel-btn-site-groups {
        color: var(--logo-color-one);
        font-weight: 500;
        background-color: var(--color-white);
        border: none;
        font-size: 1.4rem;
        padding: 0.6rem 1.2rem;
        transition: all ease-in 0.1s;
        margin-right: 1.6rem;

        &:hover {
          color: var(--color-blue);
        }
      }

      button {
        font-size: 1.4rem;
        font-weight: 500;
        padding: 0.6rem 1.2rem;
      }

      .btn-move-devices {
        border: 0.1rem solid var(--logo-color-one);
        box-shadow:
          0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
          0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
        transition: all ease-in 0.1s;
        font-size: 1.4rem;
        font-weight: 500;
        line-height: normal;
        border-radius: 0.3rem;
        touch-action: manipulation;
        background-color: var(--logo-color-one);
        color: var(--color-white);
        padding: 0.6rem 1.2rem;

        &:hover {
          border-color: var(--active-btn-bg-color);
          background-color: var(--active-btn-bg-color);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .error-message {
    margin-bottom: 2rem;
    border: 0.1rem solid transparent;
    border-radius: 0.3rem;
    border-color: var(--color-bg-red);
    background-color: var(--alert-msg-bg);
    color: var(--color-black);
    padding: 1rem 1.5rem;
    font-size: 1.4rem;

    strong {
      font-weight: 500;
    }
  }

  .mat-typography h4 {
    margin: 1.6rem 0;
  }
}

.list-container {
  width: 95%;
  max-height: 25rem;
  overflow-y: auto;
  font-size: 1.4rem;
  position: absolute;
  z-index: 1;
  margin-top: 0.2rem;
  padding: 0.8rem 0;
  border: 0.1rem solid transparent;
  border-radius: 0.3rem;
  background-color: var(--white);
  background-clip: padding-box;
  box-shadow:
    0 0.1rem 0.3rem 0 var(--md-black-20),
    0 0.1rem 0.8rem 0 var(--md-black-14),
    0 0.2rem 0.1rem -0.1rem var(--md-black-12);
}

.list-container:focus {
  outline: none;
}

.list-container:focus-visible {
  box-shadow: none;
}

.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  line-height: 2.42857143;
  padding: 0.3rem 3rem;
  white-space: nowrap;
  cursor: pointer;

  &.highlighted {
    background-color: var(--md-indigo-600);
    color: var(--white);
  }
}
