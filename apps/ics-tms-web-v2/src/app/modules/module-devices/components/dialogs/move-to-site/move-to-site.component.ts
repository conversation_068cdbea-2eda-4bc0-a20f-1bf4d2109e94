import {
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
  ViewEncapsulation,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { FormControl } from '@angular/forms';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  Subject,
  takeUntil,
} from 'rxjs';
import {
  CheckRKIParams,
  Device,
  MoveToSitesPayload,
  SiteEntity,
} from '../../../model/move-to-device.model';
import { MoveToDeviceService } from '../../../services/move-to-devices.service';
import { IMMEDIATE, WINDOW_MENTAINANCE } from '../../../constants/apiConstants';
import { CommonResponseData } from 'src/app/models/common';

@Component({
  selector: 'app-move-to-site',
  templateUrl: './move-to-site.component.html',
  styleUrls: ['./move-to-site.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MoveToSiteComponent {
  @Output() getBackToStart = new EventEmitter<string>();

  @ViewChildren('listItem', { read: ElementRef })
  listItems!: QueryList<ElementRef>;

  @ViewChild('listContainer') listContainer!: ElementRef;

  CheckRKIParams!: CheckRKIParams;

  isMoveToDeviceClicked = false;

  moveToSitesPayload!: MoveToSitesPayload;

  deviceSerials: string[] = [];

  isDeployImmediatelyClicked = false;

  errorMessage = '';

  siteData!: SiteEntity[];

  searchControl = new FormControl<string>('');

  private readonly debounceTimeMs = 300;

  highlightedIndex: number = 0;

  hoveredIndex: number | null = null;

  isNavigatingWithKeyboard = false;

  hasFocused = false;

  private readonly destroy$ = new Subject<void>();

  store = inject(Store);

  elementRef = inject(ElementRef);

  activeModal = inject(NgbActiveModal);

  moveDeviceService = inject(MoveToDeviceService);

  ngOnInit() {
    this.handleSearchedTextSubscription();
  }

  goBackToMoveDevice() {
    this.getBackToStart.emit('none');
  }

  multiLineText = '';

  selectedSite!: SiteEntity;

  getNoOfWords(): number {
    const substrings = this.multiLineText.split(/[,\n]/);
    const filteredSubstrings = substrings.filter(
      substring => substring.trim().length > 0
    );
    return filteredSubstrings.length;
  }

  adjustTextAreaHeight() {
    const textArea = this.elementRef.nativeElement.querySelector('textarea');
    if (textArea) {
      textArea.style.height = 'auto';
      if (textArea.scrollHeight < 54) {
        textArea.style.height = '54px';
      } else {
        textArea.style.height = `${textArea.scrollHeight}px`;
      }
    }
  }

  private handleSearchedTextSubscription() {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(this.debounceTimeMs),
        takeUntil(this.destroy$),
        map(value => value?.trim() ?? ''),
        distinctUntilChanged()
      )
      .subscribe(searchedValue => {
        this.performSearch(searchedValue);
      });
  }

  private performSearch(searchedValue: string): void {
    if (searchedValue === '') {
      this.siteData = [];
      return;
    }

    this.moveDeviceService
      .getSiteDataFromServer({
        fields: ['siteId', 'formattedAddress', 'siteName', 'siteTags'],
        filters: JSON.stringify({ siteName: { $contains: searchedValue } }),
        pageIndex: 0,
        pageSize: 20,
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: CommonResponseData<SiteEntity>) => {
          this.siteData = data.results;
          this.hasFocused = false;
          this.highlightedIndex = 0;
          this.errorMessage = '';
        },
        error: () => {
          this.errorMessage = 'Failed to load sites. Please try again.';
          this.siteData = [];
        },
      });
  }

  ngAfterViewChecked() {
    if (this.siteData?.length && !this.hasFocused) {
      this.listContainer?.nativeElement?.focus();
      this.hasFocused = true;
    }
  }

  handleKeyDown(event: KeyboardEvent) {
    if (!this.siteData.length) return;
    this.isNavigatingWithKeyboard = true;

    if (event.key === 'Enter') {
      if (
        this.highlightedIndex >= 0 &&
        this.highlightedIndex < this.siteData.length
      ) {
        this.selectSite(this.siteData[this.highlightedIndex]);
        event.preventDefault();
      }
    }

    if (event.key === 'ArrowDown') {
      this.highlightedIndex =
        (this.highlightedIndex + 1) % this.siteData.length;
      this.scrollHighlightedItemIntoView();
      event.preventDefault();
    } else if (event.key === 'ArrowUp') {
      this.highlightedIndex =
        (this.highlightedIndex - 1 + this.siteData.length) %
        this.siteData.length;
      this.scrollHighlightedItemIntoView();
      event.preventDefault();
    }
  }

  onMouseEnter(index: number) {
    if (!this.isNavigatingWithKeyboard) {
      this.highlightedIndex = index;
    }
  }

  cancelKeyboardNavigation() {
    this.isNavigatingWithKeyboard = false;
  }

  scrollHighlightedItemIntoView() {
    setTimeout(() => {
      const items = this.listItems.toArray();
      const item = items[this.highlightedIndex];
      item?.nativeElement.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }, 0);
  }

  selectSite(site: any): void {
    this.searchControl.setValue(site.siteName);
    this.selectedSite = site;
    this.siteData = [];
    this.hasFocused = false;
  }

  onMoveDevices() {
    this.deviceSerials = this.multiLineText
      .split(/[\s,]+/)
      .filter(item => item.trim() !== '');
    this.CheckRKIParams = {
      site: this.selectedSite.siteId,
      deviceSerials: this.deviceSerials,
    };

    this.moveDeviceService.getCheckRKI(this.CheckRKIParams).subscribe();
    this.isMoveToDeviceClicked = true;
  }

  onConfirm() {
    this.callMoveToSites(WINDOW_MENTAINANCE);
  }

  onDeployImmediately() {
    this.isDeployImmediatelyClicked = true;
  }

  onDeploy() {
    this.callMoveToSites(IMMEDIATE);
  }

  callMoveToSites(deploymentType: string) {
    const devices: Device[] = this.deviceSerials.map(serial => ({
      serialNumber: serial,
    }));
    const siteId: string = this.selectedSite.siteId || '';
    this.moveToSitesPayload = {
      deploymentType,
      devices,
      siteId,
    };

    this.moveDeviceService.postMoveToSites(this.moveToSitesPayload).subscribe({
      next: () => {
        this.activeModal.close({ success: true, siteId });
      },
      error: error => {
        this.errorMessage = this.handleError(error.error.msg);
        this.isMoveToDeviceClicked = false;
        this.isDeployImmediatelyClicked = false;
      },
    });
  }

  handleError(error: string[]): string {
    const devicesNotFound = error.length;
    const deviceSerials = error.map((msg: string) => {
      const serialNumber = msg.split(' ')[1];
      return serialNumber.startsWith('(') && serialNumber.endsWith(')')
        ? serialNumber.slice(1, -1)
        : serialNumber;
    });
    const errorMessage = `<strong>${devicesNotFound} devices not found for serial numbers:</strong> ${deviceSerials.join(
      ', '
    )}.`;
    return errorMessage;
  }

  adjustOverflow() {
    const textArea = document.querySelector(
      '.text-area'
    ) as HTMLTextAreaElement;
    if (
      textArea.scrollHeight >
      parseInt(
        window.getComputedStyle(textArea).getPropertyValue('max-height'),
        10
      )
    ) {
      textArea.style.overflowY = 'auto';
    } else {
      textArea.style.overflowY = 'hidden';
    }
  }
}
