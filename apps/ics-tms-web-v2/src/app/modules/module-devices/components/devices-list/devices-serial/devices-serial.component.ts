import { Component, Input } from '@angular/core';
import { getAssets } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-devices-serial',
  templateUrl: './devices-serial.component.html',
  styleUrls: ['./devices-serial.component.scss'],
})
export class DevicesSerialComponent {
  @Input() dataSource: any;

  getDeviceImage(): string {
    return `${getAssets()}img/device-table-img.png`;
  }
}
