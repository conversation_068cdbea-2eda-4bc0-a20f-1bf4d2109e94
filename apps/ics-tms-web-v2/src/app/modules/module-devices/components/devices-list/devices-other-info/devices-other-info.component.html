<div class="horz-flex">
  <div class="otherInfo-icon-colour" *ngFor="let item of dataSource?.otherInfo">
    <mat-icon
      matTooltip=" {{ item.category }} - {{ item.condition }}"
      matTooltipPosition="above"
      *ngIf="item.category == 'OTP Tampered'"
      >pan_tool</mat-icon
    >
    <mat-icon
      matTooltip="{{ item.category }} - {{ item.condition }}"
      matTooltipPosition="above"
      *ngIf="item.category == 'Security'"
      >no_encryption</mat-icon
    >
    <mat-icon
      matTooltip="{{ item.category }} - {{ item.condition }}"
      matTooltipPosition="above"
      *ngIf="item.category == 'OTP in Safe Mode'"
      >security</mat-icon
    >
    <mat-icon
      matTooltip="{{ item.category }} - {{ item.condition }}"
      matTooltipPosition="above"
      *ngIf="item.category == 'Component Disconnected'"
      >power</mat-icon
    >
    <mat-icon
      matTooltip="{{ item.category }} - {{ item.condition }}"
      matTooltipPosition="above"
      *ngIf="item.category == 'Site Integration'"
      >swap_calls</mat-icon
    >
  </div>
</div>
