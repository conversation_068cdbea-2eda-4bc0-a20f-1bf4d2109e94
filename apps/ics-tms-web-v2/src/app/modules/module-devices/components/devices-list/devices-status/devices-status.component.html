<div class="statusCard" [ngClass]="hasData ? 'has-status' : 'no-status'">
  <ng-container *ngIf="dataSource.statusStr === 'INACTIVE'">
    <mat-icon id="col-inactive">remove_circle</mat-icon>
    <p id="styling-inactive">Inactive</p>
  </ng-container>
  <ng-container *ngIf="dataSource.statusStr === 'OPERATIONAL'">
    <mat-icon id="col-green">check_circle</mat-icon>
    <p id="style-operational">Operational</p>
  </ng-container>
  <div
    class="out-of-service"
    *ngIf="dataSource.statusStr === 'OUT_OF_SERVICE'"
    [ngbTooltip]="tooltip"
  >
    <span class="material-icons label-text-danger" id="col-red">cancel</span>
    <p id="styling-out-of-service">
      Out of service <span id="style-span">{{ relativeTime }}</span>
    </p>
  </div>
  <div
    class="unknown"
    *ngIf="dataSource.statusStr === 'UNKNOWN'"
    [ngbTooltip]="tooltip"
  >
    <mat-icon id="col-unknown">cloud_off</mat-icon>
    <p id="margin-unknown">
      <span id="unknown-text">Unknown&nbsp;</span>
      <span id="style-unknown" *ngIf="dataSource.statusAlarmTs"
        >(<span>{{ dataSource.statusAlarmTs | dateFormat: '' : true }}</span
        >)</span
      >
    </p>
  </div>
</div>
