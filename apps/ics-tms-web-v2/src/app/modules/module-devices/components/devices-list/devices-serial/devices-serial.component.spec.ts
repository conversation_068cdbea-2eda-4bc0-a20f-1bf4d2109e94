import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { DevicesSerialComponent } from './devices-serial.component';

describe('DevicesSerialComponent', () => {
  let component: DevicesSerialComponent;
  let fixture: ComponentFixture<DevicesSerialComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DevicesSerialComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
    fixture = TestBed.createComponent(DevicesSerialComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getDeviceImage and return correct path', () => {
    const path = component.getDeviceImage();
    expect(path).toContain('img/device-table-img.png');
  });

  it('should render deviceName and serialNumber in template', () => {
    component.dataSource = {
      deviceName: 'Test Device',
      serialNumber: 'SN12345',
    };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('b')?.textContent).toContain('Test Device');
    expect(compiled.querySelector('p')?.textContent).toContain('SN12345');
  });

  it('should render the image with correct src', () => {
    component.dataSource = {
      deviceName: 'Test Device',
      serialNumber: 'SN12345',
    };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    const img = compiled.querySelector('img');
    expect(img).toBeTruthy();
    expect(img?.getAttribute('src')).toBe(component.getDeviceImage());
  });
});
