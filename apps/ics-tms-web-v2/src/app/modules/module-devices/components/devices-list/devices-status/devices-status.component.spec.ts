import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA, Pipe, PipeTransform } from '@angular/core';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import {
  YEAR_IN_SECONDS,
  MONTH_IN_SECONDS,
  DAY_IN_SECONDS,
  HOUR_IN_SECONDS,
  MINUTE_IN_SECONDS,
} from '../../../constants/apiConstants';
import { DevicesStatusComponent } from './devices-status.component';

import { DateFormatPipe } from 'src/app/utils/date-format.pipe';

@Pipe({ name: 'dateFormat' })
class MockDateFormatPipe implements PipeTransform {
  transform(_value: any, ..._args: any[]): any {
    return 'MockDate';
  }
}

describe('DevicesStatusComponent', () => {
  let component: DevicesStatusComponent;
  let fixture: ComponentFixture<DevicesStatusComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DevicesStatusComponent, MockDateFormatPipe],
      imports: [NgbTooltipModule],
      providers: [{ provide: DateFormatPipe, useClass: MockDateFormatPipe }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
    fixture = TestBed.createComponent(DevicesStatusComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle UNKNOWN status', () => {
    component.dataSource = { statusStr: 'UNKNOWN' };
    component.ngOnInit();
    expect(component.tooltip).toContain('Unknown');
    expect(component.hasData).toBe(false);
  });

  it('should handle OUT_OF_SERVICE status with statusAlarmTs', () => {
    const now = new Date();
    component.dataSource = {
      statusStr: 'OUT_OF_SERVICE',
      statusAlarmTs: now.toISOString(),
    };
    spyOn(component, 'getRelativeTime').and.returnValue('(1 day ago)');
    component.ngOnInit();
    expect(component.tooltip).toContain('Out of service');
    expect(component.hasData).toBe(true);
    expect(component.relativeTime).toBe('(1 day ago)');
  });

  it('should handle OUT_OF_SERVICE status without statusAlarmTs', () => {
    component.dataSource = { statusStr: 'OUT_OF_SERVICE' };
    component.ngOnInit();
    expect(component.tooltip).toContain('Out of service');
    expect(component.hasData).toBe(false);
  });

  it('should handle statusAlarmTs for tooltip', () => {
    const now = new Date();
    component.dataSource = {
      statusStr: 'OPERATIONAL',
      statusAlarmTs: now.toISOString(),
    };
    component.ngOnInit();
    expect(component.hasData).toBe(true);
    expect(component.tooltip).toContain('MockDate');
  });

  it('should call getTimeZoneOffsetString', () => {
    const date = new Date('2023-01-01T00:00:00Z');
    const offset = component.getTimeZoneOffsetString(date);
    expect(typeof offset).toBe('string');
    expect(offset.length).toBe(6);
  });

  it('should return correct relative time for years, months, days, hours, minutes, seconds', () => {
    const now = new Date();
    expect(
      component.getRelativeTime(
        new Date(now.getTime() - YEAR_IN_SECONDS * 1000)
      )
    ).toContain('year');
    expect(
      component.getRelativeTime(
        new Date(now.getTime() - MONTH_IN_SECONDS * 1000)
      )
    ).toContain('month');
    expect(
      component.getRelativeTime(new Date(now.getTime() - DAY_IN_SECONDS * 1000))
    ).toContain('day');
    expect(
      component.getRelativeTime(
        new Date(now.getTime() - HOUR_IN_SECONDS * 1000)
      )
    ).toContain('hour');
    expect(
      component.getRelativeTime(
        new Date(now.getTime() - MINUTE_IN_SECONDS * 1000)
      )
    ).toContain('minute');
    expect(
      component.getRelativeTime(new Date(now.getTime() - 5 * 1000))
    ).toContain('second');
  });

  it('should format time unit correctly for singular and plural', () => {
    expect(component.formatTimeUnit(60, 60, 'minute')).toBe('(1 minute ago)');
    expect(component.formatTimeUnit(120, 60, 'minute')).toBe('(2 minutes ago)');
  });

  it('should render INACTIVE template', () => {
    component.dataSource = { statusStr: 'INACTIVE' };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('#col-inactive')).toBeTruthy();
    expect(compiled.querySelector('#styling-inactive')?.textContent).toContain(
      'Inactive'
    );
  });

  it('should render OPERATIONAL template', () => {
    component.dataSource = { statusStr: 'OPERATIONAL' };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('#col-green')).toBeTruthy();
    expect(compiled.querySelector('#style-operational')?.textContent).toContain(
      'Operational'
    );
  });

  it('should render OUT_OF_SERVICE template', () => {
    component.dataSource = { statusStr: 'OUT_OF_SERVICE' };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('#col-red')).toBeTruthy();
    expect(
      compiled.querySelector('#styling-out-of-service')?.textContent
    ).toContain('Out of service');
  });

  it('should render UNKNOWN template', () => {
    component.dataSource = { statusStr: 'UNKNOWN' };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('#col-unknown')).toBeTruthy();
    expect(compiled.querySelector('#unknown-text')?.textContent).toContain(
      'Unknown'
    );
  });

  it('should render statusAlarmTs in UNKNOWN template', () => {
    component.dataSource = {
      statusStr: 'UNKNOWN',
      statusAlarmTs: new Date().toISOString(),
    };
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('#style-unknown')).toBeTruthy();
  });
});
