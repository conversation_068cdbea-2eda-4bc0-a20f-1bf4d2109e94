import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { By } from '@angular/platform-browser';
import { DevicesOtherInfoComponent } from './devices-other-info.component';

const mockOtherInfo = [
  { category: 'OTP Tampered', condition: 'Critical' },
  { category: 'Security', condition: 'Warning' },
  { category: 'OTP in Safe Mode', condition: 'Info' },
  { category: 'Component Disconnected', condition: 'Offline' },
  { category: 'Site Integration', condition: 'Active' },
];

describe('DevicesOtherInfoComponent', () => {
  let component: DevicesOtherInfoComponent;
  let fixture: ComponentFixture<DevicesOtherInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DevicesOtherInfoComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
    fixture = TestBed.createComponent(DevicesOtherInfoComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render all mat-icons for each category', () => {
    component.dataSource = { otherInfo: mockOtherInfo };
    fixture.detectChanges();
    const icons = fixture.debugElement.queryAll(By.css('mat-icon'));
    expect(icons.length).toBe(5);
    expect(icons[0].nativeElement.textContent.trim()).toBe('pan_tool');
    expect(icons[1].nativeElement.textContent.trim()).toBe('no_encryption');
    expect(icons[2].nativeElement.textContent.trim()).toBe('security');
    expect(icons[3].nativeElement.textContent.trim()).toBe('power');
    expect(icons[4].nativeElement.textContent.trim()).toBe('swap_calls');
  });

  it('should render correct icon text and order for each category', () => {
    component.dataSource = { otherInfo: mockOtherInfo };
    fixture.detectChanges();
    const icons = fixture.debugElement.queryAll(By.css('mat-icon'));
    expect(icons.length).toBe(5);
    expect(icons[0].nativeElement.textContent.trim()).toBe('pan_tool');
    expect(icons[1].nativeElement.textContent.trim()).toBe('no_encryption');
    expect(icons[2].nativeElement.textContent.trim()).toBe('security');
    expect(icons[3].nativeElement.textContent.trim()).toBe('power');
    expect(icons[4].nativeElement.textContent.trim()).toBe('swap_calls');
  });

  it('should render nothing if otherInfo is empty', () => {
    component.dataSource = { otherInfo: [] };
    fixture.detectChanges();
    const icons = fixture.debugElement.queryAll(By.css('mat-icon'));
    expect(icons.length).toBe(0);
  });

  it('should not throw if dataSource is undefined', () => {
    component.dataSource = undefined;
    expect(() => fixture.detectChanges()).not.toThrow();
  });
});
