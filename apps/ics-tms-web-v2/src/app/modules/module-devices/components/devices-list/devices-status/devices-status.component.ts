import {
  Component,
  inject,
  Input,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import {
  DAY_IN_SECONDS,
  HOUR_IN_SECONDS,
  MINUTE_IN_SECONDS,
  MONTH_IN_SECONDS,
  YEAR_IN_SECONDS,
} from '../../../constants/apiConstants';
import { DateFormatPipe } from 'src/app/utils/date-format.pipe';

@Component({
  selector: 'app-devices-status',
  templateUrl: './devices-status.component.html',
  styleUrls: ['./devices-status.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class DevicesStatusComponent implements OnInit {
  @Input() dataSource: any;

  tooltip!: string;

  relativeTime!: string;

  hasData = false;

  datePipe = inject(DateFormatPipe);

  ngOnInit(): void {
    if (this.dataSource.statusStr === 'UNKNOWN') {
      this.tooltip = 'Unknown ';
    }
    if (this.dataSource.statusStr === 'OUT_OF_SERVICE') {
      this.tooltip = 'Out of service ';
    }
    if (this.dataSource?.statusAlarmTs) {
      this.hasData = true;
      const contactDate = new Date(this.dataSource.statusAlarmTs);
      this.tooltip += this.datePipe.transform(
        contactDate,
        'MMM DD, YYYY [at] hh:mm a (UTCZ)'
      );
      this.relativeTime = this.getRelativeTime(contactDate);
    } else {
      this.tooltip += '()';
    }
  }

  getTimeZoneOffsetString(contactDate: Date): string {
    const timeZoneOffsetMinutes = contactDate.getTimezoneOffset();
    const timeZoneOffsetHours = Math.floor(
      Math.abs(timeZoneOffsetMinutes) / 60
    );
    const timeZoneOffsetMinutesPart = Math.abs(timeZoneOffsetMinutes) % 60;
    const timeZoneOffsetString = `${
      (timeZoneOffsetMinutes < 0 ? '+' : '-') +
      timeZoneOffsetHours.toString().padStart(2, '0')
    }:${timeZoneOffsetMinutesPart.toString().padStart(2, '0')}`;

    return timeZoneOffsetString;
  }

  getRelativeTime(contactDate: Date): string {
    const now = new Date();
    const diffMilliseconds = now.getTime() - contactDate.getTime();
    const diffSeconds = Math.floor(diffMilliseconds / 1000);

    if (diffSeconds >= YEAR_IN_SECONDS) {
      return this.formatTimeUnit(diffSeconds, YEAR_IN_SECONDS, 'year');
    }
    if (diffSeconds >= MONTH_IN_SECONDS) {
      return this.formatTimeUnit(diffSeconds, MONTH_IN_SECONDS, 'month');
    }
    if (diffSeconds >= DAY_IN_SECONDS) {
      return this.formatTimeUnit(diffSeconds, DAY_IN_SECONDS, 'day');
    }
    if (diffSeconds >= HOUR_IN_SECONDS) {
      return this.formatTimeUnit(diffSeconds, HOUR_IN_SECONDS, 'hour');
    }
    if (diffSeconds >= MINUTE_IN_SECONDS) {
      return this.formatTimeUnit(diffSeconds, MINUTE_IN_SECONDS, 'minute');
    }
    return this.formatTimeUnit(diffSeconds, 1, 'second');
  }

  formatTimeUnit(
    diff: number,
    unitInSeconds: number,
    unitName: string
  ): string {
    const units = Math.floor(diff / unitInSeconds);
    return `(${units} ${units === 1 ? unitName : `${unitName}s`} ago)`;
  }
}
