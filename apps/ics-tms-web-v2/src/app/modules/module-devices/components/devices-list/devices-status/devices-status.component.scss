.statusCard {
  position: relative;
  display: flex;
  align-items: center;
  align-content: center;
  margin-bottom: 2rem;

  .unknown {
    display: flex;
    align-items: center;
  }

  .out-of-service {
    display: flex;
    align-items: center;
  }

  #col-inactive {
    color: var(--label-inactive);
    font-size: 2rem;
  }

  #styling-inactive {
    margin: 0 0 0 0.225rem;
    color: var(--color-gray);
  }

  #col-green {
    color: var(--color-bg-green);
    font-size: 2rem;
  }

  #style-operational {
    margin: 0 0 0 0.225rem;
    color: var(--color-bg-green);
  }

  .mat-icon {
    overflow: visible;
  }

  #col-red {
    color: var(--color-bg-red);
    font-size: 2rem;
    margin-bottom: 0.3rem;
  }

  #styling-out-of-service {
    margin: 0 0 0 0.425rem;
    color: var(--color-bg-red);
  }

  #style-span {
    font-weight: 400;
    font-size: 13px;
    color: var(--color-black-shade-two);
  }

  #col-unknown {
    color: rgba(0, 0, 0, 0.38);
    font-size: 2rem;
  }

  #margin-unknown {
    margin: 0 0 0 0.225rem;
  }

  #style-unknown {
    font-size: 1.3rem;
    font-weight: 400;
    color: var(--color-black-shade-two);
  }

  #unknown-text {
    color: rgba(0, 0, 0, 0.38);
    margin-right: 0.5rem;
  }

  .tooltip {
    position: relative;
    display: inline-block;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &.show {
      opacity: 1;
    }

    .tooltip-arrow {
      position: relative;
    }

    .tooltip-inner {
      max-width: 40rem;
      display: flex;
      font-size: 1.2rem;
    }
  }
}

.has-status {
  .tooltip-arrow {
    transform: translate(3.4rem, -0.8rem) !important;
  }

  .tooltip-inner {
    transform: translate(3.2rem, -0.8rem);
  }
}

.no-status {
  .tooltip {
    .tooltip-arrow {
      transform: translate(-0.27rem, -0.8rem) !important;
    }

    .tooltip-inner {
      transform: translate(-0.4rem, -0.8rem);
    }
  }
}
