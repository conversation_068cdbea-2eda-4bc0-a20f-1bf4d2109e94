import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { NgxPaginationModule } from 'ngx-pagination';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { MatTreeModule } from '@angular/material/tree';
import { FormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedCommonModules } from '../common.module';
import { MaterialModule } from '../material.module';
import { MoveDevicesComponent } from './components/dialogs/move-devices/move-devices.component';
import { siteDataReducers } from './store/reducers/site-data.reducers';
import { SiteDataEffects } from './store/effects/site-data.effects';
import { DevicesOtherInfoComponent } from './components/devices-list/devices-other-info/devices-other-info.component';
import { DevicesStatusComponent } from './components/devices-list/devices-status/devices-status.component';
import { MoveToCompanyComponent } from './components/dialogs/move-to-company/move-to-company.component';
import { DevicesSerialComponent } from './components/devices-list/devices-serial/devices-serial.component';
import { DevicesListComponent } from './components/devices-list/devices-list.component';
import { ModuleDevicesComponent } from './module-devices.component';
import { ModuleDevicesRoutingModule } from './module-devices-routing.module';
import { MoveToSiteComponent } from './components/dialogs/move-to-site/move-to-site.component';
import { SharedModuleModule } from 'src/app/shared-module/shared-module.module';

@NgModule({
  declarations: [
    ModuleDevicesComponent,
    DevicesListComponent,
    DevicesSerialComponent,
    MoveToSiteComponent,
    MoveToCompanyComponent,
    DevicesStatusComponent,
    DevicesOtherInfoComponent,
    MoveDevicesComponent,
  ],
  imports: [
    CommonModule,
    ModuleDevicesRoutingModule,
    MatIconModule,
    NgxPaginationModule,
    MatTableModule,
    MatTooltipModule,
    StoreModule.forFeature('siteDataReducer', siteDataReducers),
    EffectsModule.forFeature([SiteDataEffects]),
    MaterialModule,
    MatTreeModule,
    FormsModule,
    MatAutocompleteModule,
    SharedCommonModules,
    SharedModuleModule,
    MatIconModule,
    NgbTooltipModule,
  ],
})
export class ModuleDevicesModule {}
