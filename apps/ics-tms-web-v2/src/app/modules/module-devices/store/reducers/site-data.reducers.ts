import { createReducer, on } from '@ngrx/store';
import { SiteData } from '../../model/site-data.model';
import {
  loadSiteData,
  loadSiteDataFailure,
  loadSiteDataSuccess,
} from '../actions/site-data.actions';

export interface SiteDataState {
  siteData: SiteData;
  loading?: boolean;
  error?: string | null;
}

export const initialState: SiteDataState = {
  siteData: {
    results: [],
  },
};

export const siteDataReducers = createReducer(
  initialState,
  on(loadSiteData, state => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(loadSiteDataSuccess, (state, { siteData }) => ({
    ...state,
    siteData,
    loading: false,
    error: null,
  })),
  on(loadSiteDataFailure, (state, { error }) => ({
    ...state,
    error,
  }))
);
