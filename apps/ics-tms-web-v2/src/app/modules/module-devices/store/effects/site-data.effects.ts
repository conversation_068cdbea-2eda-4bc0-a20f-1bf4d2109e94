import { Injectable, inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, of, switchMap } from 'rxjs';
import { SiteDataService } from '../../services/site-data.service';
import {
  loadSiteData,
  loadSiteDataFailure,
  loadSiteDataSuccess,
} from '../actions/site-data.actions';

@Injectable()
export class SiteDataEffects {
  actions$ = inject(Actions);

  siteDataService = inject(SiteDataService);

  getPeopleData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadSiteData),
      switchMap(action =>
        this.siteDataService.getSiteDataFromServer(action.params).pipe(
          map(data => loadSiteDataSuccess({ siteData: data })),
          catchError(error => of(loadSiteDataFailure({ error })))
        )
      )
    )
  );
}
