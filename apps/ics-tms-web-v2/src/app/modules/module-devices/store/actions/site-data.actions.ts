import { createAction, props } from '@ngrx/store';
import { SiteData, SitesParam } from '../../model/site-data.model';

export const loadSiteData = createAction(
  '[Devices] Load Site Data',
  props<{ params: SitesParam }>()
);

export const loadSiteDataSuccess = createAction(
  '[Devices] Load Site Data Success',
  props<{ siteData: SiteData }>()
);

export const loadSiteDataFailure = createAction(
  '[Devices] Load Site Data Failure',
  props<{ error: 'error' }>()
);
