import { Injectable } from '@angular/core';
import { Task } from '../model/devices.model';
import { TREE_DATA, keys, mp } from '../constants/filter-bar';

@Injectable({
  providedIn: 'root',
})
export class FilterBarService {
  getTreeData(): Task[] {
    return TREE_DATA;
  }

  getMap(): Map<string, string> {
    return mp;
  }

  getStatusClass(statusStr: string): string {
    switch (statusStr) {
      case 'UNKNOWN':
        return 'unknown';
      case 'WARNING':
        return 'warning';
      case 'NORMAL':
        return 'normal';
      case 'CRITICAL':
        return 'critical';
      case 'INACTIVE':
        return 'inactive';
      default:
        return '';
    }
  }

  getCheckboxes() {
    const checkboxes: { [key: string]: boolean } = {};
    keys.forEach((key: string | number) => {
      checkboxes[key] = false;
    });
    return checkboxes;
  }
}
