import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { getApiConstants } from '../constants/api';
import {
  CheckRKIResponse,
  MoveToCompany,
  MoveToSitesPayload,
  SiteEntity,
} from '../model/move-to-device.model';
import { CommonResponseData } from 'src/app/models/common';

@Injectable({
  providedIn: 'root',
})
export class MoveToDeviceService {
  http = inject(HttpClient);

  getSiteDataFromServer(params: {}): Observable<
    CommonResponseData<SiteEntity>
  > {
    const httpParams = new HttpParams({ fromObject: params });
    return this.http.get<CommonResponseData<SiteEntity>>(
      getApiConstants().moveDevices.getSites,
      {
        params: httpParams,
      }
    );
  }

  getCheckRKI(params: {}): Observable<CheckRKIResponse> {
    const httpParams = new HttpParams({ fromObject: params });
    return this.http.get<CheckRKIResponse>(
      getApiConstants().moveDevices.getRKICheck,
      { params: httpParams }
    );
  }

  // Need To change this any to a partivcular type
  postMoveToSites(payload: MoveToSitesPayload): Observable<any> {
    return this.http.post<any>(
      getApiConstants().moveDevices.getMoveToSites,
      payload
    );
  }

  // Need To change this any to a partivcular type
  postMoveToCompany(payload: MoveToCompany[]): Observable<any> {
    return this.http.post<any>(
      getApiConstants().moveDevices.getMoveToCompany,
      payload
    );
  }
}
