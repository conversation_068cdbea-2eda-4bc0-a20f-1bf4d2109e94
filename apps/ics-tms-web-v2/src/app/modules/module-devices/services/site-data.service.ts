import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SiteData } from '../model/site-data.model';
import { getBaseUrl } from 'src/app/constants/api';

@Injectable({
  providedIn: 'root',
})
export class SiteDataService {
  http = inject(HttpClient);

  getSiteDataFromServer(params: {}): Observable<SiteData> {
    const httpParams = new HttpParams({ fromObject: params });
    return this.http.get<SiteData>(`${getBaseUrl()}/sites`, {
      params: httpParams,
    });
  }
}
