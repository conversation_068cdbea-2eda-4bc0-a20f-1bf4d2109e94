import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { getApiConstants } from '../constants/api';
import { Devices } from '../model/devices.model';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';

@Injectable({
  providedIn: 'root',
})
export class DevicesListDataService {
  http = inject(HttpClient);

  getDevicesData(params: {}): Observable<CommonResponseData<Devices>> {
    const httpParams = new HttpParams({ fromObject: params });
    return this.http.get<CommonResponseData<Devices>>(
      `${getApiConstants().devices.getDeviceList}`,
      { params: httpParams }
    );
  }
}
