import { Task } from '../model/devices.model';

export const TREE_DATA: Task[] = [
  {
    name: 'Out of Service',
    completed: false,
    color: 'primary',
    subtasks: [
      {
        name: 'Security',
        completed: false,
        color: 'primary',
        subtasks: [
          { name: 'Secure Channel Lost', completed: false, color: 'primary' },
          {
            name: 'Component Certificate Mismatch',
            completed: false,
            color: 'primary',
          },
        ],
      },
      {
        name: 'OTP Tampered',
        completed: false,
        color: 'accent',
        subtasks: [
          { name: 'SDC Tampered (removal)', completed: false, color: 'accent' },
          {
            name: 'SDC Tampered (destructive)',
            completed: false,
            color: 'accent',
          },
          { name: 'UPC Tampered (removal)', completed: false, color: 'accent' },
          {
            name: 'UPC Tampered (destructive)',
            completed: false,
            color: 'accent',
          },
        ],
      },
      {
        name: 'OTP in Safe Mode',
        completed: false,
        color: 'warn',
        subtasks: [
          { name: 'SDC Safe Mode', completed: false, color: 'warn' },
          { name: 'UPC Safe Mode', completed: false, color: 'warn' },
          { name: 'APC Safe Mode', completed: false, color: 'warn' },
        ],
      },
      {
        name: 'Component Disconnected',
        completed: false,
        color: 'primary',
        subtasks: [
          { name: 'SDC Disconnected', completed: false, color: 'primary' },
          { name: 'UPC Disconnected', completed: false, color: 'primary' },
        ],
      },
      {
        name: 'Site Integration',
        completed: false,
        color: 'accent',
        subtasks: [
          { name: 'POS Disconnected', completed: false, color: 'accent' },
          { name: 'POS Error', completed: false, color: 'accent' },
        ],
      },
    ],
  },
];
export const keys = ['UNKNOWN', 'INACTIVE', 'OPERATIONAL'];
export const mp = new Map<string, string>();

mp.set('Out of Service', 'cancel');
mp.set('Security', 'no_encryption');
mp.set('OTP Tampered', 'pan_tool');
mp.set('OTP in Safe Mode', 'security');
mp.set('Component Disconnected', 'power');
mp.set('Site Integration', 'swap_calls');

export interface FiltersArray {
  deviceStatus?: string[];
  outOfService?: string[];
}
