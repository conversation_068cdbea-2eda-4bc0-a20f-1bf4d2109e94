export interface FoodNode {
  name: string;
  children?: FoodNode[];
}

export const TREE_DATA: FoodNode[] = [
  {
    name: 'Out of Service',
    children: [
      {
        name: 'Security',
        children: [
          { name: 'Secure Channel Lost' },
          { name: 'Component Certificate Mismatch' },
        ],
      },
      {
        name: 'OTP Tampered',
        children: [
          { name: 'SDC Tampered (removal)' },
          { name: 'SDC Tampered (destructive)' },
          { name: 'UPC Tampered (removal)' },
          { name: 'UPC Tampered (destructive)' },
        ],
      },
      {
        name: 'OTP in Safe Mode',
        children: [
          { name: 'SDC Safe Mode' },
          { name: 'UPC Safe Mode' },
          { name: 'APC Safe Mode' },
        ],
      },
      {
        name: 'Component Disconnected',
        children: [{ name: 'SDC Disconnected' }, { name: 'UPC Disconnected' }],
      },
      {
        name: 'Site Integration',
        children: [{ name: 'POS Disconnected' }, { name: 'POS Error' }],
      },
    ],
  },
];

export const treeMap = new Map();
treeMap.set('Security', 'no_encryption');
treeMap.set('Out of Service', 'cancel');
treeMap.set('OTP Tampered', 'pan_tool');
treeMap.set('OTP in Safe Mode', 'security');
treeMap.set('Component Disconnected', 'power');
treeMap.set('Site Integration', 'swap_calls');
