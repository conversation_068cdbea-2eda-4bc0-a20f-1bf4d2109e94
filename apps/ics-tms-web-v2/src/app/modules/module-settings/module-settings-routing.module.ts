import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AlarmsComponent } from './components/alarms/alarms.component';
import { MediaComponent } from './components/media/media.component';
import { PeopleEditComponent } from './components/people/people-edit/people-edit.component';
import { PeopleComponent } from './components/people/people.component';
import { EditSiteGroupsComponent } from './components/site-groups/edit-site-groups/edit-site-groups.component';
import { SiteGroupsComponent } from './components/site-groups/site-groups.component';
import { SiteTagsComponent } from './components/site-tags/site-tags.component';
import { EditTeamsComponent } from './components/teams/edit-teams/edit-teams.component';
import { TeamsComponent } from './components/teams/teams.component';
import { ModuleSettingsComponent } from './module-settings.component';
import { AlarmSubscriptionComponent } from './components/alarms/alarm-subscription/alarm-subscription.component';
import { ImportSitesComponent } from './components/import-sites/import-sites.component';
import { redirectForSettings } from 'src/app/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: ModuleSettingsComponent,
    children: [
      { path: '', redirectTo: 'alarms', pathMatch: 'full' },
      {
        path: 'alarms',
        component: AlarmsComponent,
        canActivate: [redirectForSettings],
      },
      { path: 'alarms/add', component: AlarmSubscriptionComponent },
      { path: 'alarms/:id', component: AlarmSubscriptionComponent },
      { path: 'people', component: PeopleComponent },
      { path: 'people/add', component: PeopleEditComponent },
      { path: 'people/:id', component: PeopleEditComponent },
      { path: 'media', component: MediaComponent },
      { path: 'site-tags', component: SiteTagsComponent },
      { path: 'import-sites', component: ImportSitesComponent },
      { path: 'teams', component: TeamsComponent },
      { path: 'teams/create', component: EditTeamsComponent },
      { path: 'teams/:id/edit', component: EditTeamsComponent },
      { path: 'site-groups', component: SiteGroupsComponent },
      { path: 'site-groups/create', component: EditSiteGroupsComponent },
      { path: 'site-groups/:id/edit', component: EditSiteGroupsComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModuleSettingsRoutingModule {}
