import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getApiConstants } from '../constants/api';
import {
  Alarm,
  AlarmsResponse,
  CompanyRule,
  SitesResponse,
} from '../model/alarms.model';

@Injectable({
  providedIn: 'root',
})
export class AlarmsService {
  httpClient = inject(HttpClient);

  getAlarmsData(): Observable<AlarmsResponse[]> {
    return this.httpClient.get<AlarmsResponse[]>(
      getApiConstants().settings.alarms.getAlarms
    );
  }

  updateAlarm(id: string | number, alarmPayload: Alarm): Observable<Alarm> {
    return this.httpClient.put<Alarm>(
      `${getApiConstants().settings.alarms.getAlarms}/${id}`,
      alarmPayload
    );
  }

  getSubscriptionData(id: string): Observable<Alarm> {
    return this.httpClient.get<Alarm>(
      `${getApiConstants().settings.alarms.getAlarms}/${id}`
    );
  }

  getSitesList(params: {}): Observable<SitesResponse> {
    const httpParms = new HttpParams({ fromObject: params });
    return this.httpClient.get<SitesResponse>(
      getApiConstants().settings.subscription.getSites,
      { params: httpParms }
    );
  }

  postAlarmData(payload: Alarm): Observable<Alarm> {
    return this.httpClient.post<Alarm>(
      getApiConstants().settings.alarms.getAlarms,
      payload
    );
  }

  deleteAlarmData(id: string): Observable<unknown> {
    return this.httpClient.delete<unknown>(
      `${getApiConstants().settings.alarms.getAlarms}/${id}`
    );
  }

  getCompanyRules(): Observable<CompanyRule[]> {
    return this.httpClient.get<CompanyRule[]>(
      getApiConstants().settings.alarms.getCompanyRules
    );
  }
}
