import { AlarmsComponent } from '../components/alarms/alarms.component';
import { MediaComponent } from '../components/media/media.component';
import { PeopleComponent } from '../components/people/people.component';
import { SiteGroupsComponent } from '../components/site-groups/site-groups.component';
import { SiteTagsComponent } from '../components/site-tags/site-tags.component';
import { TeamsComponent } from '../components/teams/teams.component';
import { ImportSitesComponent } from '../components/import-sites/import-sites.component';
import { AuthService } from 'src/app/services/auth.service';
import { ListItem } from 'src/app/constants/settingsSideItems';
import { PRE_DEFINED_COLORS } from 'src/app/constants/globalConstant';

export function convertToTitle(title: string): string {
  const words = title.toLowerCase().split('_');

  const titleCaseWords = words.map(
    word => word.charAt(0).toUpperCase() + word.slice(1)
  );

  const titleCaseString = titleCaseWords.join(' ');

  return titleCaseString;
}

export function generateInitialsAndColor(fullName: string) {
  const words = fullName.trim().split(' ');

  const firstInitial = words.length > 0 ? extractInitial(words[0]) : '';
  const lastInitial =
    words.length > 1 ? extractInitial(words[words.length - 1]) : '';

  const color = getColorFromString(fullName);

  return {
    initials: firstInitial + lastInitial,
    color,
  };
}

function extractInitial(word: string): string {
  const alphanumericRegex = /[a-zA-Z0-9]/;
  if (!word) return '';

  const firstChar = word[0];
  return alphanumericRegex.test(firstChar)
    ? firstChar.toUpperCase()
    : extractInitial(word.substring(1));
}

export function getColorFromString(str: string) {
  const hash = Array.from(str).reduce(
    (acc, char) => acc + char.charCodeAt(0) * 31, // replacing bitwise with a multiplier
    0
  );

  const index = Math.abs(hash) % PRE_DEFINED_COLORS.length;
  return PRE_DEFINED_COLORS[index];
}

export function getCompanySettingsMap(): ListItem[] {
  const MEDIA_SETTINGS_VIEW_PERMISSIONS = ['mediaMgmt', 'playlistMgmt'];

  const isMediaAllowed =
    MEDIA_SETTINGS_VIEW_PERMISSIONS.some(permission => {
      const featureFlags = AuthService.getFeatureFlags();
      return featureFlags?.some(flag => flag.key === permission && flag.active);
    }) && AuthService.isAllowedAccess('MEDIA_VIEW');

  const canSeeMedia = () => {
    if (isMediaAllowed) {
      return (
        AuthService.isAllowedAccess('WRITE_MEDIA_SETTINGS') ||
        AuthService.isAllowedAccess('VIEW_MEDIA_SETTINGS')
      );
    }
    return false;
  };

  return [
    {
      name: 'Alarms',
      state: '/COMPANY_NAME/settings/alarms',
      urlEndPoint: 'alarms',
      component: AlarmsComponent,
      isAllowed: AuthService.isAllowedAccess('WRITE_ALARMS'),
    },
    {
      name: 'People',
      state: '/COMPANY_NAME/settings/people',
      urlEndPoint: 'people',
      component: PeopleComponent,
      isAllowed: AuthService.isAllowedAccess('WRITE_PEOPLE'),
    },
    {
      name: 'Teams',
      state: '/COMPANY_NAME/settings/teams',
      urlEndPoint: 'teams',
      component: TeamsComponent,
      isAllowed: AuthService.isAllowedAccess('WRITE_TEAMS'),
    },
    {
      name: 'Site Groups',
      state: '/COMPANY_NAME/settings/site-groups',
      urlEndPoint: 'site-groups',
      component: SiteGroupsComponent,
      isAllowed: AuthService.isAllowedAccess('WRITE_SITE_GROUPS'),
    },
    {
      name: 'Media',
      state: '/COMPANY_NAME/settings/media',
      urlEndPoint: 'media',
      component: MediaComponent,
      isAllowed: canSeeMedia(),
    },
    {
      name: 'Site Tag Management',
      state: '/COMPANY_NAME/settings/site-tags',
      urlEndPoint: 'site-tags',
      component: SiteTagsComponent,
      isAllowed: AuthService.isAllowedAccess('VIEW_COMPANY_SETTINGS'),
    },
    {
      name: 'Import Sites',
      state: '/COMPANY_NAME/settings/import-sites',
      urlEndPoint: 'import-sites',
      component: ImportSitesComponent,
      isAllowed: AuthService.isAllowedAccess('VIEW_COMPANY_SETTINGS'),
    },
  ];
}
