import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import {
  NgbAccordionBody,
  NgbA<PERSON>rdionButton,
  NgbA<PERSON>rdion<PERSON>ollapse,
  NgbAccordionDirective,
  NgbAccordionHeader,
  NgbAccordionItem,
  NgbAccordionModule,
  NgbDropdown,
  NgbDropdownItem,
  NgbDropdownMenu,
  NgbDropdownModule,
  NgbModalModule,
  NgbPaginationNext,
  NgbPaginationPrevious,
  NgbToastModule,
  NgbTooltipModule,
} from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { SharedCommonModules } from '../common.module';
import { MaterialModule } from '../material.module';
import { AlarmSubscriptionComponent } from './components/alarms/alarm-subscription/alarm-subscription.component';
import { SiteSelectionComponent } from './components/alarms/alarm-subscription/site-selection/site-selection.component';
import { SubscribersComponent } from './components/alarms/alarm-subscription/subscribers/subscribers.component';
import { AlarmsComponent } from './components/alarms/alarms.component';
import { DayPartPopUpComponent } from './components/media/day-part-pop-up/day-part-pop-up.component';
import { MediaComponent } from './components/media/media.component';
import { PeopleEditComponent } from './components/people/people-edit/people-edit.component';
import { CancelRequestComponent } from './components/people/people-edit/people-modals/cancel-request/cancel-request.component';
import { ResetMfaComponent } from './components/people/people-edit/people-modals/reset-mfa/reset-mfa.component';
import { PeopleComponent } from './components/people/people.component';
import { ConfirmDeleteSiteGroupComponent } from './components/site-groups/edit-site-groups/confirm-delete-site-group/confirm-delete-site-group.component';
import { EditSiteGroupsComponent } from './components/site-groups/edit-site-groups/edit-site-groups.component';
import { SiteGroupsComponent } from './components/site-groups/site-groups.component';
import { SiteTagsComponent } from './components/site-tags/site-tags.component';
import { DeleteTeamComponent } from './components/teams/edit-teams/delete-team/delete-team.component';
import { EditTeamsComponent } from './components/teams/edit-teams/edit-teams.component';
import { TeamsComponent } from './components/teams/teams.component';
import { ModuleSettingsRoutingModule } from './module-settings-routing.module';
import { ModuleSettingsComponent } from './module-settings.component';
import { AlarmsEffects } from './store/effects/alarms.effects';
import { EditSiteGroupEffects } from './store/effects/edit-site-groups.effects';
import { MediaEffects } from './store/effects/media.effects';
import { SubscriptionEffects } from './store/effects/subscription.effects';
import { SummarySiteGroupsEffects } from './store/effects/summary-site-groups.effects';
import { reducers } from './store/settings.store';
import { DeleteAlarmComponent } from './components/alarms/alarm-subscription/delete-alarm/delete-alarm.component';
import { ImportSitesComponent } from './components/import-sites/import-sites.component';
import { CustomTimeFormatPipe } from 'src/app/utils/time-format.pipe';
import { SharedModuleModule } from 'src/app/shared-module/shared-module.module';

@NgModule({
  declarations: [
    ModuleSettingsComponent,
    AlarmsComponent,
    PeopleComponent,
    TeamsComponent,
    SiteGroupsComponent,
    MediaComponent,
    SiteTagsComponent,
    ImportSitesComponent,
    DayPartPopUpComponent,
    PeopleEditComponent,
    CustomTimeFormatPipe,
    AlarmSubscriptionComponent,
    EditTeamsComponent,
    SubscribersComponent,
    SiteSelectionComponent,
    EditSiteGroupsComponent,
    ResetMfaComponent,
    CancelRequestComponent,
    ConfirmDeleteSiteGroupComponent,
    DeleteTeamComponent,
    DeleteAlarmComponent,
  ],
  imports: [
    CommonModule,
    MatIconModule,
    SharedModuleModule,
    ModuleSettingsRoutingModule,
    FormsModule,
    NgSelectModule,
    StoreModule.forFeature('settings', reducers),
    EffectsModule.forFeature([
      MediaEffects,
      AlarmsEffects,
      SubscriptionEffects,
      SummarySiteGroupsEffects,
      EditSiteGroupEffects,
    ]),
    NgbPaginationPrevious,
    NgbPaginationNext,
    MaterialModule,
    NgbDropdown,
    NgbDropdownMenu,
    NgbDropdownItem,
    NgbDropdownModule,
    SharedCommonModules,
    ReactiveFormsModule,
    NgbModalModule,
    NgbToastModule,
    NgbTooltipModule,
    NgbAccordionModule,
    NgbAccordionDirective,
    NgbAccordionItem,
    NgbAccordionHeader,
    NgbAccordionButton,
    NgbAccordionCollapse,
    NgbAccordionBody,
  ],
})
export class ModuleSettingsModule {}
