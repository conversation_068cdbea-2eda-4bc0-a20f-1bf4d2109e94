import { Location } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { isEmpty, isEqual } from 'lodash';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  filter,
  take,
  takeUntil,
} from 'rxjs';
import { Tags } from '../../../../../models/tags.model';
import { loadTags } from '../../../../../store/actions/tags.actions';
import { SiteGroup, Supplier } from '../../../model/site-groups.model';
import { SummarySiteGroupResponse } from '../../../model/summary-site-groups.model';
import { loadEditedSiteGroup } from '../../../store/actions/edit-site-groups.actions';
import { loadSummarySiteGroup } from '../../../store/actions/summary-site-groups.actions';
import {
  selectSiteGroupEditData,
  selectSiteGroupEditLoading,
} from '../../../store/selectors/edit-site-groups.selectors';
import {
  selectSummarySiteGroupData,
  selectSummarySiteGroupLoading,
} from '../../../store/selectors/summary-site-groups.selectors';
import { ConfirmDeleteSiteGroupComponent } from './confirm-delete-site-group/confirm-delete-site-group.component';
import {
  selectTagsData,
  selectTagsLoading,
} from 'src/app/store/selectors/tags.selectors';
import { getLoggedInUserCompanyId } from 'src/app/store/selectors/globalStore.selectors';
import { ToastService } from 'src/app/services/toast.service';
import { SiteGroupsService } from 'src/app/services/site-groups.service';
import { SelectionEvent } from 'src/app/components/shared/ics-searchable-dropdown/ics-searchable-dropdown.component';

@Component({
  selector: 'app-edit-site-groups',
  templateUrl: './edit-site-groups.component.html',
  styleUrls: ['./edit-site-groups.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditSiteGroupsComponent implements OnInit, OnDestroy {
  siteGroupEditData!: SiteGroup;

  summarySiteGroupsData$ = new BehaviorSubject<SummarySiteGroupResponse[]>([]);

  remainAvailableSites$ = new BehaviorSubject<SummarySiteGroupResponse[]>([]);

  currAvailableSites$ = new BehaviorSubject<SummarySiteGroupResponse[]>([]);

  remainSelectedSites$ = new BehaviorSubject<SummarySiteGroupResponse[]>([]);

  currSelectedSites$ = new BehaviorSubject<SummarySiteGroupResponse[]>([]);

  suppliers$ = new BehaviorSubject<Supplier[]>([]);

  selectedSuppliers$ = new BehaviorSubject<Supplier[]>([]);

  isCreateSite = false;

  siteId!: string | null;

  isCollapsed = true;

  tags$ = new BehaviorSubject<Tags[]>([]);

  selectedTags$ = new BehaviorSubject<Tags[]>([]);

  siteGroupName = '';

  companyId!: string;

  isLoading$ = new BehaviorSubject<boolean>(true);

  isUpdating$ = new BehaviorSubject<boolean>(false);

  isDeleting$ = new BehaviorSubject<boolean>(false);

  private readonly destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private location: Location,
    private store: Store,
    private siteGroupsService: SiteGroupsService,
    public modalService: NgbModal,
    private toastService: ToastService
  ) {
    this.route.url.subscribe(segments => {
      this.isCreateSite = segments.some(segment => segment.path === 'create');
      this.siteId = this.route.snapshot.paramMap.get('id');
    });
  }

  ngOnInit() {
    this.store
      .select(getLoggedInUserCompanyId)
      .pipe(takeUntil(this.destroy$))
      .subscribe(id => {
        this.companyId = id ?? '';
        if (this.companyId) this.loadInitialData();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadInitialData() {
    const siteSummaryData$ = this.loadSiteSummaryData();
    const tags$ = this.loadTags();
    const siteGroupData$ = this.loadEditedSiteGroupData();
    const suppliers$ = this.loadSuppliers();

    combineLatest([siteSummaryData$, tags$, siteGroupData$])
      .pipe(
        filter(
          ([siteSummaryData, tags, siteGroupData]) =>
            !siteSummaryData && !tags && !siteGroupData
        ),
        take(1)
      )
      .subscribe(() => {
        this.isLoading$.next(false);
        this.loadData(suppliers$);
      });
  }

  private loadData(suppliers$: Observable<Supplier[]>): void {
    this.store
      .select(selectTagsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data =>
        this.tags$.next(data.filter(tag => tag.siteCount > 0))
      );

    this.store
      .select(selectSiteGroupEditData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.siteGroupEditData = data;
      });

    this.store
      .select(selectSummarySiteGroupData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.summarySiteGroupsData$.next(data);
        this.processSiteGroupData();
      });

    suppliers$
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => this.suppliers$.next(data));
  }

  private loadTags() {
    this.store.dispatch(loadTags());
    return this.store.select(selectTagsLoading);
  }

  private loadSiteSummaryData() {
    this.store.dispatch(loadSummarySiteGroup({ companyId: this.companyId }));
    return this.store.select(selectSummarySiteGroupLoading);
  }

  private loadEditedSiteGroupData() {
    if (this.siteId) {
      this.store.dispatch(loadEditedSiteGroup({ id: this.siteId }));
    }
    return this.store.select(selectSiteGroupEditLoading);
  }

  private loadSuppliers() {
    return this.siteGroupsService.getSuppliers();
  }

  // handle the site group data when page is load
  private processSiteGroupData() {
    this.currAvailableSites$.next(this.summarySiteGroupsData$.getValue());
    this.remainAvailableSites$.next(this.currAvailableSites$.getValue());

    if (!this.isCreateSite && this.siteGroupEditData) {
      this.siteGroupName = this.siteGroupEditData.name;
      this.selectedSuppliers$.next(this.siteGroupEditData.companies ?? []);

      const selectedSites = this.summarySiteGroupsData$
        .getValue()
        .filter(site =>
          this.siteGroupEditData.sites?.some(
            editSite => editSite.id === site.id
          )
        );

      this.currSelectedSites$.next(selectedSites);
      this.remainSelectedSites$.next(selectedSites);

      const availableSites = this.summarySiteGroupsData$
        .getValue()
        .filter(
          site =>
            !selectedSites.some(selectedSite => selectedSite.id === site.id)
        );

      this.remainAvailableSites$.next(availableSites);
      this.currAvailableSites$.next(availableSites);
    }
  }

  handleNameEdit(event: Event) {
    const target = event.target as HTMLInputElement;
    this.siteGroupName = target.value;
  }

  // filtering the available sites and selected sites on the basis of filter
  handleTagSelectionChange({
    item,
    action,
  }: {
    item: Tags;
    action: string;
  }): void {
    const currentItems = this.selectedTags$.getValue();

    const isAlreadyAdded = currentItems.some(
      existingItem => existingItem.id === item.id
    );

    if (action === 'add' && !isAlreadyAdded) {
      this.selectedTags$.next([...currentItems, item]);
    } else {
      this.selectedTags$.next(
        currentItems.filter(existingItem => existingItem.id !== item.id)
      );
    }
    this.updateSelectedSites();
  }

  private updateSelectedSites(): void {
    const selectedTags = this.selectedTags$.getValue();
    const filterByTags = (site: SummarySiteGroupResponse) =>
      site.tags?.some(tag =>
        selectedTags.some(selected => selected.id === tag.id)
      );

    if (!isEmpty(selectedTags)) {
      this.currAvailableSites$.next(
        this.remainAvailableSites$.getValue().filter(filterByTags)
      );
      this.currSelectedSites$.next(
        this.remainSelectedSites$.getValue().filter(filterByTags)
      );
      return;
    }
    this.currAvailableSites$.next(this.remainAvailableSites$.getValue());
    this.currSelectedSites$.next(this.remainSelectedSites$.getValue());
  }

  handleSearchSite(searchTerm: string): void {
    const filterByName = (site: SummarySiteGroupResponse) =>
      site.name.includes(searchTerm);
    this.currAvailableSites$.next(
      this.remainAvailableSites$.getValue().filter(filterByName)
    );
    this.currSelectedSites$.next(
      this.remainSelectedSites$.getValue().filter(filterByName)
    );
  }

  // Select all the sites from the available sites
  addAllSites(): void {
    const toAdd = this.currAvailableSites$.getValue();

    this.currSelectedSites$.next([
      ...this.currSelectedSites$.getValue(),
      ...toAdd,
    ]);
    this.remainSelectedSites$.next([
      ...this.remainSelectedSites$.getValue(),
      ...toAdd,
    ]);
    this.remainAvailableSites$.next(
      this.remainAvailableSites$
        .getValue()
        .filter(
          site =>
            !this.currSelectedSites$
              .getValue()
              .some(selectedSite => selectedSite.id === site.id)
        )
    );
    this.currAvailableSites$.next([]);
  }

  // select a particular site from the available sites
  addSite(id: string): void {
    const site = this.currAvailableSites$.getValue().find(s => s.id === id);
    if (!site) return;

    this.currAvailableSites$.next(
      this.currAvailableSites$.getValue().filter(s => s.id !== id)
    );
    this.remainAvailableSites$.next(
      this.remainAvailableSites$.getValue().filter(s => s.id !== id)
    );
    this.currSelectedSites$.next([...this.currSelectedSites$.getValue(), site]);
    this.remainSelectedSites$.next([
      ...this.remainSelectedSites$.getValue(),
      site,
    ]);
  }

  // remove all the sites from the selected sites
  removeAllSites(): void {
    const toRemove = this.currSelectedSites$.getValue();

    this.currAvailableSites$.next([
      ...this.currAvailableSites$.getValue(),
      ...toRemove,
    ]);
    this.remainAvailableSites$.next([
      ...this.remainAvailableSites$.getValue(),
      ...toRemove,
    ]);
    this.remainSelectedSites$.next(
      this.remainSelectedSites$
        .getValue()
        .filter(
          site =>
            !this.remainAvailableSites$
              .getValue()
              .some(selectedSite => selectedSite.id === site.id)
        )
    );
    this.currSelectedSites$.next([]);
  }

  // remove a particular site from the available sites
  removeSite(id: string): void {
    const site = this.currSelectedSites$.getValue().find(s => s.id === id);
    if (!site) return;

    this.currSelectedSites$.next(
      this.currSelectedSites$.getValue().filter(s => s.id !== id)
    );
    this.remainSelectedSites$.next(
      this.remainSelectedSites$.getValue().filter(s => s.id !== id)
    );
    this.currAvailableSites$.next([
      ...this.currAvailableSites$.getValue(),
      site,
    ]);
    this.remainAvailableSites$.next([
      ...this.remainAvailableSites$.getValue(),
      site,
    ]);
  }

  goBack() {
    this.location.back();
  }

  updateSiteGroup() {
    const payload = this.getPayload();
    if (!this.siteGroupEditData.id) {
      this.isUpdating$.next(false);
      return;
    }

    this.siteGroupsService
      .updateSiteGroup(this.siteGroupEditData.id, payload)
      .subscribe({
        next: () => {
          this.showToast('Site Group Updated');
          this.location.back();
          this.isUpdating$.next(false);
        },
        error: error => {
          if (error.status === 504) {
            this.showToast('Site Group Update Failed');
          }
          this.isUpdating$.next(false);
        },
      });
  }

  createSiteGroup() {
    const payload = this.getPayload();
    this.siteGroupsService.createSiteGroup(payload).subscribe({
      next: () => {
        this.showToast('Site Group Created');
        this.location.back();
        this.isUpdating$.next(false);
      },
      error: error => {
        if (error.status === 504) {
          this.showToast('Site Group Creation Failed');
        }
        this.isUpdating$.next(false);
      },
    });
  }

  deleteSiteGroup() {
    const payload = this.getPayload();
    this.isDeleting$.next(true);
    this.siteGroupsService
      .updateSiteGroup(this.siteGroupEditData.id, payload)
      .subscribe({
        next: response => {
          if (response.sites?.length !== 0) {
            // cannot delete
            this.openDialog(false);
          } else {
            // delete
            this.openDialog(true);
          }
        },
        error: () => this.isDeleting$.next(false),
      });
  }

  openDialog(canDelete: boolean) {
    const modalRef = this.modalService.open(ConfirmDeleteSiteGroupComponent, {
      container: '#ng-modal-container',
      windowClass: 'common-menu-popup-modal in confirm-delete-site-group-modal',
      size: 'sm',
      animation: true,
      centered: true,
    });
    if (!canDelete) this.isDeleting$.next(false);

    modalRef.componentInstance.canDelete = canDelete;
    modalRef.componentInstance.title = canDelete ? '' : 'Cannot';
    modalRef.componentInstance.siteGroupName = this.siteGroupEditData.name;

    // handle case after receiving result
    modalRef.result.then(isDeleted => {
      if (isDeleted) {
        this.siteGroupsService
          .deleteSiteGroup(this.siteGroupEditData.id)
          .subscribe({
            next: () => {
              this.isDeleting$.next(false);
              this.location.back();
              this.showToast('Site group deleted');
            },
          });
      }
      this.isDeleting$.next(false);
    });
  }

  private getPayload() {
    const payload = {
      name: this.siteGroupName,
      companies: this.selectedSuppliers$.getValue(),
      sites: this.currSelectedSites$.getValue(),
    };
    return payload;
  }

  private showToast(message: string) {
    this.toastService.show({
      message,
      count: 1,
    });
  }

  updateSuppliers({ item, action }: SelectionEvent) {
    const supplier = item as Supplier;

    if (action === 'remove') {
      this.selectedSuppliers$.next(
        this.selectedSuppliers$
          .getValue()
          .filter(group => group.id !== supplier.id)
      );
      return;
    }
    this.selectedSuppliers$.next([
      ...this.selectedSuppliers$.getValue(),
      supplier,
    ]);
  }

  onSubmit(): void {
    this.isUpdating$.next(true);
    this.isCreateSite ? this.createSiteGroup() : this.updateSiteGroup();
  }

  isSubmitDisabled(): boolean {
    return this.isCreateSite
      ? !this.isFormValid()
      : !(this.isFormValid() && this.isFormChanged());
  }

  isFormValid(): boolean {
    return Boolean(this.siteGroupName.trim());
  }

  isFormChanged(): boolean {
    const isNameChanged =
      this.siteGroupName.trim() !== this.siteGroupEditData.name.trim();
    const isCompanyChanged = !isEqual(
      this.selectedSuppliers$
        .getValue()
        .map(supplier => supplier.id)
        .sort(),
      this.siteGroupEditData.companies?.map(company => company?.id).sort()
    );
    const isSitesChanged = !isEqual(
      this.currSelectedSites$
        .getValue()
        .map(site => site.id)
        .sort(),
      this.siteGroupEditData.sites?.map(site => site?.id).sort()
    );
    return isNameChanged || isCompanyChanged || isSitesChanged;
  }

  public readonly displayTag = (tag: Tags): string => tag.name;

  public readonly tagFilterFn = (tag: Tags, searchTerm: string): boolean =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase());

  trackBySiteId(_: number, site: SummarySiteGroupResponse): string {
    return site.id;
  }
}
