.import-sites-container {
  .main-card {
    margin-bottom: 4rem;
    border-radius: 0.3rem;
    box-shadow:
      0 0.1rem 0.3rem 0 var(--md-black-20),
      0 0.1rem 0.1rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    background-color: var(--white);

    .header {
      color: var(--md-grey-1000);
      border-top-left-radius: 0.8rem;
      border-top-right-radius: 0.8rem;
      padding: 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .heading {
        font-size: 2rem;
        font-weight: 400;
        line-height: 1;
        display: inline-block;
        text-align: left;
        margin: 0.7rem 0rem;
      }
    }

    .panel-body {
      padding: 1.5rem;
      display: flex;
      align-items: center;

      p {
        margin: 0 0 1.6rem;
        font-size: 1.4rem;
      }
      .delete {
        padding-left: 6rem;
      }

      .custom-button {
        font-size: 1.2rem;
        line-height: 1.5;
        outline: transparent;
        border: transparent;
        background-color: transparent;
        transition: all ease-in 0.1s;
        font-weight: 500;
        position: relative;
        color: var(--md-indigo-600);
        padding: 0 0 1.6rem 0;

        .icon {
          margin-right: 0.3rem;
        }
      }

      .custom-button:hover {
        color: var(--md-dark-blue-200);
      }
    }
  }
}
