.settings-teams-container {
  .tooltip {
    transform: translate(-1.5rem, 3.6rem) !important;
  }
  .items-list {
    padding: 0;
    border-bottom-left-radius: 0.3rem;
    border-bottom-right-radius: 0.3rem;
    box-shadow:
      0 0.1rem 0.1rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);

    .item {
      position: relative;
      display: block;
      margin-bottom: -0.1rem;
      padding: 1rem 1.5rem;
      cursor: pointer;
      border-top: 0.1rem solid var(--md-red-grey-600);
      transition: all ease-in 0.1s;

      .item-row {
        display: flex;
        align-items: center;
        position: relative;
        min-height: 0.1rem;

        .item-name {
          width: 66.666667%;
          font-weight: 500;
        }

        .item-number {
          width: 33.333333%;
        }
      }
      &:hover {
        background-color: var(--md-blue-grey-100);
      }
    }
  }

  .no-results-found {
    font-size: 1.4rem;
    padding: 1.6rem;
    font-weight: 700;
    border-top: 0.1rem solid var(--md-red-grey-600);
    text-align: center;
    color: var(--md-grey-700);
  }
}
