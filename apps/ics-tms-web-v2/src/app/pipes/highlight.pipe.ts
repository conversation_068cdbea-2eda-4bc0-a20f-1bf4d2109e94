import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'highlight' })
export class HighlightPipe implements PipeTransform {
  transform(value: string, search: string): any {
    if (!value) return '';
    if (!search) return value;
    // Removed unnecessary escape for '/'
    const pattern = search.replace(/[-\\^$*+?.()|[\]{}]/g, '\\$&');
    return value.replace(
      new RegExp(pattern, 'gi'),
      match => `<mark>${match}</mark>`
    );
  }
}
