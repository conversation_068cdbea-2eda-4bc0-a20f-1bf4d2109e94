import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, isDevMode, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { JwtModule } from '@auth0/angular-jwt';
import {
  NgbActiveModal,
  NgbDropdownModule,
  NgbModule,
  NgbProgressbarModule,
  NgbToastModule,
  NgbTooltip,
} from '@ng-bootstrap/ng-bootstrap';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { RecaptchaModule } from 'ng-recaptcha';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { CommonDetailsPopupComponent } from './components/common-details-popup/common-details-popup.component';
import { EmptyComponent } from './components/empty-component/empty.component';
import { ForgetPasswordComponent } from './components/forget-password/forget-password.component';
import { ResetEmailSentComponent } from './components/forget-password/reset-email-sent/reset-email-sent.component';
import { IcsSignupComponent } from './components/ics-signup/ics-signup.component';
import { LicensesComponent } from './components/licenses/licenses.component';
import { LoginFooterComponent } from './components/login-footer/login-footer.component';
import { LoginHeaderComponent } from './components/login-header/login-header.component';
import { LoginComponent } from './components/login/login.component';
import { LogoComponent } from './components/logo/logo.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { ReleaseNotesComponent } from './components/release-notes/release-notes.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
import { ModalConfirmationComponent } from './components/shared/ics-stepper-template/modal-confirmation/modal-confirmation.component';
import { ModalMfaComponent } from './components/shared/ics-stepper-template/modal-mfa/modal-mfa.component';
import { TermsAndConditionsComponent } from './components/terms-and-conditions/terms-and-conditions.component';
import { ToastContainerComponent } from './components/toast-container/toast-container.component';
import { TwoFactorComponent } from './components/two-factor/two-factor.component';
import { authInterceptorProviders } from './interceptors/auth.interceptor';
import { SharedCommonModules } from './modules/common.module';
import { MaterialModule } from './modules/material.module';
import { AuthService } from './services/auth.service';
import { SharedModuleModule } from './shared-module/shared-module.module';
import { AppReducer } from './store/app.store';
import { ConsumersEffects } from './store/effects/consumers.effects';
import { DetailsEffects } from './store/effects/details.effects';
import { DeviceSummaryEffects } from './store/effects/device-summary.effects';
import { DeviceTypesEffects } from './store/effects/device-types.effects';
import { DeviceTableEffects } from './store/effects/deviceTable.effects';
import { EntitySettingsEffects } from './store/effects/entity-settings.effects';
import { RemoveDownloadPopupComponent } from './components/shared/ics-menu-template/remove-download-popup/remove-download-popup.component';
import { ModuleSiteModule } from './modules/module-site/module-site.module';
import { HealthStatusEffects } from './store/effects/health-status.effects';
import { SiteGroupsEffects } from './store/effects/site-groups.effects';
import { SitesEffects } from './store/effects/sites.effects';
import { SoftwareEffects } from './store/effects/software.effects';
import { TagssEffects } from './store/effects/tags.effects';
import { UserGroupsEffects } from './store/effects/user-groups.effects';
import { UserPersonasEffects } from './store/effects/user-personas.effects';
import { UserRolesEffects } from './store/effects/user-roles.effects';
import { UsersEffects } from './store/effects/users.effects';
import { DateFormatPipe } from './utils/date-format.pipe';
import { UnreadCountEffects } from './store/effects/unread-notifications.effects';
import { CompanyEffects } from './store/effects/company.effects';
import { SideNavComponent } from './components/side-nav/side-nav.component';
import { CommonHeaderComponent } from './components/common-header/common-header.component';
import { SearchBarComponent } from './components/common-header/search-bar/search-bar.component';
import { IcsProgressBarComponent } from './components/ics-progress-bar/ics-progress-bar.component';
import { MultiSelectTagsComponent } from './components/common-header/search-bar/multi-select-tags/multi-select-tags.component';
import { GlobalStoreReducer } from './store/reducers/globalStore.reducer';
import { ReleaseNotesEffects } from './store/effects/release-notes.effects';
import { deviceTableReducer } from './store/reducers/deviceTable.reducer';
import { SearchResultsComponent } from './components/search-results/search-results.component';

export function tokenGetter() {
  return sessionStorage.getItem('TOKEN_KEY');
}

@NgModule({
  declarations: [
    AppComponent,
    EmptyComponent,
    LoginComponent,
    TwoFactorComponent,
    LoginFooterComponent,
    LogoComponent,
    LoginHeaderComponent,
    ForgetPasswordComponent,
    ResetEmailSentComponent,
    ToastContainerComponent,
    ResetPasswordComponent,
    IcsSignupComponent,
    PrivacyPolicyComponent,
    TermsAndConditionsComponent,
    CommonDetailsPopupComponent,
    ModalConfirmationComponent,
    ModalMfaComponent,
    RemoveDownloadPopupComponent,
    LicensesComponent,
    ReleaseNotesComponent,
    SideNavComponent,
    CommonHeaderComponent,
    SearchBarComponent,
    IcsProgressBarComponent,
    MultiSelectTagsComponent,
    LicensesComponent,
    ReleaseNotesComponent,
    SearchResultsComponent,
  ],

  imports: [
    BrowserModule,
    RecaptchaModule,
    AppRoutingModule,
    CommonModule,
    SharedModuleModule,
    NgbDropdownModule,
    StoreModule.forRoot({ globalState: GlobalStoreReducer }),
    EffectsModule.forRoot([]),
    StoreModule.forFeature('app', AppReducer),
    StoreModule.forFeature('devices', deviceTableReducer),
    EffectsModule.forFeature([
      ConsumersEffects,
      DetailsEffects,
      DeviceSummaryEffects,
      DeviceTypesEffects,
      SiteGroupsEffects,
      SoftwareEffects,
      TagssEffects,
      EntitySettingsEffects,
      SitesEffects,
      UsersEffects,
      UserGroupsEffects,
      UserRolesEffects,
      UserPersonasEffects,
      ReleaseNotesEffects,
      UnreadCountEffects,
      CompanyEffects,
      ReleaseNotesEffects,
      HealthStatusEffects,
      DeviceTableEffects,
    ]),
    StoreDevtoolsModule.instrument({ maxAge: 25, logOnly: !isDevMode() }),
    BrowserAnimationsModule,
    JwtModule.forRoot({
      config: {
        tokenGetter,
        allowedDomains: ['localhost:8080'],
        disallowedRoutes: [],
      },
    }),
    MaterialModule,
    HttpClientModule,
    NgbModule,
    NgbDropdownModule,
    NgbProgressbarModule,
    NgbTooltip,
    SharedCommonModules,
    NgbToastModule,
    ModuleSiteModule,
  ],
  providers: [
    NgbActiveModal,
    authInterceptorProviders,
    AuthService,
    DateFormatPipe,
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}
