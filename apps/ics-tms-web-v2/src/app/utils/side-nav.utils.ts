import { kebabCase } from 'lodash';
import { NavItem, View } from '../models/navItem.model';
import { BANK_USER } from '../modules/module-settings/constants/appConstants';
import { AuthService } from '../services/auth.service';
import {
  FeatureFlags,
  PATH_MATCHERS,
  SECURE_REPORTS_NAV,
  SidebarViewState,
} from '../constants/appConstants';
import {
  NAV_ITEMS,
  Permissions,
  SettingsPathSegments,
} from '../constants/navigationConstants';

function generateHrefPath(segments: string[]): string {
  return `/${segments.join('/')}`;
}

function getUserContext() {
  const company = AuthService.getCompany();
  const roles: string[] = AuthService.getRole();
  return {
    featureFlags: company?.featureFlags ?? [],
    roles,
    companyName: kebabCase(company?.name),
    isBankUser: AuthService.hasRole(BANK_USER),
  };
}

function getFeatureAccessMap(context: ReturnType<typeof getUserContext>) {
  const { featureFlags, roles } = context;

  const hasFeature = (key: string) => featureFlags.includes(key);
  const hasAccess = (permission: string) =>
    AuthService.isAllowedAccess(permission);
  const hasRole = (keyword: string) =>
    roles.some(role => role.includes(keyword));

  return {
    isRemoteManagementAllowed:
      hasFeature(FeatureFlags.REMOTE_MANAGEMENT) &&
      hasAccess(Permissions.REMOTE_MGMT_VIEW) &&
      hasAccess(Permissions.GET_ROLLOUT),

    isConfigManagementAllowed:
      hasFeature(FeatureFlags.CONFIG_MGMT) &&
      hasAccess(Permissions.CONFIG_MGMT_VIEW) &&
      hasAccess(Permissions.VIEW_CONFIG_MGMT_MENU),

    isMediaManagementAllowed:
      hasRole(Permissions.MEDIA) && hasAccess(Permissions.MEDIA_VIEW),

    isPlaylistAllowed:
      [
        FeatureFlags.PLAYLIST,
        FeatureFlags.PLAYLIST_MANAGEMENT,
        FeatureFlags.GSTV,
      ].every(hasFeature) && hasRole(Permissions.PLAYLIST),

    isFuelPriceManagementAllowed:
      hasFeature(FeatureFlags.ENABLE_FUEL_PRICE_MULTISITES) &&
      hasAccess(Permissions.VIEW_FUEL_PRICE_MGMT_MENU),

    isReportManagementAllowed: hasAccess(Permissions.REPORTS_VIEW),

    isRKIAllowed:
      hasAccess(Permissions.GET_RKI) && hasAccess(Permissions.RKI_VIEW),

    isSettingsAllowed:
      hasAccess(Permissions.SETTINGS_VIEW) &&
      hasAccess(Permissions.VIEW_COMPANY_SETTINGS),

    isBulkOperationsAllowed:
      hasAccess(Permissions.BULK_OPERATIONS_VIEW) &&
      hasAccess(Permissions.GET_BULK_OPERATIONS),

    showOnlyMediaSettings:
      hasAccess(Permissions.VIEW_MEDIA_SETTINGS) &&
      !hasAccess(Permissions.WRITE_MEDIA_SETTINGS),
  };
}

function buildNavItems(
  access: ReturnType<typeof getFeatureAccessMap>,
  companyName: string
): NavItem[] {
  const settingsUrl = access.showOnlyMediaSettings
    ? `${companyName}/${PATH_MATCHERS.SETTINGS.join('/')}/${
        SettingsPathSegments.MEDIA
      }`
    : `${companyName}/${PATH_MATCHERS.SETTINGS.join('/')}/${
        SettingsPathSegments.ALARMS
      }`;

  return [
    {
      id: 1,
      name: '',
      featureFlag: FeatureFlags.DEFAULT,
      isAllowed: true,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.DASHBOARD.label,
          href: generateHrefPath(PATH_MATCHERS.DASHBOARD),
          icon: NAV_ITEMS.DASHBOARD.icon,
          isAllowed: true,
          state: SidebarViewState.DASHBOARD,
        },
      ],
    },
    {
      id: 2,
      name: NAV_ITEMS.ASSET_MANAGEMENT.label,
      featureFlag: FeatureFlags.DEFAULT,
      isAllowed: true,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.SITES.label,
          href: generateHrefPath([
            ...PATH_MATCHERS.ASSET_MANAGEMENT,
            ...PATH_MATCHERS.SITES,
          ]),
          icon: NAV_ITEMS.SITES.icon,
          isAllowed: true,
          state: SidebarViewState.ASSET_MANAGEMENT,
        },
        {
          id: 2,
          name: NAV_ITEMS.DEVICES.label,
          href: generateHrefPath(PATH_MATCHERS.DEVICES),
          icon: NAV_ITEMS.DEVICES.icon,
          isAllowed: true,
          state: SidebarViewState.DEVICES,
        },
      ],
    },
    {
      id: 3,
      name: NAV_ITEMS.REMOTE_MANAGEMENT.label,
      featureFlag: FeatureFlags.REMOTE_MGMT,
      isAllowed: access.isRemoteManagementAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.FILE_DOWNLOADS.label,
          href: generateHrefPath(PATH_MATCHERS.REMOTE_DOWNLOADS),
          icon: NAV_ITEMS.FILE_DOWNLOADS.icon,
          isAllowed: true,
          state: SidebarViewState.FILE_DOWNLOADS,
        },
        {
          id: 2,
          name: NAV_ITEMS.OFFLINE_PACKAGES.label,
          href: generateHrefPath(PATH_MATCHERS.REMOTE_PACKAGES),
          icon: NAV_ITEMS.OFFLINE_PACKAGES.icon,
          isAllowed: true,
          state: SidebarViewState.REMOTE_PACKAGES,
        },
        {
          id: 3,
          name: NAV_ITEMS.FILE_LIBRARY.label,
          href: generateHrefPath(PATH_MATCHERS.REMOTE_LIBRARY),
          icon: NAV_ITEMS.FILE_LIBRARY.icon,
          isAllowed: true,
          state: SidebarViewState.FILE_LIBRARY,
        },
      ],
    },
    {
      id: 4,
      featureFlag: FeatureFlags.DEFAULT,
      name: NAV_ITEMS.BULK_OPERATIONS.label,
      isAllowed: access.isBulkOperationsAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.BULK_OPERATIONS.label,
          href: generateHrefPath(PATH_MATCHERS.BULK_OPERATIONS),
          icon: NAV_ITEMS.BULK_OPERATIONS.icon,
          isAllowed: true,
          state: SidebarViewState.BULK_OPERATIONS,
        },
      ],
    },
    {
      id: 5,
      featureFlag: FeatureFlags.DEFAULT,
      name: NAV_ITEMS.CONFIG_MANAGEMENT.label,
      isAllowed: access.isConfigManagementAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.CONFIGURATION.label,
          href: generateHrefPath(PATH_MATCHERS.CONFIG_MANAGEMENT),
          icon: NAV_ITEMS.CONFIGURATION.icon,
          isAllowed: true,
          state: SidebarViewState.CONFIG_MANAGEMENT,
        },
        {
          id: 2,
          name: NAV_ITEMS.DEPLOYMENT.label,
          icon: NAV_ITEMS.DEPLOYMENT.icon,
          href: generateHrefPath([
            ...PATH_MATCHERS.CONFIG_MANAGEMENT,
            ...PATH_MATCHERS.DEPLOYMENT,
          ]),
          isAllowed: true,
          state: SidebarViewState.DEPLOYMENT,
          customClass: NAV_ITEMS.DEPLOYMENT.customClass,
        },
      ],
    },
    {
      id: 6,
      featureFlag: FeatureFlags.DEFAULT,
      name: NAV_ITEMS.FUEL_PRICE_MANAGEMENT.label,
      isAllowed: access.isFuelPriceManagementAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.FUEL_PRICES.label,
          icon: NAV_ITEMS.FUEL_PRICES.icon,
          href: generateHrefPath(PATH_MATCHERS.FUEL_PRICE_MANAGEMENT),
          isAllowed: true,
          state: SidebarViewState.FUEL_PRICE_MANAGEMENT,
        },
      ],
    },
    {
      id: 7,
      name: NAV_ITEMS.MEDIA_MANAGEMENT.label,
      featureFlag: FeatureFlags.MEDIA_MGMT,
      isAllowed: access.isMediaManagementAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.LIBRARY.label,
          href: generateHrefPath(PATH_MATCHERS.MEDIA_LIBRARY),
          icon: NAV_ITEMS.LIBRARY.icon,
          isAllowed: true,
          state: SidebarViewState.MEDIA_LIBRARY,
        },
        {
          id: 2,
          name: NAV_ITEMS.PROMPT_SETS.label,
          href: generateHrefPath(PATH_MATCHERS.PROMPTSETS),
          icon: NAV_ITEMS.PROMPT_SETS.icon,
          isAllowed: true,
          state: SidebarViewState.PROMPTSETS,
        },
        {
          id: 3,
          name: NAV_ITEMS.MEDIA_DOWNLOADS.label,
          href: generateHrefPath([
            ...PATH_MATCHERS.MEDIA,
            ...PATH_MATCHERS.DOWNLOADS,
          ]),
          icon: NAV_ITEMS.MEDIA_DOWNLOADS.icon,
          isAllowed: true,
          state: SidebarViewState.MEDIA_DOWNLOADS,
        },
      ],
    },
    {
      id: 8,
      name: NAV_ITEMS.PLAYLIST_MANAGEMENT.label,
      featureFlag: FeatureFlags.PLAYLIST_MGMT,
      isAllowed: access.isPlaylistAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.CONTENT.label,
          href: generateHrefPath(PATH_MATCHERS.PLAYLIST_CONTENT),
          icon: NAV_ITEMS.CONTENT.icon,
          isAllowed: true,
          state: SidebarViewState.PLAYLIST_CONTENT,
        },
        {
          id: 2,
          name: NAV_ITEMS.COUPONS.label,
          href: generateHrefPath(PATH_MATCHERS.PLAYLIST_COUPON),
          icon: NAV_ITEMS.COUPONS.icon,
          isAllowed: true,
          state: SidebarViewState.PLAYLIST_COUPON,
        },
        {
          id: 3,
          name: NAV_ITEMS.PLAYLISTS.label,
          href: generateHrefPath(PATH_MATCHERS.PLAYLIST),
          icon: NAV_ITEMS.PLAYLISTS.icon,
          isAllowed: true,
          state: SidebarViewState.PLAYLIST,
        },
      ],
    },
    {
      id: 9,
      name: NAV_ITEMS.REPORT_MANAGEMENT.label,
      featureFlag: FeatureFlags.DEFAULT,
      isAllowed: access.isReportManagementAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.REPORTING.label,
          icon: NAV_ITEMS.REPORTING.icon,
          featureFlag: FeatureFlags.REPORTING,
          href: generateHrefPath(PATH_MATCHERS.REPORT_MANAGEMENT),
          isAllowed: true,
          state: SidebarViewState.REPORT_MANAGEMENT,
        },
        {
          id: 2,
          name: NAV_ITEMS.SCHEDULE_LIST.label,
          icon: NAV_ITEMS.SCHEDULE_LIST.icon,
          featureFlag: FeatureFlags.REPORTING,
          href: generateHrefPath(PATH_MATCHERS.SCHEDULE_LIST),
          isAllowed: true,
          state: SidebarViewState.SCHEDULE_LIST,
        },
      ],
    },
    {
      id: 10,
      name: NAV_ITEMS.REMOTE_KEY_INJECTION.label,
      featureFlag: FeatureFlags.DEFAULT,
      isAllowed: access.isRKIAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.RKI.label,
          href: generateHrefPath(PATH_MATCHERS.RKI),
          icon: NAV_ITEMS.RKI.icon,
          isAllowed: true,
          state: SidebarViewState.RKI,
        },
      ],
    },
    {
      id: 11,
      name: NAV_ITEMS.SETTINGS.label,
      featureFlag: FeatureFlags.DEFAULT,
      isAllowed: access.isSettingsAllowed,
      views: [
        {
          id: 1,
          name: NAV_ITEMS.SETTINGS.label,
          href: settingsUrl,
          icon: NAV_ITEMS.SETTINGS.icon,
          isAllowed: true,
          state: SidebarViewState.SETTINGS,
        },
      ],
    },
  ];
}

export function getSideNavItems(): NavItem[] {
  const context = getUserContext();
  const accessMap = getFeatureAccessMap(context);
  const navItems = buildNavItems(accessMap, context.companyName);

  return context.isBankUser ? SECURE_REPORTS_NAV : navItems;
}

export function isNavigationItemActive(view: View): boolean {
  const pathSegments = window.location.pathname.split('/');

  const hasAll = (segments: string[]) =>
    segments.every(s => pathSegments.includes(s));

  const hasAny = (segments: string[]) =>
    segments.some(s => pathSegments.includes(s));

  switch (view.state) {
    case SidebarViewState.SECURE_REPORTS:
      return hasAll(PATH_MATCHERS.SECURE_REPORTS);
    case SidebarViewState.DASHBOARD:
      return hasAll(PATH_MATCHERS.DASHBOARD);

    case SidebarViewState.ASSET_MANAGEMENT:
      return (
        hasAll(PATH_MATCHERS.ASSET_MANAGEMENT) ||
        hasAll(PATH_MATCHERS.SITES_ADD)
      );

    case SidebarViewState.DEVICES:
      return (
        !hasAll(PATH_MATCHERS.ASSET_MANAGEMENT) &&
        (hasAll(PATH_MATCHERS.DEVICES) ||
          (hasAll(PATH_MATCHERS.SITES) &&
            hasAny(PATH_MATCHERS.DEVICES_UNDER_SITES)))
      );

    case SidebarViewState.FILE_DOWNLOADS:
      return hasAll(PATH_MATCHERS.REMOTE_DOWNLOADS);

    case SidebarViewState.REMOTE_PACKAGES:
      return hasAll(PATH_MATCHERS.REMOTE_PACKAGES);

    case SidebarViewState.FILE_LIBRARY:
      return hasAll(PATH_MATCHERS.REMOTE_LIBRARY);

    case SidebarViewState.BULK_OPERATIONS:
      return hasAll(PATH_MATCHERS.BULK_OPERATIONS);

    case SidebarViewState.CONFIG_MANAGEMENT:
      return hasAll(PATH_MATCHERS.CONFIG_MANAGEMENT);

    case SidebarViewState.FUEL_PRICE_MANAGEMENT:
      return hasAll(PATH_MATCHERS.FUEL_PRICE_MANAGEMENT);

    case SidebarViewState.MEDIA_LIBRARY:
      return hasAll(PATH_MATCHERS.MEDIA_LIBRARY);

    case SidebarViewState.PROMPTSETS:
      return hasAll(PATH_MATCHERS.PROMPTSETS);

    case SidebarViewState.MEDIA_DOWNLOADS:
      return (
        hasAll(PATH_MATCHERS.MEDIA) && hasAny(PATH_MATCHERS.MEDIA_DOWNLOADS)
      );

    case SidebarViewState.PLAYLIST_CONTENT:
      return hasAll(PATH_MATCHERS.PLAYLIST_CONTENT);

    case SidebarViewState.PLAYLIST_COUPON:
      return hasAll(PATH_MATCHERS.PLAYLIST_COUPON);

    case SidebarViewState.PLAYLIST:
      return (
        hasAll(PATH_MATCHERS.PLAYLIST) &&
        (pathSegments.length === 1 || hasAll(PATH_MATCHERS.PLAYLIST_BUILDER))
      );

    case SidebarViewState.REPORT_MANAGEMENT:
      return hasAll(PATH_MATCHERS.REPORT_MANAGEMENT);

    case SidebarViewState.SCHEDULE_LIST:
      return hasAll(PATH_MATCHERS.SCHEDULE_LIST);

    case SidebarViewState.RKI:
      return hasAll(PATH_MATCHERS.RKI);

    case SidebarViewState.SETTINGS:
      return hasAll(PATH_MATCHERS.SETTINGS) && !hasAll(PATH_MATCHERS.ACCOUNT);

    default:
      return false;
  }
}
