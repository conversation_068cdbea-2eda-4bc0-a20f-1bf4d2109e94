{"name": "@ics/tms-web-v2", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 3000", "build": "ng build ics-tms-web-v2 --configuration production", "ng-build": "ng build", "watch": "ng build --watch --configuration development", "json:server": "json-server --watch db.json", "format": "prettier --write './**'", "lint": "eslint \".\" --ext .ts,.tsx", "lint:fix": "eslint \".\" --ext .ts,.tsx --fix", "build:single-spa:ics-tms-web-v2": "ng build ics-tms-web-v2 --configuration production", "serve:single-spa:ics-tms-web-v2": "ng s --project ics-tms-web-v2 --disable-host-check --port 4200 --live-reload false", "test": "ng test", "test:watch": "ng test --watch", "test:coverage": "ng test --no-watch --code-coverage"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.12", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.12", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "16.2.12", "@auth0/angular-jwt": "^5.2.0", "@ng-bootstrap/ng-bootstrap": "^15.1.2", "@ng-select/ng-option-highlight": "15.1.3", "@ng-select/ng-select": "^11.2.0", "@ngrx/effects": "^16.3.0", "@ngrx/store": "^16.3.0", "@ngrx/store-devtools": "^16.3.0", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.2", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "json-server": "^0.17.4", "jwt-decode": "^4.0.0", "lodash": "4.17.21", "ng-recaptcha": "13.2.1", "ngrx": "^0.0.1-security", "ngx-bootstrap": "^11.0.2", "ngx-cookie-service": "19.0.0", "ngx-infinite-scroll": "19.0.0", "ngx-pagination": "^6.0.3", "rxjs": "~7.8.0", "semver": "^7.6.0", "single-spa": ">=4.0.0", "single-spa-angular": "9.0.1", "tslib": "^2.3.0", "webpack": "5.90.1", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-builders/custom-webpack": "16.0.1", "@angular-devkit/build-angular": "^16.2.10", "@angular-eslint/schematics": "19.2.1", "@angular/cli": "^16.2.10", "@angular/compiler-cli": "^16.2.0", "@angular/localize": "^16.2.0", "@invenco-cloud-systems-ics/eslint-config": "2.2.4", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~4.3.0", "@types/lodash": "4.17.13", "@typescript-eslint/eslint-plugin": "7.7.0", "@typescript-eslint/parser": "7.7.0", "eslint": "8.57.0", "jasmine-core": "~4.6.0", "jasmine-marbles": "^0.9.2", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "style-loader": "^3.3.1", "typescript": "~5.1.3"}}