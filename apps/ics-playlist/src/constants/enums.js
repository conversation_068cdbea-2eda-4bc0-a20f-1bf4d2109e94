const CouponType = {
  Text: 'Text',
  Graphic: 'Graphic',
};

const ContainerType = {
  Text: 'TEXT',
  Graphical: 'GRAPHICAL',
};

const CouponError = {
  couponName: 'Coupon name is required and must be 2-50 characters',
  productName: 'Product name is required and must be 2-50 characters',
  couponValue: 'Coupon value is required and must be 2-50 characters',
  startDate: 'Start date must be within 30 days from the current date',
  endDate: 'End date must be within 60 days from the start date',
  startDateRequired: 'Start date is required',
  endDateRequired: 'End date is required',
  endDateBeforeStartDate: 'End date cannot be before the start date',
  endDateExceedsLimit: 'End date must be within 60 days from the start date',
  endDateEqualsStartDate: 'End date cannot be the same as the start date',
  purchaseRestriction: 'Purchase restriction must be 2-50 characters',
  termsOfOffer: 'Terms of offer must be 2-50 characters',
  couponBarcode: 'Barcode must be exactly 12 digits (UPC format)',
  assetUrl:
    'Select an unassigned video asset from the media library to link to this coupon',
  productImageDetails: 'Select a product image to display on this coupon',
  logoImageDetails: 'Select a logo image to display on this coupon',
  couponImageDetails:
    'Select an image from the media library to be displayed on the coupon',
  displayImageDetails:
    'Select an image from the media library to be displayed on the coupon as display image',
  couponCreatedMessage: 'Coupon successfully created',
  couponCreateFailedMessage:
    'Failed to create coupon due to an unexpected error',
  couponEditedMessage: 'Coupon successfully edited',
  couponEditFailedMessage: 'Failed to edit coupon due to an unexpected error',
  couponDeletedMessage: 'Coupon successfully deleted',
  couponDeleteFailedMessage:
    'Unable to delete the coupon. Something went wrong',
  slotsInvalid: 'All slots must be completely filled before submitting',
};

const ImageType = {
  Product: 'Product',
  Logo: 'Logo',
  Coupon: 'Coupon',
  DeviceDisplayImage: 'DeviceDisplayImage',
};

const SupportedFileTypes = ['image/jpeg', 'image/png', 'image/gif'];
const SupportedFileExt = ['jpeg', 'jpg', 'png', 'gif'];
const BitmapFileTypes = ['image/bmp'];
const BitmapFileExt = ['bmp'];

export {
  CouponType,
  ContainerType,
  CouponError,
  ImageType,
  SupportedFileTypes,
  SupportedFileExt,
  BitmapFileTypes,
  BitmapFileExt,
};
