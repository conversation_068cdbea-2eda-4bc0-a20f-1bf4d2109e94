/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-filename-extension */
/* eslint-disable no-empty */
import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import Dropzone from 'react-dropzone';

import { Button } from 'react-bootstrap';
import { actions as contentActions } from '../../actions/content';
import { actions as viewActions } from '../../actions/view';
import ScrollToTop from '../../components/ScrollToTop';
import { getPresignedImageUrl } from '../../api/image';
import {
  ImageType,
  SupportedFileExt,
  SupportedFileTypes,
  BitmapFileTypes,
  BitmapFileExt,
} from '../../constants/enums';
import './ImageContent.scss';

// Function to check if a file is a bitmap
function isBitmapFormat(file) {
  return file.type === 'image/bmp' || file.name.toLowerCase().endsWith('.bmp');
}

// Function to check bitmap depth and dimensions
async function checkBitmapDepthAndWidth(file) {
  return new Promise((resolve, reject) => {
    if (!isBitmapFormat(file)) {
      reject(new Error('Not a bitmap file'));
      return;
    }

    const reader = new FileReader();
    reader.onload = e => {
      const buffer = e.target.result;
      const dataView = new DataView(buffer);

      // BMP header structure:
      // Offset 0: BM signature (2 bytes)
      // Offset 14: DIB header size (4 bytes)
      // Offset 28: Bits per pixel (2 bytes)
      // Offset 18: Width (4 bytes)

      try {
        // Check BMP signature
        const signature =
          String.fromCharCode(dataView.getUint8(0)) +
          String.fromCharCode(dataView.getUint8(1));
        if (signature !== 'BM') {
          reject(new Error('Invalid BMP file signature'));
          return;
        }

        // Get bits per pixel (color depth)
        const bitsPerPixel = dataView.getUint16(28, true);

        // Get width
        const width = dataView.getInt32(18, true);

        resolve({
          depth: bitsPerPixel,
          width,
          isValidDepth: bitsPerPixel === 1, // Check if it's 1-bit depth
          isValidWidth: width <= 368, // Check if width is 368px or less
        });
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };

    reader.readAsArrayBuffer(file);
  });
}

async function validateFile(file, couponImageType = 'logo') {
  const fileUploadStatsArray = [];
  const fileExt = file.name.split('.').pop().toLowerCase();
  let fileUploadStats = {};
  let hasError = false;
  const isDeviceDisplayImage =
    couponImageType.toLowerCase() === 'devicedisplayimage';
  const isBitmapRequired =
    couponImageType.toLowerCase() === 'logo' ||
    couponImageType.toLowerCase() === 'product' ||
    couponImageType.toLowerCase() === 'coupon';

  // For bitmap required images, first check if it's a bitmap file
  if (isBitmapRequired) {
    // Check if the file is a bitmap format
    if (!isBitmapFormat(file)) {
      fileUploadStats = {
        text: `${file.name} must be a bitmap (.bmp) file`,
        level: 'danger',
      };
      fileUploadStatsArray.push(fileUploadStats);
      hasError = true;
    } else {
      // If it is a bitmap, check depth and width
      try {
        const bmpInfo = await checkBitmapDepthAndWidth(file);

        // Check bitmap depth
        if (!bmpInfo.isValidDepth) {
          fileUploadStats = {
            text: `${file.name} has ${bmpInfo.depth}-bit depth. Only 1-bit depth bitmap files are accepted.`,
            level: 'danger',
          };
          fileUploadStatsArray.push(fileUploadStats);
          hasError = true;
        }

        // Check bitmap width
        if (!bmpInfo.isValidWidth) {
          fileUploadStats = {
            text: `${file.name} width must be 368 pixels or less (current: ${bmpInfo.width}px)`,
            level: 'danger',
          };
          fileUploadStatsArray.push(fileUploadStats);
          hasError = true;
        }
      } catch (error) {
        console.error('Error checking bitmap properties:', error);
        // If we can't validate the bitmap properties, reject the file
        fileUploadStats = {
          text: `${file.name} bitmap depth is larger than expected. Only 1-bit depth bitmap files are accepted.`,
          level: 'danger',
        };
        fileUploadStatsArray.push(fileUploadStats);
        hasError = true;
      }
    }
  }

  // validate file resolution
  const validateImageResolution = () =>
    new Promise((resolve, reject) => {
      const image = new Image();

      // For bitmap required images, we only check width (368px max)
      // For device display images, we check both width and height
      const maxImageResolution = {
        bitmap: {
          width: 368, // Only check width for bitmap images
        },
        deviceDisplayImage: {
          height: 400,
          width: 1000,
        },
      };

      const maxResolution = isDeviceDisplayImage
        ? maxImageResolution.deviceDisplayImage
        : maxImageResolution.bitmap;

      image.addEventListener('load', () => {
        if (isDeviceDisplayImage) {
          // For device display images, check both width and height
          if (image.height > maxResolution.height) {
            fileUploadStats = {
              text: `${file.name} exceeds the file resolution limit of 1000x400 px`,
              level: 'danger',
            };
            fileUploadStatsArray.push(fileUploadStats);
            hasError = true;
            resolve(fileUploadStats);
          } else if (image.width > maxResolution.width) {
            fileUploadStats = {
              text: `${file.name} exceeds the file resolution limit of 1000x400 px`,
              level: 'danger',
            };
            fileUploadStatsArray.push(fileUploadStats);
            hasError = true;
            resolve(fileUploadStats);
          } else {
            reject();
          }
        } else if (image.width > maxResolution.width) {
          // For bitmap images, check only width
          fileUploadStats = {
            text: `${file.name} width exceeds the limit of 368 px (current: ${image.width}px)`,
            level: 'danger',
          };
          fileUploadStatsArray.push(fileUploadStats);
          hasError = true;
          resolve(fileUploadStats);
        } else {
          reject();
        }
      });
      image.src = URL.createObjectURL(file);
    });

  try {
    await validateImageResolution();
  } catch (error) {}

  // validate file's size
  if (file.size <= 0) {
    fileUploadStats = {
      text: `File size is invalid for ${file.name}`,
      level: 'danger',
    };
    fileUploadStatsArray.push(fileUploadStats);
    hasError = true;
  }

  // validate file name's length, should have minimum of 3 chars
  if (file.name.length < 3) {
    fileUploadStats = {
      text: `File name is too short for ${file.name}`,
      level: 'danger',
    };
    fileUploadStatsArray.push(fileUploadStats);
    hasError = true;
  }

  // Use appropriate file types based on image type
  const supportedTypes = isBitmapRequired
    ? BitmapFileTypes
    : SupportedFileTypes;
  const supportedExt = isBitmapRequired ? BitmapFileExt : SupportedFileExt;

  // validate file types here or maybe validate from API response
  if (file.type && !supportedTypes.includes(file.type)) {
    fileUploadStats = {
      text: `${file.name} ${isBitmapRequired ? 'must be a bitmap file' : 'is not a supported file'}`,
      level: 'danger',
    };
    fileUploadStatsArray.push(fileUploadStats);
    hasError = true;
  } else if (fileExt && !supportedExt.includes(fileExt)) {
    // for IE11, validate by file's extension
    fileUploadStats = {
      text: `${file.name} ${isBitmapRequired ? 'must be a bitmap file' : 'is not a supported file'}`,
      level: 'danger',
    };
    fileUploadStatsArray.push(fileUploadStats);
    hasError = true;
  }

  // check file size
  const logoProductCouponImageSize = 2 * 1024 * 1024;
  const deviceDisplayImageSize = 5 * 1024 * 1024;
  const maxFileSize = isDeviceDisplayImage
    ? deviceDisplayImageSize
    : logoProductCouponImageSize;
  if (file.size >= maxFileSize) {
    fileUploadStats = {
      text: `${file.name} exceeds the file size limit of ${isDeviceDisplayImage ? '5MB' : '2MB'}`,
      level: 'danger',
    };
    fileUploadStatsArray.push(fileUploadStats);
    hasError = true;
  }

  return {
    fileUploadStatsArray,
    hasError,
  };
}

class ImageContent extends React.Component {
  constructor(props) {
    super(props);

    this.onDrop = this.onDrop.bind(this);

    this.uploadButtonLabels = {
      Select: 'Select Image',
      Add: 'Add Image',
      Change: 'Change Image',
    };

    this.state = {
      accept: '',
      toastData: null,
      imageType: props.imageType,
      customText: props.customText,
      handleImageSelect: props.handleImageSelect,
      productUploadLabel: props.isEditMode
        ? this.uploadButtonLabels.Change
        : this.uploadButtonLabels.Add,
      logoUploadLabel: props.isEditMode
        ? this.uploadButtonLabels.Change
        : this.uploadButtonLabels.Add,
      couponImageUploadLabel: props.isEditMode
        ? `Change ${props.customText ? props.customText : 'Image'}${props.isCustomTextRequired ? '*' : ''}`
        : `Add ${props.customText ? props.customText : 'Image'}${props.isCustomTextRequired ? '*' : ''}`,
      displayImageUploadLabel: props.isEditMode
        ? `Change ${props.customText ? props.customText : 'Image'}${props.isCustomTextRequired ? '*' : ''}`
        : `Add ${props.customText ? props.customText : 'Image'}${props.isCustomTextRequired ? '*' : ''}`,
    };
  }

  // eslint-disable-next-line react/no-deprecated
  componentWillUpdate(nextProps, nextState) {
    if (
      (nextProps.isUploading !== this.props.isUploading &&
        !nextProps.isUploading) ||
      (!nextProps.isUploading &&
        nextState.toastData !== this.state.toastData &&
        nextState.toastData)
    ) {
      if (nextState.toastData != null) {
        this.props.showToast(nextState.toastData);
      }
      // eslint-disable-next-line react/no-will-update-set-state
      this.setState({ toastData: null });
    }
  }

  onDrop(files) {
    if (files && files.length > 0) {
      const placeHolderData = [];
      let fileUploadStatsArray = [];
      let uploadedCount = 0;
      const tmpImages = [];

      files.forEach(async (file, idx) => {
        const fileContent = file;

        const fileExt = file.name.split('.').pop();
        let hasError = false;

        const isImageFile =
          SupportedFileTypes.includes(file.type) ||
          SupportedFileExt.includes(fileExt.toLowerCase());
        const results = await validateFile(
          file,
          this.state.imageType.toLowerCase()
        );
        fileUploadStatsArray = [
          ...results.fileUploadStatsArray,
          ...fileUploadStatsArray,
        ];
        hasError = results.hasError;

        if (!hasError) {
          if (isImageFile) {
            const imgData = {
              id: `img-${idx}`,
              name: file.name,
              thumbnailFileUrl: file.preview,
              length: 5,
              fileContent,
            };

            tmpImages.push(imgData);
          }

          getPresignedImageUrl({
            contentType: file.type,
            mediaType: this.state.imageType.toLowerCase(),
          }).then(async data => {
            const uploadUrl = data.url;
            this.state.handleImageSelect(
              { presignedImageUrl: uploadUrl, imageFile: file },
              this.state.imageType
            );

            if (this.state.imageType === ImageType.Product) {
              this.setState({
                productUploadLabel: this.uploadButtonLabels.Change,
              });
            } else if (this.state.imageType === ImageType.Logo) {
              this.setState({
                logoUploadLabel: this.uploadButtonLabels.Change,
              });
            } else if (this.state.imageType === ImageType.Coupon) {
              this.setState(prevState => ({
                couponImageUploadLabel: `Change ${prevState.customText ? prevState.customText : 'Image'}`,
              }));
            } else if (this.state.imageType === ImageType.DeviceDisplayImage) {
              this.setState(prevState => ({
                displayImageUploadLabel: `Change ${prevState.customText ? prevState.customText : 'Image'}`,
              }));
            }

            fileUploadStatsArray.push({
              text: `${file.name} was successfully uploaded`,
              level: 'success',
            });
            uploadedCount += 1;

            const toastData = {
              message: `${uploadedCount} of ${files.length} item(s) uploaded`,
              show: true,
            };
            if (fileUploadStatsArray.length > 0) {
              toastData.sub = fileUploadStatsArray;
            }

            if (placeHolderData.length > 0) {
              this.props.setTmpFiles(placeHolderData);
            }

            this.setState({
              toastData,
            });
          });
        } else {
          const toastData = {
            message: `${uploadedCount} of ${files.length} item(s) uploaded`,
            show: true,
          };
          if (fileUploadStatsArray.length > 0) {
            toastData.sub = fileUploadStatsArray;
          }

          if (placeHolderData.length > 0) {
            this.props.setTmpFiles(placeHolderData);
          }

          this.setState({
            toastData,
          });
        }
      });
    }
  }

  mappedButtonLabel = () => {
    switch (this.state.imageType) {
      case ImageType.Product:
        return this.state.productUploadLabel;
      case ImageType.Logo:
        return this.state.logoUploadLabel;
      case ImageType.Coupon:
        return this.state.couponImageUploadLabel;
      case ImageType.DeviceDisplayImage:
        return this.state.displayImageUploadLabel;
      default:
        break;
    }
    return null;
  };

  render() {
    const { accept } = this.state;

    return (
      <Dropzone
        multiple={false}
        disableClick
        style={{ position: 'relative' }}
        accept={accept}
        onDrop={this.onDrop}
        onDragEnter={this.onDragEnter}
        onDragLeave={this.onDragLeave}
        ref={node => {
          this.dropzoneRef = node;
        }}
        acceptClassName='dropzone-accept'
      >
        <Button
          disabled={this.props?.disabled}
          bsStyle='secondary'
          className='btn-sm coupon-button'
          onClick={() => {
            this.dropzoneRef.open();
          }}
        >
          {this.mappedButtonLabel()}
        </Button>
        <ScrollToTop />
      </Dropzone>
    );
  }
}

ImageContent.propTypes = {
  setTmpFiles: PropTypes.func,
  showToast: PropTypes.func,
  isUploading: PropTypes.bool,
  handleImageSelect: PropTypes.func,
  imageType: PropTypes.string,
  customText: PropTypes.string,
  isEditMode: PropTypes.bool,
  isCustomTextRequired: PropTypes.bool,
  disabled: PropTypes.bool,
};

function mapStateToProps({ view, content }) {
  return {
    ...view,
    ...content,
  };
}

export default connect(mapStateToProps, {
  setTmpFiles: contentActions.setTmpFiles,
  showToast: viewActions.showToast,
})(ImageContent);
