/* eslint-disable react/forbid-prop-types */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable react/button-has-type */
import React, { useState, useEffect } from 'react';
import {
  Grid,
  Row,
  Col,
  Button,
  FormGroup,
  ControlLabel,
  FormControl,
  Collapse,
  OverlayTrigger,
  Tooltip,
} from 'react-bootstrap';
import PropTypes from 'prop-types';
import Drawer from 'react-modern-drawer';
import { formatDate, parseDate } from 'react-day-picker/moment';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom'; // Ensure this is imported
import { compose } from 'redux'; //
import LazyImg from '../../../components/LazyImg';
import './MultiCoupon.scss';
import 'react-modern-drawer/dist/index.css';
import { CouponType, ImageType } from '../../../constants/enums';
import TextCouponForm from '../Forms/TextCouponForm';
import {
  getLastSegmentWithLimit,
  processUploadsAndTrim,
  truncateString,
} from '../Forms/Helpers';
import LinkMediaModal from '../../../components/LinkMediaModal';
import DayPicker from '../../../components/DayPicker';
import ImageContent from '../../ImageContent';
import { actions as couponActions } from '../../../actions/coupon';
import { history } from '../../../index';
import {
  validateMultiCoupon,
  validateSlotsOnly,
} from '../Forms/Validators/formValidator';

const customStyles = {
  drawer: {
    marginTop: '50px',
    padding: '10px',
    width: '30%',
    overflow: 'auto',
  },
};

function arraysEqual(a, b) {
  if (a === b) return true;
  if (!Array.isArray(a) || !Array.isArray(b)) return false;
  if (a.length !== b.length) return false;
  return a.every((v, i) => v === b[i]);
}

const slotPositionsMap = {
  1: [['sl1', 'sr1', 'sl2', 'sr2', 'sl3', 'sr3', 'sl4', 'sr4']],
  4: [
    ['sl1', 'sl2'],
    ['sr1', 'sr2'],
    ['sl3', 'sl4'],
    ['sr3', 'sr4'],
  ],
  6: [['sl1'], ['sr1'], ['sl2'], ['sr2'], ['sl3', 'sl4'], ['sr3', 'sr4']],
  8: [['sl1'], ['sr1'], ['sl2'], ['sr2'], ['sl3'], ['sr3'], ['sl4'], ['sr4']],
};

const textFormFieldDetails = {
  productName: '',
  couponValue: '',
  purchaseRestriction: '',
  termsOfOffer: '',
  productImageUrl: '',
  logoImageUrl: '',
  couponBarcode: '',
  deviceDisplayImageUrl: '',
};

const graphicFormFieldDetails = {
  couponImageUrl: '',
  deviceDisplayImageUrl: '',
};

const MultiCoupon = props => {
  const { match, createTextCoupon } = props;
  const isTextCoupon = match?.params?.type === 'text';
  const containerType = isTextCoupon ? CouponType.Text : 'Graphical';
  const couponLabel = isTextCoupon ? 'Text' : 'Graphic';
  const dateDisplayFormat = 'MM/DD/YYYY';
  // CHANGE: Use single state object for common form fields
  const initialData = {
    couponName: '',
    assetId: '',
    assetUrl: '',
    startDate: new Date(),
    endDate: new Date(),
  };
  const [formData, setFormData] = useState(initialData);
  const [selectedVideo, setSelectedVideo] = useState({});
  const [selectedImage, setSelectedImage] = useState({});
  const [isLinkedModalOpen, setIsLinkedModalOpen] = useState(false);
  const [modalImageType, setModalImageType] = useState(null);
  const [formErrors, setFormError] = useState({});

  const [touchedFields, setTouchedFields] = useState({});

  // per-slot form data
  const [displayCount, setDisplayCount] = useState(4);
  const [slotsData, setSlotsData] = useState([]);
  const [isDrawerOpen, setDrawerOpen] = useState(false);
  const [currentSlotPosition, setCurrentSlotPosition] = useState(null);
  const [editData, setEditData] = useState({});

  const handleSlotSave = (position, updates) => {
    setSlotsData(prev =>
      prev.map(slot => {
        if (arraysEqual(slot.slotPosition, position)) {
          return {
            ...slot,
            ...updates,
            ...(updates?.productImageDetails && {
              productImageDetails: updates?.productImageDetails,
            }),
            ...(updates?.productImageDetails && {
              productImageUrl: updates?.productImageDetails?.url,
            }),
            ...(updates?.logoImageDetails && {
              logoImageDetails: updates?.logoImageDetails,
            }),
            ...(updates?.logoImageDetails && {
              logoImageUrl: updates?.logoImageDetails?.url,
            }),
            ...(updates?.displayImageDetails && {
              deviceDisplayImageUrl: updates?.displayImageDetails?.url,
            }),
          };
        }
        return slot;
      })
    );
  };

  const shallowEqual = (fData, iData) => {
    const extractDate = d => new Date(d).toISOString().substring(0, 10);
    const formatToCompare = ({ startDate, endDate, ...rest }) => ({
      ...rest,
      startDate: extractDate(startDate),
      endDate: extractDate(endDate),
    });
    return (
      JSON.stringify(formatToCompare(fData)) ===
      JSON.stringify(formatToCompare(iData))
    );
  };

  const getSlotData = (couponCount, isText) => {
    const positions = slotPositionsMap[couponCount];
    const template = isText ? textFormFieldDetails : graphicFormFieldDetails;

    return positions.map(pos => ({
      ...template,
      slotPosition: pos,
    }));
  };

  useEffect(() => {
    setSlotsData(getSlotData(displayCount, isTextCoupon));
  }, [displayCount, isTextCoupon]);

  useEffect(() => {
    if (Object.keys(selectedVideo || {}).length) {
      setFormData(prevState => ({
        ...prevState,
        assetUrl: selectedVideo.sourceFileUrl,
        assetId: selectedVideo.id,
      }));
    }
  }, [selectedVideo]);

  // this will run for multi graphic coupon
  useEffect(() => {
    if (selectedImage?.url) {
      if (modalImageType === ImageType.Product) {
        handleSlotSave(currentSlotPosition, {
          productImageDetails: selectedImage,
          productImageUrl: selectedImage.url,
        });
      } else if (modalImageType === ImageType.Logo) {
        handleSlotSave(currentSlotPosition, {
          logoImageDetails: selectedImage,
          logoImageUrl: selectedImage.url,
        });
      } else if (modalImageType === ImageType.Coupon) {
        handleSlotSave(currentSlotPosition, {
          couponImageDetails: selectedImage,
          couponImageUrl: selectedImage.url,
        });
      } else if (modalImageType === ImageType.DeviceDisplayImage) {
        handleSlotSave(currentSlotPosition, {
          displayImageDetails: selectedImage,
          deviceDisplayImageUrl: selectedImage.url,
        });
      }
    }
  }, [selectedImage]);

  useEffect(() => {
    const errors = validateSlotsOnly(containerType, displayCount, slotsData);
    if (!Object.keys(errors || {}).length) {
      setFormError(errors);
    }
  }, [slotsData]);

  const handleOpenLinkedModal = () => {
    setIsLinkedModalOpen(true);
    setTouchedFields(prev => ({ ...prev, assetUrl: true }));
  };

  const handleMediaSelect = video => {
    setSelectedVideo(video);
    setFormData(prev => {
      const updatedFormData = { ...prev, assetUrl: video.sourceFileUrl };
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        const newErrors = validateMultiCoupon(
          updatedFormData,
          containerType,
          'assetUrl'
        );
        delete updatedErrors.assetUrl;
        return { ...updatedErrors, ...newErrors };
      });
      return updatedFormData;
    });
    setIsLinkedModalOpen(false);
  };

  // for multi graphic coupon
  const handleImageSelectCallback = (imageDetails, imageType) => {
    setModalImageType(imageType);
    setSelectedImage({
      url: imageDetails?.presignedImageUrl,
      imageFile: imageDetails?.imageFile,
    });
  };

  const handleStartDayChange = selectedDay => {
    setTouchedFields(prevTouched => ({ ...prevTouched, startDate: true }));
    setFormData(prevData => {
      // Check if we need to adjust the end date
      let { endDate } = prevData;

      // If end date exists and is before the new start date, set end date equal to start date
      if (endDate && new Date(selectedDay) > new Date(endDate)) {
        endDate = selectedDay;
        // Also mark end date as touched since we're auto-updating it
        setTouchedFields(current => ({ ...current, endDate: true }));
      }

      const updatedFormData = {
        ...prevData,
        startDate: selectedDay,
        endDate,
      };

      const newErrors = validateMultiCoupon(updatedFormData, containerType, [
        'startDate',
        'endDate',
      ]);
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        // Remove any existing date errors
        delete updatedErrors.startDate;
        delete updatedErrors.endDate;
        // Add new errors if any
        return { ...updatedErrors, ...newErrors };
      });

      return updatedFormData;
    });
  };

  const handleEndDayChange = selectedDay => {
    setTouchedFields(prevTouched => ({ ...prevTouched, endDate: true }));
    setFormData(prevData => {
      const updatedFormData = { ...prevData, endDate: selectedDay };

      const newErrors = validateMultiCoupon(updatedFormData, containerType, [
        'startDate',
        'endDate',
      ]);
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        // Remove any existing date errors
        delete updatedErrors.startDate;
        delete updatedErrors.endDate;
        // Add new errors if any
        return { ...updatedErrors, ...newErrors };
      });

      return updatedFormData;
    });
  };

  const handleInputChange = (fieldName, value) => {
    setTouchedFields(prevTouched => ({ ...prevTouched, [fieldName]: true }));
    setFormData(prevData => {
      const updatedFormData = { ...prevData, [fieldName]: value };

      const newErrors = validateMultiCoupon(
        updatedFormData,
        containerType,
        fieldName
      );
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        delete updatedErrors[fieldName];
        return { ...updatedErrors, ...newErrors };
      });

      return updatedFormData;
    });
  };

  // handle display count change with confirmation
  const handleCouponCountChange = newCount => {
    const hasData = slotsData.some(slot =>
      Object.entries(slot)
        // Exclude the slotPosition field
        .filter(([key]) => key !== 'slotPosition')
        .filter(([key]) => key !== 'kind')
        // Check if any remaining value is non-empty
        .some(([, val]) => val && val !== '')
    );
    if (hasData) {
      /* eslint no-alert: 0 */
      const ok = window.confirm(
        'Changing number of coupons will reset entered coupon details. Continue?'
      );
      if (!ok) return;
    }
    setDisplayCount(newCount);
  };

  const handleCancelClick = () => {
    const hasData = slotsData.some(slot =>
      Object.entries(slot)
        // Exclude the slotPosition field
        .filter(([key]) => key !== 'slotPosition')
        .filter(([key]) => key !== 'kind')
        // Check if any remaining value is non-empty
        .some(([, val]) => val && val !== '')
    );
    if (!shallowEqual(formData, initialData) || hasData) {
      /* eslint-disable no-alert */
      const ok = window.confirm(
        'You have unsaved changes, are you sure you want to leave without saving?'
      );
      /* eslint-enable no-alert */
      if (!ok) return;
    }
    setSlotsData(getSlotData(displayCount, isTextCoupon));
    setFormData(initialData);
    setFormError({});
    setTouchedFields({});
    history.push('/playlist/coupon');
  };

  const handleTextSlotSave = data => {
    handleSlotSave(currentSlotPosition, data);
  };

  // submit all coupons
  const handleSubmit = async () => {
    const errors = validateMultiCoupon(
      formData,
      containerType,
      null,
      displayCount,
      slotsData
    );

    if (!Object.keys(errors || {}).length) {
      try {
        const updatedSlot = await processUploadsAndTrim(
          slotsData,
          containerType.toUpperCase() === 'TEXT' ? 'TEXT' : 'GRAPHICAL'
        );
        const payload = {
          containerName: formData.couponName,
          containerType:
            containerType.toUpperCase() === 'TEXT' ? 'TEXT' : 'GRAPHICAL',
          assetId: formData.assetId,
          assetUrl: formData.assetUrl,
          startDate: formData?.startDate,
          endDate: formData?.endDate,
          numberOfCoupons: displayCount,
          slots: updatedSlot,
        };
        await createTextCoupon(payload);
      } catch (err) {
        console.error(err);
      }
      setFormError({});
    } else {
      setFormError(errors);
    }
  };

  const handleDisable = () => {
    const mendatoryTouchFields = ['couponName', 'assetUrl', 'slots'];
    return !mendatoryTouchFields.every(field =>
      Object.prototype.hasOwnProperty.call(touchedFields, field)
    );
  };

  const graphicTile = ({
    couponImageUrl,
    slotPostion,
    deviceDisplayImageUrl,
  }) => (
    <div className='upload-tile'>
      <div className='d-flex flex-column'>
        <div className='placeholder'>
          {couponImageUrl ? (
            <LazyImg
              imageSrc={couponImageUrl}
              alt='Coupon Image'
              className='placeholder'
              key={couponImageUrl}
            />
          ) : (
            <div className='placeholder'>
              <i
                className='fa fa-image'
                style={{
                  position: 'sticky',
                  transform: 'translate(-50%, -50%)',
                  fontSize: '45px',
                  color: '#9aa0a6',
                  marginTop: '40%',
                  marginLeft: '40%',
                }}
              />
            </div>
          )}
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            paddingTop: '5px',
          }}
          onClick={() => {
            setCurrentSlotPosition(slotPostion);
            setTouchedFields(prev => ({ ...prev, slots: true }));
          }}
        >
          <ImageContent
            key={`${slotPostion}`}
            imageType={ImageType.Coupon}
            handleImageSelect={handleImageSelectCallback}
            customText='Coupon'
            isCustomTextRequired
          />
        </div>
      </div>
      <div className='d-flex flex-column'>
        {deviceDisplayImageUrl ? (
          <div className='placeholderDeviceDisplay'>
            <LazyImg
              imageSrc={deviceDisplayImageUrl}
              alt='Coupon Image'
              className='placeholderDeviceDisplay'
              key={deviceDisplayImageUrl}
            />
          </div>
        ) : (
          <div className='placeholderDeviceDisplay'>
            <i
              className='fa fa-image'
              style={{
                position: 'sticky',
                transform: 'translate(-50%, -50%)',
                fontSize: '55px',
                color: '#9aa0a6',
                marginTop: '40%',
                marginLeft: '40%',
              }}
            />
          </div>
        )}
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            paddingTop: '5px',
          }}
          onClick={() => {
            setCurrentSlotPosition(slotPostion);
          }}
        >
          <ImageContent
            key={`${slotPostion}`}
            imageType={ImageType.DeviceDisplayImage}
            handleImageSelect={handleImageSelectCallback}
            customText='Device Display'
          />
        </div>
      </div>
    </div>
  );

  const textTile = ({ productImageUrl, productName, slotPostion }) => {
    if (!productImageUrl && !productName) {
      return (
        <div className='upload-tile justify-center-text'>
          <Button
            className='btn-upload'
            onClick={() => {
              setCurrentSlotPosition(slotPostion);
              setDrawerOpen(true);
              setTouchedFields(prev => ({ ...prev, slots: true }));
              setEditData({});
            }}
          >
            Add Coupon
          </Button>
        </div>
      );
    }
    function wrapText(originalText, maxLength) {
      const lines = [];
      let remainingText = originalText;
      while (remainingText.length > maxLength) {
        lines.push(remainingText.substring(0, maxLength));
        remainingText = remainingText.substring(maxLength);
      }
      lines.push(remainingText);
      return lines;
    }

    const productTooltip = (
      <Tooltip id='productTooltip'>
        {wrapText(productName, 25).map(line => (
          <div key={line}>{line}</div>
        ))}
      </Tooltip>
    );

    return (
      <div className='upload-tile'>
        {productImageUrl ? (
          <LazyImg
            imageSrc={productImageUrl}
            alt='Product Image'
            className='placeholder'
            key='test'
          />
        ) : (
          <div className='placeholder'>
            <i
              className='fa fa-image'
              style={{
                position: 'sticky',
                transform: 'translate(-50%, -50%)',
                fontSize: '45px',
                color: '#9aa0a6',
                marginTop: '40%',
                marginLeft: '40%',
              }}
            />
          </div>
        )}

        <div>
          <OverlayTrigger placement='top' overlay={productTooltip}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <p className='tile-product-name'>{truncateString(productName)}</p>
            </div>
          </OverlayTrigger>
          <div
            onClick={() => {
              setCurrentSlotPosition(slotPostion);
              setDrawerOpen(true);
              const matchData = slotsData.find(
                item =>
                  Array.isArray(item.slotPosition) &&
                  item.slotPosition.length === slotPostion.length &&
                  item.slotPosition.every(
                    (pos, index) => pos === slotPostion[index]
                  )
              );

              setEditData(matchData);
            }}
            style={{ display: 'flex', flexDirection: 'row-reverse' }}
          >
            <Button className='btn-upload'>Edit Coupon</Button>
          </div>
        </div>
      </div>
    );
  };

  const renderTile = (slot, i) => (
    <Collapse in={i < displayCount} key={i}>
      <Col xs={12} sm={6} md={6} lg={6} xl={6} xxl={6} className='tile-col'>
        {isTextCoupon
          ? textTile({
              slotPostion: slot?.slotPosition,
              productImageUrl: slot?.productImageDetails?.imageFile?.preview,
              productName: slot?.productName,
            })
          : graphicTile({
              couponImageUrl: slot?.couponImageDetails?.imageFile?.preview,
              deviceDisplayImageUrl:
                slot?.displayImageDetails?.imageFile?.preview,
              slotPostion: slot?.slotPosition,
            })}
      </Col>
    </Collapse>
  );

  return (
    <div className='panel panel-default bs-primary'>
      <Grid fluid className='page-shell'>
        <div>
          <div className='header-container'>
            <h3 className='panel-title'>Create Multi {couponLabel} Coupon</h3>
          </div>

          <Row className='coupon-form-container'>
            {/* LEFT PANEL */}
            <Col md={4}>
              <div className='form-panel'>
                <FormGroup controlId='couponName'>
                  <ControlLabel>Coupon Name*</ControlLabel>
                  <FormControl
                    maxLength={50}
                    required
                    type='text'
                    name='couponName'
                    value={formData.couponName}
                    className='form-control form-control-sm'
                    placeholder='Enter the coupon name'
                    onChange={e => {
                      handleInputChange('couponName', e.target.value);
                    }}
                  />
                  {touchedFields.couponName && formErrors.couponName && (
                    <p className='error-text'>{formErrors.couponName}</p>
                  )}
                </FormGroup>

                <hr className='divider' />

                <FormGroup controlId='linkedContent'>
                  <ControlLabel>Linked Content*</ControlLabel>
                  <Row className='linked-content-wrapper-multi'>
                    <div className='thumb-container'>
                      {!formData.assetUrl ? (
                        <div className='thumbnail'>
                          <i
                            className='fa-solid fa-photo-film thumbnail'
                            style={{
                              position: 'absolute',
                              fontSize: '50px',
                              color: '#9aa0a6',
                              backgroundColor: '#dadce0',
                              paddingLeft: '8px',
                            }}
                          />
                        </div>
                      ) : (
                        <video
                          key={formData.assetUrl}
                          className='thumbnail position-absolute top-50 start-50 translate-middle'
                          src={formData.assetUrl}
                          muted
                        />
                      )}
                      <div className='thumb-container-right'>
                        <div
                          style={
                            touchedFields.assetUrl &&
                            formErrors.assetUrl && { paddingLeft: '10px' }
                          }
                        >
                          {' '}
                          <Button
                            onClick={handleOpenLinkedModal}
                            className='btn-sm coupon-button btn btn-secondary'
                          >
                            {formData.assetUrl
                              ? 'Change Linked Asset'
                              : 'Link To Asset'}
                          </Button>
                          <p
                            className='linked-content-text'
                            style={
                              touchedFields.assetUrl &&
                              formErrors.assetUrl && { paddingTop: '0px' }
                            }
                          >
                            {formData.assetUrl
                              ? `[${selectedVideo.filename ? selectedVideo.filename : getLastSegmentWithLimit(formData.assetUrl)}]`
                              : 'Select or upload .jpg/.png file to attach with the coupon.'}
                          </p>
                          {touchedFields.assetUrl && formErrors.assetUrl && (
                            <div style={{ paddingRight: '10px' }}>
                              <p className='error-text'>
                                {formErrors.assetUrl}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Row>
                </FormGroup>
                <hr className='divider' />

                <FormGroup controlId='startDate'>
                  <div>
                    <ControlLabel>Start Date*</ControlLabel>
                  </div>

                  <DayPicker
                    formatDate={formatDate}
                    parseDate={parseDate}
                    onDayChange={handleStartDayChange}
                    format={dateDisplayFormat}
                    placeholder={`${formatDate(new Date(), dateDisplayFormat)}`}
                    dayPickerProps={{
                      showOutsideDays: true,
                      disabledDays: [
                        {
                          before: new Date(),
                        },
                      ],
                    }}
                    value={formData.startDate}
                  />
                  {touchedFields.startDate && formErrors.startDate && (
                    <p className='error-text' style={{ paddingLeft: '10px' }}>
                      {formErrors.startDate}
                    </p>
                  )}
                </FormGroup>

                <FormGroup controlId='endDate'>
                  <div>
                    <ControlLabel>End Date*</ControlLabel>
                  </div>
                  <DayPicker
                    formatDate={formatDate}
                    parseDate={parseDate}
                    onDayChange={handleEndDayChange}
                    format={dateDisplayFormat}
                    placeholder={`${formatDate(new Date(), dateDisplayFormat)}`}
                    dayPickerProps={{
                      showOutsideDays: true,
                      disabledDays: [
                        {
                          before: new Date(),
                        },
                      ],
                    }}
                    value={formData.endDate}
                  />
                  {touchedFields.endDate && formErrors.endDate && (
                    <p className='error-text' style={{ paddingLeft: '10px' }}>
                      {formErrors.endDate}
                    </p>
                  )}
                </FormGroup>
              </div>
            </Col>

            {/* RIGHT PANEL */}
            <Col md={0} lg={0} xxl={1} />
            <Col md={8} lg={8} xxl={7}>
              <div className='right-panel'>
                <div className='display-switch'>
                  <ControlLabel className='display-label'>
                    Display:
                  </ControlLabel>
                  <div className='segmented-control'>
                    {[4, 6, 8].map(n => (
                      <div
                        key={n}
                        className={`segment${displayCount === n ? ' active' : ''}`}
                        onClick={() => handleCouponCountChange(n)}
                      >
                        {n}
                      </div>
                    ))}
                  </div>
                </div>

                <div className='upload-panel'>
                  <Row className='tiles-row'>
                    {slotsData.map((slot, i) => renderTile(slot, i))}
                  </Row>
                </div>
                {touchedFields.slots && formErrors.slots && (
                  <div className='note-error'>
                    <p className='error-text'>{formErrors.slots}</p>
                  </div>
                )}
                {!isTextCoupon && (
                  <div className='note'>
                    Maximum dimensions: Coupon image must be in .bmp format with
                    1-bit depth and a maximum width of 368 pixels. Device
                    display image must be in .jpg, .png, or .gif format with a
                    maximum size of 1000 by 400 pixels.
                  </div>
                )}
              </div>
            </Col>
          </Row>
        </div>
        <Row>
          <Col md={12} className='action-buttons'>
            <Button
              className='btn btn-primary btn-sm'
              bsStyle='primary'
              id='btnPlaylistPublish'
              onClick={handleSubmit}
              disabled={handleDisable()}
            >
              Create Coupon
            </Button>
            <Button
              className='btn btn-default ml10'
              id='cancel'
              onClick={() => {
                handleCancelClick();
              }}
            >
              Cancel
            </Button>
          </Col>
        </Row>
      </Grid>
      {isLinkedModalOpen && (
        <LinkMediaModal
          isOpen={isLinkedModalOpen}
          onClose={() => setIsLinkedModalOpen(false)}
          onMediaSelect={handleMediaSelect}
        />
      )}
      {isTextCoupon && isDrawerOpen && (
        <Drawer
          style={{ ...customStyles.drawer }}
          open={isDrawerOpen}
          onClose={() => setDrawerOpen(false)}
          direction='right'
        >
          <TextCouponForm
            onDrawerClose={() => {
              setDrawerOpen(false);
            }}
            couponType={CouponType.Text}
            multiEditData={editData}
            handleDeleteClick={() => {}}
            multiCoupon
            handleTextSlotSave={handleTextSlotSave}
          />
        </Drawer>
      )}
    </div>
  );
};
MultiCoupon.propTypes = {
  match: PropTypes.object,
  createTextCoupon: PropTypes.func,
};

const mapStateToProps = state => ({
  coupon: state.coupon,
});

export default compose(
  withRouter,
  connect(mapStateToProps, {
    createTextCoupon: couponActions.createTextCoupon,
    saveTextCoupon: couponActions.saveTextCoupon,
  })
)(MultiCoupon);
