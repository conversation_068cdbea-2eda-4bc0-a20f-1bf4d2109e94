/* eslint-disable no-useless-escape */

const validateS3Url = s3Url => {
  const s3Pattern =
    /^https:\/\/([a-z0-9\-]+\.s3\.([a-z0-9\-]+)\.amazonaws\.com|s3\.amazonaws\.com)/;
  return s3Pattern.test(s3Url);
};

const isVideo = assetUrl => {
  if (validateS3Url(assetUrl)) {
    const path = new URL(assetUrl).pathname;
    if (path.match(/\.(webm|mp4)$/i)) {
      return true;
    }
  }
  return false;
};

const truncateString = str => {
  if (str.length > 20) {
    return `${str.substring(0, 20)}...`;
  }
  return str;
};

const extractImageFromUrl = imageUrl => {
  if (validateS3Url(imageUrl)) {
    const path = new URL(imageUrl).pathname;
    if (path.match(/\.(jpg|jpeg|png|gif|bmp)$/i)) {
      return { isValid: true, fileName: path.split('/').pop() };
    }
  }
  return { isValid: false, fileName: null };
};

const getLastSegmentWithLimit = url => {
  if (!url) {
    return '';
  }

  const parts = url.split('/');
  let lastPart = parts[parts.length - 1];

  // Check for file extension (e.g., .webm, .mp4, etc.)
  const extensionMatch = lastPart.match(/\.[a-z0-9]+$/i);
  const extension = extensionMatch ? extensionMatch[0] : '';
  const filenameWithoutExtension = lastPart.replace(extension, '');

  if (filenameWithoutExtension.length > 40) {
    lastPart = filenameWithoutExtension.slice(0, 40) + extension;
  }

  return lastPart;
};

const getBaseAssetUrl = assetUrl => {
  if (!assetUrl) return '';

  try {
    const url = new URL(assetUrl);
    const { pathname } = url;

    const match = pathname.match(/\.(jpg|jpeg|png|gif|bmp)$/i);

    if (match) {
      const endIndex = pathname.indexOf(match[0]) + match[0].length;
      const cleanPath = pathname.substring(0, endIndex);

      return `${url.origin}${cleanPath}`;
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Invalid URL:', err);
  }

  return '';
};

const getImageFileName = assetUrl => {
  if (!assetUrl) return '';

  try {
    const url = new URL(assetUrl);
    const { pathname } = url;

    // Get the last part of the path (filename with extension)
    const parts = pathname.split('/');
    const filename = parts[parts.length - 1];

    // Check if it's an image file
    const isImage = /\.(jpg|jpeg|png|gif|bmp)$/i.test(filename);

    if (isImage) {
      return filename;
    }
  } catch (err) {
    console.error('Invalid URL:', err);
  }

  return '';
};

async function uploadImage(url, imageFile) {
  if (!url || !imageFile) {
    return false;
  }

  try {
    const response = await fetch(url, {
      method: 'PUT',
      body: imageFile,
      headers: {
        'Content-Type': imageFile.type,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to upload image, status: ${response.status}`);
    }

    return true;
  } catch (error) {
    const errorMessage =
      error.message || 'An error occurred while uploading the image.';
    console.error('Upload failed:', errorMessage);

    return { success: false, error: errorMessage };
  }
}

async function handlePreFormSubmit({
  productImageDetails,
  logoImageDetails,
  couponImageDetails,
  displayImageDetails,
}) {
  try {
    const uploadPromises = [];

    if (productImageDetails) {
      uploadPromises.push(
        uploadImage(productImageDetails.url, productImageDetails.imageFile)
      );
    }

    if (logoImageDetails) {
      uploadPromises.push(
        uploadImage(logoImageDetails.url, logoImageDetails.imageFile)
      );
    }

    if (couponImageDetails) {
      uploadPromises.push(
        uploadImage(couponImageDetails.url, couponImageDetails.imageFile)
      );
    }
    if (displayImageDetails) {
      uploadPromises.push(
        uploadImage(displayImageDetails.url, displayImageDetails.imageFile)
      );
    }

    await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Image upload failed:', error);
  }
}

/**
 * Processes uploads in parallel, then returns the updated items with URLs.
 *
 * @param {Array<Object>} items       – array of slot objects
 * @param {string}        kind        – e.g. "GRAPHICAL" or "TEXT"
 * @returns {Promise<Array<Object>>}  – resolves to new array of updated slot objects
 */
async function processUploadsAndTrim(items, kind) {
  // Define the image detail keys and their corresponding URL keys
  const imageDetailsKeys = [
    { detailsKey: 'logoImageDetails', urlKey: 'logoImageUrl' },
    { detailsKey: 'productImageDetails', urlKey: 'productImageUrl' },
    { detailsKey: 'couponImageDetails', urlKey: 'couponImageUrl' },
    { detailsKey: 'displayImageDetails', urlKey: 'deviceDisplayImageUrl' },
  ];

  // Collect upload promises for all items where upload is needed
  const uploadPromises = items.map(item => {
    // For each detailsKey, check if it exists and handle the image upload
    const promises = imageDetailsKeys.map(({ detailsKey, urlKey }) => {
      const details = item[detailsKey];
      if (details && details.url && details.imageFile) {
        return uploadImage(details.url, details.imageFile).then(() => ({
          item,
          detailsKey,
          urlKey,
        }));
      }
      return Promise.resolve();
    });

    return Promise.all(promises).then(results => {
      const validResults = results.filter(result => result);
      return {
        item,
        validResults,
      };
    });
  });

  const uploadedSlotItems = await Promise.all(uploadPromises);

  return uploadedSlotItems.map(({ item, validResults }) => {
    const updatedItem = validResults.reduce(
      (newItem, { detailsKey, urlKey }) => {
        const { [detailsKey]: _omitted, ...rest } = newItem;

        const trimmedUrl =
          typeof rest[urlKey] === 'string'
            ? getBaseAssetUrl(rest[urlKey])
            : rest[urlKey];
        return {
          ...rest,
          kind,
          ...(typeof trimmedUrl === 'string' && trimmedUrl.trim() !== ''
            ? { [urlKey]: trimmedUrl }
            : {}),
        };
      },
      item
    );
    return updatedItem;
  });
}

/**
 * Basic validator: checks that arr is an array of objects
 * and that no object has an empty string, null, or undefined value.
 *
 * @param {Array<Object>} arr
 * @returns {boolean}
 */
function validateArrayObjectsNonEmpty(arr) {
  if (!Array.isArray(arr)) return false;
  return arr.every(obj => {
    if (typeof obj !== 'object' || obj === null) return false;
    return Object.values(obj).every(val => val !== '' && val != null);
  });
}

export {
  validateS3Url,
  extractImageFromUrl,
  isVideo,
  getLastSegmentWithLimit,
  getBaseAssetUrl,
  handlePreFormSubmit,
  processUploadsAndTrim,
  validateArrayObjectsNonEmpty,
  truncateString,
  getImageFileName,
};
