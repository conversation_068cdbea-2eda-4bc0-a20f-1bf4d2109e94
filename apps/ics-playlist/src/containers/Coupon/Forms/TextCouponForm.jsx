/* eslint-disable react/forbid-prop-types */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, Col, Row, FormControl, ControlLabel } from 'react-bootstrap';
import { connect } from 'react-redux';
import { formatDate, parseDate } from 'react-day-picker/moment';
import moment from 'moment';
import { actions as couponActions } from '../../../actions/coupon';
import LazyImg from '../../../components/LazyImg';
import DayPicker from '../../../components/DayPicker';
import LinkMediaModal from '../../../components/LinkMediaModal';
import ImageContent from '../../ImageContent';
import { ImageType } from '../../../constants/enums';
import { validateCouponForm } from './Validators/formValidator';
import {
  handlePreFormSubmit,
  getLastSegmentWithLimit,
  getBaseAssetUrl,
  getImageFileName,
} from './Helpers';
import './customStyles.css';

const TextCouponForm = props => {
  const {
    editData,
    multiEditData,
    onDrawerClose,
    createTextCoupon,
    couponType,
    saveTextCoupon,
    multiCoupon,
    handleTextSlotSave,
  } = props;
  const [formData, setFormData] = useState(multiEditData || {});
  const [formErrors, setFormError] = useState({});
  const [selectedVideo, setSelectedVideo] = useState({});
  const [selectedImage, setSelectedImage] = useState({});
  const [isLinkedModalOpen, setIsLinkedModalOpen] = useState(false);
  const [modalImageType, setModalImageType] = useState(null);
  const [initialData, setInitialData] = useState(multiEditData || {});
  const dateDisplayFormat = 'MM/DD/YYYY';
  const [touchedFields, setTouchedFields] = useState({});
  const [isEditMode, setIsEditMode] = useState(false);
  const isSingleCouponForm = !multiCoupon;

  useEffect(() => {
    if (editData) {
      const editedData = {
        couponName: editData.couponName,
        productName: editData.productName,
        couponValue: editData.couponValue,
        startDate: new Date(editData.startDate),
        endDate: new Date(editData.endDate),
        assetId: editData.assetId,
        purchaseRestriction: editData.purchaseRestriction,
        termsOfOffer: editData.termsOfOffer,
        assetUrl: editData.assetUrl,
        couponBarcode: editData.couponBarcode,
      };
      setFormData(editedData);
      setInitialData(editedData);
      setIsEditMode(true);
    } else {
      setFormData(prev => {
        const updatedFormData = {
          ...prev,
          startDate: new Date(),
          endDate: new Date(),
        };
        return updatedFormData;
      });
      setInitialData({});
      setIsEditMode(false);
    }
  }, [editData]);

  useEffect(() => {
    if (Object.keys(selectedVideo || {}).length) {
      setFormData(prevState => ({
        ...prevState,
        assetUrl: selectedVideo.sourceFileUrl,
        assetId: selectedVideo.id,
      }));
    }
  }, [selectedVideo]);

  useEffect(() => {
    if (selectedImage?.url) {
      if (modalImageType === ImageType.Product) {
        setFormData(prevState => ({
          ...prevState,
          productImageDetails: selectedImage,
        }));
      } else if (modalImageType === ImageType.Logo) {
        setFormData(prevState => ({
          ...prevState,
          logoImageDetails: selectedImage,
        }));
      } else if (modalImageType === ImageType.DeviceDisplayImage) {
        setFormData(prevState => {
          const updatedFormData = {
            ...prevState,
            displayImageDetails: selectedImage,
          };
          setFormError(prevErrors => {
            const updatedErrors = { ...prevErrors };
            const newErrors = validateCouponForm(
              updatedFormData,
              couponType,
              'displayImageDetails'
            );
            delete updatedErrors.displayImageDetails;
            return { ...updatedErrors, ...newErrors };
          });
          return updatedFormData;
        });
      }
    }
  }, [selectedImage]);

  const handleOpenLinkedModal = () => {
    setIsLinkedModalOpen(true);
  };

  const handleMediaSelect = video => {
    setSelectedVideo(video);
    setFormData(prev => {
      const updatedFormData = { ...prev, assetUrl: video.sourceFileUrl };
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        const newErrors = validateCouponForm(
          updatedFormData,
          couponType,
          'assetUrl'
        );
        delete updatedErrors.assetUrl;
        return { ...updatedErrors, ...newErrors };
      });
      return updatedFormData;
    });
    setIsLinkedModalOpen(false);
  };

  const handleImageSelectCallback = (imageDetails, imageType) => {
    setModalImageType(imageType);
    setSelectedImage({
      url: imageDetails?.presignedImageUrl,
      imageFile: imageDetails?.imageFile,
    });
  };

  const handleStartDayChange = selectedDay => {
    setTouchedFields(prevTouched => ({ ...prevTouched, startDate: true }));
    setFormData(prevData => {
      // Check if we need to adjust the end date
      let { endDate } = prevData;

      // If end date exists and is before the new start date, set end date equal to start date
      if (endDate && new Date(selectedDay) > new Date(endDate)) {
        endDate = selectedDay;
        // Also mark end date as touched since we're auto-updating it
        setTouchedFields(current => ({ ...current, endDate: true }));
      }

      const updatedFormData = {
        ...prevData,
        startDate: selectedDay,
        endDate,
      };

      // Completely replace errors for date fields, don't merge
      const newErrors = validateCouponForm(updatedFormData, couponType, [
        'startDate',
        'endDate',
      ]);
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        // Remove any existing date errors
        delete updatedErrors.startDate;
        delete updatedErrors.endDate;
        // Add new errors if any
        return { ...updatedErrors, ...newErrors };
      });

      return updatedFormData;
    });
  };

  const handleEndDayChange = selectedDay => {
    setTouchedFields(prevTouched => ({ ...prevTouched, endDate: true }));
    setFormData(prevData => {
      const updatedFormData = { ...prevData, endDate: selectedDay };

      // Completely replace errors for date fields, don't merge
      const newErrors = validateCouponForm(updatedFormData, couponType, [
        'startDate',
        'endDate',
      ]);
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        // Remove any existing date errors
        delete updatedErrors.startDate;
        delete updatedErrors.endDate;
        // Add new errors if any
        return { ...updatedErrors, ...newErrors };
      });

      return updatedFormData;
    });
  };

  const handleInputChange = (fieldName, value) => {
    setTouchedFields(prevTouched => ({ ...prevTouched, [fieldName]: true }));
    setFormData(prevData => {
      const updatedFormData = { ...prevData, [fieldName]: value };

      // For other fields: clear existing error, then add new one if invalid
      const newErrors = validateCouponForm(
        updatedFormData,
        couponType,
        fieldName
      );
      setFormError(prevErrors => {
        const updatedErrors = { ...prevErrors };
        delete updatedErrors[fieldName];
        return { ...updatedErrors, ...newErrors };
      });

      return updatedFormData;
    });
  };

  const handleSubmit = async () => {
    // Mark all fields as touched when submitting
    const allFields = [
      'couponName',
      'productName',
      'couponValue',
      'startDate',
      'endDate',
      'assetUrl',
      'productImageDetails',
      'logoImageDetails',
      'couponBarcode',
      'purchaseRestriction',
      'termsOfOffer',
      'displayImageDetails',
    ];
    const multiCouponFields = [
      'productName',
      'couponValue',
      'productImageDetails',
      'logoImageDetails',
      'couponBarcode',
      'purchaseRestriction',
      'termsOfOffer',
      'displayImageDetails',
    ];
    if (isSingleCouponForm) {
      setTouchedFields(
        allFields.reduce((acc, field) => ({ ...acc, [field]: true }), {})
      );
    } else {
      setTouchedFields(
        multiCouponFields.reduce(
          (acc, field) => ({ ...acc, [field]: true }),
          {}
        )
      );
    }

    const errors = validateCouponForm(
      formData,
      couponType,
      null,
      isSingleCouponForm
    );
    if (!Object.keys(errors || {}).length) {
      if (editData) {
        // If editing, call save action
        await saveTextCoupon({
          couponId: editData.id,
          payload: {
            ...formData,
            startDate: formatDate(formData.startDate, dateDisplayFormat),
            endDate: formatDate(formData.endDate, dateDisplayFormat),
          },
        });
      } else {
        // If creating, call create action
        try {
          if (!isSingleCouponForm) {
            handleTextSlotSave({
              productName: formData?.productName,
              couponValue: formData?.couponValue,
              purchaseRestriction: formData?.purchaseRestriction,
              termsOfOffer: formData?.termsOfOffer,
              productImageDetails: formData?.productImageDetails,
              logoImageDetails: formData?.logoImageDetails,
              displayImageDetails: formData?.displayImageDetails,
              couponBarcode: formData?.couponBarcode,
            });
            onDrawerClose();
          } else {
            if (
              formData?.productImageDetails?.url ||
              formData?.logoImageDetails?.url ||
              formData?.displayImageDetails?.url
            ) {
              handlePreFormSubmit({
                productImageDetails: formData?.productImageDetails,
                logoImageDetails: formData?.logoImageDetails,
                displayImageDetails: formData?.displayImageDetails,
              });
            }

            const payload = {
              containerName: formData?.couponName,
              containerType: 'TEXT',
              assetId: formData?.assetId,
              assetUrl: formData?.assetUrl,
              startDate: moment(formData?.startDate)
                .utc(-4 * 60)
                .startOf('day')
                .format('YYYY-MM-DD[T]HH:mm:ss[Z]'),
              endDate: moment(formData?.endDate)
                .utc(-4 * 60)
                .endOf('day')
                .format('YYYY-MM-DD[T]HH:mm:ss[Z]'),
              numberOfCoupons: 1,
              slots: [
                {
                  kind: 'TEXT',
                  slotPosition: [
                    'sl1',
                    'sl2',
                    'sl3',
                    'sl4',
                    'sr1',
                    'sr2',
                    'sr3',
                    'sr4',
                  ],
                  productName: formData?.productName,
                  couponValue: formData?.couponValue,
                  purchaseRestriction: formData?.purchaseRestriction,
                  termsOfOffer: formData?.termsOfOffer,
                  productImageUrl:
                    formData?.productImageDetails?.url &&
                    getBaseAssetUrl(formData?.productImageDetails?.url),
                  logoImageUrl:
                    formData?.logoImageDetails?.url &&
                    getBaseAssetUrl(formData?.logoImageDetails?.url),
                  deviceDisplayImageUrl:
                    formData?.displayImageDetails?.url &&
                    getBaseAssetUrl(formData?.displayImageDetails?.url),
                  couponBarcode: formData?.couponBarcode,
                },
              ],
            };

            if (!payload.slots[0]?.productImageUrl?.length) {
              delete payload.slots[0].productImageUrl;
            }
            if (!payload.slots[0]?.logoImageUrl?.length) {
              delete payload.slots[0].logoImageUrl;
            }
            await createTextCoupon(payload);
          }
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('One of the API calls failed:', error);
          return;
        }
      }
      setFormError({});
    } else {
      setFormError(errors);
    }
  };

  // Check if the form has changed
  const isFormDataChanged =
    JSON.stringify(formData) !== JSON.stringify(initialData);

  const customStyles = {
    panelBody: {
      paddingTop: '0px',
    },
    row: {
      marginBottom: '8px',
      borderRadius: '5px',
      paddingBottom: '4px',
    },
    formControl: {
      borderRadius: '6px',
    },
  };

  return (
    <>
      <div
        className='panel-heading no-border panel-heading-has-btn'
        style={{ color: '#000000', paddingBottom: '5px', paddingTop: '5px' }}
      >
        <h3 className='panel-title'>
          {editData ? 'Edit' : 'Create'} Text Coupon
        </h3>
        <div className='pull-right' style={{ paddingTop: '8px' }}>
          <i
            className='fa fa-close'
            aria-hidden='true'
            onClick={() => {
              onDrawerClose();
            }}
            style={{ cursor: 'pointer' }}
          />
        </div>
      </div>

      <div className='panel-body' style={customStyles.panelBody}>
        {isSingleCouponForm ? (
          <Row style={customStyles.row}>
            <Col className='col-md-11'>
              <ControlLabel>Coupon Name*</ControlLabel>
              <FormControl
                style={customStyles.formControl}
                maxLength={50}
                required
                type='text'
                name='couponName'
                value={formData.couponName}
                className='form-control form-control-sm'
                placeholder='Enter the coupon name'
                onChange={e => {
                  handleInputChange('couponName', e.target.value);
                }}
              />
              {touchedFields.couponName && formErrors.couponName && (
                <p className='error-text'>{formErrors.couponName}</p>
              )}
            </Col>
          </Row>
        ) : (
          <Row style={customStyles.row}>
            <Col className='col-md-11'>
              <ControlLabel>Product Name*</ControlLabel>
              <FormControl
                style={customStyles.formControl}
                maxLength={50}
                required
                type='text'
                name='productName'
                id='productName'
                value={formData.productName}
                className='form-control form-control-sm'
                placeholder='Enter the product name'
                onChange={e => {
                  handleInputChange('productName', e.target.value);
                }}
              />
              {touchedFields.productName && formErrors.productName && (
                <p className='error-text'>{formErrors.productName}</p>
              )}
            </Col>
          </Row>
        )}
        <Row style={customStyles.row}>
          <Col className='col-md-8'>
            <ControlLabel>Coupon Value*</ControlLabel>
            <FormControl
              style={customStyles.formControl}
              maxLength={50}
              required
              type='text'
              name='couponValue'
              id='couponValue'
              value={formData.couponValue}
              className='form-control form-control-sm'
              placeholder='Enter the coupon value'
              onChange={e => {
                handleInputChange('couponValue', e.target.value);
              }}
            />
            {touchedFields.couponValue && formErrors.couponValue && (
              <p className='error-text'>{formErrors.couponValue}</p>
            )}
          </Col>
          <Col className='col-md-4' />
        </Row>

        <Row style={customStyles.row}>
          <Col className='col-md-12'>
            <p className='font-weight-bold mb-2'>Device Display Image</p>
          </Col>
          <Col className='col-md-4' style={{ paddingRight: '0px' }}>
            <div
              className='position-relative lazy-device-display-image'
              style={{ backgroundColor: '#dadce0' }}
            >
              {formData.displayImageDetails?.imageFile?.preview && (
                <LazyImg
                  imageSrc={formData.displayImageDetails?.imageFile?.preview}
                  alt='Product Image'
                  className='lazy-device-display-image'
                  key={`${formData.displayImageDetails?.imageFile?.name} + ${ImageType.DeviceDisplayImage} + ${isEditMode}`}
                />
              )}

              {!formData.displayImageDetails?.imageFile?.preview && (
                <i
                  className='fa fa-image'
                  style={{
                    position: 'sticky',
                    transform: 'translate(-50%, -50%)',
                    fontSize: '55px',
                    color: '#9aa0a6',
                    marginTop: '30%',
                    marginLeft: '50%',
                  }}
                />
              )}
            </div>
          </Col>
          <Col
            className='col-md-8'
            style={{
              paddingLeft: '35px',
              paddingRight: '0px',
              marginTop: '10px',
            }}
          >
            <ImageContent
              key={`${ImageType.DeviceDisplayImage} + ${isEditMode}`}
              isEditMode={multiEditData?.displayImageDetails?.imageFile}
              imageType={ImageType.DeviceDisplayImage}
              handleImageSelect={handleImageSelectCallback}
            />
            <p className='lazy-image-text'>
              {formData.displayImageDetails?.imageFile?.preview ? (
                <>
                  {/* [{extractImageFromUrl(formData.logoImageUrl).fileName}] */}
                  [{formData?.displayImageDetails?.imageFile?.name}]
                  <br />
                  Max file size: 1000x400 pixels
                </>
              ) : (
                'Supported file formats are .jpg, .png and .gif with a maximum resolution of 1000x400 pixels.'
              )}
            </p>
          </Col>
          <Col className='col-md-12'>
            <p className='lazy-image-text'>
              This image will be shown on the dispenser touchscreen device and
              used to print this coupon. See device specs documentation.
            </p>
          </Col>
        </Row>

        {isSingleCouponForm && (
          <>
            <hr className='divider' />

            <Row style={customStyles.row}>
              <Col className='col-md-11'>
                <ControlLabel>Product Name*</ControlLabel>
                <FormControl
                  style={customStyles.formControl}
                  maxLength={50}
                  required
                  type='text'
                  name='productName'
                  id='productName'
                  value={formData.productName}
                  className='form-control form-control-sm'
                  placeholder='Enter the product name'
                  onChange={e => {
                    handleInputChange('productName', e.target.value);
                  }}
                />
                {touchedFields.productName && formErrors.productName && (
                  <p className='error-text'>{formErrors.productName}</p>
                )}
              </Col>
            </Row>
          </>
        )}

        <Row style={customStyles.row}>
          <Col className='col-md-12'>
            <p className='font-weight-bold mb-2'>Product Image</p>
          </Col>
          <Col className='col-md-2 ' style={{ paddingRight: '0px' }}>
            <div
              className='position-relative lazy-image'
              style={{ backgroundColor: '#dadce0' }}
            >
              {formData.productImageDetails?.imageFile?.preview && (
                <LazyImg
                  imageSrc={formData.productImageDetails?.imageFile?.preview}
                  alt='Product Image'
                  className='lazy-image'
                  key={`${formData.productImageDetails?.imageFile?.name} + ${ImageType.Product} + ${isEditMode}`}
                />
              )}

              {!formData.productImageDetails?.imageFile?.preview && (
                <i
                  className='fa fa-image'
                  style={{
                    position: 'sticky',
                    transform: 'translate(-50%, -50%)',
                    fontSize: '45px',
                    color: '#9aa0a6',
                    marginTop: '50%',
                    marginLeft: '50%',
                  }}
                />
              )}
            </div>
          </Col>
          <Col
            className='col-md-10'
            style={{ paddingLeft: '36px', paddingRight: '0px' }}
          >
            <ImageContent
              key={`${ImageType.Product} + ${isEditMode}`}
              isEditMode={multiEditData?.productImageDetails?.imageFile}
              imageType={ImageType.Product}
              handleImageSelect={handleImageSelectCallback}
            />
            <p className='lazy-image-text'>
              {formData.productImageDetails?.imageFile?.preview ||
              formData.productImageDetails?.url ? (
                <>
                  [
                  {formData?.productImageDetails?.imageFile?.name ||
                    getImageFileName(formData?.productImageDetails?.url)}
                  ]
                  <br />
                  Max width: 368 pixels
                </>
              ) : (
                'Supported file format is bitmap (.bmp) with maximum width of 368 pixels and 1-bit depth.'
              )}
            </p>
          </Col>
          {touchedFields.productImageDetails &&
            formErrors.productImageDetails && (
              <p className='error-text' style={{ paddingLeft: '10px' }}>
                {formErrors.productImageDetails}
              </p>
            )}
        </Row>
        <hr className='divider' />
        {isSingleCouponForm && (
          <>
            <Row style={customStyles.row}>
              <Col className='col-md-12 d-flex align-items-center'>
                <span
                  className='font-weight-bold'
                  style={{ marginRight: '10px' }}
                >
                  Start Date*
                </span>
                <DayPicker
                  formatDate={formatDate}
                  parseDate={parseDate}
                  onDayChange={handleStartDayChange}
                  format={dateDisplayFormat}
                  placeholder={`${formatDate(new Date(), dateDisplayFormat)}`}
                  dayPickerProps={{
                    showOutsideDays: true,
                    disabledDays: [
                      {
                        before: new Date(),
                      },
                    ],
                  }}
                  value={formData.startDate}
                />
              </Col>

              {touchedFields.startDate && formErrors.startDate && (
                <p className='error-text' style={{ paddingLeft: '10px' }}>
                  {formErrors.startDate}
                </p>
              )}
            </Row>

            <Row style={customStyles.row}>
              <Col className='col-md-12 d-flex align-items-center'>
                <span
                  className='font-weight-bold'
                  style={{ marginRight: '16px' }}
                >
                  End Date*
                </span>
                <DayPicker
                  formatDate={formatDate}
                  parseDate={parseDate}
                  onDayChange={handleEndDayChange}
                  format={dateDisplayFormat}
                  placeholder={`${formatDate(new Date(), dateDisplayFormat)}`}
                  dayPickerProps={{
                    showOutsideDays: true,
                    disabledDays: [
                      {
                        before: new Date(),
                      },
                    ],
                  }}
                  value={formData.endDate}
                />
              </Col>

              {touchedFields.endDate && formErrors.endDate && (
                <p className='error-text' style={{ paddingLeft: '10px' }}>
                  {formErrors.endDate}
                </p>
              )}
            </Row>

            <Row style={customStyles.row}>
              <Col className='col-md-8'>
                <ControlLabel>Barcode</ControlLabel>
                <FormControl
                  style={customStyles.formControl}
                  type='text'
                  pattern='[0-9]*'
                  inputMode='numeric'
                  name='couponBarcode'
                  id='couponBarcode'
                  value={formData.couponBarcode}
                  className='form-control form-control-sm'
                  placeholder='Enter the barcode (Optional)'
                  onChange={e => {
                    // Replace any non-digit character with an empty string
                    const onlyNums = e.target.value.replace(/\D/g, '');
                    handleInputChange('couponBarcode', onlyNums);
                  }}
                />
                {touchedFields.couponBarcode && formErrors.couponBarcode && (
                  <p className='error-text'>{formErrors.couponBarcode}</p>
                )}
              </Col>
              <div className='col-md-4' />
            </Row>
          </>
        )}

        <Row style={customStyles.row}>
          <Col className='col-md-12'>
            <p className='font-weight-bold mb-2'>Logo Image</p>
          </Col>
          <Col className='col-md-2 ' style={{ paddingRight: '0px' }}>
            <div
              className='position-relative lazy-image'
              style={{ backgroundColor: '#dadce0' }}
            >
              {formData.logoImageDetails?.imageFile?.preview && (
                <LazyImg
                  imageSrc={formData.logoImageDetails?.imageFile?.preview}
                  alt='Product Image'
                  className='lazy-image'
                  key={`${formData.logoImageDetails?.imageFile?.name} + ${ImageType.Logo} + ${isEditMode}`}
                />
              )}

              {!formData.logoImageDetails?.imageFile?.preview && (
                <i
                  className='fa fa-image'
                  style={{
                    position: 'sticky',
                    transform: 'translate(-50%, -50%)',
                    fontSize: '45px',
                    color: '#9aa0a6',
                    marginTop: '50%',
                    marginLeft: '50%',
                  }}
                />
              )}
            </div>
          </Col>
          <Col
            className='col-md-10'
            style={{ paddingLeft: '35px', paddingRight: '0px' }}
          >
            <ImageContent
              key={`${ImageType.Logo} + ${isEditMode}`}
              isEditMode={multiEditData?.logoImageDetails?.imageFile}
              imageType={ImageType.Logo}
              handleImageSelect={handleImageSelectCallback}
            />
            <p className='lazy-image-text'>
              {formData.logoImageDetails?.imageFile?.preview ||
              formData.logoImageDetails?.url ? (
                <>
                  [
                  {formData?.logoImageDetails?.imageFile?.name ||
                    getImageFileName(formData.logoImageDetails?.url)}
                  ]
                  <br />
                  Max width: 368 pixels
                </>
              ) : (
                'Supported file format is bitmap (.bmp) with maximum width of 368 pixels and 1-bit depth.'
              )}
            </p>
          </Col>
          {touchedFields.logoImageDetails && formErrors.logoImageDetails && (
            <p className='error-text' style={{ paddingLeft: '10px' }}>
              {formErrors.logoImageDetails}
            </p>
          )}
        </Row>

        {isSingleCouponForm && (
          <>
            <hr className='divider' />

            <Row
              className='linked-content-wrapper d-flex align-items-center'
              style={{
                ...customStyles.row,
              }}
            >
              <Col className='col-md-12'>
                <p className='font-weight-bold mb-0'>Linked Content*</p>
                <i className='bi bi-file-earmark-play' />
              </Col>
              <Col
                className='col-md-3 wrapper position-relative'
                style={{ height: '100px', overflow: 'hidden' }}
              >
                <div
                  className='position-relative'
                  style={{ width: '100%', height: '100%' }}
                >
                  <video
                    key={formData.assetUrl}
                    className='position-absolute top-50 start-50 translate-middle'
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      objectPosition: 'center',
                    }}
                    src={formData.assetUrl}
                    muted
                  />

                  {!formData.assetUrl && (
                    <i
                      className='fa-solid fa-photo-film'
                      style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        fontSize: '50px',
                        color: '#9aa0a6',
                        backgroundColor: '#dadce0',
                        padding: '40%',
                      }}
                    />
                  )}
                </div>
              </Col>
              <Col className='col-md-9 text-wrapper pl-3'>
                <Button
                  bsStyle='secondary'
                  className='btn-sm coupon-button'
                  onClick={handleOpenLinkedModal}
                >
                  {formData.assetUrl ? 'Change Linked Video' : 'Link To Video'}
                </Button>
                <br />
                <p className='lazy-image-text'>
                  {formData.assetUrl
                    ? `[${selectedVideo.filename ? selectedVideo.filename : getLastSegmentWithLimit(editData.assetUrl)}]`
                    : 'Select or upload .jpg/.png file to attach with the coupon.'}
                </p>
                {touchedFields.assetUrl && formErrors.assetUrl && (
                  <p className='error-text'>{formErrors.assetUrl}</p>
                )}
              </Col>
            </Row>
          </>
        )}

        <Row style={customStyles.row}>
          <Col className='col-md-11'>
            <ControlLabel>Purchase Restriction</ControlLabel>
            <FormControl
              style={customStyles.formControl}
              maxLength={50}
              type='text'
              name='purchaseRestriction'
              id='purchaseRestriction'
              value={formData.purchaseRestriction}
              className='form-control form-control-sm'
              placeholder='Enter the purchase restriction (Optional)'
              onChange={e => {
                handleInputChange('purchaseRestriction', e.target.value);
              }}
            />
            {touchedFields.purchaseRestriction &&
              formErrors.purchaseRestriction && (
                <p className='error-text'>{formErrors.purchaseRestriction}</p>
              )}
          </Col>
        </Row>

        <Row style={customStyles.row}>
          <Col className='col-md-11'>
            <ControlLabel>Terms of Offer</ControlLabel>
            <FormControl
              style={customStyles.formControl}
              maxLength={50}
              type='text'
              name='termsOfOffer'
              id='termsOfOffer'
              value={formData.termsOfOffer}
              className='form-control form-control-sm'
              placeholder='Enter the terms of offer (Optional)'
              onChange={e => {
                handleInputChange('termsOfOffer', e.target.value);
              }}
            />
            {touchedFields.termsOfOffer && formErrors.termsOfOffer && (
              <p className='error-text'>{formErrors.termsOfOffer}</p>
            )}
          </Col>
        </Row>
        {!isSingleCouponForm && (
          <Row style={customStyles.row}>
            <Col className='col-md-8'>
              <ControlLabel>Barcode</ControlLabel>
              <FormControl
                style={customStyles.formControl}
                type='text'
                pattern='[0-9]*'
                inputMode='numeric'
                name='couponBarcode'
                id='couponBarcode'
                value={formData.couponBarcode}
                className='form-control form-control-sm'
                placeholder='Enter the barcode (Optional)'
                onChange={e => {
                  // Replace any non-digit character with an empty string
                  const onlyNums = e.target.value.replace(/\D/g, '');
                  handleInputChange('couponBarcode', onlyNums);
                }}
              />
              {touchedFields.couponBarcode && formErrors.couponBarcode && (
                <p className='error-text'>{formErrors.couponBarcode}</p>
              )}
            </Col>
            <div className='col-md-4' />
          </Row>
        )}

        <Row className='action-buttons-wrapper'>
          <Col className='pull-right'>
            <Button
              bsStyle='secondary'
              className='btn-sm btn-box-shadow'
              onClick={() => {
                onDrawerClose();
              }}
            >
              Cancel
            </Button>
            <Button
              bsStyle='primary'
              className='btn-sm btn-box-shadow'
              style={{ marginLeft: '10px' }}
              onClick={handleSubmit}
              disabled={!isFormDataChanged} // Disable if form is unchanged
            >
              {editData ||
              (multiEditData && Object.keys(multiEditData).length > 0)
                ? 'Save'
                : 'Create'}
            </Button>
          </Col>
        </Row>
      </div>
      {isLinkedModalOpen && (
        <LinkMediaModal
          isOpen={isLinkedModalOpen}
          onClose={() => setIsLinkedModalOpen(false)}
          onMediaSelect={handleMediaSelect}
        />
      )}
    </>
  );
};

TextCouponForm.propTypes = {
  couponType: PropTypes.string,
  onDrawerClose: PropTypes.func,
  createTextCoupon: PropTypes.func,
  saveTextCoupon: PropTypes.func,
  editData: PropTypes.object,
  multiEditData: PropTypes.object,
  multiCoupon: PropTypes.bool,
  handleTextSlotSave: PropTypes.func,
};

const mapStateToProps = scope => ({ coupon: scope.coupon });

export default connect(mapStateToProps, {
  createTextCoupon: couponActions.createTextCoupon,
  saveTextCoupon: couponActions.saveTextCoupon,
})(TextCouponForm);
