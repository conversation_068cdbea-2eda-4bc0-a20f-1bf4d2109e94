export const IMPORT_ATTRIBUTES_INSTRUCTIONS = {
  title: 'Instructions for Import Site Attributes',
  content: [
    {
      subTitle: 'Merchant ID',
      body: [
        'Merchant ID values must exist. Merchant ID can be found on the Site -> Site Attributes page.',
        'Same Merchant ID cannot be imported twice. In case of multiple entry, the duplicate error will be shown.',
      ],
    },
    {
      subTitle: 'SIP Secret Key',
      body: [
        'Valid SIP secret keys must be used as provided by the mobile host owner.',
      ],
    },
  ],
};

export const IMPORT_DEVICES_INSTRUCTIONS = {
  title: 'Instructions for Import Devices',
  content: [
    {
      subTitle: 'Site Name',
      body: [
        'Site name values must exist. Site name can be found on the Site Overview page.',
      ],
    },
    {
      subTitle: 'Device Serial',
      body: [
        'Only new device serial numbers can be imported. These must not be already assigned to a site.',
      ],
    },
  ],
};

export const IMPORT_SITE_TAGS_INSTRUCTIONS = {
  title: 'Instructions for Import Site Tags',
  content: [
    {
      subTitle: 'Site',
      body: [
        'Site name values must exist. Site name can be found on the Site Overview page.',
      ],
    },
    {
      subTitle: 'Tag',
      body: [
        'Tag import can be for existing or new tags. Existing tag names can be found on the Site Overview page. New tags can be created before being assigned to site(s).',
        'Tag names must be between 3 and 100 characters in length (inclusive), allowed characters are: A-Z, a-z, 0-9, and underscore (_)',
        `User will not be allowed to map the tag name which was assigned to another instance with the identical config file associated with the  site.\n\nE.g.:\nTag 1 - Config Instance 1 - SiteSetting Config File - Site 1 (Allowed)\nTag 2 - Config Instance 2 - SiteSetting Config File - Site 1 (Error: \${tagName} was assigned to another instance with the identical config file associated with the  site).`,
      ],
    },
  ],
};

export const IMPORT_SITES_INSTRUCTIONS = {
  title: 'Instructions for Import Sites',
  content: [
    {
      subTitle: 'name',
      body: ['[REQUIRED] Site name values must NOT exist.'],
    },
    {
      subTitle: 'address',
      body: [
        '[REQUIRED] Formatted Address as per Google Maps API. It should be a valid address that can be geocoded.',
      ],
    },
    {
      subTitle: 'site_group',
      body: ['[REQUIRED] Site group value must exist for Tenant.'],
    },
    {
      subTitle: 'site_tags',
      body: [
        '[REQUIRED] Tag import must be existing tags. Existing tag names can be found on the Site Overview page. New tags can be created before being assigned to site(s).',
        'Tag names must be between 3 and 100 characters in length (inclusive), allowed characters are: A-Z, a-z, 0-9, and underscore (_)',
      ],
    },
    {
      subTitle: 'hidden',
      body: ['[REQUIRED] Hidden sites will not be shown in the UI.'],
    },
    {
      subTitle: 'gvr',
      body: ['[REQUIRED] GVR reference ID must NOT exist.'],
    },
    {
      subTitle: 'fc_powered_off',
      body: [
        '[REQUIRED] FC powered off sites will not be shown in the UI. Y/N',
      ],
    },
    {
      subTitle: 'integration_id',
      body: ['[OPTIONAL] Integration ID values must NOT exist.'],
    },
    {
      subTitle: 'gstv',
      body: ['[OPTIONAL] GSTV reference ID must NOT exist.'],
    },
    {
      subTitle: 'hours_mon',
      body: [
        '[OPTIONAL] Hours of operation for Tuesday must be in HH:mm - HH:mm format, e.g. 09:00 - 17:00',
      ],
    },
    {
      subTitle: 'hours_wed',
      body: [
        '[OPTIONAL] Hours of operation for Wednesday must be in HH:mm - HH:mm format, e.g. 09:00 - 17:00',
      ],
    },
    {
      subTitle: 'hours_thur',
      body: [
        '[OPTIONAL] Hours of operation for Thursday must be in HH:mm - HH:mm format, e.g. 09:00 - 17:00',
      ],
    },
    {
      subTitle: 'hours_fri',
      body: [
        '[OPTIONAL] Hours of operation for Friday must be in HH:mm - HH:mm format, e.g. 09:00 - 17:00',
      ],
    },
    {
      subTitle: 'hours_sat',
      body: [
        '[OPTIONAL] Hours of operation for Saturday must be in HH:mm - HH:mm format, e.g. 09:00 - 17:00',
      ],
    },
    {
      subTitle: 'hours_sun',
      body: [
        '[OPTIONAL] Hours of operation for Sunday must be in HH:mm - HH:mm format, e.g. 09:00 - 17:00',
      ],
    },
    {
      subTitle: 'phone',
      body: ['[OPTIONAL] Phone number'],
    },
    {
      subTitle: 'email',
      body: ['[OPTIONAL] Email address'],
    },
    {
      subTitle: 'deployment_type',
      body: [
        '[OPTIONAL] Deployment type values must be one of the following: "maintenance-window", "immediate", "schedule". Default "maintenance-window"',
      ],
    },
  ],
};
