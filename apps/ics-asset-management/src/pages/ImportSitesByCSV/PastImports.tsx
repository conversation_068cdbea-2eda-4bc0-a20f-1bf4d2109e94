/* eslint-disable import/no-cycle */
import React, { memo, useMemo } from 'react';
import {
  LocationOn as LocationOnIcon,
  KeyboardBackspace as KeyboardBackspaceIcon,
} from '@mui/icons-material';
import { Link } from '@mui/material';
import { <PERSON>ridColumns, GridRenderCellParams } from '@mui/x-data-grid/models';
import {
  ASSET_MGMT_IMPORT_SITES_BY_CSV,
  ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV,
} from '../../constants/routes';
import { ImportByCsvCategory } from '../../constants/types';
import useToken from '../../hooks/useToken';
import MainLayout from '../../layouts/MainLayout';
import { PastImportsTable } from '../../components/CSVuploader/components';
import CSVTableCell from '../../components/CSVuploader/components/CSVTableCell';
import CSVTableNumberCell from '../../components/CSVuploader/components/CSVTableNumberCell';
import CSVStatusCell from '../../components/CSVuploader/components/CSVStatusCell';
import CSVOutcomeCell from '../../components/CSVuploader/components/CSVOutcomeCell';
import CSVDateCell from '../../components/CSVuploader/components/CSVDateCell';
import CSVActionsCell from '../../components/CSVuploader/components/CSVActionsCell';
import { ImportSitesContent } from './constants';

const PastImports = memo(
  ({
    category,
    pageSize,
  }: {
    category: ImportByCsvCategory;
    pageSize?: number;
  }) => {
    const token = useToken();

    const columns: GridColumns = useMemo(
      () => [
        {
          field: 'fileName',
          editable: false,
          flex: 3,
          headerName: 'File name',
          sortable: false,
          renderCell: (params: GridRenderCellParams) => (
            <CSVTableCell {...params} />
          ),
        },
        {
          field: 'totalCount',
          editable: false,
          flex: 1,
          headerName: 'Rows',
          sortable: false,
          renderCell: (params: GridRenderCellParams) => (
            <CSVTableNumberCell {...params} />
          ),
        },
        {
          field: 'status',
          editable: false,
          flex: 1,
          headerName: 'Status',
          sortable: false,
          renderCell: (params: GridRenderCellParams) => (
            <CSVStatusCell {...params} />
          ),
        },
        {
          field: 'processedCount',
          editable: false,
          flex: 2,
          headerName: 'Outcome',
          sortable: false,
          renderCell: (params: GridRenderCellParams) => (
            <CSVOutcomeCell row={params.row} />
          ),
        },
        {
          field: 'createdDate',
          editable: false,
          headerName: 'Created',
          flex: 2,
          sortable: false,
          renderCell: (params: GridRenderCellParams) => (
            <CSVDateCell
              {...params}
              format='{year}/{iso-month}/{date-pad} {hour-24-pad}:{minute-pad}:{second-pad}'
            />
          ),
        },
        {
          field: 'updatedDate',
          editable: false,
          headerName: 'Completed',
          flex: 2,
          sortable: false,
          renderCell: (params: GridRenderCellParams) =>
            params.row.status === 'completed' && params.value ? (
              <CSVDateCell
                {...params}
                format='{year}/{iso-month}/{date-pad} {hour-24-pad}:{minute-pad}:{second-pad}'
              />
            ) : (
              ''
            ),
        },
        {
          field: 'errorCount',
          editable: false,
          headerName: 'Errors',
          flex: 1,
          renderCell: ({
            value,
            row: { fileUploadId, fileName, totalCount, processedCount },
          }: GridRenderCellParams) =>
            Number(value) && Number(totalCount) === Number(processedCount) ? (
              <CSVActionsCell
                errorCount={value}
                fileName={fileName}
                fileUploadId={fileUploadId}
              />
            ) : (
              ''
            ),
          sortable: false,
        },
      ],
      []
    );

    return (
      <MainLayout
        header={{
          eyebrow: (
            <Link
              sx={{
                color: 'common.closeButton',
                '& .MuiSvgIcon-root': {
                  fontSize: '16px',
                },
                '& span': { fontSize: '12px', lineHeight: '16px' },
              }}
              href={`/${token?.company?.name?.toLowerCase()}/settings/import-sites`}
              underline='hover'
              display='flex'
              alignItems='center'
            >
              <KeyboardBackspaceIcon />
              <span>Company Settings</span>
            </Link>
          ),
          icon: <LocationOnIcon sx={{ color: 'common.icon' }} />,
          links: [
            {
              label: 'New Import',
              href: ASSET_MGMT_IMPORT_SITES_BY_CSV,
              key: 'devices',
            },
            {
              label: 'Past Imports',
              href: ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV,
              key: 'devices',
            },
          ],
          title: ImportSitesContent.pageTitle,
        }}
      >
        <PastImportsTable
          callbackOptions={{
            category,
            pageSize,
          }}
          contentOptions={{
            tableTitle: ImportSitesContent.pastImportsTableTitle,
            columns,
          }}
        />
      </MainLayout>
    );
  },
  (prevProps, nextProps) =>
    prevProps.category === nextProps.category &&
    prevProps.pageSize === nextProps.pageSize
);

export default PastImports;
