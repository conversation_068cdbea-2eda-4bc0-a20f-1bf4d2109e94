/* eslint-disable import/no-cycle */
import React, { lazy, memo, useCallback, Suspense } from 'react';
import {
  LocationOn as LocationOnIcon,
  KeyboardBackspace as KeyboardBackspaceIcon,
} from '@mui/icons-material';
import { LinearProgress, Link } from '@mui/material';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import useToken from '../../hooks/useToken';
import {
  ASSET_MGMT_IMPORT_SITES_BY_CSV,
  ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV,
} from '../../constants/routes';
import MainLayout from '../../layouts/MainLayout';
import { usePostImportByCsv } from '../../services/use-query';
import { ImportByCsvCategory } from '../../constants/types';
import { IMPORT_SITES_INSTRUCTIONS } from '../../constants/instructionModalsForCSVs';

import { initializePendo } from '../../utils/pendo';
import configService from '../../services/configService';

import {
  ImportSitesContent,
  MAX_ROWS_ALLOWED,
  MAX_SITE_TAGS_ALLOWED,
  REQUIRED_CSV_HEADERS,
  UNIQUE_KEY,
} from './constants';

const CSVuploader = lazy(() => import('../../components/CSVuploader'));

const ImporSitesByCSV = memo(
  ({ category }: { category: ImportByCsvCategory }) => {
    const { enqueueSnackbar } = useSnackbar();
    const navigate = useNavigate();
    const token = useToken();

    configService().then(config => {
      if (config?.pendo?.enabled) {
        initializePendo(token?.sub);
      }
    });

    const { mutate: uploadCSVFile, isLoading: isUploading } =
      usePostImportByCsv({
        retry: false,
        onError: (err: any) => {
          if (err?.response?.data) {
            enqueueSnackbar(err.response.data, { variant: 'error' });
          } else {
            enqueueSnackbar('Error uploading file', { variant: 'error' });
          }
        },
        onSuccess: data => {
          if (data === 204) {
            enqueueSnackbar('File uploaded successfully', {
              variant: 'success',
            });
            navigate(ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV);
            return;
          }
          enqueueSnackbar('Error uploading file', { variant: 'error' });
        },
      });

    const handleOnFormSubmitValidation = useCallback(
      csvFile => {
        if (!isUploading && Boolean(csvFile?.length)) {
          const file = csvFile[0];
          const formData = new FormData();
          formData.append('category', category);
          formData.append('file', file, file.name);
          uploadCSVFile(formData);
        }
      },
      [isUploading]
    );

    return (
      <MainLayout
        header={{
          eyebrow: (
            <Link
              sx={{
                color: 'common.closeButton',
                '& .MuiSvgIcon-root': {
                  fontSize: '16px',
                },
                '& span': { fontSize: '12px', lineHeight: '16px' },
              }}
              href={`/${token?.company?.name?.toLowerCase()}/settings/import-sites`}
              underline='hover'
              display='flex'
              alignItems='center'
            >
              <KeyboardBackspaceIcon />
              <span>Company Settings</span>
            </Link>
          ),
          icon: <LocationOnIcon sx={{ color: 'common.icon' }} />,
          links: [
            {
              label: 'New Import',
              href: ASSET_MGMT_IMPORT_SITES_BY_CSV,
              key: 'devices',
            },
            {
              label: 'Past Imports',
              href: ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV,
              key: 'devices',
            },
          ],
          title: ImportSitesContent.pageTitle,
        }}
        isLoading={isUploading}
      >
        <Suspense fallback={<LinearProgress />}>
          <CSVuploader
            contentOptions={{
              ...ImportSitesContent,
              formInstructions: IMPORT_SITES_INSTRUCTIONS,
              uniqueKey: UNIQUE_KEY,
              requiredCsvHeaders: REQUIRED_CSV_HEADERS,
            }}
            callbackOptions={{
              isUploading,
              onFormSubmitValidation: handleOnFormSubmitValidation,
              maxRows: MAX_ROWS_ALLOWED,
              maxSiteTags: MAX_SITE_TAGS_ALLOWED,
            }}
          />
        </Suspense>
      </MainLayout>
    );
  }
);

export default ImporSitesByCSV;
