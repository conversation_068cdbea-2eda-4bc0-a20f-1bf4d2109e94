export enum ImportSitesContent {
  csvFileName = 'site-import',
  csvHeader = 'name,address,site_group,site_tags,hidden,fc_powered_off,hours_mon,hours_tue,hours_wed,hours_thur,hours_fri,hours_sat,hours_sun,integration_id,gstv,gvr,phone,email,deployment_type',
  formActionButtonLabel = 'Import Sites',
  formDescription = 'Import sites. This will overwrite existing attribute values in sites.',
  formTitle = 'Import Sites (CSV)',
  pageTitle = 'Site Import Tool',
  pastImportsTableTitle = 'Past Site Imports',
}
export const REQUIRED_CSV_HEADERS = [
  'name',
  'address',
  'site_group',
  'site_tags',
  'hidden',
  'fc_powered_off',
  'gvr',
];
export const UNIQUE_KEY = 'name';
export const MAX_ROWS_ALLOWED = 10_000;
export const MAX_SITE_TAGS_ALLOWED = 50;
