/* eslint-disable import/no-cycle */
import React, {
  FC,
  Suspense,
  useEffect,
  useMemo,
  useState,
  MouseEvent,
} from 'react';
import {
  Info as InfoIcon,
  Warning as WarningIcon,
  LocationOn as LocationOnIcon,
  KeyboardBackspace as KeyboardBackspaceIcon,
  WarningAmber,
} from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  Link,
  Drawer,
  ToggleButtonGroup,
  ToggleButton,
  Button,
  Typography,
  Dialog,
  DialogContent,
  DialogActions,
} from '@mui/material';
import lowerCase from 'lodash/lowerCase';
import { useNavigate, useParams } from 'react-router';
import { enqueueSnackbar } from 'notistack';
import { TabsNames } from '../constants/entities';
import {
  ASSET_MGMT_SITE_DETAIL_TABS,
  ASSET_MGMT_SITE_LIST,
} from '../constants/routes';
import { LOADING_MSG, ALL_SITE_MSG } from '../constants/messages';
import { TabProps, TabType } from '../constants/types';
import { drawer as drawerTheme } from '../constants/theme';
import { useMergeState } from '../hooks/useMergeStates';
import MainLayout from '../layouts/MainLayout';
import {
  useGetDevices,
  useGetSite,
  useGetStagedStatus,
  useMismatchedConfigBySiteId,
  useGetDuplicateSitename,
} from '../services/use-query';
import {
  getCurrentCompanyId,
  hasFeatureFlag,
  hasSomeUserRoles,
  hasUserRole,
} from '../utils/helpers';
import FeatureFlags from '../constants/featureFlags';
import UserRoles from '../constants/userRoles';
import { LinkTabProps } from '../components/Header';
import lazyWithPreload from '../utils/lazyWithPreload';
import useHasPermissions from '../hooks/useHasPermissions';
import TwoFactorVerification from '../components/TwoFactorAuthentication/TwoFactorAuthentication';
import { postAutomaticDeployment } from '../services/api-request';
import { SiteIssueDrawer, SiteOverviewDrawer } from './SiteDetailDrawers';
import { PendingTransferContext } from './PendingTransferContext';

const CallToActionBtn = lazyWithPreload(
  () => import('../components/CallToActionBtn')
);
const AttributesTab = lazyWithPreload(
  () => import('./SiteDetailTabs/AttributesTab')
);
const DevicesTab = lazyWithPreload(
  () => import('./SiteDetailTabs/DevicesTab/DevicesTab')
);
const PaymentsTab = lazyWithPreload(
  () => import('./SiteDetailTabs/PaymentsTab')
);
const ApplicationsTab = lazyWithPreload(
  () => import('./SiteDetailTabs/ApplicationTab/ApplicationsTab')
);

const ConfigurationTab = lazyWithPreload(
  () => import('./SiteDetailTabs/ConfigurationTab/ConfigurationTab')
);

const ApplicationsAndPaymentsTab = lazyWithPreload(
  () =>
    import(
      './SiteDetailTabs/ApplicationsAndPaymentsTab/ApplicationsAndPaymentsTab'
    )
);

DevicesTab.preload();
AttributesTab.preload();
PaymentsTab.preload();
ApplicationsTab.preload();
ApplicationsAndPaymentsTab.preload();

/**
 *  Types
 */
enum SelectedDrawer {
  SITE_ISSUE,
  SITE_OVERVIEW,
}

const TabComponents: { [key in Lowercase<TabType>]: FC<TabProps> } = {
  devices: DevicesTab,
  attributes: AttributesTab,
  payments: PaymentsTab,
  configuration: ConfigurationTab,
  applicationsandpayments: ApplicationsAndPaymentsTab,
};

interface State {
  title?: string;
  currentDrawer?: SelectedDrawer;
  isDrawerOpen: boolean;
  eyebrow?: JSX.Element;
  hasSiteAttributesTab: boolean;
  timezoneId?: string | null;
}

/**
 * Styles
 * TODO: Move to separate file
 */

const iconButtonStyles = {
  padding: '8px',
  width: '32px',
  height: '32px',
  minWidth: '32px',
  maxHeight: '32px',
  border: 'none',
  borderRadius: 'inherit !important',
  '& .MuiButtonBase-root': {
    borderRadius: 'inherit !important',
  },
  '& .MuiSvgIcon-root': {
    color: 'common.closeButton',
  },
};

/**
 * Helpers
 */
const getTabContent = (tab: string) =>
  !TabComponents[tab] ? DevicesTab : TabComponents[tab];

/**
 * Partial Components
 * TODO: Move to separate file
 */
const headerEyebrow = (
  <Link
    sx={{
      color: 'common.closeButton',
      '& .MuiSvgIcon-root': {
        fontSize: '16px',
      },
      '& span': { fontSize: '12px', lineHeight: '16px' },
    }}
    href={ASSET_MGMT_SITE_LIST}
    underline='hover'
    display='flex'
    alignItems='center'
  >
    <KeyboardBackspaceIcon />
    <span>{ALL_SITE_MSG}</span>
  </Link>
);

const SiteDetail = () => {
  const companyIdFromToken = getCurrentCompanyId();
  const { tab, siteId } = useParams();
  const navigate = useNavigate();
  const TabContent = getTabContent(tab);
  const [isWarningDialogOpen, setWarningDialogOpen] = useState(false);
  const [isMfaOpen, setMfaOpen] = useState(false);
  const [selectedWindow] = useState('all');
  const [
    {
      title,
      eyebrow,
      currentDrawer,
      isDrawerOpen,
      hasSiteAttributesTab,
      timezoneId,
    },
    setStates,
  ] = useMergeState<State>({
    isDrawerOpen: false,
    currentDrawer: null,
    title: LOADING_MSG,
    eyebrow: headerEyebrow,
    hasSiteAttributesTab: false,
    timezoneId: null,
  });

  const [isSiteTransferApprovalModalOpen, setSiteTransferApprovalModalOpen] =
    useState<boolean>(false);
  const [isMismatchedConfigModalOpen, setMismatchedConfigModalOpen] =
    useState<boolean>(false);
  const [hasUserDeployedConfig, setUserHasDeployedConfig] =
    useState<boolean>(false);

  const hasFuelPricingAccess = useHasPermissions({
    companyFeatureFlags: [FeatureFlags.FUEL_PRICE_MGMT],
    userRoles: [UserRoles.FUEL_PRICE_MGMT_VIEW],
  });

  const hasPaymentsAccess = hasFeatureFlag(FeatureFlags.PAYMENT_DASHBOARD);

  const hasSiteAttributesFeatureFlag = hasFeatureFlag(
    FeatureFlags.SITE_ATTRIBUTES
  );

  const hasConfigMismatchFetchUserRole =
    hasSomeUserRoles([
      UserRoles.CONFIG_MGMT_DEPLOY,
      UserRoles.CONFIG_MGMT_PUBLISH,
      UserRoles.CONFIG_MGMT_ASSIGN,
    ]) && hasFeatureFlag(FeatureFlags.CONFIG_MGMT);

  const hasConfigMismatchDeployUserRole = hasUserRole(
    UserRoles.CONFIG_MGMT_DEPLOY
  );

  const hasApplicationsAndPaymentsAccess = hasFeatureFlag(
    FeatureFlags.APPLICATION_PAYMENT_DASHBOARD_MERGE
  );

  const {
    data: siteDetailData,
    isFetching: isSiteDetailDataLoading,
    isStale: isSiteDetailDataStale,
    refetch: refetchSiteDetailData,
  } = useGetSite(siteId);

  const companyIdFromResponse = siteDetailData?.owner?.id;

  const isMatchingCompany =
    Boolean(companyIdFromResponse) &&
    companyIdFromResponse === companyIdFromToken;

  const { data } = useGetDevices({
    siteId,
  });
  const { results: devices } = data || {};

  const {
    data: isSiteTransferPending,
    isFetching: isSiteTransferLoading,
    isStale: isSiteTransferStale,
    refetch: siteTransferPendingStatusRefetch,
  } = useGetStagedStatus('sites', siteId, {
    enabled: isMatchingCompany && hasSiteAttributesFeatureFlag,
  });

  const { data: getDuplicateSitenameResponse } = useGetDuplicateSitename(
    'sites',
    siteId,
    {
      enabled: isMatchingCompany && hasSiteAttributesFeatureFlag,
    }
  );

  const {
    data: mismatchedConfigs,
    isFetching: isMismatchedConfigLoading,
    refetch: mismatchedConfigRefetch,
  } = useMismatchedConfigBySiteId(
    {
      siteId,
    },
    {
      retry: false,
      enabled: hasConfigMismatchFetchUserRole,
    }
  );

  const automaticDeployment = async (mfaCode: string) => {
    try {
      const payload = {
        deploymentType: selectedWindow,
        entityType: 'site',
        entityId: [siteId],
        deployDisabledSites: selectedWindow,
        mfaCode,
      };

      await postAutomaticDeployment(payload);

      enqueueSnackbar('Deploy action completed successfully', {
        variant: 'success',
        autoHideDuration: 2000,
      });
    } catch (error) {
      enqueueSnackbar(error.message, {
        variant: 'error',
        autoHideDuration: 2000,
      });
    }
  };

  const handleSelectedDrawer = (
    event: MouseEvent<HTMLElement>,
    drawer: SelectedDrawer
  ) => {
    event?.preventDefault();
    setStates({
      currentDrawer: drawer,
      isDrawerOpen: drawer !== null && drawer >= 0,
    });
  };

  const handleDeployNowClick = () => {
    siteDetailData?.disableCmAutomaticDeployments
      ? setWarningDialogOpen(true)
      : setMfaOpen(true);

    // Open warning dialog when "Deploy Now" is clicked
  };

  const handleWarningDialogClose = () => {
    setWarningDialogOpen(false);
  };

  const handleProceedWithDeployment = () => {
    setWarningDialogOpen(false);
    setMfaOpen(true);
  };

  const handleMfaSubmit = (mfaCode: string) => {
    automaticDeployment(mfaCode);
    setMfaOpen(false);
  };

  useEffect(() => {
    setStates({
      title: siteDetailData?.name,
      timezoneId: siteDetailData?.timezoneId,
    });
  }, [siteDetailData, setStates]);

  useEffect(() => {
    if (siteDetailData) {
      const hasAttributesTab =
        hasSiteAttributesFeatureFlag && isMatchingCompany;
      setStates({ hasSiteAttributesTab: hasAttributesTab });
      if (!hasAttributesTab && tab === 'attributes') {
        const redirectionUrl = `${ASSET_MGMT_SITE_DETAIL_TABS}`
          .replace(':siteId', siteId)
          .replace(':tab', 'details');
        refetchSiteDetailData();
        navigate(redirectionUrl);
        return;
      }
    }

    if (siteDetailData && isSiteDetailDataStale) {
      // Re-Fetch on initial load when staled
      refetchSiteDetailData();
    }

    if (
      isMatchingCompany &&
      hasSiteAttributesFeatureFlag &&
      isSiteTransferStale
    ) {
      // Re-Fetch on initial load when stale
      siteTransferPendingStatusRefetch();
    }
  }, [
    hasSiteAttributesFeatureFlag,
    siteDetailData,
    isMatchingCompany,
    devices,
    tab,
  ]);

  const memoizedLinks = useMemo<LinkTabProps[]>(
    () =>
      Object.values(TabsNames)
        .map((tabName: string) => {
          if (tabName === TabsNames.Attributes && !hasSiteAttributesTab) {
            return null;
          }
          if (tabName === TabsNames.Payments && !hasPaymentsAccess) {
            return null;
          }
          if (
            tabName === TabsNames.ApplicationsAndPayments &&
            !hasApplicationsAndPaymentsAccess
          ) {
            return null;
          }

          // To-do : Need to change the logic
          let key = lowerCase(tabName) as Lowercase<TabType>;
          if (tabName === TabsNames.Configuration) {
            key = 'configuration';
          } else if (tabName === TabsNames.ApplicationsAndPayments) {
            key = 'applicationsandpayments';
          }
          return {
            key,
            href: `${ASSET_MGMT_SITE_LIST}/${siteId}/${key}`,
            label: tabName,
          };
        })
        .filter(Boolean),
    [hasSiteAttributesTab, hasFuelPricingAccess]
  );

  const contextObject = useMemo(
    () => ({
      isSiteTransferPending,
      isSiteTransferLoading,
      isSiteTransferApprovalModalOpen,
      hasConfigMismatchFetchUserRole,
      setSiteTransferApprovalModalOpen,
      mismatchedConfigs,
      hasConfigMismatchDeployUserRole,
      hasUserDeployedConfig,
      mismatchedConfigRefetch,
      isMismatchedConfigLoading,
      isMismatchedConfigModalOpen,
      setMismatchedConfigModalOpen,
      setUserHasDeployedConfig,
      getDuplicateSitenameResponse,
    }),
    [
      isSiteTransferPending,
      isSiteTransferLoading,
      isSiteTransferApprovalModalOpen,
      hasConfigMismatchFetchUserRole,
      setSiteTransferApprovalModalOpen,
      mismatchedConfigs,
      hasConfigMismatchDeployUserRole,
      hasUserDeployedConfig,
      mismatchedConfigRefetch,
      isMismatchedConfigLoading,
      isMismatchedConfigModalOpen,
      setMismatchedConfigModalOpen,
      setUserHasDeployedConfig,
      getDuplicateSitenameResponse,
    ]
  );

  const dialogFontStyles = {
    fontFamily: 'Roboto',
    fontWeight: 500,
    fontSize: '11px',
    lineHeight: '24px',
    letterSpacing: '0.1px',
  };

  return (
    <MainLayout
      isLoading={isSiteDetailDataLoading}
      header={{
        title,
        icon: <LocationOnIcon sx={{ color: 'common.icon' }} />,
        eyebrow,
        links: memoizedLinks,
        action: (
          <Box display='flex' alignItems='center' sx={{ gap: 0.5 }}>
            <ToggleButtonGroup
              orientation='horizontal'
              value={currentDrawer}
              exclusive
              onChange={handleSelectedDrawer}
            >
              <ToggleButton
                sx={iconButtonStyles}
                value={SelectedDrawer.SITE_OVERVIEW}
              >
                <InfoIcon fontSize='small' />
              </ToggleButton>
              <ToggleButton
                sx={iconButtonStyles}
                value={SelectedDrawer.SITE_ISSUE}
              >
                <WarningIcon fontSize='small' />
              </ToggleButton>
            </ToggleButtonGroup>
            <PendingTransferContext.Provider value={contextObject}>
              <Suspense fallback={<CircularProgress />}>
                <CallToActionBtn
                  deployNow={handleDeployNowClick}
                  siteId={siteId}
                />
              </Suspense>
            </PendingTransferContext.Provider>
          </Box>
        ),
      }}
    >
      {isSiteDetailDataLoading && !siteId ? (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box
          sx={{
            position: 'relative',
            display: 'flex',
            height: '100%',
            overflow: 'hidden',
          }}
        >
          <Box
            id={siteId}
            sx={{
              backgroundColor: 'common.backgroundLight',
              flex: 1,
              height: '100%',
              overflow: 'hidden auto',
              transition: 'margin 0.2s ease',
            }}
          >
            <PendingTransferContext.Provider value={contextObject}>
              <Suspense fallback={<CircularProgress />}>
                <TabContent siteId={siteId} siteTimeZone={timezoneId} />
              </Suspense>
            </PendingTransferContext.Provider>
          </Box>
          <Drawer
            hideBackdrop
            sx={{
              '& .MuiDrawer-paper': {
                width: drawerTheme.width,
                top: 123,
                bottom: 0,
                height: 'calc(100% - 123px)',
              },
            }}
            anchor='right'
            open={isDrawerOpen}
            onClose={() => {
              handleSelectedDrawer(null, null);
            }}
            ModalProps={{
              slots: { backdrop: 'div' },
              slotProps: {
                root: {
                  style: {
                    position: 'absolute',
                    top: 'unset',
                    bottom: 'unset',
                    left: 'unset',
                    right: 'unset',
                  },
                },
              },
            }}
          >
            {currentDrawer === SelectedDrawer.SITE_ISSUE && (
              <SiteIssueDrawer
                siteId={siteId}
                toggleCallback={() => {
                  handleSelectedDrawer(null, null);
                }}
              />
            )}
            {currentDrawer === SelectedDrawer.SITE_OVERVIEW && isDrawerOpen && (
              <SiteOverviewDrawer
                siteId={siteId}
                toggleCallback={() => {
                  handleSelectedDrawer(null, null);
                }}
                totalDevices={(devices as unknown[])?.length ?? 0}
              />
            )}
          </Drawer>
        </Box>
      )}
      {/* Warning Dialog */}
      <Dialog
        open={isWarningDialogOpen}
        onClose={handleWarningDialogClose}
        sx={{
          '& .MuiDialog-container': {
            '& .MuiPaper-root': {
              width: '70%',
            },
          },
        }}
      >
        {siteDetailData?.disableCmAutomaticDeployments && (
          <Box
            display='flex'
            alignItems='center'
            gap={1}
            sx={{ padding: '16px 24px' }}
          >
            <WarningAmber sx={{ color: 'orange', fontSize: '28px' }} />
            <Typography
              variant='h6'
              sx={{ ...dialogFontStyles, fontSize: '18px', fontWeight: 600 }}
            >
              Warning
            </Typography>
          </Box>
        )}

        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: !siteDetailData?.disableCmAutomaticDeployments ? 4 : 2,
          }}
        >
          {siteDetailData?.disableCmAutomaticDeployments && (
            <Typography color='error'>
              Automatic deployment is disabled for this site. Are you sure you
              want to proceed with manual deployment? It will deploy all the
              pending jobs.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Box sx={{ display: 'flex', marginRight: '20px', gap: 45 }}>
            <Button
              onClick={handleWarningDialogClose}
              color='primary'
              variant='outlined'
              sx={dialogFontStyles}
            >
              Cancel
            </Button>
            <Button
              onClick={handleProceedWithDeployment}
              color='primary'
              variant='contained'
              sx={dialogFontStyles}
            >
              Proceed
            </Button>
          </Box>
        </DialogActions>
      </Dialog>

      {/* MFA Dialog for "Deploy Now" action */}
      <TwoFactorVerification
        open={isMfaOpen}
        onClose={() => setMfaOpen(false)}
        onSubmit={handleMfaSubmit}
      />
    </MainLayout>
  );
};

export default SiteDetail;
