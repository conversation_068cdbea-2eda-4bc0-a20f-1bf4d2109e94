import React, {
  FC,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Tooltip from '@mui/material/Tooltip';
import Checkbox from '@mui/material/Checkbox';
import Accordion from '@mui/material/Accordion';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import LinearProgress from '@mui/material/LinearProgress';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

import uniqWith from 'lodash/uniqWith';
import isEqual from 'lodash/isEqual';
import { ApplicationsAndPaymentsTabContext } from '../context';
import { theme } from '../../../../constants/theme';
import { NO_APPLICATIONS_PRESENT_IN_DEVICE } from '../../../../constants/messages';
import {
  useGetApplicationDetails,
  useGetPaymentDashboard,
  useGetSite,
} from '../../../../services/use-query';
import {
  ApplicationDevice,
  ApplicationStatus,
  ISelectedApplication,
  PaymentTabCardType,
} from '../../../../constants/types';
import { stylesAccordion, stylesTooltip } from '../styles';
import {
  APPLICATION_SYSTEM,
  MONITORING_STATUS_VALUE,
  PAYMENT_TAB_ARRAY_FILTERS_SEPARATOR,
} from '../../PaymentsTab/constants';
import { useMergeState } from '../../../../hooks/useMergeStates';
import { PAYMENT_DASHBOARD_PAGE_SIZE } from '../../../../constants/app';
import ApplicationNameTag from './ApplicationNameTag';
import ApplicationStatusTag from './ApplicationStatusTag';
import ApplicationActionsMenu from './ApplicationActionsMenu';
import ApplicationIntensiveMonitoringToggle from './ApplicationIntensiveMonitoringToggle';

interface DeviceAccordionProps {
  siteId: string;
  device: ApplicationDevice;
  isExpanded: boolean;
  onChange: (
    deviceId: number,
    expanded: boolean,
    device: ApplicationDevice
  ) => void;
}

export interface State {
  paymentDashboardDataSet: Array<PaymentTabCardType>;
  totalResults: number | undefined;
  numberOfApplications: number | undefined;
  numberOfShowedApplications: number | undefined;
  appNameFilter: string[];
  deviceNameFilter: string[];
  pageIndex: number;
  pageSize: number;
  hasMore: boolean;
}

const isValidJSON = (str: unknown) => {
  if (typeof str !== 'string') return false;
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

const DeviceAccordion: FC<DeviceAccordionProps> = ({
  siteId,
  device,
  isExpanded,
  onChange,
}) => {
  const [
    {
      paymentDashboardDataSet,
      pageIndex,
      pageSize,
      totalResults,
      numberOfApplications,
      appNameFilter,
      deviceNameFilter,
    },
    setStates,
  ] = useMergeState<State>({
    paymentDashboardDataSet: [],
    totalResults: undefined,
    numberOfApplications: undefined,
    numberOfShowedApplications: undefined,
    appNameFilter: [],
    deviceNameFilter: [],
    pageIndex: 0,
    pageSize: PAYMENT_DASHBOARD_PAGE_SIZE,
    hasMore: true,
  });

  const processedSystemAppRef = useRef([]);

  const mergeDevicesSystemsData = useMemo(
    () => (results, currentDataSet) => {
      const deviceAppNameMap = new Map();
      const systemAppNameMap = new Map();

      results.forEach(({ deviceId, appName, ...rest }) => {
        const mapToUse =
          appName === APPLICATION_SYSTEM ? systemAppNameMap : deviceAppNameMap;
        mapToUse.set(
          appName === APPLICATION_SYSTEM
            ? deviceId.toString()
            : `${deviceId}_${appName}`,
          { deviceId, appName, ...rest }
        );
      });

      const hasNonDefaultStatus = dashboard =>
        dashboard.some(
          item =>
            item.label.includes('Status') &&
            item.value !== MONITORING_STATUS_VALUE.DEFAULT
        );

      const extractDedupLabels = dashboard => [
        ...new Set(
          dashboard
            .filter(item => item.label.includes('Labels'))
            .map(item =>
              isValidJSON(item.value) ? JSON.parse(item.value) : []
            )
            .flat()
        ),
      ];

      const extractNonDefaultLabels = dashboard =>
        extractDedupLabels(dashboard).filter(
          label => label !== MONITORING_STATUS_VALUE.DEFAULT
        );

      const isAgentAndAdaptorStatusNotExist = ({ paymentDashboard }) => {
        const filteredStatusValue = paymentDashboard.filter(
          dashboard =>
            dashboard.label.includes('Status') && dashboard.value.length
        );
        const hasAgentStatus = filteredStatusValue.some(
          dashboard => dashboard.label === 'Agent Status'
        );
        const hasAdaptorStatus = filteredStatusValue.some(
          dashboard => dashboard.label === 'Adaptor Status'
        );

        return !(hasAgentStatus && hasAdaptorStatus);
      };

      // Merge system data into appName data
      deviceAppNameMap.forEach((appData, key) => {
        const [deviceId] = key.split('_');
        if (
          systemAppNameMap.has(deviceId.toString()) &&
          !processedSystemAppRef.current.some(
            sa => sa.toString() === deviceId.toString()
          )
        ) {
          const systemAppData = systemAppNameMap.get(deviceId.toString());
          const paymentDashboard = [
            ...appData.paymentDashboard,
            ...systemAppData.paymentDashboard,
          ];

          // Inject `toggleLabel`
          const [label] = extractNonDefaultLabels(
            systemAppData.paymentDashboard
          );
          const toggleLabel =
            label === MONITORING_STATUS_VALUE.INTENSIVE ? label : undefined;

          // Inject `isToggleOn`
          const isToggleOn = hasNonDefaultStatus(
            systemAppData.paymentDashboard
          );

          // Inject `hideToggle`
          const dedupLabels = extractDedupLabels(
            systemAppData.paymentDashboard
          );
          const hideToggle =
            isAgentAndAdaptorStatusNotExist(systemAppData) ||
            dedupLabels.length === 0 ||
            dedupLabels.every(
              dedupLabel => dedupLabel === MONITORING_STATUS_VALUE.DEFAULT
            ) ||
            !toggleLabel;

          deviceAppNameMap.set(key, {
            ...appData,
            paymentDashboard,
            toggleLabel,
            isToggleOn,
            hideToggle,
          });
          processedSystemAppRef.current.push(systemAppData.deviceId);
        } else if (currentDataSet.length === 0 || !currentDataSet) {
          deviceAppNameMap.set(key, {
            ...appData,
            hideToggle: true,
          });
        } else {
          deviceAppNameMap.set(key, {
            ...appData,
          });
        }
      });
      return Array.from(deviceAppNameMap.values());
    },
    []
  );

  const classesAccordion = stylesAccordion();
  const classesTooltip = stylesTooltip();

  const { selectedApplications, addRemoveSelectedApplication } = useContext(
    ApplicationsAndPaymentsTabContext
  );

  const { data, isLoading } = useGetApplicationDetails(
    siteId,
    0,
    100,
    true,
    isExpanded ? device.deviceId : null
  );

  const { data: siteDetailData } = useGetSite(siteId);

  const { data: paymentDashboardData } = useGetPaymentDashboard(
    siteId,
    pageIndex,
    pageSize,
    appNameFilter.join(PAYMENT_TAB_ARRAY_FILTERS_SEPARATOR) || undefined,
    deviceNameFilter.join(PAYMENT_TAB_ARRAY_FILTERS_SEPARATOR) || undefined,
    {
      cacheTime: 0,
      enabled: !!siteDetailData,
    }
  );

  const accordionOnChange = (
    event: React.SyntheticEvent,
    expanded: boolean
  ) => {
    if (!device.applications.length) {
      event.stopPropagation();
    } else {
      onChange(device.deviceId, expanded, device);

      // de-select all application(s) of the device on collapse
      if (!expanded) {
        device.applications.forEach(application => {
          const currentApplication: ISelectedApplication = {
            ...application,
            deviceId: device.deviceId,
            deviceName: device.deviceName,
          };
          addRemoveSelectedApplication(currentApplication, false);
        });
      }
    }
  };

  useEffect(() => {
    if (paymentDashboardData && siteDetailData) {
      const { resultsMetadata, results } = paymentDashboardData;
      const cloneResults = structuredClone(results);
      const mergedPreData = uniqWith(
        [...paymentDashboardDataSet, ...cloneResults],
        isEqual
      );
      const mergedData = mergeDevicesSystemsData(
        mergedPreData,
        paymentDashboardDataSet
      );
      if (mergedData.length) {
        setStates({
          paymentDashboardDataSet: mergedData,
          totalResults: resultsMetadata.totalResults,
          numberOfApplications: resultsMetadata.numberOfApplications,
          numberOfShowedApplications: new Set(
            mergedData.map(dashboard => dashboard.appName)
          ).size,
          pageIndex: resultsMetadata.pageIndex,
          hasMore:
            mergedData.length < resultsMetadata.totalResults &&
            resultsMetadata.pageIndex <
              Math.ceil(resultsMetadata.totalResults / pageSize),
        });
      } else {
        setStates({
          paymentDashboardDataSet: [],
          totalResults,
          numberOfApplications,
          numberOfShowedApplications: 0,
          pageIndex,
          hasMore: 0,
        });
      }
    }
  }, [paymentDashboardData, siteDetailData, setStates]);

  const getDeviceStatus = (applications: ApplicationStatus[]) => {
    const isNotRunning =
      !applications?.length ||
      applications?.some(
        application => application?.status.toLowerCase() !== 'running'
      );
    return isNotRunning ? (
      <HighlightOffIcon
        sx={{ color: theme.palette.common.errorRed, fontSize: '18px' }}
      />
    ) : (
      <CheckCircleOutlineIcon
        sx={{ color: theme.palette.common.iconGreen, fontSize: '18px' }}
      />
    );
  };

  const applicationsMetaData = useMemo(
    () =>
      (device?.applications ?? []).sort((a, b) => {
        if (a.appName === 'infx-eps') return -1;
        if (b.appName === 'infx-eps') return 1;
        return a.appName.localeCompare(b.appName);
      }),
    [device]
  );

  const updateSelectedApplications =
    (application: ApplicationStatus) => (_, checked: boolean) => {
      const selectedApplication: ISelectedApplication = {
        ...application,
        deviceId: device.deviceId,
        deviceName: device.deviceName,
      };

      addRemoveSelectedApplication(selectedApplication, checked);
    };

  const paymentMetaData = useMemo(() => {
    const result = {};

    if (data && data.results.length) {
      data.results.forEach(metaData => {
        const { appName } = metaData;

        if (metaData.paymentDashboard.length) {
          metaData.paymentDashboard.forEach(field => {
            if (!result[appName]) {
              result[appName] = [];
            }
            result[appName].push(field);
          });
        }
      });
    }

    return result;
  }, [data]);

  const renderPaymentMetaData = useCallback(
    applicationName => {
      if (paymentMetaData[applicationName]?.length) {
        const transformedMetaData = [
          ...paymentMetaData[applicationName],
          ...Array(3).fill({ label: 'NA', value: 'NA' }),
        ].slice(0, 3);

        const getText = metadata =>
          metadata.label === 'NA'
            ? 'NA'
            : `${metadata.label} : ${metadata.value}`;

        return transformedMetaData.map(metadata => (
          <Grid
            item
            xs={4}
            display='flex'
            alignItems='center'
            justifyContent='center'
            padding='8px'
            borderRight='1px solid #48464A'
          >
            <Tooltip key={metadata.value} title={getText(metadata)}>
              <Typography
                fontSize='16px'
                fontWeight={400}
                color='#48464A'
                whiteSpace='nowrap'
                textOverflow='ellipsis'
                overflow='hidden'
              >
                {getText(metadata)}
              </Typography>
            </Tooltip>
          </Grid>
        ));
      }
      return (
        <Grid
          item
          xs={12}
          display='flex'
          alignItems='center'
          justifyContent='center'
          padding='8px'
        >
          <Typography
            width={1}
            fontSize='16px'
            fontWeight={400}
            color='#48464A'
            textAlign='center'
          >
            No meta-data
          </Typography>
        </Grid>
      );
    },
    [paymentMetaData]
  );

  const isApplicationSelected = (application: ApplicationStatus): boolean =>
    selectedApplications.some(
      app =>
        app.appName === application.appName && app.deviceId === device.deviceId
    );

  const getDeviceTooltip = (): string => {
    const isNotRunning =
      !device?.applications?.length ||
      device?.applications?.some(
        application => application?.status.toLowerCase() !== 'running'
      );
    return isNotRunning ? 'Device Out of Service' : 'Device Active';
  };

  const getExpandIcon = (): JSX.Element =>
    device?.applications?.length ? (
      <ExpandMoreIcon className={classesAccordion.summaryIcon} />
    ) : (
      <Tooltip title={NO_APPLICATIONS_PRESENT_IN_DEVICE} placement='top'>
        <ExpandMoreIcon
          className={`${classesAccordion.summaryIcon} ${classesAccordion.disabled}`}
        />
      </Tooltip>
    );

  return (
    <Accordion
      disableGutters
      key={device.deviceId}
      expanded={isExpanded}
      onChange={accordionOnChange}
    >
      <AccordionSummary
        expandIcon={getExpandIcon()}
        aria-controls={`${device.deviceId}-accordion`}
        id={`${device.deviceId}-accordion-header`}
        className={classesAccordion.summary}
      >
        <Box display='flex' gap='12px' alignItems='center'>
          {getDeviceStatus(device.applications)}

          <Tooltip
            key={device.deviceId}
            title={getDeviceTooltip()}
            placement='top'
            arrow
            classes={{
              arrow: classesTooltip.arrow,
              tooltip: classesTooltip.tooltip,
            }}
          >
            <Typography
              component='span'
              className={classesAccordion.typography}
            >
              {device.deviceName}
            </Typography>
          </Tooltip>

          <Box display='flex' gap='8px'>
            {applicationsMetaData.length
              ? applicationsMetaData.map(application => (
                  <ApplicationNameTag application={application} />
                ))
              : NO_APPLICATIONS_PRESENT_IN_DEVICE}
          </Box>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <Grid container spacing={2}>
          {applicationsMetaData.map(application => (
            <Grid item xs={6} key={application.appName}>
              <Card>
                <CardContent>
                  <Box display='flex' justifyContent='space-between'>
                    {/* CARD HEADER */}
                    <Box display='flex' gap='8px' alignItems='center'>
                      <Checkbox
                        sx={{ width: '24px', height: '24px' }}
                        onChange={updateSelectedApplications(application)}
                        checked={isApplicationSelected(application)}
                      />

                      <Typography
                        noWrap
                        fontSize='21px'
                        lineHeight='24px'
                        fontWeight={400}
                      >
                        {application.appName}
                      </Typography>

                      <ApplicationStatusTag application={application} />

                      {paymentDashboardDataSet
                        ?.filter(item => item.deviceId === device.deviceId)
                        ?.map(
                          ({
                            deviceId,
                            toggleLabel,
                            isToggleOn,
                            hideToggle,
                          }) => (
                            <ApplicationIntensiveMonitoringToggle
                              deviceId={deviceId}
                              toggleLabel={toggleLabel}
                              isToggleOn={isToggleOn}
                              hideToggle={hideToggle}
                            />
                          )
                        )}
                    </Box>
                    <ApplicationActionsMenu
                      siteId={siteId}
                      device={device}
                      applicationName={application.appName}
                      menuId={`${device.deviceId}-${application.appName}-application-menu`}
                      menuType='single'
                      buttonType='icon-button'
                    />
                  </Box>

                  {/* CARD SUB-HEADER */}
                  <Box>
                    <Typography
                      noWrap
                      variant='body1'
                      fontWeight={300}
                      lineHeight='24px'
                    >
                      {device.deviceName}
                    </Typography>
                  </Box>

                  <Grid
                    container
                    display='flex'
                    alignItems='center'
                    sx={{
                      '& .MuiGrid-item:last-child': {
                        border: 'none',
                      },
                    }}
                  >
                    {isLoading ? (
                      <Grid
                        xs={12}
                        height='32px'
                        display='flex'
                        alignItems='center'
                      >
                        <LinearProgress sx={{ width: '100%' }} />
                      </Grid>
                    ) : (
                      renderPaymentMetaData(application.appName)
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </AccordionDetails>
    </Accordion>
  );
};

export default DeviceAccordion;
