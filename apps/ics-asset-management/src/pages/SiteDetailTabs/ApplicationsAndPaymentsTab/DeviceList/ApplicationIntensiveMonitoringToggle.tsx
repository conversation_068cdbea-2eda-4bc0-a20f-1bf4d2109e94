import React, { FC, useCallback, useState } from 'react';
import { useSnackbar } from 'notistack';
import { usePostDeviceMonitoring } from '../../../../services/use-query';
import { hasFeatureFlag } from '../../../../utils/helpers';
import MonitoringToggle from '../MonitoringToggle';
import FeatureFlags from '../../../../constants/featureFlags';

interface ApplicationIntensiveMonitoringToggleProps {
  deviceId: number;
  isToggleOn: boolean;
  toggleLabel: string;
  hideToggle?: boolean;
}

const ApplicationIntensiveMonitoringToggle: FC<
  ApplicationIntensiveMonitoringToggleProps
> = ({ deviceId, isToggleOn, toggleLabel, hideToggle }) => {
  const [toggleChecked, setToggleChecked] = useState(isToggleOn);

  const { enqueueSnackbar } = useSnackbar();

  const hasMonitoringFeatureFlag = hasFeatureFlag(FeatureFlags.MONITORING);

  const { mutate: postDeviceMonitoring, isLoading } = usePostDeviceMonitoring();

  const handleToggle = useCallback(() => {
    postDeviceMonitoring(
      { deviceId, monitoringModeOn: !toggleChecked },
      {
        onSuccess: () => setToggleChecked(prev => !prev),
        onError: e => enqueueSnackbar(e.message, { variant: 'error' }),
      }
    );
  }, [postDeviceMonitoring, deviceId, toggleChecked, enqueueSnackbar]);

  return (
    <MonitoringToggle
      toggleChecked={toggleChecked}
      handleToggle={handleToggle}
      isLoading={isLoading}
      toggleLabel={toggleLabel}
      hasMonitoringFeatureFlag={hasMonitoringFeatureFlag}
      hideToggle={hideToggle}
    />
  );
};

export default ApplicationIntensiveMonitoringToggle;
