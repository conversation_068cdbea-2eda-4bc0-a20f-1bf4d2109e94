import React, { FC, useContext, useMemo, useState } from 'react';

import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

import Menu from '@mui/material/Menu';
import Dialog from '@mui/material/Dialog';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { ApplicationsAndPaymentsTabContext } from '../context';
import NestedMenuItem from '../../../../components/NestedMenuItem';
import ApplicationDialog from '../../../../components/ApplicationStatusTable/ApplicationDialog';
import {
  ApplicationDevice,
  IPaymentActionsResponse,
} from '../../../../constants/types';
import { getIconArrowDownStyles, stylesActionMenu } from '../styles';
import { useGetPaymentActions } from '../../../../services/use-query';
import { PAYMENT_DASHBOARD_LABEL_GROUP_DELIMITER } from '../../../../constants/app';
import { PaymentActionModal } from '../../PaymentsTab/ActionModal';
import { useMergeState } from '../../../../hooks/useMergeStates';
import { ACTIONS_MSG } from '../../../../constants/messages';

interface PaymentActionMenu {
  menuLabel: string;
  sub: Array<PaymentActionMenu>;
  data?: IPaymentActionsResponse;
}

interface PaymentCardActionMenuProps {
  menus: PaymentActionMenu[];
  parentMenuOpen: boolean;
  clickCallBack(actionData: IPaymentActionsResponse, shouldOpen: boolean): void;
}

const PaymentCardActionMenu: FC<PaymentCardActionMenuProps> = ({
  menus,
  parentMenuOpen,
  clickCallBack,
}) => {
  const actionMenus: PaymentActionMenu[] = menus || [];

  const handleClick = (data, bol) => {
    clickCallBack(data, bol);
  };

  return (
    <>
      {actionMenus.map(actionMenuItem => (
        <NestedMenuItem
          key={actionMenuItem.menuLabel}
          label={actionMenuItem.menuLabel}
          parentMenuOpen={parentMenuOpen}
          onClick={() => {
            if (actionMenuItem.data) {
              handleClick(actionMenuItem.data, true);
            }
          }}
        >
          {actionMenuItem.sub.length > 0 && (
            <PaymentCardActionMenu
              menus={actionMenuItem.sub}
              parentMenuOpen={parentMenuOpen}
              clickCallBack={clickCallBack}
            />
          )}
        </NestedMenuItem>
      ))}
    </>
  );
};

interface ApplicationActionsMenuProps {
  siteId: string;
  menuId: string;
  menuType: 'single' | 'multiple';
  buttonType: 'icon-button' | 'button';
  device?: ApplicationDevice;
  applicationName?: string;
}

const ApplicationActionsMenu: FC<ApplicationActionsMenuProps> = ({
  siteId,
  menuId,
  menuType,
  buttonType,
  device,
  applicationName,
}) => {
  const { selectedApplications } = useContext(
    ApplicationsAndPaymentsTabContext
  );

  const [anchorEl, setAnchorEl] = useState(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] =
    useState<boolean>(false);

  const isMenuOpen = useMemo(() => Boolean(anchorEl), [anchorEl]);

  const deviceId: number = useMemo(() => {
    if (menuType === 'single') {
      return device.deviceId;
    }
    return selectedApplications[0]?.deviceId;
  }, [menuType, selectedApplications]);

  const deviceName: string = useMemo(() => {
    if (menuType === 'single') {
      return device.deviceName;
    }
    return selectedApplications[0]?.deviceName;
  }, [menuType, selectedApplications]);

  const deviceAppPairFilter = useMemo(
    () =>
      selectedApplications.reduce((acc, current) => {
        const existingDevice = acc.find(
          deviced => deviced.deviceId === current.deviceId
        );
        if (existingDevice) {
          if (!existingDevice.appName.includes(current.appName)) {
            existingDevice.appName.push(current.appName);
          }
        } else {
          acc.push({ deviceId: current.deviceId, appName: [current.appName] });
        }
        return acc;
      }, []),
    [selectedApplications]
  );

  const deviceNames: string[] = useMemo(() => {
    const names = selectedApplications.map(app => app.deviceName);
    return [...new Set(names)];
  }, [selectedApplications]);

  const selectedApplicationName: string = useMemo(() => {
    if (menuType === 'single') {
      return applicationName;
    }
    const eps = selectedApplications.find(app => app.appName === 'infx-eps');
    return eps ? 'infx-eps' : '';
  }, [menuType, selectedApplications]);

  const closeDetailsialog = () => setIsDetailsDialogOpen(false);

  const isFetchPaymentActions: boolean = useMemo(
    () =>
      isMenuOpen &&
      deviceId > 0 &&
      selectedApplications?.length === 1 &&
      selectedApplicationName === 'infx-eps',
    [isMenuOpen, deviceId, selectedApplications, selectedApplicationName]
  );
  const { data } = useGetPaymentActions(deviceId, selectedApplicationName, {
    cacheTime: 0,
    enabled: isFetchPaymentActions,
    retry: false,
  });

  const [{ openPaymentActionModal, selectedAction }, setStates] =
    useMergeState<{
      openPaymentActionModal: boolean;
      selectedAction: IPaymentActionsResponse;
    }>({ openPaymentActionModal: false, selectedAction: null });

  const preMenuItems = (
    actionList: IPaymentActionsResponse[]
  ): PaymentActionMenu[] => {
    function setLevel(
      array: PaymentActionMenu[],
      levelArray: Array<string>,
      content: IPaymentActionsResponse
    ) {
      if (!levelArray || levelArray.length <= 0) {
        return;
      }
      const nextLevelLabel = levelArray.shift();
      const menuExisting = array.find(n => n.menuLabel === nextLevelLabel);

      if (!menuExisting) {
        // Create a child level
        array.push({
          menuLabel: nextLevelLabel,
          sub: [],
        });
      }

      const nextLevelData = array.find(n => n.menuLabel === nextLevelLabel);
      if (levelArray.length <= 0) {
        nextLevelData.data = content;
        return;
      }

      setLevel(nextLevelData.sub, levelArray, content);
    }

    const menuArray = [];

    // eslint-disable-next-line no-restricted-syntax
    for (const action of actionList) {
      const splitLevels = action.label
        .split(PAYMENT_DASHBOARD_LABEL_GROUP_DELIMITER)
        .filter(Boolean);
      setLevel(menuArray, splitLevels, action);
    }

    return menuArray;
  };

  const actionMenu = useMemo(
    () => (isFetchPaymentActions && data ? preMenuItems(data) : null),
    [isFetchPaymentActions, data]
  );

  const handleOpenModal = (
    actionData: IPaymentActionsResponse,
    shouldOpen: boolean
  ) => {
    if (actionData) {
      setStates({
        selectedAction: actionData,
        openPaymentActionModal: shouldOpen,
      });
    }
  };

  const handleCloseModal = () => {
    setStates({
      openPaymentActionModal: false,
    });
  };

  const handleButtonClick = (event: React.MouseEvent) => {
    event.preventDefault();
    setAnchorEl(event.currentTarget);
  };

  const isActionsDisabled: boolean = useMemo(() => {
    if (!selectedApplications.length) {
      return true;
    }
    return false;
  }, [selectedApplications]);

  const getApplicationNames = useMemo(() => {
    if (menuType === 'single') {
      return [applicationName];
    }
    return selectedApplications.map(application => application.appName);
  }, [menuType, applicationName, selectedApplications]);

  const stylesIconArrowDown = useMemo(
    () => getIconArrowDownStyles(isActionsDisabled),
    [isActionsDisabled]
  );

  return (
    <Box>
      {buttonType === 'icon-button' ? (
        <IconButton
          aria-controls={`${deviceId}-application-menu`}
          aria-haspopup='true'
          onClick={handleButtonClick}
          sx={stylesActionMenu.iconButton}
        >
          <MoreVertIcon />
        </IconButton>
      ) : (
        <Button
          disabled={isActionsDisabled}
          variant='outlined'
          aria-controls={`${deviceId}-application-menu`}
          aria-haspopup='menu'
          onClick={handleButtonClick}
          sx={stylesActionMenu.buttonAction}
        >
          {ACTIONS_MSG}

          <KeyboardArrowDownIcon sx={stylesIconArrowDown.root} />
        </Button>
      )}

      <Menu
        id={menuId}
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={(e: MouseEvent) => {
          e.preventDefault();
          setAnchorEl(null);
        }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            setIsDetailsDialogOpen(true);
          }}
          style={{
            width: '140px',
            height: '40px',
          }}
        >
          <Typography
            component='span'
            style={{
              width: '100%',
              fontSize: '16px',
              lineHeight: '0.2',
            }}
          >
            Details
          </Typography>
        </MenuItem>

        {actionMenu && (
          <PaymentCardActionMenu
            menus={actionMenu}
            parentMenuOpen={Boolean(anchorEl)}
            clickCallBack={(a, b) => handleOpenModal(a, b)}
          />
        )}
      </Menu>

      <PaymentActionModal
        open={openPaymentActionModal}
        data={selectedAction}
        onClose={handleCloseModal}
      />

      <Dialog
        fullScreen
        open={isDetailsDialogOpen}
        onClose={() => setIsDetailsDialogOpen(false)}
        aria-labelledby='device-dialog-title'
        PaperProps={{
          style: {
            width: '538px',
            maxHeight: '100vh',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
            borderRadius: 0,
            boxSizing: 'border-box',
            padding: '16px',
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
            overflowY: 'auto',
          },
        }}
      >
        <ApplicationDialog
          siteId={siteId}
          device={{ deviceId, deviceName }}
          applicationFilter={getApplicationNames}
          closeDialog={closeDetailsialog}
          breakpoints={{ xs: 16, sm: 16, md: 16 }}
          deviceAppPairFilter={deviceAppPairFilter}
          deviceNames={deviceNames}
        />
      </Dialog>
    </Box>
  );
};

export default ApplicationActionsMenu;
