import React, { FC, useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';

import { ApplicationDevice } from '../../../../constants/types';
import DeviceAccordion from './DeviceAccordion';

interface DeviceListProps {
  siteId: string;
  devices: Array<ApplicationDevice>;
  toggleLabel?: string;
  isToggleOn?: boolean;
  hideToggle?: boolean;
}

const DeviceList: FC<DeviceListProps> = ({
  siteId,
  devices,
  toggleLabel,
  hideToggle,
  isToggleOn,
}) => {
  const [expandedDeviceIds, setExpandedDeviceIds] = useState<number[]>([]);

  const handleAccordionChange = (
    deviceId: number,
    expanded: boolean,
    appDevices: ApplicationDevice
  ) => {
    if (!appDevices.applications || appDevices.applications.length === 0) {
      return;
    }
    setExpandedDeviceIds(prev =>
      expanded ? [...prev, deviceId] : prev.filter(id => id !== deviceId)
    );
  };

  const handleExpandAll = () => {
    setExpandedDeviceIds(
      devices
        .filter(device => device.applications.length !== 0)
        .map(device => device.deviceId)
    );
  };

  const handleCollapseAll = () => {
    setExpandedDeviceIds([]);
  };

  return (
    <>
      <Box display='flex' flexDirection='column' gap='16px'>
        {devices.map(device => (
          <DeviceAccordion
            key={device.deviceId}
            siteId={siteId}
            device={device}
            isExpanded={expandedDeviceIds.includes(device.deviceId)}
            onChange={handleAccordionChange}
            hideToggle={hideToggle}
            toggleLabel={toggleLabel}
            isToggleOn={isToggleOn}
          />
        ))}
      </Box>
      <Stack
        direction='row'
        spacing={2}
        marginBottom={2}
        justifyContent='end'
        paddingTop='15px'
      >
        <Button
          variant='outlined'
          onClick={handleExpandAll}
          sx={{
            fontSize: '1.4rem',
          }}
        >
          {/* Move these to constant file */}
          Expand All
        </Button>
        <Button
          variant='outlined'
          onClick={handleCollapseAll}
          disabled={expandedDeviceIds.length === 0}
          sx={{
            fontSize: '1.4rem',
          }}
        >
          Collapse All
        </Button>
      </Stack>
    </>
  );
};

export default DeviceList;
