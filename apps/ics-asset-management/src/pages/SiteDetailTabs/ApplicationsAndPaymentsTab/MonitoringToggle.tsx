import React, { memo } from 'react';
import {
  FormGroup,
  FormControlLabel,
  Box,
  Switch,
  CircularProgress,
  Typography,
} from '@mui/material';

interface MonitoringToggleProps {
  toggleChecked: boolean;
  handleToggle: () => void;
  isLoading: boolean;
  toggleLabel: string;
  hideToggle: boolean;
  hasMonitoringFeatureFlag: boolean;
}

const MonitoringToggle: React.FC<MonitoringToggleProps> = ({
  toggleChecked,
  handleToggle,
  isLoading,
  toggleLabel,
  hideToggle,
  hasMonitoringFeatureFlag,
}) => {
  if (!hasMonitoringFeatureFlag || hideToggle || hideToggle === undefined) {
    return null;
  }

  const isModeOn = toggleChecked ? 'On' : 'Off';

  return (
    <FormGroup>
      <FormControlLabel
        control={
          <Box position='relative' display='flex' alignItems='center'>
            <Switch
              checked={toggleChecked}
              onClick={handleToggle}
              disabled={isLoading}
              aria-label='payment-monitoring-toggle'
            />
            {isLoading && (
              <CircularProgress
                size={24}
                sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  marginTop: '-24px',
                  marginLeft: '-12px',
                }}
              />
            )}
          </Box>
        }
        label={
          <Typography variant='body1' sx={{ fontSize: 10 }}>
            {toggleLabel} mode {isModeOn}
          </Typography>
        }
        labelPlacement='top'
        sx={{ top: 10, position: 'relative', m: 0 }}
      />
    </FormGroup>
  );
};

export default memo(MonitoringToggle);
