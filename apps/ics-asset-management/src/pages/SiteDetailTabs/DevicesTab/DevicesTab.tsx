/* eslint-disable import/no-cycle */
import React, { FC, useEffect, useContext, useMemo, useState } from 'react';
import { Info, VpnKey } from '@mui/icons-material';
import spacetime from 'spacetime';
import {
  Alert,
  Box,
  Button,
  Chip,
  CircularProgress,
  Grid,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import { DataGrid, GridColDef, GridRowParams } from '@mui/x-data-grid';
import concat from 'lodash/concat';
import uniqBy from 'lodash/uniqBy';
import map from 'lodash/map';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { useQueryClient } from '@tanstack/react-query';
import StackedBar from '../../../components/StackedBar/StackedBar';
import StackedBarItem from '../../../components/StackedBar/StackedBarItem';
import { RKI_FEES_MSG } from '../../../constants/messages';
import {
  isPendingRKI,
  isDeviceRKIMismatch,
  isSitesRKIMismatch,
  getDecodeToken,
  applySyncKeysAction,
  hasFeatureFlag,
} from '../../../utils/helpers';
import {
  Device,
  TabProps,
  AlarmRule,
  DeviceStatus,
} from '../../../constants/types';
import {
  useGetAlarmRules,
  useGetDevices,
  useGetKeyGroups,
} from '../../../services/use-query';
import { ASSET_MGMT_DEVICE_OVERVIEW } from '../../../constants/routes';
import SnoozeAlarmsModal from '../../../components/SnoozeAlarmsModal';
import ConfirmDialog from '../../../components/ConfirmDialogV1';
import { PendingTransferContext } from '../../PendingTransferContext';
import AddDeviceDialog from '../../../components/modals/AddDeviceDialog';
import FeatureFlags from '../../../constants/featureFlags';
import ScheduleDeploymentAlert from '../../../components/ScheduleDeploymentAlert';
import useHasPermissions from '../../../hooks/useHasPermissions';
import UserRoles from '../../../constants/userRoles';
import { IDeviceRow } from './types';
import {
  STACKBAR_TEXT,
  DATA_GRID_HEADER_CUSTOM_STYLE,
  RKI_MISMATCH_MSG,
} from './constants';
import {
  getDeviceAppVersions,
  getOtherStatuses,
  getStatusIcon,
} from './helpers';

const PAGE_SIZE = 50;
const PAGE_INDEX = 0;
const decodedToken = getDecodeToken();
const companyId: string = decodedToken?.company?.id;
const permissionForSnoozeAlarm: boolean = decodedToken?.roles?.some(
  (permission: string) =>
    permission === 'COMPANY_ADMIN' || permission === 'ANALYST'
);

const gridColumnConfig = {
  sortable: false,
  filterable: false,
  disableColumnMenu: true,
};

const flags = localStorage.getItem('flags') || '[]';

const parsedFlags = JSON.parse(flags);

const playlistMgmtFlag = parsedFlags.find(flag => flag.key === 'playlistMgmt');

const DevicesTab: FC<TabProps> = ({ siteId }) => {
  const [isAlarmModalOpen, setIsAlarmModalOpen] = useState(false);
  const [isSyncKeyOpen, setIsSyncKeyOpen] = useState(false);
  const [pageSize, setPageSize] = React.useState(PAGE_SIZE);
  const [page, setPage] = useState(PAGE_INDEX);
  useGetKeyGroups(companyId);
  const { data, isLoading } = useGetDevices({
    siteId,
    pageIndex: page,
    pageSize,
  });
  const { results: siteDevices = [], resultsMetadata: deviceMetadata } =
    data || {};
  const hasScedulePermission = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.SCHEDULING_MD],
  });

  const shouldShowAppVersions = hasFeatureFlag(FeatureFlags.SHOW_APP_VERSIONS);
  const { data: alarmRules } = useGetAlarmRules(siteId);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { enqueueSnackbar } = useSnackbar();

  const {
    isMismatchedConfigLoading,
    hasConfigMismatchFetchUserRole,
    mismatchedConfigRefetch,
  } = useContext(PendingTransferContext);

  const statusCount = useMemo(() => {
    const initialCounts = {
      operational: 0,
      outOfService: 0,
      unknown: 0,
      inactive: 0,
    };

    const counts =
      siteDevices?.reduce((acc, item) => {
        switch (item.status) {
          case DeviceStatus.OPERATIONAL:
            acc.operational += 1;
            break;
          case DeviceStatus.OUT_OF_SERVICE:
            acc.outOfService += 1;
            break;
          case DeviceStatus.UNKNOWN:
            acc.unknown += 1;
            break;
          case DeviceStatus.INACTIVE:
            acc.inactive += 1;
            break;
          default:
            break;
        }
        return acc;
      }, initialCounts) || initialCounts;

    const total = siteDevices?.length || 0;

    const deviceStatusCount = {
      operational: {
        count: counts.operational,
        color: 'common.healthStatus.normal',
        percentage: total ? `${(counts.operational / total) * 100}%` : '0%',
        text: STACKBAR_TEXT.operational,
      },
      outOfService: {
        count: counts.outOfService,
        color: 'common.healthStatus.outOfService',
        percentage: total ? `${(counts.outOfService / total) * 100}%` : '0%',
        text: STACKBAR_TEXT.outOfService,
      },
      unknown: {
        count: counts.unknown,
        color: 'common.healthStatus.unknown',
        percentage: total ? `${(counts.unknown / total) * 100}%` : '0%',
        text: STACKBAR_TEXT.unknown,
      },
      inactive: {
        count: counts.inactive,
        color: 'common.healthStatus.inactive',
        percentage: total ? `${(counts.inactive / total) * 100}%` : '0%',
        text: STACKBAR_TEXT.inactive,
      },
    };

    return deviceStatusCount;
  }, [siteDevices]);

  const nextAlarm = useMemo(() => {
    const empty = '\u00A0';
    if (alarmRules?.length) {
      const theNextAlarm = alarmRules.reduce((minAlarm, currentAlarm) => {
        if (
          currentAlarm.suspendedFrom &&
          (!minAlarm.suspendedFrom ||
            currentAlarm.suspendedFrom < minAlarm.suspendedFrom)
        ) {
          return currentAlarm;
        }
        return minAlarm;
      }, {} as AlarmRule);

      const currentTime = spacetime();
      if (currentTime.isAfter(new Date(theNextAlarm.suspendedFrom))) {
        return empty;
      }

      return `Next alarm will be on ${spacetime(
        theNextAlarm.suspendedFrom
      ).format('{date-ordinal} {month-short} {year} {time}')} - ${spacetime(
        theNextAlarm.suspendedUntil
      ).format('time')}`;
    }
    return empty;
  }, [alarmRules]);

  const columns: GridColDef[] = [
    {
      field: 'status',
      maxWidth: 50,
      ...gridColumnConfig,
      renderHeader: () => <Info sx={{ color: '#5D5D67', pl: '8px' }} />,
      renderCell: params => getStatusIcon(params.row),
    },
    {
      field: 'id',
      headerName: 'Id',
      minWidth: 150,
      hide: true,
      ...gridColumnConfig,
    },
    {
      field: 'name',
      headerName: 'Device name',
      minWidth: 300,
      ...gridColumnConfig,
      renderCell: params => (
        <Box display='flex' alignItems='center'>
          <Typography component='span'>{params.row.name}</Typography>
          {playlistMgmtFlag?.active &&
            params.row.isMaster &&
            params.row.status !== 3 && (
              <Typography component='span' marginLeft='4px' position='relative'>
                <span className='ii-crown' />
              </Typography>
            )}
        </Box>
      ),
    },
    {
      field: 'serialNumber',
      headerName: 'Serial',
      minWidth: 150,
      ...gridColumnConfig,
    },
    {
      field: 'releaseVersion',
      headerName: 'Release',
      minWidth: 100,
      ...gridColumnConfig,
    },
    {
      field: 'deviceType',
      headerName: 'Device type',
      minWidth: 150,
      ...gridColumnConfig,
      renderCell: params => (
        <Box>
          {params.row.deviceType?.id ?? ''}
          {params.row.deviceType?.screenSize && (
            <Typography sx={{ fontWeight: 600 }} component='span'>
              {` (${params.row.deviceType.screenSize}")`}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'terminalId',
      headerName: 'Terminal ID',
      minWidth: 100,
      ...gridColumnConfig,
    },
    {
      field: 'ipAndGateway',
      headerName: 'IP & gateway',
      minWidth: 250,
      ...gridColumnConfig,
    },
    {
      field: 'appVersions',
      headerName: 'App versions',
      minWidth: 500,
      ...gridColumnConfig,
      renderCell: params => getDeviceAppVersions(params.row.appVersions),
    },
    {
      field: 'info',
      headerName: 'Info',
      flex: 0.5,
      headerAlign: 'right' as const,
      align: 'right' as const,
      ...gridColumnConfig,
      renderCell: params => getOtherStatuses(params.row.info),
    },
  ].filter(
    ({ field }) =>
      shouldShowAppVersions ||
      (!shouldShowAppVersions && field !== 'appVersions')
  );

  const formatIpAndGateway = (item: Device) => {
    try {
      const ip = item.ipAddress === null ? '' : item.ipAddress;
      const gateway =
        item.gatewayAddress === null ? '' : `, ${item.gatewayAddress}`;
      return ip + gateway;
    } catch (err) {
      return '';
    }
  };

  const rows: IDeviceRow[] = useMemo(
    () =>
      siteDevices?.map((item: Device) => {
        const row = {
          ...item,
          deviceType: {
            id: item.deviceType.id,
            screenSize: item.deviceType.screenSize,
          },
          ipAndGateway: formatIpAndGateway(item),
          appVersions: item.appVersions || [],
          info: {
            isRKIMismatch: isDeviceRKIMismatch(item),
            isPendingRKI: isPendingRKI(item),
            isAuxiliary: item.isAuxiliary,
          },
        };
        return row;
      }),
    [siteDevices]
  );

  const getAssetTypesList = () => {
    const uniqueList = uniqBy(siteDevices, 'deviceType.id');
    const devicesTypeList = map(uniqueList, (item, index) =>
      concat(item.deviceType.id, index !== uniqueList.length - 1 ? ', ' : '')
    );
    return devicesTypeList;
  };

  const siteRKIMismatch = useMemo(
    () => isSitesRKIMismatch(siteDevices),
    [siteDevices]
  );

  const initialGridOptions = {
    columns: {
      columnVisibilityModel: {
        id: false,
      },
    },
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleAssetClick = (params: GridRowParams<any>) => {
    const assetDetailsUrl = ASSET_MGMT_DEVICE_OVERVIEW.replace(
      ':siteId',
      params.row.siteId
    ).replace(':deviceId', params.row.id);
    navigate(assetDetailsUrl);
  };

  const handleSnoozeAlarms = () => {
    setIsAlarmModalOpen(true);
  };

  const handleSyncKeys = () => {
    setIsSyncKeyOpen(true);
  };

  useEffect(() => {
    if (!isMismatchedConfigLoading && hasConfigMismatchFetchUserRole) {
      mismatchedConfigRefetch();
    }
  }, []);

  return (
    <Box
      sx={{
        overflowY: 'auto',
        overflowX: 'hidden',
        height: '100%',
        padding: '24px',
      }}
    >
      {siteRKIMismatch && (
        <Alert
          severity='info'
          sx={{ mb: '24px', maxHeight: '48px' }}
          icon={<VpnKey color='primary' fontSize='small' />}
          action={
            <Button
              onClick={handleSyncKeys}
              variant='text'
              sx={{ my: 0, fontSize: '11px' }}
            >
              Sync Keys
            </Button>
          }
        >
          {RKI_MISMATCH_MSG}
        </Alert>
      )}
      {hasScedulePermission && (
        <Box sx={{ mb: '24px' }}>
          <ScheduleDeploymentAlert siteId={siteId} sourceType='Device' />
        </Box>
      )}
      <ConfirmDialog
        isOpen={isSyncKeyOpen}
        onCancel={() => {
          setIsSyncKeyOpen(false);
        }}
        onApply={async () => {
          try {
            await applySyncKeysAction(siteId, siteDevices).then(() => {
              setIsSyncKeyOpen(false);
              queryClient.invalidateQueries(['getSite', siteId]);
              queryClient.invalidateQueries(['getDevices', siteId]);
            });
          } catch (error) {
            setIsSyncKeyOpen(false);
            enqueueSnackbar('Fail to summit RKI request', { variant: 'error' });
          }
        }}
        applyButtonName='Yes'
        closeButtonName='No'
        title='RKI confirmation'
        description={RKI_FEES_MSG}
      />
      <Paper variant='outlined' sx={{ height: 'auto' }}>
        {isLoading && (
          <Box sx={{ height: '100%', textAlign: 'center' }}>
            <CircularProgress />
          </Box>
        )}
        {isLoading === false && (
          <Box sx={{ display: 'flex', flexDirection: 'column', p: 2 }}>
            <Grid container spacing={2} columns={16}>
              <Grid item sm={8} md={8}>
                <Typography sx={{ fontSize: 'h2', fontWeight: 'bold' }}>
                  {siteDevices?.length} Devices on site
                </Typography>
                <Typography sx={{ fontSize: '14px', mt: '5px' }}>
                  {getAssetTypesList()}
                </Typography>
              </Grid>

              <Grid item sm={8} md={8} sx={{ textAlign: 'right' }}>
                {permissionForSnoozeAlarm && (
                  <Button
                    variant='text'
                    sx={{ fontSize: '11px' }}
                    onClick={handleSnoozeAlarms}
                  >
                    Snooze alarms
                  </Button>
                )}
                <AddDeviceDialog siteId={siteId} />
              </Grid>
              <Typography
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'end',
                  width: '100%',
                }}
                variant='bodySmall'
              >
                {nextAlarm}
              </Typography>
            </Grid>
            <StackedBar>
              {Object.keys(statusCount)
                .filter((key: string) => statusCount[key].count)
                .map((key: string) => (
                  <StackedBarItem
                    key={key}
                    width={statusCount[key].percentage}
                    color={statusCount[key].color}
                    toolTipItem={`${statusCount[key].text}: ${statusCount[key].count}`}
                  />
                ))}
            </StackedBar>
            <Stack direction='row' spacing={4}>
              {Object.keys(statusCount)
                .filter((key: string) => statusCount[key].count)
                .map((key: string) => (
                  <Box key={key}>
                    <Chip
                      label={statusCount[key].count}
                      size='small'
                      sx={{
                        mr: '10px',
                        bgcolor: statusCount[key].color,
                        color: 'white',
                      }}
                    />
                    {statusCount[key].text}
                  </Box>
                ))}
            </Stack>
          </Box>
        )}

        <Box sx={{ pt: '16px' }}>
          {!isLoading && (
            <DataGrid
              sx={{
                cursor: 'pointer',
                ...DATA_GRID_HEADER_CUSTOM_STYLE,
                borderRadius: 0,
                mx: '-1px',
                '& .MuiTablePagination': {
                  '&-selectLabel': { margin: '0px' },
                  '&-displayedRows': { margin: '0px' },
                },
              }}
              disableColumnMenu
              initialState={initialGridOptions}
              autoHeight
              columns={columns}
              rows={rows}
              pagination
              paginationMode='server'
              page={page}
              onPageChange={newPage => setPage(newPage)}
              pageSize={pageSize}
              onPageSizeChange={(newPage: number): void => setPageSize(newPage)}
              rowCount={deviceMetadata?.totalResults ?? 0}
              onRowClick={params => {
                handleAssetClick(params);
              }}
            />
          )}
        </Box>
      </Paper>
      <SnoozeAlarmsModal
        isOpen={isAlarmModalOpen}
        onClose={() => setIsAlarmModalOpen(false)}
        siteId={siteId}
      />
    </Box>
  );
};

export default DevicesTab;
