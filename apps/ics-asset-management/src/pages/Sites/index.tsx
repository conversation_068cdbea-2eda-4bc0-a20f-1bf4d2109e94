import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { useUpdateEffect } from 'usehooks-ts';
import { MenuItem, Select, TextField } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import MainLayout from '../../layouts/MainLayout';
import { drawer as drawerTheme } from '../../constants/theme';
import useHasPermissions from '../../hooks/useHasPermissions';
import FeatureFlags from '../../constants/featureFlags';
import PaginationContainer from '../../layouts/PaginationContainer';
import Actions from './Actions';
import Copy from './constants/copy';
import Filters from './Filters';
import FilterForm from './FilterForm';
import SearchForm from './SearchForm';
import SiteItem from './components/SiteItem';
import SitesSkeleton from './components/SitesSkeleton';
import { defaultSiteParams } from './constants/sitesSchema';
import useFilterParams from './hooks/useFilterParams';
import useSitesPageData from './hooks/useSitesPageData';
import useGetHealthStatus from './hooks/useGetHealthStatus';

const Sites = () => {
  const siteListRef = useRef<HTMLDivElement | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const { currentParams, updateParams, valuesFromParams } = useFilterParams();
  const {
    handlePageChange,
    handlePageSizeChange,
    isFetchingSites,
    sitesPageData,
  } = useSitesPageData();
  const [inputPage, setInputPage] = useState(sitesPageData.page.toString());
  const [inputPageSize, setInputPageSize] = useState(
    sitesPageData.pageSize.toString()
  );

  useEffect(() => {
    setInputPage(sitesPageData.page.toString());
    setInputPageSize(sitesPageData.pageSize.toString());
  }, [sitesPageData.page, sitesPageData.pageSize]);

  const { data: sitesHealthStatusData } = useGetHealthStatus();

  const hasSiteDetailV2 = useHasPermissions({
    companyFeatureFlags: [FeatureFlags.SITE_DETAIL_V2],
  });

  const handleCloseDrawer = useCallback(() => {
    setIsDrawerOpen(false);
  }, [setIsDrawerOpen]);

  const handleResetForm = useCallback(() => {
    handleCloseDrawer();
    updateParams(defaultSiteParams);
  }, [handleCloseDrawer, updateParams]);

  const onSubmit = useCallback(
    data => {
      updateParams({ ...data, pageIndex: '0' });
      if (isDrawerOpen) {
        handleCloseDrawer();
      }
    },
    [updateParams, handleCloseDrawer, isDrawerOpen]
  );

  const handleToggleDrawer = useCallback(() => {
    setIsDrawerOpen(prev => !prev);
  }, [setIsDrawerOpen]);

  const paramsChangedFromDefault = useMemo(() => {
    const currentParamsArray = Object.entries(currentParams);
    return currentParamsArray
      .filter(([key]) => !['pageIndex', 'pageSize'].includes(key))
      .filter(([key, value]) => {
        const defaultValue = defaultSiteParams[key];
        const isDifferent =
          JSON.stringify(value) !== JSON.stringify(defaultValue);
        return isDifferent;
      }).length;
  }, [currentParams]);

  const paginationInfo = useMemo(() => {
    if (isFetchingSites) return Copy.loading;
    if (!sitesPageData?.totalResults && !isFetchingSites)
      return Copy.paginationNoResultsLabel;
    const { page, pageSize, totalResults } = sitesPageData;
    const first = page * pageSize - pageSize + 1;
    const last =
      sitesPageData.pageCount === page
        ? totalResults.toLocaleString()
        : page * pageSize;
    const maxTotalResults = Math.max(
      totalResults,
      sitesHealthStatusData?.siteStatus?.TOTAL ?? 0
    );
    const regex = /{([^}]+)}/g;
    const replacements = {
      first: first.toLocaleString(),
      last: last.toLocaleString(),
      totalResults: totalResults.toLocaleString(),
      maxTotalResults: maxTotalResults.toLocaleString(),
    };
    if (paramsChangedFromDefault) {
      return Copy.paginationResultsWithFilterLabel.replace(
        regex,
        (_, key) => replacements[key]
      );
    }
    return Copy.paginationResultsLabel.replace(
      regex,
      (_, key) => replacements[key]
    );
  }, [
    sitesPageData,
    isFetchingSites,
    paramsChangedFromDefault,
    sitesHealthStatusData,
  ]);

  const startRecord = (sitesPageData.page - 1) * sitesPageData.pageSize + 1;
  const endRecord = Math.min(
    sitesPageData.page * sitesPageData.pageSize,
    sitesPageData.totalResults
  );

  const handlePageSizeChanged = value => {
    const newSize = Number(value);
    setInputPageSize(value);
    handlePageSizeChange(newSize);
  };

  useUpdateEffect(() => {
    if (siteListRef?.current) {
      siteListRef.current.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
    }
  }, [siteListRef, valuesFromParams]);

  return (
    <MainLayout
      header={{
        action: <Actions />,
        title: Copy.sitesTitle,
        icon: <LocationOnIcon sx={{ color: 'common.icon' }} />,
      }}
      isLoading={isFetchingSites}
    >
      <Drawer
        anchor='right'
        hideBackdrop
        ModalProps={{
          slots: { backdrop: 'div' },
          slotProps: {
            root: {
              style: {
                position: 'absolute',
                top: 'unset',
                bottom: 'unset',
                left: 'unset',
                right: 'unset',
              },
            },
          },
        }}
        onClose={handleCloseDrawer}
        open={isDrawerOpen}
        sx={{
          '& .MuiDrawer-paper': {
            width: drawerTheme.width,
            top: 123,
            bottom: 0,
            height: 'calc(100% - 123px)',
          },
        }}
      >
        <Box display='flex' flex='1' flexDirection='column' overflow='hidden'>
          <Box
            alignItems='center'
            bgcolor='common.modalBackground'
            display='flex'
            justifyContent='space-between'
            px={2}
            py={1}
          >
            <Typography>{Copy.formTitle}</Typography>
            <IconButton onClick={handleCloseDrawer} size='small'>
              <CloseIcon />
            </IconButton>
          </Box>
          {isDrawerOpen && (
            <FilterForm
              defaultValues={valuesFromParams}
              onSubmitForm={onSubmit}
              onResetForm={handleResetForm}
            />
          )}
        </Box>
      </Drawer>
      <Box
        display='flex'
        alignItems='center'
        justifyContent='space-between'
        mt={0}
        bgcolor='white'
        padding='16px 24px'
      >
        <Box alignItems='center' display='flex' gap={1}>
          <Typography component='p' variant='titleMedium'>
            All sites
          </Typography>
          <Typography variant='body2' sx={{ lineHeight: '24px' }}>
            {paginationInfo}
          </Typography>
        </Box>
        <Box display='flex' gap={1}>
          <SearchForm
            defaultValues={valuesFromParams}
            onSearchChange={onSubmit}
          />
          <Filters
            handleToggleDrawer={handleToggleDrawer}
            paramsChangedFromDefault={paramsChangedFromDefault}
          />
        </Box>
      </Box>
      <Box
        bgcolor='common.backgroundLight'
        flex='1'
        height='100%'
        overflow='hidden'
      >
        <Box
          ref={siteListRef}
          flex='1'
          height='100%'
          p={2}
          sx={{
            overflowY: 'auto',
            overflowX: 'hidden',
          }}
        >
          <Box display='flex' flex='1' flexDirection='column' gap={1}>
            {isFetchingSites && <SitesSkeleton />}
            {Boolean(sitesPageData.rowData.length) &&
              sitesPageData.rowData.map(site => (
                <SiteItem
                  key={site.id}
                  hasSiteDetailV2={hasSiteDetailV2}
                  siteDetails={site}
                />
              ))}
          </Box>
        </Box>
      </Box>

      <PaginationContainer>
        {sitesPageData?.rowData?.length > 0 && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              p: '5px 30px',
            }}
          >
            {/* Previous Page Button */}
            <IconButton
              onClick={() => {
                const newPage = Math.max(0, sitesPageData.page - 1);
                handlePageChange(newPage);
              }}
              disabled={sitesPageData.page === 1 || isFetchingSites}
            >
              <ChevronLeft />
            </IconButton>

            {/* Page Number Input */}
            <TextField
              value={inputPage}
              size='small'
              sx={{
                width: 50,
                '& .MuiOutlinedInput-input': {
                  padding: '4px 14px',
                },
              }}
              onChange={e => setInputPage(e.target.value)}
              onBlur={() => {
                const pageNum = Number(inputPage);
                console.log('pageNum', pageNum);
                if (pageNum > sitesPageData.pageCount) return;
                if (pageNum && pageNum !== sitesPageData.page) {
                  handlePageChange(pageNum);
                }
              }}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  const pageNum = Number(inputPage);
                  if (pageNum > sitesPageData.pageCount) return;

                  if (pageNum && pageNum !== sitesPageData.page) {
                    handlePageChange(pageNum);
                  }
                }
              }}
              disabled={isFetchingSites}
            />
            <span> / {sitesPageData.pageCount}</span>

            {/* Next Page Button */}
            <IconButton
              onClick={() => {
                const newPage = Math.min(
                  sitesPageData.pageCount,
                  sitesPageData.page + 1
                );
                handlePageChange(newPage);
              }}
              disabled={
                sitesPageData.page === sitesPageData.pageCount ||
                isFetchingSites
              }
            >
              <ChevronRight />
            </IconButton>

            {/* Page Size Dropdown */}
            <Select
              value={inputPageSize}
              onChange={e => handlePageSizeChanged(e.target.value)}
              size='small'
              sx={{
                '& .MuiOutlinedInput-input': {
                  padding: '4px 8px',
                },
              }}
              disabled={isFetchingSites}
            >
              {['10', '25', '50', '100'].map(size => (
                <MenuItem key={size} value={size}>
                  {size}
                </MenuItem>
              ))}
            </Select>

            {/* Displaying Records Info */}
            <span>{`${startRecord} - ${endRecord} of ${sitesPageData.totalResults} Records`}</span>
          </Box>
        )}
      </PaginationContainer>
    </MainLayout>
  );
};

export default Sites;
