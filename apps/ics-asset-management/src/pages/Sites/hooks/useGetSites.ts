import {
  useQuery,
  type UseQueryOptions,
  type UseQueryResult,
} from '@tanstack/react-query';
import request from '../../../services/request';
import paramsSerializer from '../utils/paramsSerializer';
import {
  type GetSitesParams,
  type GetSitesResponse,
} from '../constants/sitesSchema';

const getSites = (
  sitesParams: GetSitesParams,
  signal: AbortSignal
): Promise<GetSitesResponse> =>
  request()
    .get(`/sites/list`, {
      params: {
        autoPoll: false,
        ...sitesParams,
      },
      paramsSerializer,
      signal,
    })
    .then(response => response.data);

const useGetSites = (
  sitesParams: GetSitesParams,
  queryOpts?: UseQueryOptions<GetSitesResponse>
): UseQueryResult<GetSitesResponse> =>
  useQuery<GetSitesResponse>(
    ['getSites', sitesParams],
    ({ signal }) =>
      getSites(
        {
          ...sitesParams,
          pageSize: sitesParams?.isCSV ? -1 : sitesParams.pageSize ?? 10,
          applyStrictMatchTags:
            sitesParams.applyStrictMatchTags === 'all' ? 'true' : 'false',
        },
        signal
      ),
    queryOpts
  );

export default useGetSites;
