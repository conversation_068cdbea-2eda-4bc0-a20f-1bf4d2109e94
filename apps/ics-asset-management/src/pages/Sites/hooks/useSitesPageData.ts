import { useMemo, useCallback } from 'react';
import useGetSites from './useGetSites';
import useGetHealthStatus from './useGetHealthStatus';
import useFilterParams from './useFilterParams';

const useSitesPageData = () => {
  const { currentParams, updateParams, valuesFromParams } = useFilterParams();

  const { data: dataSites, isFetching: isFetchingSites } = useGetSites(
    valuesFromParams,
    {
      cacheTime: 5_000,
      staleTime: 2_000,
    }
  );

  const sitesPageData = useMemo(() => {
    let currentPageData = {
      page: 0,
      pageCount: 0,
      pageSize: 20,
      rowData: [],
      totalResults: 0,
    };

    if (dataSites?.results?.length) {
      const {
        resultsMetadata: { pageIndex, pageSize, totalResults },
        results,
      } = dataSites;

      const pageCount = Math.ceil(totalResults / pageSize);

      currentPageData = {
        ...currentPageData,
        page: pageIndex + 1,
        pageCount,
        pageSize,
        rowData: results,
        totalResults,
      };
    }

    return currentPageData;
  }, [dataSites]);

  const handlePageChange = useCallback(
    (value: number) => {
      const newPage = Math.max(0, value - 1);
      const newParams = {
        ...currentParams,
        pageIndex: newPage.toString(),
      };
      updateParams(newParams);
    },
    [sitesPageData.page, updateParams, currentParams]
  );

  const handlePageSizeChange = useCallback(
    (value: number) => {
      const newSize = value;
      const newParams = {
        ...currentParams,
        pageSize: newSize.toString(),
        pageIndex: '0',
      };
      updateParams(newParams);
    },
    [sitesPageData.pageSize, updateParams, currentParams]
  );

  useGetHealthStatus();

  return {
    handlePageSizeChange,
    handlePageChange,
    isFetchingSites,
    sitesPageData,
  };
};

export default useSitesPageData;
