import { useMemo, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import removeEmptyFromObject from '../utils/removeEmptyFromObject';
import {
  type DeviceStatusType,
  type GetSitesParams,
  type SiteEventType,
  type SiteStatusType,
  defaultSiteParams,
} from '../constants/sitesSchema';

const useFilterParams = () => {
  const [params, setParams] = useSearchParams(defaultSiteParams);

  const currentParams = useMemo(() => {
    const deviceStatuses =
      (params.getAll('deviceStatuses').filter(Boolean) as DeviceStatusType[]) ??
      [];
    const pageIndex = params.get('pageIndex') ?? '0';
    const pageSize = params.get('pageSize') ?? '20';
    const q = params.get('q') ?? '';
    const showHiddenSites = params.get('showHiddenSites') ?? 'false';
    const siteEvents =
      (params.getAll('siteEvents').filter(Boolean) as SiteEventType[]) ?? [];
    const statuses =
      (params.getAll('statuses').filter(Boolean) as SiteStatusType[]) ?? [];
    const tags =
      params
        .getAll('tags')
        ?.map(tag => tag.split(','))
        ?.flat() ?? [];
    const applyStrictMatchTags = params.get('applyStrictMatchTags') ?? 'any';

    return {
      deviceStatuses,
      pageIndex,
      pageSize,
      q,
      showHiddenSites,
      siteEvents,
      statuses,
      tags,
      applyStrictMatchTags,
    };
  }, [params]);

  const valuesFromParams: GetSitesParams = useMemo(() => {
    const pageIndex = Number(currentParams.pageIndex);
    const pageSize = Number(currentParams.pageSize);
    const showHiddenSites = currentParams.showHiddenSites === 'true';

    return {
      ...currentParams,
      pageIndex,
      pageSize,
      showHiddenSites,
    };
  }, [currentParams]);

  const updateParams = useCallback(
    newParams => {
      const { tags = [] } = newParams;
      let updatedParams = newParams;
      if (tags.length) {
        updatedParams = {
          ...newParams,
          tags: tags.join(','),
          applyStrictMatchTags: newParams?.applyStrictMatchTags,
        };
      }
      setParams(removeEmptyFromObject(updatedParams));
    },
    [setParams]
  );

  return {
    currentParams,
    updateParams,
    valuesFromParams,
  };
};

export default useFilterParams;
