import { z } from 'zod';

const defaultSiteParams = {
  deviceStatuses: [],
  pageIndex: '0',
  q: '',
  siteEvents: [],
  showHiddenSites: 'false',
  statuses: [],
  tags: [],
  applyStrictMatchTags: 'any',
};

enum OrderEnum {
  STATUS_DESC = 'status_desc',
}

const Order = z.nativeEnum(OrderEnum);

enum SiteStatusEnum {
  CRITICAL = 'CRITICAL',
  INACTIVE = 'INACTIVE',
  NORMAL = 'NORMAL',
  UNKNOWN = 'UNKNOWN',
  WARNING = 'WARNING',
}

const SiteStatus = z.nativeEnum(SiteStatusEnum);

enum DeviceStatusEnum {
  INACTIVE = 'INACTIVE',
  OPERATIONAL = 'OPERATIONAL',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE',
  UNKNOWN = 'UNKNOWN',
}

const DeviceStatus = z.nativeEnum(DeviceStatusEnum);

enum SiteEventEnum {
  NEW_SITE = 'NEW_SITE',
}

const SiteEvent = z.nativeEnum(SiteEventEnum);

// enum OutOfService {
//   Security = 'Security',
//   OPTTampered = 'OPT Tampered',
//   OPTInSafeMode = 'OPT In Safe Mode',
//   ComponentDisconected = 'Component Disconected',
//   SiteIntegration = 'Site Integration',
// }

// enum Security {
//   SecureChannelLost = 'Security Secure Channel Lost',
//   ComponentCertificateMismatch = 'Component Certificate Mismatch',
// }

// enum OPTTampered {
//   SDCTamperedRemoval = 'SDC Tampered (removal)',
//   SDCTamperedDestructive = 'SDC Tampered (destructive)',
//   UPCTamperedRemoval = 'UPC Tampered (removal)',
//   UPCTamperedDestructive = 'UPC Tampered (destructive)',
// }

// enum OPTInSafeMode {
//   SDCSafeMode = 'SDC Safe Mode',
//   UPCSafeMode = 'UPC Safe Mode',
//   APCSafeMode = 'APC Safe Mode',
// }

// enum ComponentDisconnected {
//   SDCDisconnected = 'SDC Disconnected',
//   UPCDisconnected = 'UPC Disconnected',
// }

// enum SiteIntegration {
//   POSDisconnected = 'POS Disconnected',
//   POSError = 'POS Error',
// }

const getSitesParams = z.object({
  isCSV: z.boolean().optional().default(false),
  deviceStatuses: z.array(DeviceStatus).optional(),
  order: Order.optional().default(OrderEnum.STATUS_DESC),
  pageIndex: z.number().optional().default(0),
  pageSize: z.number().optional().default(10),
  q: z.string().optional(),
  showHiddenSites: z.boolean().optional().default(false),
  siteEvents: z.array(SiteEvent).optional(),
  statuses: z.array(SiteStatus).optional(),
  tags: z.array(z.string()).optional(),
  applyStrictMatchTags: z.string().optional().default('any'),
});

const idAndName = z.object({ id: z.string(), name: z.string() });

const site = z.object({
  address: z.string().optional(),
  counts: z.object({
    inactiveDevices: z.number(),
    oosDevices: z.number(),
    operationalDevices: z.number(),
    totalDevices: z.number(),
    unknownDevices: z.number(),
  }),
  created: z.string(),
  criticalAlarmTs: z.string().nullable(),
  formattedAddress: z.string(),
  id: z.string(),
  name: z.string(),
  owner: idAndName,
  status: z.number(),
  statusStr: SiteStatus,
  tags: z.array(idAndName),
  visible: z.boolean(),
});

const getSitesResponse = z.object({
  results: z.array(site),
  resultsMetadata: z.object({
    totalResults: z.number(),
    pageIndex: z.number(),
    pageSize: z.number(),
  }),
});

type DeviceStatusType = z.infer<typeof DeviceStatus>;
type GetSitesParams = z.infer<typeof getSitesParams>;
type GetSitesResponse = z.infer<typeof getSitesResponse>;
type SiteDetails = z.infer<typeof site>;
type SiteEventType = z.infer<typeof SiteEvent>;
type SiteStatusType = z.infer<typeof SiteStatus>;

export {
  defaultSiteParams,
  DeviceStatusEnum,
  getSitesParams,
  OrderEnum,
  SiteEventEnum,
  SiteStatusEnum,
};

export type {
  DeviceStatusType,
  GetSitesParams,
  GetSitesResponse,
  SiteDetails,
  SiteEventType,
  SiteStatusType,
};
