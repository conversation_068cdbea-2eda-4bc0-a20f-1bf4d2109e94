/* eslint-disable import/no-cycle */
import React, {
  FC,
  useContext,
  useEffect,
  useMemo,
  useState,
  MouseEvent,
} from 'react';
import {
  ArrowDropDown as ArrowDropDownIcon,
  Edit,
  VpnKey,
  Add,
  DoneAll,
  Assignment,
  CloudUpload,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { AxiosError } from 'axios';
import {
  Box,
  Button,
  Popper,
  Typography,
  ClickAwayListener,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import {
  ACTIONS_MSG,
  RKI_FEES_MSG,
  assetReportStatusMessages,
} from '../constants/messages';
import {
  applySyncKeysAction,
  createBlobFile,
  downloadFile,
} from '../utils/helpers';
import { DevicePresence } from '../constants/entities';
import { PendingTransferContext } from '../pages/PendingTransferContext';
import UserRoles from '../constants/userRoles';
import FeatureFlags from '../constants/featureFlags';
import {
  useGetKeyGroups,
  useGetSite,
  useGetDevices,
  useGetAssetReport,
} from '../services/use-query';
import useToken from '../hooks/useToken';
import useHasPermissions from '../hooks/useHasPermissions';
import EditSiteDialog from './modals/EditSiteDialog';
import GenerateKeyBundleModal from './modals/GenerateKeyBundleModal';
import ConfirmDialog from './ConfirmDialogV1';

/**
 * Types
 */
interface CallToActionBtnType {
  disabled?: boolean;
  siteId: string;
  deployNow?: () => void;
}

const CallToActionBtn: FC<CallToActionBtnType> = ({
  disabled,
  siteId,
  deployNow,
}) => {
  const token = useToken();
  const queryClient = useQueryClient();
  const [menuOpen, setMenuOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isGenerateKeyBundleModalOpen, setIsGenerateKeyBundleModalOpen] =
    useState(false);
  const [isSyncKeyOpen, setIsSyncKeyOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isDownloadAssetReportBtnClicked, setIsDownloadAssetReportBtnClicked] =
    useState<boolean>(false);

  const canShowPendingSiteTransferApproval = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
  });

  const permissionForRKI = useHasPermissions({
    userRoles: [UserRoles.RKI],
  });

  const permissionForEditSite = useHasPermissions({
    userRoles: [
      UserRoles.COMPANY_ADMIN,
      UserRoles.ANALYST,
      UserRoles.POWER_USER,
    ],
    partialRoleCheck: true,
  });

  const canCreateOfflineRKIPermission = useHasPermissions({
    userRoles: [UserRoles.WRITE_ROLLOUT, UserRoles.RKI],
    partialRoleCheck: true,
  });

  const hasShellSupport = useHasPermissions({
    companyFeatureFlags: [FeatureFlags.SHELL_SUPPORT],
  });

  const { isSiteTransferPending, setSiteTransferApprovalModalOpen } =
    useContext(PendingTransferContext);

  const { enqueueSnackbar } = useSnackbar();

  const companyId = token?.company?.id;

  const { data: siteDetail } = useGetSite(siteId);
  const { data: keyGroups } = useGetKeyGroups(companyId, {
    enabled: Boolean(companyId),
    placeholderData: [],
  });
  const keyGroupsWithNoneItem = [...keyGroups, { name: 'None', id: 'null' }];
  const { data } = useGetDevices({ siteId });
  const { results: siteDevices } = data || {};
  const deviceList = siteDevices?.length ? siteDevices : [];
  const {
    error: downloadAssetReportError,
    data: assetReportFileResponse,
    refetch: downloadAssetReport,
  } = useGetAssetReport(siteId, {
    enabled: false,
    retry: 0,
    retryOnMount: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    keepPreviousData: true,
    staleTime: Infinity,
  });

  const isSyncKeyEnable = useMemo(() => {
    let result = false;
    if (deviceList?.length) {
      // Get the device unmatched site group with device group
      const isKeygroupMisMatchDevices =
        deviceList?.filter(
          device =>
            !device.inFlight && device.siteKeygroup !== device.keyGroupId
        ) ?? [];
      const validRKIMismatches =
        isKeygroupMisMatchDevices?.filter(
          device => device.presence !== DevicePresence.OutOfInstance
        ) ?? [];
      const isSitesRKIMismatchWithoutInactive = Boolean(
        validRKIMismatches?.length
      );
      const isSiteKeyGroupNull = deviceList[0]?.siteKeygroup === null;

      result =
        permissionForRKI &&
        isSitesRKIMismatchWithoutInactive &&
        !isSiteKeyGroupNull;
    }
    return result;
  }, [deviceList, permissionForRKI]);

  useEffect(() => {
    if (downloadAssetReportError && isDownloadAssetReportBtnClicked) {
      setIsDownloadAssetReportBtnClicked(false);
      const {
        response: { status },
      } = downloadAssetReportError as AxiosError;
      enqueueSnackbar(
        assetReportStatusMessages[status] ||
          assetReportStatusMessages.unknownError,
        {
          variant: 'error',
          autoHideDuration: 3000,
        }
      );
    }
  }, [downloadAssetReportError]);

  useEffect(() => {
    if (assetReportFileResponse && isDownloadAssetReportBtnClicked) {
      setIsDownloadAssetReportBtnClicked(false);
      const {
        data: fileData,
        headers: {
          'content-disposition': contentDisposition,
          'content-type': contentType,
        },
      } = assetReportFileResponse;
      const { 1: fileName } = contentDisposition.split('; ');
      const url = createBlobFile(fileData, contentType);
      downloadFile(fileName, url);
    }
  }, [assetReportFileResponse]);

  const handleToggle = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setMenuOpen(prevOpen => !prevOpen);
  };

  const clickAway = () => setMenuOpen(false);

  return (
    <ClickAwayListener onClickAway={clickAway}>
      <Box>
        <Button
          disabled={disabled}
          variant='outlined'
          style={{ padding: '8px 16px', marginLeft: '16px' }}
          aria-controls={menuOpen ? 'split-button-menu' : undefined}
          aria-expanded={menuOpen ? 'true' : undefined}
          aria-label='select merge strategy'
          aria-haspopup='menu'
          onClick={handleToggle}
          data-testid='actions-btn'
        >
          {ACTIONS_MSG}
          <ArrowDropDownIcon
            sx={{ color: 'common.closeButton' }}
            style={{ paddingLeft: '8px', fontSize: '24px' }}
          />
        </Button>

        <Popper
          open={menuOpen}
          anchorEl={anchorEl}
          role={undefined}
          placement='bottom-end'
          disablePortal={false}
          sx={{
            borderRadius: 2,
            bgcolor: 'common.modalBackground',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {permissionForEditSite && siteDetail && menuOpen && (
            <Button
              sx={{ py: 2, my: 'unset', justifyContent: 'flex-start' }}
              onClick={() => {
                clickAway();
                setIsEditOpen(true);
              }}
            >
              <Edit sx={{ fontSize: '2rem' }} />
              <Typography variant='labelSmall' sx={{ ml: 1 }}>
                Edit Site
              </Typography>
            </Button>
          )}

          {isSyncKeyEnable && (
            <Button
              sx={{ py: 2, my: 'unset', justifyContent: 'flex-start' }}
              onClick={() => {
                clickAway();
                setIsSyncKeyOpen(true);
              }}
            >
              <VpnKey sx={{ fontSize: '2rem' }} />
              <Typography variant='labelSmall' sx={{ ml: 1 }}>
                Sync Key bundle
              </Typography>
            </Button>
          )}

          {canCreateOfflineRKIPermission && (
            <Button
              sx={{ py: 2, my: 'unset', justifyContent: 'flex-start' }}
              onClick={() => {
                clickAway();
                setIsGenerateKeyBundleModalOpen(true);
              }}
            >
              <Add sx={{ fontSize: '2rem' }} />
              <Typography variant='labelSmall' sx={{ ml: 1 }}>
                Generate key bundle package
              </Typography>
            </Button>
          )}

          {isSiteTransferPending && canShowPendingSiteTransferApproval && (
            <Button
              sx={{ py: 2, my: 'unset', justifyContent: 'flex-start' }}
              onClick={() => {
                clickAway();
                setSiteTransferApprovalModalOpen(true);
              }}
            >
              <DoneAll sx={{ fontSize: '2rem' }} />
              <Typography variant='labelSmall' sx={{ ml: 1 }}>
                Approve site transfer
              </Typography>
            </Button>
          )}

          {hasShellSupport && (
            <Button
              sx={{ py: 2, my: 'unset', justifyContent: 'flex-start' }}
              onClick={() => {
                setIsDownloadAssetReportBtnClicked(true);
                enqueueSnackbar(assetReportStatusMessages.downloadStarted, {
                  variant: 'info',
                  autoHideDuration: 3000,
                });
                clickAway();
                downloadAssetReport();
              }}
              data-testid='download-asset-report-btn'
            >
              <Assignment sx={{ fontSize: '2rem' }} />
              <Typography variant='labelSmall' sx={{ ml: 1 }}>
                Site asset export
              </Typography>
            </Button>
          )}

          <Button
            sx={{ py: 2, my: 'unset', justifyContent: 'flex-start' }}
            disabled={false}
            onClick={() => {
              clickAway();
              deployNow();
            }}
          >
            <CloudUpload sx={{ fontSize: '2rem' }} />
            <Typography variant='labelSmall' sx={{ ml: 1 }}>
              Deploy Now
            </Typography>
          </Button>
        </Popper>

        {siteDetail && (
          <EditSiteDialog
            isOpen={isEditOpen}
            onClose={() => setIsEditOpen(false)}
            siteDetail={siteDetail}
            keyGroups={keyGroupsWithNoneItem}
            siteId={siteId}
          />
        )}
        <GenerateKeyBundleModal
          isOpen={isGenerateKeyBundleModalOpen}
          onClose={() => setIsGenerateKeyBundleModalOpen(false)}
          siteId={siteId}
        />
        <ConfirmDialog
          isOpen={isSyncKeyOpen}
          onCancel={() => {
            setIsSyncKeyOpen(false);
          }}
          onApply={async () => {
            try {
              await applySyncKeysAction(siteId, deviceList).then(() => {
                setIsSyncKeyOpen(false);
                queryClient.invalidateQueries(['getSite', siteId]);
                queryClient.invalidateQueries(['getDevices', siteId]);
              });
            } catch (error) {
              setIsSyncKeyOpen(false);
              enqueueSnackbar('Fail to summit RKI request', {
                variant: 'error',
              });
            }
          }}
          applyButtonName='Yes'
          closeButtonName='No'
          title='RKI confirmation'
          description={RKI_FEES_MSG}
        />
      </Box>
    </ClickAwayListener>
  );
};

export default CallToActionBtn;
