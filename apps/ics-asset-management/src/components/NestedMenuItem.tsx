import React, { FC, useRef } from 'react';
import { Box, MenuItem, Typography, Menu } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { useMergeState } from '../hooks/useMergeStates';

interface NestedMenuItemProps {
  label: string;
  parentMenuOpen: boolean;
  onClick?(): void;
  showArrow?: boolean;
  children?: React.ReactNode;
  containerProps?: React.HTMLAttributes<HTMLElement> &
    React.RefAttributes<HTMLElement | null>;
}

interface NestedMenuItemStates {
  isSubMenuOpen: boolean;
}

const NestedMenuItem: FC<NestedMenuItemProps> = ({
  label,
  parentMenuOpen,
  onClick,
  showArrow = true,
  children,
  containerProps: containerPropsProp = {},
}) => {
  const [{ isSubMenuOpen }, setStates] = useMergeState<NestedMenuItemStates>({
    isSubMenuOpen: false,
  });

  const { ref: containerRefProp, ...containerProps } = containerPropsProp;
  const open = isSubMenuOpen && parentMenuOpen;

  const menuItemRef = useRef<HTMLLIElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const menuContainerRef = useRef<HTMLDivElement | null>(null);

  // Check if any immediate children are active
  const isSubmenuFocused = () => {
    const active = containerRef.current?.ownerDocument.activeElement ?? null;
    // eslint-disable-next-line no-restricted-syntax, no-unsafe-optional-chaining
    const childRefs = menuContainerRef?.current
      .children as unknown as Array<any>;
    // eslint-disable-next-line no-restricted-syntax
    for (const child of childRefs) {
      if (child === active) {
        return true;
      }
    }
    return false;
  };

  const handleFocus = (e: React.FocusEvent<HTMLElement>) => {
    if (e.target === containerRef.current) {
      setStates({ isSubMenuOpen: true });
    }

    if (containerProps.onFocus) {
      containerProps.onFocus(e);
    }
  };

  const handleMouseEnter = (e: React.MouseEvent<HTMLElement>) => {
    if (children) {
      setStates({ isSubMenuOpen: true });

      if (containerProps?.onMouseEnter) {
        containerProps?.onMouseEnter(e);
      }
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLElement>) => {
    setStates({ isSubMenuOpen: false });

    if (containerProps?.onMouseLeave) {
      containerProps?.onMouseLeave(e);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      return;
    }

    if (isSubmenuFocused()) {
      e.stopPropagation();
    }

    const active = containerRef.current?.ownerDocument.activeElement;

    if (e.key === 'ArrowLeft' && isSubmenuFocused()) {
      containerRef.current?.focus();
    }

    if (
      e.key === 'ArrowRight' &&
      e.target === containerRef.current &&
      e.target === active
    ) {
      const firstChild = menuContainerRef.current
        ?.children[0] as HTMLDivElement;
      firstChild?.focus();
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLElement>): void => {
    handleMouseLeave(null);
    if (children) {
      setStates({ isSubMenuOpen: !isSubMenuOpen });
      if (containerProps?.onClick) {
        containerProps.onClick(e);
      }
    }
    onClick();
  };

  return (
    <Box
      {...containerProps}
      ref={containerRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={handleKeyDown}
      onFocus={handleFocus}
      onClick={handleClick}
    >
      <MenuItem
        ref={menuItemRef}
        sx={{
          width: '100%',
          height: '40px',
          typography: 'bodyMedium',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'left',
          gap: '8px',
        }}
      >
        {showArrow && children && (
          <ArrowBackIosIcon sx={{ fontSize: '16px' }} />
        )}
        <Typography
          component='span'
          fontSize='inherit'
          sx={{
            width: '100%',
            fontSize: '16px',
            lineHeight: '0.2',
          }}
        >
          {label}
        </Typography>
      </MenuItem>
      <Menu
        sx={{
          // Require to ignore popper events.
          pointerEvents: 'none',
          '& .MuiPaper-root': {
            bgcolor: 'common.backgroundLight',
          },
          '& .MuiMenu-list': {
            padding: '0px',
          },
        }}
        anchorEl={menuItemRef.current}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        open={open}
        autoFocus={false}
        disableAutoFocus
        disableEnforceFocus
        onClose={() => {
          handleMouseLeave(null);
        }}
      >
        <Box
          sx={{
            // To capture event in the child menu
            pointerEvents: 'auto',
          }}
          ref={menuContainerRef}
        >
          {children}
        </Box>
      </Menu>
    </Box>
  );
};

export default NestedMenuItem;
