/* eslint-disable import/no-cycle */
import {
  Cancel,
  CheckCircle,
  CloudOff,
  DoDisturbOnOutlined,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import {
  Box,
  Button,
  CircularProgress,
  Dialog as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Text<PERSON>ield,
  Tooltip,
  Typography,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import spacetime from 'spacetime';
import { theme } from '../constants/theme';
import { Device, DeviceStatus, DevicesData } from '../constants/types';
import { INPUT_STEP } from '../constants/app';
import { postAlarmRules } from '../services/api-request';

interface Props {
  isOpen: boolean;
  onClose?: () => void;
  siteId?: string;
}

const SnoozeAlarmsModal = ({ isOpen, onClose, siteId }: Props) => {
  const isFetching = false;
  const queryClient = useQueryClient();

  const [startDate, setStartDate] = useState(
    spacetime.now().format('iso-short')
  );
  const [startTime, setStartTime] = useState(spacetime.now().format('time'));
  const [finishTime, setFinishTime] = useState(
    spacetime.now().add(2, 'hours').format('time')
  );
  const [isInvalidTimeRange, setIsInvalidTimeRange] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState([]);
  const [suspendedFrom, setSuspendedFrom] = useState(0);
  const [suspendedUntil, setSuspendedUntil] = useState(0);

  const devicesData: DevicesData =
    siteId && queryClient.getQueryData(['getDevices', siteId, 0, 0]);
  const devices: Device[] = devicesData?.results || [];

  useEffect(() => {
    const start = `${startDate} ${startTime}`;
    const startSpacetime = spacetime(start);

    const end = startSpacetime.add(12, 'hours').format('iso-short');
    const endSpacetime = spacetime(end);

    const to = `${startDate} ${finishTime}`;
    const toSpacetime = spacetime(to);

    setIsInvalidTimeRange(
      !toSpacetime.isAfter(startSpacetime) ||
        !toSpacetime.isBefore(endSpacetime)
    );
    setSuspendedFrom(startSpacetime.epoch);
    setSuspendedUntil(toSpacetime.epoch);
  }, [startDate, startTime, finishTime]);

  const { enqueueSnackbar } = useSnackbar();

  const submitAlarmRule = async () => {
    try {
      const object = {
        devices: [...selectedAsset],
        siteId,
        suspendedFrom,
        suspendedUntil,
      };
      const array = [];
      array.push(object);
      await postAlarmRules(array);
      queryClient.invalidateQueries(['getAlarmRules', siteId]);
      onClose();
    } catch (e) {
      onClose();
      enqueueSnackbar('Fail to save alarm rule', { variant: 'error' });
    }
  };

  return (
    <MuiDialog
      open={isOpen}
      onClose={onClose}
      PaperProps={{
        style: { borderRadius: 20, width: 720, height: 760, maxWidth: 720 },
      }}
      data-testid='sk'
    >
      {isFetching && (
        <Box
          width='100%'
          height='100%'
          display='flex'
          justifyContent='center'
          alignItems='center'
        >
          <CircularProgress />
        </Box>
      )}

      {!isFetching && isOpen && (
        <>
          <Box
            display='flex'
            flexDirection='row'
            justifyContent='space-between'
            borderBottom={1}
            borderColor='common.inactiveIcon'
          >
            <Typography px={3} py={2} variant='titleMedium' fontWeight={600}>
              Snooze alarms
            </Typography>
          </Box>

          <Box
            bgcolor='common.backgroundLight'
            minHeight={0}
            height={1}
            overflow='auto'
          >
            <Box
              m={3}
              display='flex'
              flexDirection='row'
              justifyContent='space-between'
              width='90%'
            >
              <Box
                display='flex'
                flexDirection='row'
                alignItems='center'
                gap={2}
                width='50%'
              >
                <Typography width={40}>From</Typography>
                <TextField
                  id='date'
                  label='Start date'
                  type='date'
                  variant='filled'
                  defaultValue={startDate}
                  value={startDate}
                  onChange={e => {
                    setStartDate(e.target.value);
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  inputProps={{
                    min: startDate,
                  }}
                  sx={{
                    width: '90%',
                    fieldset: { border: 0 },
                  }}
                  size='small'
                />
              </Box>

              <TextField
                id='time'
                label='Start time'
                type='time'
                variant='filled'
                defaultValue={startTime}
                value={startTime}
                onChange={e => {
                  setStartTime(e.target.value);
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                inputProps={{
                  step: INPUT_STEP,
                }}
                sx={{
                  width: '45%',
                  fieldset: { border: 0 },
                }}
                size='small'
              />
            </Box>

            <Box
              display='flex'
              flexDirection='row'
              alignItems='center'
              gap={2}
              m={3}
            >
              <Typography width={40}>To</Typography>
              <TextField
                id='time'
                label='Finish time'
                type='time'
                variant='filled'
                error={isInvalidTimeRange}
                defaultValue={finishTime}
                value={finishTime}
                onChange={e => setFinishTime(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                inputProps={{
                  step: INPUT_STEP,
                }}
                sx={{
                  width: '88%',
                  fieldset: { border: 0 },
                }}
                size='small'
              />
            </Box>

            <Typography p={3} variant='bodyMedium'>
              Alarms can be snoozed for a maximum of 12 hours
            </Typography>

            <Box
              display='flex'
              flexDirection='column'
              width='90%'
              px='24px'
              py={2}
              mt={5}
              gap={1}
            >
              <Box display='flex' flexDirection='row'>
                <Typography width='200px' variant='titleMedium'>
                  Select assets on site
                </Typography>
              </Box>
            </Box>

            <DataGrid
              getCellClassName={params => {
                if (params.field === 'name') {
                  return 'firstColumn';
                }
                return '';
              }}
              sx={{
                border: 'unset',
                px: 3,
                bgcolor: 'common.backgroundLight',
                height: 350,
                '& .MuiDataGrid-cell': {
                  border: 'unset',
                  fontSize: '11px',
                  fontWeight: 500,
                  bgcolor: 'common.backgroundLight',
                },
                '& .MuiTablePagination-selectLabel': {
                  marginBottom: 0,
                },
                '& .MuiTablePagination-displayedRows': {
                  marginBottom: 0,
                },
              }}
              columns={[
                {
                  field: 'name',
                  headerName: 'Name',
                  flex: 1,
                  renderCell: params => (
                    <Box alignItems='center' display='flex' gap={2}>
                      {params.row.status === DeviceStatus.INACTIVE && (
                        <Tooltip title='Inactive'>
                          <DoDisturbOnOutlined
                            sx={{
                              height: '16px',
                              color: theme.palette.common.healthStatus.inactive,
                            }}
                          />
                        </Tooltip>
                      )}
                      {params.row.status === DeviceStatus.OPERATIONAL && (
                        <Tooltip title='Operational'>
                          <CheckCircle
                            sx={{
                              height: '16px',
                              color: theme.palette.common.healthStatus.normal,
                            }}
                          />
                        </Tooltip>
                      )}
                      {params.row.status === DeviceStatus.OUT_OF_SERVICE && (
                        <Tooltip title='Out of service'>
                          <Cancel
                            sx={{
                              height: '16px',
                              color:
                                theme.palette.common.healthStatus.outOfService,
                            }}
                          />
                        </Tooltip>
                      )}
                      {params.row.status === DeviceStatus.UNKNOWN && (
                        <Tooltip title='Unknown'>
                          <CloudOff
                            sx={{
                              height: '16px',
                              color: theme.palette.common.healthStatus.inactive,
                            }}
                          />
                        </Tooltip>
                      )}

                      <Typography variant='bodySmall'>
                        {' '}
                        {params.value}
                      </Typography>
                    </Box>
                  ),
                },
                {
                  field: 'serialNumber',
                  headerName: 'Serial',
                  flex: 1,
                },
                {
                  field: 'ipAddress',
                  headerName: 'IP Address',
                  flex: 1,
                  renderCell: params => (
                    <Box alignItems='center' display='flex' gap={2}>
                      <Typography variant='bodySmall'>
                        {params.value}
                      </Typography>
                    </Box>
                  ),
                },
                {
                  field: 'deviceType',
                  headerName: 'Type',
                  renderCell: params => (
                    <Box alignItems='center' display='flex' gap={2}>
                      <Typography variant='bodySmall'>
                        {params.value.name}
                      </Typography>
                    </Box>
                  ),
                },
              ]}
              getRowId={row => row.id}
              pageSize={20}
              rowsPerPageOptions={[20]}
              rows={devices || []}
              hideFooterSelectedRowCount
              checkboxSelection
              selectionModel={selectedAsset}
              onSelectionModelChange={(ids: string[]) => {
                setSelectedAsset(ids);
              }}
              rowHeight={40}
            />
          </Box>
          <Box
            display='flex'
            justifyContent='end'
            px={2}
            gap={0}
            borderTop={1}
            borderColor='common.inactiveIcon'
          >
            <Button onClick={onClose}>Cancel</Button>
            <Button
              variant='contained'
              onClick={submitAlarmRule}
              disabled={isInvalidTimeRange || !selectedAsset.length}
            >
              Snooze alarms
            </Button>
          </Box>
        </>
      )}
    </MuiDialog>
  );
};

export default SnoozeAlarmsModal;
