const BASE_VALIDATE_CSV_FILE_RETURN = {
  jsonData: [],
  reason: [],
  valid: false,
};

const HEADER_FOOTER_HEIGHT = 111; // Height of the header and footer combined from MUI DataGrid
const HEADER_TOOLBAR_FOOTER_HEIGHT = 171; // Height of the header, toolbar, and footer combined from MUI DataGrid
const PAGE_SIZE = 20; // Rows per table before triggering pagination
const ROW_HEIGHT = 48; // Height of each row in the table
const PX_PER_CHAR = 10;
export {
  BASE_VALIDATE_CSV_FILE_RETURN,
  HEADER_FOOTER_HEIGHT,
  HEADER_TOOLBAR_FOOTER_HEIGHT,
  PAGE_SIZE,
  ROW_HEIGHT,
  PX_PER_CHAR,
};
