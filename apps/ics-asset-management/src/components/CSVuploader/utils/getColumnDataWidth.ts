function getColumnDataWidth(rowData) {
  return rowData.reduce(
    (acc, row) => {
      Object.keys(row).forEach(column => {
        const value = row[column];
        let columnLength = 0;
        if (typeof value === 'string') {
          columnLength = value.length;
        } else if (typeof value === 'number') {
          columnLength = String(value).length;
        } else if (Array.isArray(value)) {
          columnLength = value.reduce(
            (max, item) => Math.max(max, String(item).length),
            0
          );
        }
        if (!acc[column] || columnLength > acc[column]) {
          acc[column] = columnLength;
        }
      });
      return acc;
    },
    {} as Record<string, number>
  );
}

export default getColumnDataWidth;

export { getColumnDataWidth };
