import type { ValidateCsvFileProps, ValidateCsvFileReturn } from '../types';
import { formatNumber } from './formatNumber';
import { BASE_VALIDATE_CSV_FILE_RETURN } from './constants';

const validateCsvFileData = ({
  cellsCanBeEmpty = false,
  csvData,
  csvHeader,
  maxRows,
  uniqueKey,
  requiredCsvHeaders,
}: ValidateCsvFileProps): ValidateCsvFileReturn => {
  let correct = [];
  const duplicates = [];
  const incorrect = [];
  const reason = [];
  const baseResult = {
    ...BASE_VALIDATE_CSV_FILE_RETURN,
    reason,
  };

  if (!(csvData?.length ?? 0)) {
    reason.push('No rows found in CSV file');
    return { ...baseResult, valid: false };
  }

  if (maxRows && maxRows > 0 && csvData.length > maxRows) {
    reason.push(
      `Too many rows in CSV file, maximum number of rows is ${formatNumber(
        maxRows
      )} your file is ${formatNumber(csvData.length)} rows long`
    );
    /**
     * If the CSV file is more than double the maxRows value
     * then we can assume that the file is too large to be
     * processed by the UI and should not do any other validations
     */
    if (csvData.length > maxRows + maxRows) {
      return { ...baseResult, valid: false };
    }
  }

  /**
   * Initialise the csvData array with an originalIndex
   * for each row, and an empty csvRowValidationReason array
   */
  correct = csvData.map((item, index) => ({
    ...item,
    originalIndex: item?.originalIndex ?? index,
    csvRowValidationReason: [],
  }));

  /**
   * If we have a uniqueKey we check for duplicate values
   * on a specific column in the CSV file,
   * add the duplicate values to the duplicates array,
   * and add a reason to the reason array
   */
  if (uniqueKey) {
    const findDuplicates = correct
      .reduce((accumulator, item) => {
        const newItem = accumulator.find(
          accItem => accItem[uniqueKey] === item[uniqueKey]
        );

        if (newItem) {
          newItem.count += 1;
        } else {
          accumulator.push({
            [uniqueKey]: item[uniqueKey],
            count: 1,
            duplicateValue: item[uniqueKey],
          });
        }

        return accumulator;
      }, [])
      .filter(item => item.count > 1);

    findDuplicates.forEach(item => duplicates.push(item.duplicateValue));

    const hasDuplicates = findDuplicates.reduce((accumulator, item) => {
      // eslint-disable-next-line no-param-reassign
      accumulator += item.count;
      return accumulator;
    }, 0);

    if (hasDuplicates) {
      reason.push(
        `Some rows with duplicate ${uniqueKey} values found in CSV file`
      );
    }
  }
  let headerToArray = csvHeader.split(',').join(' or ');

  /**
   * Find rows that are missing values
   * for any of the CSV Required header keys
   */
  if (requiredCsvHeaders && requiredCsvHeaders.length) {
    correct.forEach(forEachItem => {
      const { csvRowValidationReason, originalIndex, ...restOfItem } =
        forEachItem;

      const itemHasRequiredKeys = requiredCsvHeaders.every(key =>
        Object.keys(restOfItem).includes(key)
      );
      const itemHasRequiredValues = requiredCsvHeaders.every(
        key => restOfItem[key] !== ''
      );
      if (!itemHasRequiredKeys || !itemHasRequiredValues) {
        correct = correct.filter(
          filterItem => forEachItem.originalIndex !== filterItem.originalIndex
        );
        headerToArray = requiredCsvHeaders.join(' or ');
        incorrect.push({
          ...forEachItem,
          csvRowValidationReason: [
            ...forEachItem.csvRowValidationReason,
            `Row is missing Required ${headerToArray} values`,
          ],
        });
      }
    });
  } else if (!cellsCanBeEmpty) {
    /**
     * Find rows that are missing values
     * for any of the CSV header keys
     */
    correct.forEach(forEachItem => {
      const { csvRowValidationReason, originalIndex, ...restOfItem } =
        forEachItem;
      const itemHasKeyValuePairs = Object.values(restOfItem).every(
        value => value !== ''
      );
      if (!itemHasKeyValuePairs) {
        correct = correct.filter(
          filterItem => forEachItem.originalIndex !== filterItem.originalIndex
        );
        incorrect.push({
          ...forEachItem,
          csvRowValidationReason: [`Row is missing ${headerToArray} values`],
        });
      }
    });
  }

  /**
   * Remove duplicates from the correct array,
   * if we have a uniqueKey and add them to the
   * incorrect array with a 'duplicate' reason.
   * Add a 'duplicate' reason to the incorrect array if it
   * does not already exist
   */
  if (uniqueKey && duplicates.length) {
    correct.forEach(forEachItem => {
      if (duplicates.includes(forEachItem[uniqueKey])) {
        correct = correct.filter(
          filterItem => forEachItem.originalIndex !== filterItem.originalIndex
        );
        incorrect.push({
          ...forEachItem,
          csvRowValidationReason: [
            ...forEachItem.csvRowValidationReason,
            `Duplicate ${uniqueKey} value '${forEachItem[uniqueKey]}'`,
          ],
        });
      }
    });
    incorrect.forEach((item, index) => {
      const validationReason = `Duplicate ${uniqueKey} value '${item[uniqueKey]}'`;
      if (
        duplicates.includes(item[uniqueKey]) &&
        !item.csvRowValidationReason.includes(validationReason)
      ) {
        incorrect[index] = {
          ...item,
          csvRowValidationReason: [
            ...item.csvRowValidationReason,
            validationReason,
          ],
        };
      }
    });
  }

  /**
   * Add a reason if any rows are missing values
   */
  const someIncorrectItemsAreMissingValues = incorrect.some(item => {
    const hasMissingValues = item.csvRowValidationReason.some(itemReason =>
      itemReason.startsWith('Row is missing')
    );
    return hasMissingValues;
  });
  if (someIncorrectItemsAreMissingValues) {
    if (requiredCsvHeaders && requiredCsvHeaders.length) {
      reason.push(`Some rows are missing Required ${headerToArray} values`);
    } else {
      reason.push(`Some rows are missing ${headerToArray} values`);
    }
  }

  /**
   * Sort the correct and incorrect arrays
   * by the originalIndex
   */
  const jsonData = [...correct, ...incorrect].sort(
    (a, b) => a.originalIndex - b.originalIndex
  );

  const valid = !reason.length;

  return {
    jsonData,
    valid,
    reason,
  };
};

export default validateCsvFileData;

export { validateCsvFileData };
