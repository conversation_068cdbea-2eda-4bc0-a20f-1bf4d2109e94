import React, { useCallback, useState } from 'react';
import { Box, Grid, Paper } from '@mui/material';
import {
  CSVUploaderDescription,
  CSVUploaderForm,
  CSVUploadGrid,
} from './components';
import { BASE_VALIDATE_CSV_FILE_RETURN } from './utils';
import type {
  CSVUploaderValidFormReturn,
  CSVUploaderProps,
  ValidateCsvFileReturn,
} from './types';

const CSVUploader = ({
  contentOptions: {
    formTitle,
    formDescription,
    csvFileName,
    csvHeader,
    formActionButtonLabel,
    uniqueKey,
    requiredCsvHeaders,
    formInstructions,
  },
  callbackOptions: {
    isUploading,
    onFormSubmitValidation,
    maxRows,
    maxSiteTags,
  },
}: CSVUploaderProps) => {
  const [validationResult, setValidationResult] =
    useState<ValidateCsvFileReturn>(BASE_VALIDATE_CSV_FILE_RETURN);

  const handleOnFormSubmitValidation = useCallback(
    ({ formData, validation }: CSVUploaderValidFormReturn) => {
      if (formData?.csvFile && validation.valid && !isUploading) {
        onFormSubmitValidation(formData.csvFile);
      }
      setValidationResult(validation);
    },
    [setValidationResult, validationResult, isUploading]
  );

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        flex: 1,
      }}
    >
      <Box
        sx={{
          backgroundColor: 'common.backgroundLight',
          flexGrow: 1,
          overflow: 'hidden',
          display: 'flex',
          flex: 1,
          pt: 3,
          px: 3,
        }}
      >
        <Box sx={{ flexDirection: 'column', display: 'flex', flex: 1 }}>
          <Grid container>
            <Grid item lg={4} md={8} sm={12}>
              <Paper variant='outlined' sx={{ p: 2, flex: 1, mb: 2 }}>
                <Box
                  sx={{
                    flexDirection: 'column',
                    display: 'flex',
                    gap: 2,
                  }}
                >
                  <CSVUploaderDescription
                    contentOptions={{
                      csvFileName,
                      csvHeader,
                      formDescription,
                      formTitle,
                      formInstructions,
                    }}
                    maxRows={maxRows}
                    maxSiteTags={maxSiteTags}
                  />
                  <Box>
                    <CSVUploaderForm
                      contentOptions={{
                        csvHeader,
                        formActionButtonLabel,
                        uniqueKey,
                        requiredCsvHeaders,
                      }}
                      callbackOptions={{
                        onFormSubmitValidation: handleOnFormSubmitValidation,
                        isUploading,
                        maxRows,
                        maxSiteTags,
                      }}
                    />
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
          {Boolean(validationResult.reason.length) && (
            <CSVUploadGrid
              callbackOptions={{
                isUploading,
                validation: validationResult,
                maxRows,
                maxSiteTags,
              }}
              contentOptions={{
                csvFileName,
                csvHeader,
                uniqueKey,
                requiredCsvHeaders,
              }}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default CSVUploader;
