import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Button } from '@mui/material';
import { type GridRenderCellParams } from '@mui/x-data-grid';
import { useSnackbar } from 'notistack';
import {
  useGetErrorsCsvUrl,
  useGetErrorsCsv,
} from '../../../services/use-query';
import { convertCSVToJSON } from '../../../utils/helpers';
import type { CSVFileAsJSON } from '../types';
import { PX_PER_CHAR, getColumnDataWidth } from '../utils';
import CSVTableCell from './CSVTableCell';
import CSVErrorsCell from './CSVErrorsCell';
import CSVErrorsModal from './CSVErrorsModal';

const CSVActionsCell = memo(
  ({
    fileUploadId,
    fileName,
    errorCount,
  }: {
    fileUploadId: string;
    fileName: string;
    errorCount: number;
  }) => {
    const [isErrorsModalOpen, toggleErrorsModalOpen] = useState<boolean>(false);
    const [fileData, setFileData] = useState<CSVFileAsJSON[]>([]);
    const { enqueueSnackbar } = useSnackbar();
    const { data: csvFileUrl, isFetching: isCsvUrlLoading } =
      useGetErrorsCsvUrl(
        { fileUploadId },
        {
          enabled: Boolean(isErrorsModalOpen),
          onError: () => {
            enqueueSnackbar('Failed to load CSV Url', {
              variant: 'error',
            });
          },
          placeholderData: { url: null },
          retry: false,
        }
      );

    const { data: csvFile, isFetching: isCsvLoading } = useGetErrorsCsv(
      { url: csvFileUrl?.url },
      {
        enabled: Boolean(csvFileUrl?.url),
        onError: () => {
          enqueueSnackbar('Failed to load CSV', {
            variant: 'error',
          });
        },
        retry: false,
      }
    );

    const columns = useMemo(() => {
      if (fileData.length) {
        const columnWidths = getColumnDataWidth(fileData);

        return Object.keys(fileData[0]).map(column => {
          const headerName = column.charAt(0).toUpperCase() + column.slice(1);
          const headerWidth = column.length * PX_PER_CHAR || 150;
          const minWidth =
            headerWidth > columnWidths[column] * PX_PER_CHAR
              ? headerWidth
              : columnWidths[column] * PX_PER_CHAR;

          return {
            editable: true,
            field: column,
            headerName,
            minWidth,
            renderCell: (params: GridRenderCellParams) =>
              column === 'errorMessage' ? (
                <CSVErrorsCell {...params} />
              ) : (
                <CSVTableCell {...params} />
              ),
          };
        });
      }
      return [];
    }, [fileData]);

    const getJsonDataFromDownloadedCSV = useCallback(async () => {
      if (csvFile instanceof Blob) {
        const content = await csvFile.text();
        const jsonData = await convertCSVToJSON(content);
        return setFileData(jsonData);
      }
      return setFileData([]);
    }, [csvFile, setFileData]);

    const handleCloseModal = useCallback(() => {
      toggleErrorsModalOpen(false);
    }, [toggleErrorsModalOpen]);

    const handleOpenModal = useCallback(() => {
      toggleErrorsModalOpen(true);
    }, [toggleErrorsModalOpen]);

    const isFetching = isCsvLoading || isCsvUrlLoading;

    useEffect(() => {
      getJsonDataFromDownloadedCSV();
    }, [csvFile]);

    return (
      <>
        <CSVErrorsModal
          isModalOpen={isErrorsModalOpen}
          isFetching={isFetching}
          fileName={fileName}
          errorCount={errorCount}
          columns={columns}
          rows={fileData}
          handleCloseModal={handleCloseModal}
        />

        <Button
          onClick={handleOpenModal}
          size='small'
          sx={{ px: 2 }}
          variant='outlined'
        >
          View errors
        </Button>
      </>
    );
  },
  (prevProps, nextProps) =>
    prevProps.fileUploadId === nextProps.fileUploadId &&
    prevProps.errorCount === nextProps.errorCount &&
    prevProps.fileName === nextProps.fileName
);

export default CSVActionsCell;
