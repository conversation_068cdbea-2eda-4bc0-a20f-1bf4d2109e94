import React, { memo, useCallback, useMemo, useState } from 'react';
import { Box, IconButton, Paper, Typography } from '@mui/material';
import {
  DataGrid,
  GridColumns,
  GridToolbarContainer,
  GridToolbarExport,
  type GridRenderCellParams,
} from '@mui/x-data-grid';
import isEqual from 'lodash/isEqual';
import { DeleteForever as DeleteIcon } from '@mui/icons-material';
import { validateCsvFileData, getColumnDataWidth } from '../utils';
import {
  HEADER_TOOLBAR_FOOTER_HEIGHT,
  PAGE_SIZE,
  PX_PER_CHAR,
  ROW_HEIGHT,
} from '../utils/constants';
import type { CSVUploaderGridProps, ValidateCsvFileReturn } from '../types';
import CSVTableCell from './CSVTableCell';

const CSVUploadGrid = memo(
  ({
    contentOptions: { csvFileName, csvHeader, uniqueKey, requiredCsvHeaders },
    callbackOptions: { validation, maxRows },
  }: CSVUploaderGridProps) => {
    const [currentValidation, setCurrentValidation] =
      useState<ValidateCsvFileReturn>(validation);
    const availableColumns = useMemo(() => csvHeader.split(','), [csvHeader]);

    const handleDeleteRow = useCallback(
      (params: GridRenderCellParams) => {
        const newJsonData = currentValidation.jsonData.filter(
          item => item.originalIndex !== params.row.originalIndex
        );
        const validationResult = validateCsvFileData({
          csvHeader,
          csvData: newJsonData,
          maxRows,
          uniqueKey,
          requiredCsvHeaders,
        });
        setCurrentValidation(validationResult);
      },
      [currentValidation, setCurrentValidation]
    );
    const columnWidths = getColumnDataWidth(currentValidation.jsonData);

    const columns: GridColumns = useMemo(
      () => [
        ...availableColumns.map(column => {
          const headerWidth = column.length * PX_PER_CHAR || 150;
          const minWidth =
            headerWidth > columnWidths[column] * PX_PER_CHAR
              ? headerWidth
              : columnWidths[column] * PX_PER_CHAR;
          return {
            editable: true,
            field: column,
            minWidth,
            headerName: column,
            renderCell: (params: GridRenderCellParams) => (
              <CSVTableCell {...params} />
            ),
            sortable: false,
          };
        }),
        {
          editable: false,
          field: 'csvRowValidationReason',
          headerName: 'Status',
          minWidth: 400,
          renderCell: (params: GridRenderCellParams) => (
            <Box
              alignItems='center'
              display='flex'
              justifyContent={params.value.length ? 'space-between' : 'end'}
              width='100%'
            >
              {Boolean(params.value.length) && (
                <ul
                  style={{
                    marginBottom: 0,
                    marginBlockStart: 0,
                    marginBlockEnd: 0,
                    paddingLeft: '16px',
                    whiteSpace: 'normal',
                    wordBreak: 'break-word',
                  }}
                >
                  {params.value.map(item => (
                    <li key={item}>{item}</li>
                  ))}
                </ul>
              )}
              <div>
                <IconButton
                  onClick={() => handleDeleteRow(params)}
                  type='button'
                >
                  <DeleteIcon />
                </IconButton>
              </div>
            </Box>
          ),
          sortable: false,
        },
      ],
      [availableColumns, currentValidation]
    );

    const tableHeight = useMemo(
      () =>
        currentValidation.jsonData.length > PAGE_SIZE
          ? `${PAGE_SIZE * ROW_HEIGHT + HEADER_TOOLBAR_FOOTER_HEIGHT}px`
          : `${
              validation.jsonData.length * ROW_HEIGHT +
              HEADER_TOOLBAR_FOOTER_HEIGHT +
              ROW_HEIGHT
            }px`,
      [currentValidation.jsonData.length]
    );

    const rows = useMemo(
      () =>
        currentValidation.jsonData.sort(
          (a, b) =>
            b.csvRowValidationReason.length - a.csvRowValidationReason.length
        ),
      [currentValidation]
    );

    const toolbar = useMemo(
      () => (
        <GridToolbarContainer>
          <GridToolbarExport
            csvOptions={{
              fields: availableColumns,
              fileName: csvFileName.replace('.csv', ''),
            }}
            printOptions={{ disableToolbarButton: true }}
          />
        </GridToolbarContainer>
      ),
      []
    );

    const processRowUpdate = useCallback(
      (newRow, oldRow) => {
        const rowHasChanged = !isEqual(newRow, oldRow);
        if (rowHasChanged) {
          const newRows = currentValidation.jsonData.map(item => {
            if (item.originalIndex === newRow.originalIndex) {
              return newRow;
            }
            return item;
          });
          const validationResult = validateCsvFileData({
            csvHeader,
            csvData: newRows,
            maxRows,
            uniqueKey,
            requiredCsvHeaders,
          });
          setCurrentValidation(validationResult);
        }
      },
      [currentValidation, setCurrentValidation]
    );

    if (!rows.length) {
      return null;
    }

    return (
      <Box>
        <Paper variant='outlined' sx={{ p: 2, flex: 1, mb: 2 }}>
          <Box sx={{ flex: 1, height: '100%' }}>
            <Box mb={1}>
              {currentValidation.reason.length ? (
                <Typography variant='h6'>Validation Errors</Typography>
              ) : (
                <>
                  <Typography variant='h6'>No Errors Found</Typography>
                  <Typography variant='body2'>
                    Your data seems to be correct, you can export to CSV and
                    retry the upload
                  </Typography>
                </>
              )}
              {currentValidation.reason.map(reason => (
                <Typography key={reason} variant='body2'>
                  {reason}
                </Typography>
              ))}
            </Box>
            <DataGrid
              getRowHeight={() => 'auto'}
              columns={columns}
              components={{
                Toolbar: () => toolbar,
              }}
              disableColumnMenu
              disableColumnFilter
              disableSelectionOnClick
              experimentalFeatures={{ newEditingApi: true }}
              getRowId={row => row.originalIndex}
              onProcessRowUpdateError={() => {}}
              pageSize={PAGE_SIZE}
              processRowUpdate={processRowUpdate}
              rowHeight={ROW_HEIGHT}
              rows={rows}
              rowsPerPageOptions={[PAGE_SIZE]}
              sx={{
                height: tableHeight,
              }}
            />
          </Box>
        </Paper>
      </Box>
    );
  },
  (prevProps, nextProps) =>
    isEqual(
      prevProps.callbackOptions.validation,
      nextProps.callbackOptions.validation
    )
);

export default CSVUploadGrid;
export { CSVUploadGrid };
