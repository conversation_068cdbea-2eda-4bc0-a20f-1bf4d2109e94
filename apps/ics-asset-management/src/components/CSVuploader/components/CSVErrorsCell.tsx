import React, { memo } from 'react';
import { Box } from '@mui/material';
import type { GridRenderCellParams } from '@mui/x-data-grid';
import { safeJsonParse } from '../../../utils/safeJsonParse';

const CSVErrorsCell = memo(
  (params: GridRenderCellParams) => {
    const { value } = params;

    const parsedValue = safeJsonParse(value, null);
    const errors = parsedValue ? Object.values(parsedValue)?.flat() : [];
    return (
      <Box
        alignItems='center'
        flex={1}
        display='flex'
        justifyContent={errors.length ? 'space-between' : 'end'}
      >
        <ul
          style={{
            marginBottom: 0,
            marginBlockStart: 0,
            marginBlockEnd: 0,
            paddingLeft: '16px',
            whiteSpace: 'normal',
            wordBreak: 'break-word',
          }}
        >
          {errors.map((error: string) => (
            <li key={error}>{error}</li>
          ))}
        </ul>
      </Box>
    );
  },
  (prevProps, nextProps) => prevProps.value === nextProps.value
);

export default CSVErrorsCell;
