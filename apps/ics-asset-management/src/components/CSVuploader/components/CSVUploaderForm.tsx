import React, {
  memo,
  useCallback,
  useMemo,
  useState,
  type DragEvent,
} from 'react';
import { useForm } from 'react-hook-form';
import {
  Box,
  IconButton,
  Input,
  InputAdornment,
  Typography,
} from '@mui/material';
import LoadingButton from '@mui/lab/LoadingButton';
import {
  Folder as FolderIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import {
  readCsvFile,
  validateCsvFileData,
  BASE_VALIDATE_CSV_FILE_RETURN,
} from '../utils';
import type {
  CSVFormData,
  CSVUploaderFormProps,
  ValidateCsvFileReturn,
} from '../types';

const CSVUploaderForm = memo(
  ({
    contentOptions: {
      csvHeader,
      formActionButtonLabel,
      uniqueKey,
      requiredCsvHeaders,
    },
    callbackOptions: { maxRows, onFormSubmitValidation, isUploading },
  }: CSVUploaderFormProps) => {
    const [validationState, setValidationState] =
      useState<ValidateCsvFileReturn>(BASE_VALIDATE_CSV_FILE_RETURN);
    const [isFileDragging, setIsFileDragging] = useState<boolean>(false);
    const {
      clearErrors,
      formState: { errors },
      handleSubmit,
      register,
      resetField,
      setValue,
      watch,
    } = useForm();
    const { onChange, onBlur, name, ref } = register('csvFile', {
      required: 'Please select a file',
    });
    const watchFile = watch('csvFile');

    const isFileSelected: boolean = useMemo(() => {
      if (!watchFile || !watchFile.length) return false;
      return Boolean(watchFile?.[0]?.name);
    }, [watchFile]);

    const fileSelectedTitle: string = useMemo(
      () => watchFile?.[0]?.name ?? 'Select a File',
      [isFileSelected]
    );

    const isSubmitButtonDisabled: boolean = useMemo(
      () =>
        isUploading ||
        !isFileSelected ||
        (isFileSelected && Boolean(validationState.reason.length)),
      [isFileSelected, isUploading]
    );

    const errorsToString = useMemo(
      () =>
        Object.values(errors)
          ?.map(value => {
            if (value?.message) {
              return value.message;
            }
            return null;
          })
          ?.filter(Boolean)
          ?.join(', ') ?? null,
      [errors]
    );

    const handleResetForm = useCallback(() => {
      const baseReturn = {
        formData: null,
        validation: BASE_VALIDATE_CSV_FILE_RETURN,
      };
      resetField('csvFile');
      clearErrors('csvFile');
      setValidationState(BASE_VALIDATE_CSV_FILE_RETURN);
      onFormSubmitValidation(baseReturn);
    }, [onFormSubmitValidation, resetField, setValidationState]);

    const handleOnDragOver = useCallback(
      (event: DragEvent) => {
        event.preventDefault();
        event.stopPropagation();
        if (isUploading) {
          return;
        }
        setIsFileDragging(true);
        handleResetForm();
      },
      [handleResetForm, isUploading, resetField, setIsFileDragging]
    );

    const handleOnDragLeave = useCallback(
      (event: DragEvent) => {
        event.preventDefault();
        event.stopPropagation();
        if (isUploading) {
          return;
        }
        setIsFileDragging(false);
      },
      [isUploading, setIsFileDragging]
    );

    const handleFileOnDrop = useCallback(
      (event: DragEvent) => {
        event.preventDefault();
        event.stopPropagation();
        if (isUploading) {
          return;
        }
        setIsFileDragging(false);
        const { files } = event.dataTransfer;
        if (files.length !== 1) {
          setValidationState(prevState => ({
            ...prevState,
            valid: false,
            reason: ['Please select only one file'],
          }));
          return;
        }
        const fileIsCSV = files[0].type === 'text/csv';
        if (fileIsCSV) {
          setValidationState(BASE_VALIDATE_CSV_FILE_RETURN);
          setValue('csvFile', files, {
            shouldDirty: true,
            shouldValidate: true,
            shouldTouch: true,
          });
        } else {
          setValidationState(prevState => ({
            ...prevState,
            valid: false,
            reason: ['Sorry, only CSV files are allowed'],
          }));
        }
      },
      [
        isUploading,
        setValue,
        setValidationState,
        setIsFileDragging,
        validationState,
      ]
    );

    const handleCsvFormSubmit = useCallback(
      async (data: CSVFormData) => {
        const { csvFile = null } = data;

        const baseReturn = {
          formData: null,
          validation: BASE_VALIDATE_CSV_FILE_RETURN,
        };
        /**
         * If the user selects more than one file,
         * we don't want to do anything.
         */
        if (csvFile.length !== 1) {
          return onFormSubmitValidation(baseReturn);
        }
        const csvFileAsJSON = await readCsvFile({
          csvFile: csvFile[0],
          csvHeader,
          maxRows,
        });
        if (!csvFileAsJSON.valid) {
          setValidationState(prevState => ({
            ...prevState,
            valid: csvFileAsJSON.valid,
            reason: csvFileAsJSON.reason,
          }));
          return onFormSubmitValidation({
            ...baseReturn,
            validation: {
              ...baseReturn.validation,
              reason: csvFileAsJSON.reason,
            },
          });
        }
        if (csvFileAsJSON?.valid) {
          const validation = validateCsvFileData({
            csvHeader,
            csvData: csvFileAsJSON.jsonData,
            maxRows,
            uniqueKey,
            requiredCsvHeaders,
          });
          setValidationState(validation);
          if (validation.valid) {
            return onFormSubmitValidation({
              formData: data ?? null,
              validation,
            });
          }
          return onFormSubmitValidation({
            formData: null,
            validation,
          });
        }
        return onFormSubmitValidation(baseReturn);
      },
      [
        csvHeader,
        maxRows,
        resetField,
        setValidationState,
        uniqueKey,
        requiredCsvHeaders,
      ]
    );

    return (
      <form
        data-testid='csv-file-upload-form'
        onSubmit={handleSubmit(handleCsvFormSubmit)}
        method='dialog'
      >
        <Box>
          <Input
            disabled={isUploading}
            endAdornment={
              isFileSelected ? (
                <InputAdornment
                  position='start'
                  sx={{
                    height: '48px',
                    width: '48px',
                    marginRight: 0,
                    backgroundColor: '#E1E1EC',
                    flexShrink: 0,
                  }}
                >
                  <IconButton
                    data-testid='csvFileClear'
                    disabled={isUploading}
                    onClick={handleResetForm}
                  >
                    <CancelIcon
                      color={
                        validationState.reason.length ? 'error' : 'inherit'
                      }
                    />
                  </IconButton>
                </InputAdornment>
              ) : (
                ''
              )
            }
            fullWidth
            inputProps={{
              'data-testid': 'csv-file-input',
              onChange,
              onBlur,
              name,
              ref,
              id: name,
              accept: '.csv, text/csv',
              style: { display: 'none' },
            }}
            onDragLeave={handleOnDragLeave}
            onDragOver={handleOnDragOver}
            onDrop={handleFileOnDrop}
            startAdornment={
              <InputAdornment
                position='start'
                sx={{
                  height: '48px',
                  marginRight: 0,
                  backgroundColor: '#E1E1EC',
                  flex: 1,
                  flexShrink: 0,
                }}
              >
                <IconButton
                  component='label'
                  data-testid='csv-file-select-label'
                  disabled={isUploading}
                  htmlFor='csvFile'
                  onClick={handleResetForm}
                >
                  <FolderIcon />
                </IconButton>
                {fileSelectedTitle}
              </InputAdornment>
            }
            style={{
              border: `1px solid ${isFileDragging ? '#5D5D67' : '#FFFFFF'}`,
            }}
            title={fileSelectedTitle}
            type='file'
          />
        </Box>
        <Box data-testid='csv-file-errors-container'>
          {errorsToString && (
            <Typography color='error' component='p' variant='bodySmall'>
              {errorsToString}
            </Typography>
          )}
          {Boolean(validationState.reason.length) && (
            <>
              {validationState.reason.map(item => (
                <Typography
                  key={item}
                  color='error'
                  component='p'
                  variant='bodySmall'
                >
                  {item}
                </Typography>
              ))}
            </>
          )}
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'end' }}>
          <LoadingButton
            data-testid='csv-file-submit-button'
            disabled={isSubmitButtonDisabled}
            loading={isUploading}
            type='submit'
            variant='outlined'
          >
            {formActionButtonLabel}
          </LoadingButton>
        </Box>
      </form>
    );
  },
  (prevProps, nextProps) =>
    prevProps.callbackOptions.isUploading ===
    nextProps.callbackOptions.isUploading
);

export default CSVUploaderForm;

export { CSVUploaderForm };
