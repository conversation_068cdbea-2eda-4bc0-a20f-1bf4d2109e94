import { GridColDef, GridColumns } from '@mui/x-data-grid/models';
import { ImportByCsvCategory, InstructionProps } from '../../constants/types';

export type CSVFormData = {
  csvFile: FileList;
};

export type CSVFileAsJSON = {
  [key: string]: string | number | string[];
  originalIndex?: number;
  csvRowValidationReason?: string[];
};

export type ReadCSVFileProps = {
  csvFile: File;
  csvHeader: string;
  maxRows?: number | 0;
  maxSiteTags?: number | 0;
};

export type ReadCSVFileReturn = {
  jsonData: CSVFileAsJSON[];
  valid: boolean;
  reason: string[];
};

export type CSVUploaderProps = {
  contentOptions: {
    csvFileName: string;
    csvHeader: string;
    formActionButtonLabel: string;
    formDescription: string;
    formTitle: string;
    uniqueKey?: string;
    requiredCsvHeaders?: string[];
    formInstructions?: InstructionProps;
  };
  callbackOptions: {
    isUploading: boolean;
    maxRows: number;
    maxSiteTags?: number;
    onFormSubmitValidation: (csvFile: FileList) => void;
  };
};

export type ValidateCsvFileProps = {
  cellsCanBeEmpty?: boolean;
  csvHeader: string;
  csvData: CSVFileAsJSON[];
  maxRows: number;
  maxSiteTags?: number;
  uniqueKey?: string;
  requiredCsvHeaders?: string[];
};

export type ValidateCsvFileReturn = {
  jsonData: CSVFileAsJSON[];
  valid: boolean;
  reason: string[];
};

export type CSVUploaderValidFormReturn = {
  formData?: CSVFormData | null;
  validation: ValidateCsvFileReturn;
};

export type CSVUploaderFormProps = {
  callbackOptions: {
    maxRows: number;
    maxSiteTags?: number;
    onFormSubmitValidation: (result: CSVUploaderValidFormReturn) => void;
    isUploading: boolean;
  };
  contentOptions: {
    csvHeader: string;
    formActionButtonLabel: string;
    uniqueKey?: string;
    requiredCsvHeaders?: string[];
  };
};

export type CSVUploaderGridProps = {
  callbackOptions: {
    validation: ValidateCsvFileReturn;
    maxRows: number;
    maxSiteTags?: number;
    isUploading: boolean;
  };
  contentOptions: {
    csvFileName: string;
    csvHeader: string;
    uniqueKey?: string;
    requiredCsvHeaders?: string[];
  };
};

export type CSVUploaderDescriptionProps = {
  contentOptions: {
    csvHeader: string;
    csvFileName: string;
    formDescription: string;
    formTitle: string;
    formInstructions: InstructionProps;
  };
  maxRows?: number;
  maxSiteTags?: number;
};

export type PastImportsTableProps = {
  callbackOptions: {
    category: ImportByCsvCategory;
    pageSize: number;
  };
  contentOptions: {
    tableTitle: string;
    columns: GridColumns;
  };
};

export type CSVErrorsModalProps = {
  isModalOpen: boolean;
  isFetching: boolean;
  fileName: string;
  errorCount: number;
  columns: Array<GridColDef>;
  rows: Array<any>;
  handleCloseModal: () => void;
};
