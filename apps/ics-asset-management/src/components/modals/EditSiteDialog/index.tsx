/* eslint-disable import/no-cycle */
import React, { useEffect, useMemo, useState } from 'react';
import tzlookup from 'tz-lookup';
import { useSnackbar } from 'notistack';
import isEmpty from 'lodash/isEmpty';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  CircularProgress,
  Dialog as MuiDialog,
  Typography,
  TextField,
} from '@mui/material';
import isEqual from 'lodash/isEqual';
import { AxiosError } from 'axios';
import { useQueryClient } from '@tanstack/react-query';
import useHasPermissions from '../../../hooks/useHasPermissions';
import {
  useGetAllTags,
  usePostSiteDetails,
  usePostValidateSiteTags,
} from '../../../services/use-query';
import { deleteSite, getCheckRKI } from '../../../services/api-request';
import {
  Site,
  Device,
  DevicesData,
  Schedule,
  schedule,
  Week,
  week,
  ExternalRef,
  Tag,
  IUpdateCustomAttributeDeploymentType,
  ValidateSiteTagsResponseBadRequest,
} from '../../../constants/types';
import { ASSET_MGMT_SITE_LIST } from '../../../constants/routes';
import ConfirmDialog from '../../ConfirmDialogV1';
import EditSiteDeploymentTypeDialog from '../EditSiteDeploymentTypeDialog';
import UserRoles from '../../../constants/userRoles';
import FeatureFlags from '../../../constants/featureFlags';
import ScheduleDeploymentAlert from '../../ScheduleDeploymentAlert';
import EditSiteForm from './EditSiteForm';
import { EditSiteDialogProps, StoreHourObject } from './types';
import millisTo24Hour from './millisTo24Hour';
import hourToMillis from './hourToMillis';

const noneItemId = 'null';
const defaultDeploymentType =
  IUpdateCustomAttributeDeploymentType.maintenanceWindow;

const EditSiteDialog = ({
  isOpen,
  siteId,
  onClose,
  siteDetail,
  keyGroups,
}: EditSiteDialogProps) => {
  const queryClient = useQueryClient();
  const devicesData: DevicesData =
    siteId && queryClient.getQueryData(['getDevices', siteId, 0, 0]);
  const devices: Device[] = devicesData?.results || [];
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const {
    data: allSiteTags,
    isLoading,
    refetch: refetchTags,
  } = useGetAllTags();
  const [selectedTags, setSelectedTags] = useState<Tag[]>(
    siteDetail?.tags ?? []
  );

  const updateSelectedTags = async () => {
    const getUpdatedTags = await refetchTags();
    setSelectedTags(previousTags => {
      const latestAddedTags = previousTags.filter(tag => !tag.id);
      const remainingTags = previousTags.slice(
        0,
        previousTags.length - latestAddedTags.length
      );
      let updatedTags =
        latestAddedTags.length === 0 ? previousTags : [...remainingTags];

      latestAddedTags.forEach(latestTag => {
        const isTagFound = getUpdatedTags?.data?.find(
          tag => tag.name === latestTag?.name
        );
        if (isTagFound) {
          updatedTags = [...updatedTags, isTagFound];
        }
      });

      return updatedTags;
    });
  };

  const handleSuccessChanges = async () => {
    queryClient.invalidateQueries(['getSite', siteId]);
    queryClient.invalidateQueries(['getSiteSummary', siteId]);
    queryClient.invalidateQueries(['getSites']);
    queryClient.invalidateQueries(['getAllTags']);
    updateSelectedTags();
    onClose();
  };

  const hasSchedulePermission = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.SCHEDULING_UST],
  });

  const [reset, toggleReset] = useState<boolean>(true);
  const [name, setName] = useState<string>(siteDetail?.name ?? '');
  const [email, setEmail] = useState<string>(siteDetail?.contactEmail ?? '');
  const [refId, setRefId] = useState<string>(siteDetail?.referenceId ?? '');
  const [phone, setPhone] = useState<string>(siteDetail?.contactPhone ?? '');
  const [address, setAddress] = useState(null);
  const [addressObject, setAddressObjectString] = useState(null);
  const [isHidden, setIsHidden] = useState(false);
  const [isPowerOff, setIsPowerOff] = useState(false);
  const [errorMessage, setErrorMessage] = useState<String>('');
  const [inputValue, setInputValue] = useState('');
  const [selectedStoreHours, setSelectedStoreHours] = useState<string>('');
  const [selectedRKI, setSelectedRKI] = useState<string>(
    siteDetail?.keyGroup?.id ?? noneItemId
  );
  const [initRKI, setInitRKI] = useState<string>(
    siteDetail?.keyGroup?.id ?? noneItemId
  );
  const [latitude, setLatitude] = useState<string>(siteDetail?.latitude ?? '');
  const [longitude, setLongitude] = useState<string>(
    siteDetail?.longitude ?? ''
  );
  const [tz, setTz] = useState<string>(siteDetail.timezoneId ?? '');
  const [hoursOptions] = useState<Schedule[]>([...schedule]);
  const [weekOrder] = useState<Week[]>([...week]);
  const [storeHourObject, setStoreHourObject] = useState<StoreHourObject[]>(
    siteDetail?.hours?.map(d => {
      const hoursObject = {
        openAt: millisTo24Hour(d.openAt),
        closeAt: millisTo24Hour(d.closeAt),
      };
      return hoursObject;
    }) ?? []
  );

  const [isRKIModalOpen, setIsRKIModalOpen] = useState<boolean>(false);
  const [isMFAModalOpen, setIsMFAModalOpen] = useState<boolean>(false);
  const [isDeploymentTypeModalOpen, setIsDeploymentTypeModalOpen] =
    useState<boolean>(false);
  const [mfa, setMfa] = useState<string>('');
  const [isSaveButtonEnable, setIsSaveButtonEnable] = useState<boolean>(false);
  const isRKIChanged = selectedRKI !== initRKI;
  const areTagsChanged = !isEqual(selectedTags, siteDetail?.tags);

  const [externalRef, setExternalRef] = useState<ExternalRef[]>(
    siteDetail?.externalReferences ?? []
  );
  const [availableallowExternalRef, setAvailableallowExternalRef] = useState<
    string[]
  >(siteDetail?.allowedExternalReferenceTypes ?? []);

  const [isDisableCmAutomaticDeployments, setIsDisableCmAutomaticDeployments] =
    useState(false);
  const [isDisableFileDownloads, setIsDisableFileDownloads] = useState(false);

  const { mutate: postSiteDetails } = usePostSiteDetails({
    onSuccess: () => {
      setAddress(address?.label ? address.label : address);
      handleSuccessChanges();
      setIsSaveButtonEnable(false);
    },
    onError: error => {
      enqueueSnackbar(
        (error as AxiosError)?.message?.toString() ??
          'Failed to save site details',
        { variant: 'error' }
      );
    },
  });

  const {
    mutate: validateSiteTags,
    data: siteTagValidationData,
    isLoading: postValidateSiteTagsIsLoading,
  } = usePostValidateSiteTags({
    siteId,
    siteTagIds: selectedTags.map(tag => tag?.id).filter(Boolean),
  });

  const invalidSiteTags = useMemo(() => {
    if (postValidateSiteTagsIsLoading) return [];
    if (
      (siteTagValidationData as ValidateSiteTagsResponseBadRequest)?.errors
        ?.length
    ) {
      return (
        siteTagValidationData as ValidateSiteTagsResponseBadRequest
      ).errors.map(error => error.property.siteTagId);
    }
    return [];
  }, [siteTagValidationData, postValidateSiteTagsIsLoading]);

  const invalidSiteTagMessage = useMemo(() => {
    if (postValidateSiteTagsIsLoading) return 'Validating site tags';
    return (
      (siteTagValidationData as ValidateSiteTagsResponseBadRequest)?.message ??
      ''
    );
  }, [siteTagValidationData, postValidateSiteTagsIsLoading]);

  const hasImmediateDeploymentAccess = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.CONFIG_MGMT],
  });

  const hasConfigManagementAccess = useHasPermissions({
    companyFeatureFlags: [FeatureFlags.CONFIG_MGMT],
  });

  const handleResetSite = () => {
    if (siteDetail) {
      setName(siteDetail?.name);
      setEmail(siteDetail?.contactEmail);
      setSelectedTags(siteDetail?.tags);
      setSelectedRKI(siteDetail?.keyGroup?.id ?? noneItemId);
      setInitRKI(siteDetail?.keyGroup?.id ?? noneItemId);

      setIsHidden(!siteDetail?.visible);
      setIsPowerOff(siteDetail?.suppressOffhoursAlarm);
      setRefId(siteDetail?.referenceId);
      setPhone(siteDetail?.contactPhone);
      setAddress(siteDetail?.formattedAddress);
      setAddressObjectString(siteDetail.address ?? siteDetail.formattedAddress);
      setLatitude(siteDetail?.latitude);
      setLongitude(siteDetail?.longitude);
      setTz(
        siteDetail?.timezoneId ??
          tzlookup(siteDetail.latitude, siteDetail.longitude)
      );
      setIsSaveButtonEnable(false);
      setExternalRef(siteDetail?.externalReferences ?? []);
      setAvailableallowExternalRef(
        siteDetail?.allowedExternalReferenceTypes ?? []
      );

      const msToHours = siteDetail?.hours?.map(d => {
        const hoursObject = {
          openAt: millisTo24Hour(d.openAt),
          closeAt: millisTo24Hour(d.closeAt),
        };
        return hoursObject;
      });
      setStoreHourObject(msToHours);
      setIsDisableCmAutomaticDeployments(
        siteDetail?.disableCmAutomaticDeployments
      );
      setIsDisableFileDownloads(siteDetail?.disableFileDownloads);
    }
  };

  const shouldPoweredOffOptionDisable = newStoreHourObject => {
    if (newStoreHourObject) {
      const disable = newStoreHourObject.every(
        d => d.openAt === '00:00' && d.closeAt === '23:59'
      );
      return disable;
    }
    return false;
  };

  useEffect(() => {
    shouldPoweredOffOptionDisable(storeHourObject) && setIsPowerOff(false);
  }, [storeHourObject]);

  const handleCheckRKI = async () => {
    const { devicesToRKI } = await getCheckRKI({
      devicesId: devices.map(({ id }) => id),
      keyGroupRef: selectedRKI === noneItemId ? null : selectedRKI,
    });

    if (!isEmpty(devicesToRKI)) {
      setIsRKIModalOpen(true);
    } else {
      setMfa('');
      setIsMFAModalOpen(true);
    }
  };

  const payload = useMemo(() => {
    const finalRKI = selectedRKI === noneItemId ? null : selectedRKI;
    const hours = hourToMillis(storeHourObject);
    const siteObject = {
      ...siteDetail,
      name,
      address: addressObject,
      formattedAddress: address?.label ? address.label : address,
      hours,
      tags: selectedTags,
      visible: !isHidden,
      suppressOffhoursAlarm: isPowerOff,
      referenceId: refId || null,
      contactPhone: phone,
      contactEmail: email,
      latitude,
      longitude,
      timezoneId: tz,
      ...(isRKIChanged && { keyGroupId: finalRKI, mfaCode: mfa }),
      ...(!isRKIChanged && finalRKI && { keyGroupId: finalRKI }),
      ...(externalRef && { externalReferences: externalRef }),
      disableCmAutomaticDeployments: isDisableCmAutomaticDeployments,
      disableFileDownloads: isDisableFileDownloads,
    };
    const { status, owner, keyGroup, isDefault, ...rest } = siteObject;

    return {
      site: rest as unknown as Site,
      deploymentType: defaultDeploymentType,
    };
  }, [
    siteDetail,
    selectedRKI,
    name,
    addressObject,
    address,
    storeHourObject,
    selectedTags,
    isHidden,
    isPowerOff,
    refId,
    phone,
    email,
    latitude,
    longitude,
    isRKIChanged,
    mfa,
    externalRef,
    isDisableFileDownloads,
    isDisableCmAutomaticDeployments,
  ]);

  const handleSaveSiteChanges = () => {
    setMfa('');
    setIsMFAModalOpen(false);
    postSiteDetails(payload);
  };

  const handleSubmitChanges = async () => {
    if (isRKIChanged) {
      await handleCheckRKI();
      return;
    }
    if (areTagsChanged && hasImmediateDeploymentAccess) {
      setIsDeploymentTypeModalOpen(true);
      return;
    }

    handleSaveSiteChanges();
  };

  const removeSite = async () => {
    try {
      await deleteSite(siteDetail.id);
      enqueueSnackbar('Site deleted', { variant: 'success' });
      onClose();
      setTimeout(() => {
        navigate(ASSET_MGMT_SITE_LIST);
      }, 1000);
    } catch (err) {
      enqueueSnackbar('Failed to delete site', { variant: 'error' });
    }
  };

  const isValidEmail = newEmail => {
    if (!newEmail) return true;
    // eslint-disable-next-line
    return /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
      newEmail
    );
  };

  const isButtonDisabled = useMemo(() => {
    if (
      postValidateSiteTagsIsLoading ||
      invalidSiteTags?.length ||
      errorMessage !== ''
    ) {
      return true;
    }
    const validAddress = address?.label ? address.label : address;
    const validEmail = isValidEmail(email);
    const nullCheck = validAddress === null ? true : !validAddress.length;
    const isExternalRefValid = externalRef?.every(ref =>
      Object.values(ref).every(value => value && value !== '')
    );
    return (
      !validEmail ||
      !name.length ||
      !selectedTags.length ||
      nullCheck ||
      !isSaveButtonEnable ||
      !isExternalRefValid
    );
  }, [
    address,
    email,
    name,
    selectedTags,
    isSaveButtonEnable,
    externalRef,
    postValidateSiteTagsIsLoading,
    invalidSiteTags,
    errorMessage,
  ]);

  useEffect(() => {
    if (selectedTags.length && hasConfigManagementAccess) {
      validateSiteTags({
        siteId,
        siteTagIds: selectedTags
          .filter(tag => Boolean(tag?.id))
          .map(tag => tag.id)
          .filter(Boolean),
      });
    }
  }, [siteId, selectedTags]);

  useEffect(() => {
    handleResetSite();
  }, []);

  return (
    <MuiDialog
      open={isOpen}
      onClose={() => {
        toggleReset(!reset);
        onClose();
      }}
      PaperProps={{
        style: { borderRadius: 20, width: 720, height: 760, maxWidth: 720 },
      }}
      data-testid='sk'
    >
      {isLoading && isOpen && (
        <Box
          width='100%'
          height='100%'
          display='flex'
          justifyContent='center'
          alignItems='center'
        >
          <CircularProgress />
        </Box>
      )}
      {!isLoading && isOpen && (
        <>
          <Box
            display='flex'
            flexDirection='row'
            justifyContent='space-between'
            borderBottom={1}
            borderColor='common.inactiveIcon'
          >
            <Typography px={3} py={2} variant='titleMedium' fontWeight={600}>
              Edit site
            </Typography>
          </Box>
          {hasSchedulePermission && (
            <Box
              style={{
                paddingTop: '20px',
                paddingLeft: '22px',
                paddingRight: '36px',
              }}
            >
              <ScheduleDeploymentAlert
                siteId={siteId}
                sourceType='UpdateSiteTags'
              />
            </Box>
          )}
          <EditSiteForm
            {...{
              address,
              allSiteTags,
              availableallowExternalRef,
              email,
              externalRef,
              hoursOptions,
              isHidden,
              isPowerOff,
              keyGroups,
              name,
              phone,
              refId,
              selectedRKI,
              selectedStoreHours,
              selectedTags,
              errorMessage,
              inputValue,
              setAddress,
              setAddressObjectString,
              setEmail,
              setExternalRef,
              setIsHidden,
              setIsPowerOff,
              setIsSaveButtonEnable,
              setLatitude,
              setLongitude,
              setName,
              setPhone,
              setRefId,
              setSelectedRKI,
              setSelectedStoreHours,
              setSelectedTags,
              setErrorMessage,
              setInputValue,
              setStoreHourObject,
              setTz,
              storeHourObject,
              weekOrder,
              invalidSiteTags,
              invalidSiteTagMessage,
              isDisableCmAutomaticDeployments,
              isDisableFileDownloads,
              setIsDisableCmAutomaticDeployments,
              setIsDisableFileDownloads,
            }}
          />
          <Box
            display='flex'
            justifyContent='space-between'
            px={2}
            gap={0}
            borderTop={1}
            borderColor='common.inactiveIcon'
          >
            {devices?.length === 0 ? (
              <Button
                sx={{ color: 'common.healthStatus.critical' }}
                onClick={removeSite}
              >
                Delete site
              </Button>
            ) : (
              <Box />
            )}
            <Box>
              <Button
                onClick={() => {
                  toggleReset(!reset);
                  handleResetSite();
                  setErrorMessage('');
                  setInputValue('');
                  onClose();
                }}
              >
                Cancel
              </Button>
              <Button
                variant='contained'
                onClick={handleSubmitChanges}
                disabled={isButtonDisabled}
              >
                Save changes
              </Button>
            </Box>
          </Box>
        </>
      )}
      <ConfirmDialog
        applyButtonName='Yes'
        closeButtonName='No'
        description='This action will trigger an RKI for some of the devices. Do you wish to continue?'
        title='RKI Confirmation'
        isOpen={isRKIModalOpen}
        onCancel={() => {
          toggleReset(!reset);
          setIsRKIModalOpen(false);
        }}
        onApply={() => {
          setIsMFAModalOpen(true);
          setIsRKIModalOpen(false);
        }}
      />
      <ConfirmDialog
        applyButtonName='Confirm'
        closeButtonName='Cancel'
        description='Re-enter your MFA code to create a new request for a key loading session.'
        title='Verify MFA'
        isOpen={isMFAModalOpen}
        onCancel={() => {
          setMfa('');
          toggleReset(!reset);
          setIsMFAModalOpen(false);
        }}
        disabled={mfa.length < 6}
        onApply={() => {
          setIsMFAModalOpen(false);
          if (areTagsChanged && hasImmediateDeploymentAccess) {
            setIsDeploymentTypeModalOpen(true);
            return;
          }
          handleSaveSiteChanges();
        }}
        component={
          <TextField
            autoComplete='one-time-code'
            name='mfa'
            error={mfa.length < 6}
            inputMode='numeric'
            label='code'
            onChange={e => {
              setMfa(e.target.value);
            }}
            value={mfa}
            variant='standard'
          />
        }
      />
      <EditSiteDeploymentTypeDialog
        isDialogOpen={isDeploymentTypeModalOpen}
        closeDialog={() => setIsDeploymentTypeModalOpen(false)}
        onConfirm={() => {
          setIsDeploymentTypeModalOpen(false);
          handleSuccessChanges();
        }}
        payload={payload}
      />
    </MuiDialog>
  );
};

export default EditSiteDialog;
