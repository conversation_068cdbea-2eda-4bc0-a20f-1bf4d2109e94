/* eslint-disable import/no-cycle */
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  TextField,
  Typography,
} from '@mui/material';
import React, { FC, useEffect, useState, ChangeEvent } from 'react';
import { DataGrid, GridRenderCellParams } from '@mui/x-data-grid';
import { Controller, useForm } from 'react-hook-form';
import LoadingButton from '@mui/lab/LoadingButton';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { Device, KeyGroup } from '../../constants/types';
import {
  useGetDevices,
  useGetKeyGroupsByCompany,
  useGetSiteSummary,
} from '../../services/use-query';
import {
  acceptableOfflinePackage,
  createRkiKeyBundlePackage,
} from '../../services/rkiService';
import { ASSET_MGMT_RKI_DETAILS } from '../../constants/routes';
import { getCurrentCompanyId } from '../../utils/helpers';

const PAGE_SIZE = 50;
const PAGE_INDEX = 0;
interface IProps {
  isOpen: boolean;
  onClose?: () => void;
  onOpen?: () => void;
  onError?: () => void;
  siteId?: string;
}

interface PackageModal {
  name: string;
  deviceIds?: number[];
}

const GenerateKeyBundleModal: FC<IProps> = ({
  isOpen,
  onClose,
  siteId,
}: IProps) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const {
    watch,
    control,
    reset,
    handleSubmit,
    setValue,
    clearErrors,
    formState: { isSubmitting },
  } = useForm<PackageModal>({ mode: 'all', reValidateMode: 'onChange' });

  const watchPackageName = watch('name', '');

  const INPUT_VALIDATION_MESSAGE = {
    name: {
      required: 'Package name is not allowed to be empty',
      pattern:
        'Only allowing uppercase, lowercase, number, dashes and underscore',
      minLength: 'Package name must be at least 3 characters long',
      maxLength: 'Package name must be less than 255 characters',
      unique: 'Package name already in use',
    },
  };

  const { enqueueSnackbar } = useSnackbar();
  const [pageSize, setPageSize] = React.useState(PAGE_SIZE);
  const [page, setPage] = useState(PAGE_INDEX);
  const { data, isLoading } = useGetDevices({
    siteId,
    pageIndex: page,
    pageSize,
  });
  const { results: allDevices, resultsMetadata: deviceMetadata } = data || {};

  const { data: siteData } = useGetSiteSummary(siteId);
  const { data: keyGroups } = useGetKeyGroupsByCompany(getCurrentCompanyId());

  const [allowedDevices, setAllowedDevices] = useState([]);
  const [hasAnyValidDevices, setHasAnyValidDevices] = useState(false);
  const [isPackagenameUnique, setIsPackagenameUnique] = useState(false);

  const close = () => {
    reset();
    onClose();
    setIsPackagenameUnique(false);
    clearErrors();
  };

  const handleGenerateKey = async (packageName: string) => {
    const model: PackageModal = {
      name: packageName,
      deviceIds: [],
    };
    allowedDevices.forEach((device: Device) => {
      if (device.letRKI) {
        model.deviceIds.push(Number(device.id));
      }
    });
    await createRkiKeyBundlePackage(model, null).then(
      (response: { id: string }) => {
        close();
        const rkiDetailsUrl = ASSET_MGMT_RKI_DETAILS.replace(
          '{0}',
          response.id
        );
        navigate(rkiDetailsUrl);
      },
      error => {
        close();
        enqueueSnackbar(error?.message, { variant: 'error' });
      }
    );
  };

  const packageNameChange = async (packageName: string) => {
    setValue('name', packageName);
    acceptableOfflinePackage(packageName, null).then(
      (unique: boolean) => {
        setIsPackagenameUnique(unique);
      },
      () => {
        setIsPackagenameUnique(false);
      }
    );
  };

  const onSubmit = async (formData: PackageModal) => {
    await packageNameChange(formData.name);
    await handleGenerateKey(formData.name);
  };

  const getAllowedDevices = () =>
    (allDevices as any[])?.map((device: Device) => {
      const tempDevice = { ...device };
      const isDeviceInKeyGroup =
        keyGroups?.find(
          (item: KeyGroup) => item.ref === tempDevice.keyGroupRef
        ) !== undefined;
      if (tempDevice.hasRki && isDeviceInKeyGroup) {
        // device has a key group
        tempDevice.keyGroup = tempDevice.keyGroupRef;
        tempDevice.letRKI = true;
      } else {
        // device doesn't have a key group
        tempDevice.keyGroup = '';
        tempDevice.letRKI = false;
      }
      return tempDevice;
    });

  useEffect(() => {
    if (!isLoading) {
      queryClient.invalidateQueries(['getDevices']);
      const letRkiDevices = getAllowedDevices();
      setAllowedDevices(letRkiDevices);

      setHasAnyValidDevices(
        siteData?.keyGroup?.id !== null &&
          letRkiDevices.some((item: Device) => item.letRKI === true)
      );
    }
  }, [siteId, isLoading]);

  return (
    <Dialog
      open={isOpen}
      onClose={close}
      PaperProps={{
        style: { borderRadius: 20, width: 720, height: 'auto', maxWidth: 720 },
      }}
    >
      {isLoading && (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CircularProgress />
        </Box>
      )}

      {!isLoading && isOpen && (
        <Box component='form' onSubmit={handleSubmit(onSubmit)}>
          <Box
            display='flex'
            flexDirection='row'
            justifyContent='space-between'
            borderBottom={1}
            borderColor='common.inactiveIcon'
          >
            <Typography
              sx={{ px: 3, py: 2, variant: 'titleMedium', fontWeight: 600 }}
            >
              Generate key bundle package
            </Typography>
          </Box>

          <Box
            sx={{
              bgcolor: 'common.backgroundLight',
              minHeight: 0,
              width: '100%',
              overflow: 'auto',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: 2,
                m: 3,
              }}
            >
              {hasAnyValidDevices && (
                <>
                  <Typography sx={{ flex: 0.5 }}>Key group name</Typography>
                  <Controller
                    control={control}
                    name='name'
                    rules={{
                      pattern: /^[a-zA-Z0-9._-]*$/,
                      required: true,
                      minLength: 3,
                      maxLength: 255,
                    }}
                    defaultValue=''
                    render={({ field, fieldState: { error } }) => (
                      <TextField
                        {...field}
                        label='Key group name'
                        variant='standard'
                        sx={{
                          flex: 2,
                          fieldset: { border: 0 },
                          minHeight: '64px',
                        }}
                        size='small'
                        autoFocus
                        error={!!error}
                        helperText={
                          error ? INPUT_VALIDATION_MESSAGE.name[error.type] : ''
                        }
                        onChange={(e: ChangeEvent<HTMLInputElement>) => {
                          packageNameChange(e.target.value);
                        }}
                      />
                    )}
                  />
                </>
              )}
              {!hasAnyValidDevices && (
                <Typography
                  sx={{ variant: 'bodySmall', color: 'common.errorRed' }}
                >
                  This site does not have any devices that have a key group
                </Typography>
              )}
            </Box>

            <DataGrid
              sx={{
                border: 'unset',
                px: 3,
                bgcolor: 'common.backgroundLight',
                height: 550,
                '& .MuiDataGrid-cell': {
                  border: 'unset',
                  fontSize: '11px',
                  fontWeight: 500,
                  bgcolor: 'common.backgroundLight',
                },
                '& .MuiTablePagination-selectLabel': {
                  marginBottom: 0,
                },
                '& .MuiTablePagination-displayedRows': {
                  marginBottom: 0,
                },
              }}
              columns={[
                {
                  field: 'name',
                  headerName: 'Device name',
                  flex: 1,
                  renderCell: (params: GridRenderCellParams) => (
                    <Box sx={{ alignItems: 'center', display: 'flex', gap: 2 }}>
                      <Typography
                        variant='bodySmall'
                        sx={{
                          color:
                            params.row.letRKI === false &&
                            !params.row.keyGroupId
                              ? 'common.iconFill'
                              : '',
                        }}
                      >
                        {params.row.name}
                      </Typography>
                    </Box>
                  ),
                },
                {
                  field: 'serialNumber',
                  headerName: 'Serial',
                  flex: 0.5,
                  renderCell: (params: GridRenderCellParams) => (
                    <Box sx={{ alignItems: 'center', display: 'flex', gap: 2 }}>
                      <Typography
                        variant='bodySmall'
                        sx={{
                          color:
                            params.row.letRKI === false &&
                            !params.row.keyGroupId
                              ? 'common.iconFill'
                              : '',
                        }}
                      >
                        {params.row.serialNumber}
                      </Typography>
                    </Box>
                  ),
                },
                {
                  field: 'keyGroupId',
                  headerName: 'Key group',
                  flex: 0.5,
                  renderCell: (params: GridRenderCellParams) => (
                    <Box>
                      <Box
                        sx={{ alignItems: 'center', display: 'flex', gap: 2 }}
                      >
                        {params.row.keyGroupId ? (
                          <Typography variant='bodySmall'>
                            {params.row.keyGroupRef}
                          </Typography>
                        ) : (
                          <Typography
                            variant='bodySmall'
                            sx={{
                              color:
                                params.row.letRKI === false
                                  ? 'common.iconFill'
                                  : '',
                            }}
                          >
                            Unknown
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  ),
                },
              ]}
              getRowId={row => row.id}
              rowsPerPageOptions={[20]}
              pagination
              paginationMode='server'
              page={page}
              onPageChange={newPage => setPage(newPage)}
              pageSize={pageSize}
              onPageSizeChange={(newPage: number): void => setPageSize(newPage)}
              rowCount={deviceMetadata?.totalResults ?? 0}
              rows={allowedDevices || null}
              rowHeight={40}
            />
          </Box>
          <Box display='flex'>
            {hasAnyValidDevices && (
              <Box
                sx={{
                  justifyContent: 'flex-start',
                  pt: 2,
                  px: 2,
                  gap: 0,
                  borderTop: 1,
                  borderColor: 'common.inactiveIcon',
                }}
              >
                <Typography variant='bodySmall'>
                  You must name the keygroup to continue
                </Typography>
              </Box>
            )}

            <Box
              sx={{
                display: 'flex',
                flex: 1,
                justifyContent: 'end',
                px: 2,
                gap: 0,
                borderTop: 1,
                borderColor: 'common.inactiveIcon',
              }}
            >
              {hasAnyValidDevices && (
                <Box>
                  <Button onClick={close}>Cancel</Button>
                  <LoadingButton
                    variant='contained'
                    type='submit'
                    loading={isSubmitting}
                    disabled={
                      !(
                        allowedDevices?.length > 0 &&
                        watchPackageName &&
                        isPackagenameUnique
                      )
                    }
                  >
                    Generate
                  </LoadingButton>
                </Box>
              )}
              {!hasAnyValidDevices && (
                <Button variant='contained' onClick={close}>
                  Ok
                </Button>
              )}
            </Box>
          </Box>
        </Box>
      )}
    </Dialog>
  );
};

export default GenerateKeyBundleModal;
