/* eslint-disable import/no-cycle */
import React, { Suspense } from 'react';
import { ThemeProvider } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { Navigate, useRoutes } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ErrorBoundary } from 'react-error-boundary';
import lazyWithPreload from './utils/lazyWithPreload';
import MainLayoutSkeleton from './layouts/MainLayoutSkeleton';

/* Constants */
import {
  BASE_URL,
  ASSET_MGMT_ROOT,
  ASSET_MGMT_SITE_DETAIL_ROOT,
  ASSET_MGMT_SITE_DETAIL_TABS,
  ASSET_MGMT_SITE_LIST,
  ASSET_MGMT_LEGACY_PAYMENT_DASHBOARD,
  NOT_FOUND_PAGE,
  ASSET_MGMT_ATTRIBUTES_BULK_EDITOR,
  ASSET_MGMT_ATTRIBUTES_BULK_EDITOR_PAGE,
  ASSET_MGMT_IMPORT_ATTRIBUTES,
  ASSET_MGMT_IMPORT_DEVICES_BY_CSV,
  ASSET_MGMT_PAST_IMPORT_DEVICES_BY_CSV,
  ASSET_MGMT_IMPORT_SITE_TAGS_BY_CSV,
  ASSET_MGMT_PAST_IMPORT_SITE_TAGS_BY_CSV,
  ASSET_MGMT_DELETE_SITE_TAGS_BY_CSV,
  ASSET_MGMT_PAST_DELETE_SITE_TAGS_BY_CSV,
  ASSET_MGMT_IMPORT_SITES_BY_CSV,
  ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV,
} from './constants/routes';
import { snackBar, theme } from './constants/theme';

/* Hooks & Helpers */
import { AppProvider } from './hooks/Context';
import queryClient from './utils/queryClient';

/* Page Components */
import { PaymentsTabLegacy } from './pages/SiteDetailTabs/PaymentsTab';

import FallbackComponent from './pages/FallbackComponent';
import { ImportByCsvCategory } from './constants/types';
import SnackbarWithTitle from './components/CSVuploader/components/CustomSnackbar/SnackbarWithTitle';

const Sites = lazyWithPreload(() => import('./pages/Sites'));

const SiteDetail = lazyWithPreload(() => import('./pages/SiteDetail'));

const BulkEditorSelection = lazyWithPreload(
  () => import('./pages/SiteAttributes/BulkEditorSelection')
);
const BulkEditorForm = lazyWithPreload(
  () => import('./pages/SiteAttributes/BulkEditorForm')
);

const ImportDevicesByCSV = lazyWithPreload(
  () => import('./pages/ImportDevicesByCSV')
);
const PastImportDevicesByCSV = lazyWithPreload(
  () => import('./pages/ImportDevicesByCSV/PastImports')
);

const ImportSiteTagsByCSV = lazyWithPreload(
  () => import('./pages/ImportSiteTagsByCSV')
);
const PastImportSiteTagsByCSV = lazyWithPreload(
  () => import('./pages/ImportSiteTagsByCSV/PastImports')
);

const DeleteSiteTagsByCSV = lazyWithPreload(
  () => import('./pages/DeleteSiteTagsByCSV')
);
const PastDeleteSiteTagsByCSV = lazyWithPreload(
  () => import('./pages/DeleteSiteTagsByCSV/PastImports')
);

const ImportSitesByCSV = lazyWithPreload(
  () => import('./pages/ImportSitesByCSV')
);
const PastImportSitesByCSV = lazyWithPreload(
  () => import('./pages/ImportSitesByCSV/PastImports')
);

const ImportAttributes = lazyWithPreload(
  () => import('./pages/ImportAttributes')
);

SiteDetail.preload();

const {
  styles,
  constants: { duration, max, documentRoot },
} = snackBar;

const Routes = () =>
  useRoutes([
    {
      path: BASE_URL,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <Sites />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_ROOT,
      element: <Navigate to={ASSET_MGMT_SITE_LIST} replace />,
    },
    {
      path: ASSET_MGMT_SITE_LIST,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <Sites />
        </Suspense>
      ),
    },
    {
      // Redirect to Devices Tab
      path: ASSET_MGMT_SITE_DETAIL_ROOT,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <SiteDetail />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_SITE_DETAIL_TABS,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <SiteDetail />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_ATTRIBUTES_BULK_EDITOR,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <BulkEditorSelection />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_ATTRIBUTES_BULK_EDITOR_PAGE,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <BulkEditorForm />
        </Suspense>
      ),
    },
    {
      path: `${ASSET_MGMT_ROOT}/*`,
      element: <Navigate to={NOT_FOUND_PAGE} replace />,
    },
    {
      path: ASSET_MGMT_IMPORT_ATTRIBUTES,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ImportAttributes />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_IMPORT_DEVICES_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ImportDevicesByCSV category={ImportByCsvCategory.assignDevices} />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_PAST_IMPORT_DEVICES_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <PastImportDevicesByCSV
            category={ImportByCsvCategory.assignDevices}
          />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_IMPORT_SITE_TAGS_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ImportSiteTagsByCSV category={ImportByCsvCategory.assignSiteTags} />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_DELETE_SITE_TAGS_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <DeleteSiteTagsByCSV category={ImportByCsvCategory.deleteSiteTags} />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_IMPORT_SITES_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ImportSitesByCSV category={ImportByCsvCategory.importSites} />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_PAST_IMPORT_SITE_TAGS_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <PastImportSiteTagsByCSV
            category={ImportByCsvCategory.assignSiteTags}
          />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_PAST_DELETE_SITE_TAGS_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <PastDeleteSiteTagsByCSV
            category={ImportByCsvCategory.deleteSiteTags}
          />
        </Suspense>
      ),
    },
    {
      path: ASSET_MGMT_PAST_IMPORT_SITES_BY_CSV,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <PastImportSitesByCSV category={ImportByCsvCategory.importSites} />
        </Suspense>
      ),
    },
    {
      // Payment Dashboard for Legacy Site-Detail
      path: ASSET_MGMT_LEGACY_PAYMENT_DASHBOARD,
      element: <PaymentsTabLegacy />,
    },
  ]);

const Root = () => (
  <div className='ics-app'>
    <ErrorBoundary FallbackComponent={FallbackComponent}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={theme}>
          <SnackbarProvider
            maxSnack={max}
            autoHideDuration={duration}
            style={styles}
            domRoot={documentRoot}
            Components={{ snackbarWithTitle: SnackbarWithTitle }}
          >
            <AppProvider>
              <Routes />
            </AppProvider>
          </SnackbarProvider>
        </ThemeProvider>
        <ReactQueryDevtools position='bottom-left' />
      </QueryClientProvider>
    </ErrorBoundary>
  </div>
);

export default Root;
