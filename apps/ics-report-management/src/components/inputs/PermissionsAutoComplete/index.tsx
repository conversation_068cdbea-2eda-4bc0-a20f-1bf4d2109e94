import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  type SyntheticEvent,
} from 'react';
import Autocomplete, {
  AutocompleteInputChangeReason,
} from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';

import type { ForwardedRefProps } from '../types';

import useGetRoles from '../../../services/useGetRoles';
import UserRoles, { roles } from '../../../constants/userRoles';
import useRenderers from '../../../hooks/useAutoCompleteRendererers';
import customAutoCompleteMultipleProps from '../CustomAutocomplete/customAutoCompleteMultipleProps';
import DropdownHeader from './hooks/useHeader';

const PermissionsAutoComplete = forwardRef(
  (props: ForwardedRefProps, forwardedRef) => {
    const { label, onChange, error, helperText, ...field } = props;
    const [inputValue, setInputValue] = useState('');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedValues, setSelectedValues] = useState<string[]>([]);
    const [availableFilterValues, setAvailableFilterValues] = useState<
      string[]
    >([]);
    const listBoxRef = useRef<HTMLUListElement>(null);
    const scrollPositionRef = useRef<number>(0);

    const {
      data: dataRoles,
      isSuccess: isSuccessRoles,
      isFetching: isLoadingRoles,
    } = useGetRoles({
      queryKey: ['roles'],
    });

    const availablePermissions = useMemo(() => {
      if (!isSuccessRoles || !dataRoles.length) {
        return [];
      }
      const onlyPermissions = dataRoles.filter(
        role =>
          !roles.includes(role.name as UserRoles) &&
          role.name !== UserRoles.BRIDGE_APP
      );
      return onlyPermissions.map(role => role.name);
    }, [dataRoles, isSuccessRoles]);

    useEffect(() => {
      if (dataRoles) {
        const searchFilteredOptions = availablePermissions.filter(permission =>
          permission.toLowerCase().includes(searchQuery.toLowerCase())
        );

        setAvailableFilterValues(searchFilteredOptions);
      }
    }, [dataRoles, searchQuery]);

    useEffect(() => {
      const savedPermissions = sessionStorage.getItem(label);
      if (savedPermissions) {
        const parsedSelections = JSON.parse(savedPermissions);
        setSelectedValues(parsedSelections);
        onChange(parsedSelections);
      }

      // Clear sessionStorage on component unmount
      return () => {
        sessionStorage.removeItem(label);
      };
    }, [label, onChange]);

    const { renderInput, renderOption, renderTags } = useRenderers({
      inputLabel: label,
    });

    const handleOnChange = useCallback(
      (_: SyntheticEvent<Element, Event>, newValue: string[]) => {
        setSelectedValues(newValue);
        onChange(newValue);
        sessionStorage.setItem(label, JSON.stringify(newValue));
      },
      [onChange, label]
    );

    const CustomPaper = useCallback(
      ({ children, ...paperProps }) => (
        <Paper {...paperProps}>
          <DropdownHeader
            maxSelected={availableFilterValues.length}
            value={selectedValues}
            availableOptions={availableFilterValues}
            onChange={newSelectedNames => {
              const newSelectedValues = availableFilterValues.filter(option =>
                newSelectedNames.includes(option)
              );
              setSelectedValues(newSelectedValues);
              onChange(newSelectedValues);
              sessionStorage.setItem(label, JSON.stringify(newSelectedValues));
            }}
          />
          {children}
        </Paper>
      ),
      [availableFilterValues, selectedValues, onChange, label]
    );

    const handleInputChange = useCallback(
      (
        _event: SyntheticEvent,
        value: string,
        reason: AutocompleteInputChangeReason
      ) => {
        if (reason === 'input') {
          setInputValue(value);
          setSearchQuery(value);
        }
        if (reason === 'clear') {
          setInputValue('');
          setSearchQuery('');
          setSelectedValues([]);
          onChange([]);
          sessionStorage.removeItem(label);
        }
      },
      [onChange, label]
    );

    const handleScroll = useCallback((event: SyntheticEvent) => {
      const listboxNode = event.currentTarget;
      scrollPositionRef.current = listboxNode.scrollTop;
    }, []);

    useEffect(() => {
      if (listBoxRef.current) {
        listBoxRef.current.scrollTop = scrollPositionRef.current;
      }
    }, [selectedValues]);

    return (
      <Box flex='1' position='relative'>
        <Autocomplete
          {...customAutoCompleteMultipleProps}
          {...field}
          loading={isLoadingRoles}
          onChange={handleOnChange}
          options={availableFilterValues}
          ref={forwardedRef}
          renderInput={renderInput}
          renderOption={renderOption}
          renderTags={renderTags}
          PaperComponent={CustomPaper}
          inputValue={inputValue}
          value={selectedValues}
          onInputChange={handleInputChange}
          ListboxProps={{
            onScroll: handleScroll,
            ref: listBoxRef,
          }}
        />
      </Box>
    );
  }
);

export default memo(PermissionsAutoComplete);
