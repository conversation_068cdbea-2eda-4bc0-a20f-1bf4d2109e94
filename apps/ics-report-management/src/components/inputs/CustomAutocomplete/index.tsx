import {
  forwardRef,
  memo,
  useCallback,
  useMemo,
  useState,
  type SyntheticEvent,
  useEffect,
} from 'react';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';

import { ForwardedRefProps } from '../types';
import defaultAutoCompleteMultipleProps from '../shared/defaultAutoCompleteMultipleProps';
import useRenderers from '../../../hooks/useAutoCompleteRendererers';
import DropdownHeader from './hooks/useHeader';

type FilterOption = {
  id: number;
  name: string;
};

type CustomAutocompleteProps = ForwardedRefProps & {
  options: string[];
  isDisabled?: boolean;
  maxSelected?: number;
};

const CustomAutocomplete = forwardRef(
  (props: CustomAutocompleteProps, forwardedRef) => {
    const {
      label,
      error,
      onChange,
      helperText,
      options,
      isDisabled,
      maxSelected = options.length,
      ...field
    } = props;

    const [inputValue, setInputValue] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedValues, setSelectedValues] = useState<FilterOption[]>([]);

    const allFilterValues = useMemo(
      () =>
        options.map((option, index) => ({
          id: index,
          name: option,
        })),
      [options]
    );

    const availableFilterValues = useMemo(
      () =>
        allFilterValues.filter(option =>
          option.name.toLowerCase().includes(searchQuery.toLowerCase())
        ),
      [allFilterValues, searchQuery]
    );

    const { renderInput, renderOption, renderTags } = useRenderers({
      inputLabel: label,
      itemLabel: 'name',
      error,
      helperText,
    });

    useEffect(() => {
      const savedRoles = sessionStorage.getItem('selectedRoles');
      if (savedRoles) {
        const parsedRoles = JSON.parse(savedRoles);
        const selectedRoleOptions = allFilterValues.filter(option =>
          parsedRoles.includes(option.name)
        );
        setSelectedValues(selectedRoleOptions);
        onChange?.(selectedRoleOptions);
      }
      return () => {
        sessionStorage.removeItem('selectedRoles');
      };
    }, [allFilterValues, onChange]);

    const handleOnChange = useCallback(
      (_: SyntheticEvent<Element, Event>, newValue: FilterOption[]) => {
        const isDeselecting = selectedValues.some(
          selected => !newValue.some(newItem => newItem.id === selected.id)
        );

        const updatedValues = isDeselecting
          ? selectedValues.filter(selected =>
              newValue.some(newItem => newItem.id === selected.id)
            )
          : newValue;

        if (!maxSelected || updatedValues.length <= maxSelected) {
          setSelectedValues(updatedValues);
          onChange?.(updatedValues);

          const selectedRoles = updatedValues.map(item => item.name);
          sessionStorage.setItem(
            'selectedRoles',
            JSON.stringify(selectedRoles)
          ); // Save to sessionStorage
        }
      },
      [onChange, maxSelected, selectedValues]
    );

    const CustomPaper = useCallback(
      ({ children, ...paperProps }) => (
        <Paper {...paperProps}>
          <DropdownHeader
            maxSelected={maxSelected ?? availableFilterValues.length}
            value={selectedValues.map(item => item.name)}
            availableOptions={availableFilterValues.map(item => item.name)}
            onChange={newSelectedNames => {
              const newSelectedValues = availableFilterValues.filter(option =>
                newSelectedNames.includes(option.name)
              );
              const updatedSelection = [
                ...selectedValues.filter(
                  selected =>
                    !availableFilterValues.some(
                      option => option.id === selected.id
                    )
                ),
                ...newSelectedValues,
              ];
              setSelectedValues(updatedSelection);
              onChange(updatedSelection);
            }}
          />
          {children}
        </Paper>
      ),
      [availableFilterValues, selectedValues, maxSelected, onChange]
    );

    return (
      <Box>
        <Autocomplete
          {...field}
          {...defaultAutoCompleteMultipleProps}
          onChange={handleOnChange}
          getOptionLabel={(option: FilterOption) => option.name}
          options={availableFilterValues}
          ref={forwardedRef}
          renderInput={renderInput}
          renderOption={renderOption}
          renderTags={renderTags}
          inputValue={inputValue}
          value={selectedValues}
          PaperComponent={CustomPaper}
          onInputChange={(event, value, reason) => {
            if (reason === 'input') {
              setInputValue(value);
              setSearchQuery(value);
            } else if (reason === 'clear') {
              setInputValue('');
              setSearchQuery('');
              setSelectedValues([]);
              onChange?.([]);
            }
          }}
        />
      </Box>
    );
  }
);

export default memo(CustomAutocomplete);
