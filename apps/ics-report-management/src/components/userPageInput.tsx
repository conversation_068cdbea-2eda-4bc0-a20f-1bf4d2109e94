import { useState, useEffect, ChangeEvent, KeyboardEvent } from 'react';

interface UsePageInputProps {
  currentPage: number;
  totalPages: number;
  updatePageIndex: (pageIndex: number) => void;
}

interface UsePageInputReturn {
  inputPage: string;
  handlePageInputChange: (event: ChangeEvent<HTMLInputElement>) => void;
  handlePageInputBlur: () => void;
  handlePageInputKeyDown: (event: KeyboardEvent<HTMLInputElement>) => void;
}

//   Custom hook to handle page input logic

export function usePageInput({
  currentPage,
  totalPages,
  updatePageIndex,
}: UsePageInputProps): UsePageInputReturn {
  const [inputPage, setInputPage] = useState<string>(currentPage.toString());

  // Sync local inputPage state when currentPage changes externally
  useEffect(() => {
    setInputPage(currentPage.toString());
  }, [currentPage]);

  // Update local input state on typing
  const handlePageInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const val = event.target.value;
    // Allow only numbers or empty string to allow user to clear input
    if (/^\d*$/.test(val)) {
      setInputPage(val);
    }
  };

  // Commit page change on blur or on Enter key press
  const commitPageChange = () => {
    const newPageNum = Number(inputPage);
    if (
      newPageNum >= 1 &&
      newPageNum <= totalPages &&
      newPageNum !== currentPage
    ) {
      updatePageIndex(newPageNum - 1);
    } else {
      // Reset input to current page if invalid
      setInputPage(currentPage.toString());
    }
  };

  const handlePageInputBlur = () => {
    commitPageChange();
  };

  const handlePageInputKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      commitPageChange();
      // Optionally, remove focus after Enter
      (event.target as HTMLElement).blur();
    }
  };

  return {
    inputPage,
    handlePageInputChange,
    handlePageInputBlur,
    handlePageInputKeyDown,
  };
}
