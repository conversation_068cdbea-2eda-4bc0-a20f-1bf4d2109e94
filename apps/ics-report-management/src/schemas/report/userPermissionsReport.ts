import { z } from 'zod';

import FeatureFlags from '../../constants/featureFlags';
import ReportModules from '../../constants/reportModules';
import UserRoles from '../../constants/userRoles';
import removeEmptyFromObject from '../../utils/removeEmptyFromObject';
import { roleSchema } from '../system/roles';

const RequireFeatureFlags = z.literal(FeatureFlags.REPORTING);

const RequireRoles = z.union([
  z.literal(UserRoles.COMPANY_ADMIN),
  z.literal(UserRoles.SUPER_ADMIN),
]);

const userPermissionsReportSchema = z.object({
  active: z.boolean(),
  baseUrl: z.union([
    z.literal('/report-management/user-permissions-report'),
    z.literal('/reports/user-permissions-report'),
  ]),
  description: z.string(),
  id: z.number(),
  module: z.literal(ReportModules.Administration),
  name: z.literal('User Permissions Report'),
  requireFeatureFlags: z.array(RequireFeatureFlags),
  requireRoles: z.array(RequireRoles),
});

const userPermissionsForm = z.object({
  pageIndex: z.number().optional().nullable(),
  pageSize: z.number().optional(),
  permissions: z.array(z.nativeEnum(UserRoles)).optional(),
  roles: z.array(roleSchema).optional(),
  statuses: z.boolean().optional(),
  user: z.string().optional(),
});

const userPermissionsFormSchema = userPermissionsForm.transform(data =>
  removeEmptyFromObject({
    ...data,
    roles: data.roles?.map(({ name }) => name) ?? [],
  })
);

type UserPermissionsFormSchema = z.infer<typeof userPermissionsForm>;

const userPermissionsReportRequestSchema = z.object({
  pageIndex: z.number().optional(),
  pageSize: z.number().optional(),
  statuses: z.array(z.number()).optional(),
  permissions: z.array(z.nativeEnum(UserRoles)).optional(),
  roles: z.array(z.nativeEnum(UserRoles)).optional(),
  user: z.string().optional(),
});

type UserPermissionsReportRequest = z.infer<
  typeof userPermissionsReportRequestSchema
>;

const userHistoryReportResult = z.object({
  id: z.string(),
  email: z.string(),
  fullName: z.string(),
  status: z.number(),
  lastLoggedIn: z.string().optional().nullable(),
  permissions: z
    .array(z.union([z.nativeEnum(UserRoles), z.string()]))
    .optional(),
  roles: z.array(z.union([z.nativeEnum(UserRoles), z.string()])),
});

const resultsMetadata = z.object({
  totalResults: z.number(),
  pageIndex: z.number(),
  pageSize: z.number(),
});

const userHistoryReportResponseSchema = z.object({
  resultsMetadata,
  results: z.array(userHistoryReportResult),
});

type UserHistoryReportResponse = z.infer<
  typeof userHistoryReportResponseSchema
>;

type UserPermissionsReportResult = z.infer<typeof userHistoryReportResult>;

export {
  userPermissionsReportSchema,
  userPermissionsFormSchema,
  userHistoryReportResponseSchema,
  userPermissionsReportRequestSchema,
  type UserHistoryReportResponse,
  type UserPermissionsFormSchema,
  type UserPermissionsReportRequest,
  type UserPermissionsReportResult,
};
