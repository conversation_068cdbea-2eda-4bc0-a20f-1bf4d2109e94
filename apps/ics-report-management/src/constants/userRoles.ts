enum UserRoles {
  ANALYST = 'ANALYST',
  BR<PERSON><PERSON>_APP = 'BRIDGE_APP',
  COMPANY_ADMIN = 'COMPANY_ADMIN',
  CONFIG_MGMT_ASSIGN = 'CONFIG_MGMT_ASSIGN',
  CONFIG_MGMT_DEPLOY = 'CONFIG_MGMT_DEPLOY',
  CONFIG_MGMT_PUBLISH = 'CONFIG_MGMT_PUBLISH',
  DEVICE_MERCHANT_RESET = 'DEVICE_MERCHANT_RESET',
  FACTORY_RESET = 'FACTORY_RESET',
  FUEL_PRICE_MGMT_CANCEL = 'FUEL_PRICE_MGMT_CANCEL',
  FUEL_PRICE_MGMT_EDIT = 'FUEL_PRICE_MGMT_EDIT',
  FUEL_PRICE_MGMT_THRESHOLD = 'FUEL_PRICE_MGMT_THRESHOLD',
  FUEL_PRICE_MGMT_VIEW = 'FUEL_PRICE_MGMT_VIEW',
  MEDIA_APPROVER = 'MEDIA_APPROVER',
  MEDIA_DEPLOYER = 'MEDIA_DEPLOYER',
  MEDIA_DESIGNER = 'MEDIA_DESIGNER',
  POWER_USER = 'POWER_USER',
  RKI = 'RKI',
  SITE_SETTINGS_EDIT = 'SITE_SETTINGS_EDIT',
  SITE_SETTINGS_VIEW = 'SITE_SETTINGS_VIEW',
  SPECIALIST = 'SPECIALIST',
  SUPER_ADMIN = 'SUPER_ADMIN',
  TAMPER_CLEAR = 'TAMPER_CLEAR',
  USER = 'USER',
  CERTIFICATE_APPROVER = 'CERTIFICATE_APPROVER',
  PLAYLIST_VIEW = 'PLAYLIST_VIEW',
  PLAYLIST_MANAGE = 'PLAYLIST_MANAGE',
  PLAYLIST_DEPLOY = 'PLAYLIST_DEPLOY',
  MEDIA_VIEW = 'MEDIA_VIEW',
  COMFIG_MGMT_VIEW = 'CONFIG_MGMT_VIEW',
  REPORTS_VIEW = 'REPORTS_VIEW',
  RKI_VIEW = 'RKI_VIEW',
  SETTINGS_VIEW = 'SETTINGS_VIEW',
  REMOTE_MGMT_VIEW = 'REMOTE_MGMT_VIEW',
  BULK_OPERATIONS_VIEW = 'BULK_OPERATIONS_VIEW',
  BANK_USER = 'BANK_USER',
  CONFIG_MGMT_APPROVER = 'CONFIG_MGMT_APPROVER',
  SITE_CA_APPROVER = 'SITE_CA_APPROVER',
  CSR = 'CSR',
}
const roles = [
  UserRoles.ANALYST,
  UserRoles.COMPANY_ADMIN,
  UserRoles.POWER_USER,
  UserRoles.SPECIALIST,
  UserRoles.USER,
  UserRoles.SUPER_ADMIN,
  UserRoles.BANK_USER,
];

export default UserRoles;

export { roles };
