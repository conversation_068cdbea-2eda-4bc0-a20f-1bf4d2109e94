import type { ColDef } from '@ag-grid-community/core';

import '../style.css';
import { mediaPlaybackHistoryReportResult } from '../../../schemas/report/mediaPlaybackHistoryReport';

let header;

const styleDataWithoutDir = {
  height: '100%',
  display: 'flex',
  alignItems: 'center',
};

const columnDefs: ColDef<mediaPlaybackHistoryReportResult>[] = [
  {
    field: 'siteName',
    headerName: (header = 'Site'),
    headerTooltip: header,
    cellStyle: { ...styleDataWithoutDir },

    cellRenderer: params => {
      const { siteName } = params.data;

      const { siteId } = params.data;
      const previousRow = params.api.getDisplayedRowAtIndex(
        params.node.rowIndex - 1
      );
      // Determine if the current row is the first in its group
      const isFirstRow = !previousRow || previousRow.data.siteName !== siteName;

      // If this is the first row of the group, display the site details
      if (isFirstRow) {
        return (
          <div
            style={{
              fontSize: '14px',
              whiteSpace: 'normal',
              wordWrap: 'break-word',
            }}
          >
            <a
              href={`/sites/${siteId}`}
              style={{ textDecoration: 'none', color: '#000' }}
              target='_blank'
              rel='noopener noreferrer'
            >
              <strong style={{ fontSize: '13px', display: 'block' }}>
                {siteName}
              </strong>
            </a>
          </div>
        );
      }
      return '';
    },
  },

  {
    field: 'referenceId',
    headerName: (header = 'Site Reference ID'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },
  {
    field: 'name',
    flex: 1,
    headerName: (header = 'Media Segment'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },
  {
    field: 'date',
    headerName: (header = 'Date (Local time)'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
    valueGetter: params => {
      const { date } = params.data;
      if (date === null) {
        return '';
      }
      // Fix: Prevent date from shifting to previous day
      // Adding T00:00:00 ensures date displays correctly for all users
      const parsedDate = new Date(`${date}T00:00:00`);
      const options: Intl.DateTimeFormatOptions = {
        month: '2-digit',
        day: '2-digit',
        year: '2-digit',
      };

      // Get the formatted date string in MM/DD/YY format
      return parsedDate.toLocaleDateString('en-US', options);
    },
  },
  {
    field: 'runtime',
    flex: 1,
    headerName: (header = 'Runtime'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
    valueGetter: params => {
      const { runtime } = params.data;
      if (runtime === null || runtime < 0) {
        return ''; // Return empty if runtime is null or negative
      }

      // Convert milliseconds to total seconds
      const totalSeconds = Math.floor(runtime / 1000);

      // Calculate hours, minutes, and seconds
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      // Format the output string as HH:MM:SS, ensuring two digits for each component
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    },
  },

  {
    field: 'count',
    flex: 1,
    headerName: (header = 'Count'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },
];

export default columnDefs;
