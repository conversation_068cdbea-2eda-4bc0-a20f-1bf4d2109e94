import { memo, useCallback, Suspense } from 'react';
import { Box, IconButton, MenuItem, Select, TextField } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';

import ReportContainer from '../../components/layout/ReportContainer';
import AGgridContainer from '../../components/layout/AGgridContainer';
import PaginationContainer from '../../components/layout/PaginationContainer';
import Form from './Form';
import useFormSubmit from './hooks/useFormSubmit';
import lazyWithPreload from '../../utils/lazyWithPreload';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import encodeQueryParams from '../../utils/encodeQueryParams';
import useMutateDataForRender from './hooks/useMutateDataForRender';
import extractSubmitValues from './utils/extractSubmitValues';
import { usePageInput } from '../../components/userPageInput';

const Grid = lazyWithPreload(() => import('./Grid'));
Grid.preload();

const Report = () => {
  const { valuesFromParams, setParams } = useGetValuesFromParams();

  const {
    isPendingPermissionsReport,
    dataUserPermissionsReport,
    isSuccessPermissionsReport,
  } = useFormSubmit();

  const { page, pageCount, rowData } = useMutateDataForRender({
    data: dataUserPermissionsReport,
    isDataSuccess: isSuccessPermissionsReport,
  });

  const currentPage = page + 1;
  const totalPages = pageCount;

  // Correctly get totalResults from resultsMetadata
  const totalResults =
    dataUserPermissionsReport?.resultsMetadata?.totalResults ?? 0;

  // Calculate start and end record numbers
  const startRecord = (currentPage - 1) * valuesFromParams.pageSize + 1;
  const endRecord = Math.min(
    currentPage * valuesFromParams.pageSize,
    totalResults
  );

  // Use the custom page input hook
  const {
    inputPage,
    handlePageInputChange,
    handlePageInputBlur,
    handlePageInputKeyDown,
  } = usePageInput({
    currentPage,
    totalPages,
    updatePageIndex: (newPageIndex: number) => {
      if (newPageIndex === page) return;
      const newParams = encodeQueryParams({
        ...valuesFromParams,
        page: newPageIndex,
      });
      setParams(newParams);
    },
  });

  const handlePageSizeChange = useCallback(
    event => {
      const newPageSize = event.target.value;
      const newParams = encodeQueryParams({
        ...valuesFromParams,
        pageSize: newPageSize,
        page: 0,
      }); // reset to first page on page size change
      setParams(newParams);
    },
    [setParams, valuesFromParams]
  );

  const handleRunReportClick = useCallback(() => {
    const newParams = extractSubmitValues(valuesFromParams);
    const encoded = encodeQueryParams(newParams);
    setParams(encoded);
  }, [valuesFromParams, setParams]);

  return (
    <ReportContainer>
      <Form
        isLoading={isPendingPermissionsReport}
        onRunReportClick={handleRunReportClick}
      />
      <AGgridContainer style={{ minHeight: '440px' }}>
        <Suspense fallback={<div />}>
          {isSuccessPermissionsReport && <Grid results={rowData} />}
        </Suspense>
      </AGgridContainer>
      <PaginationContainer>
        {dataUserPermissionsReport?.results?.length > 0 && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              p: '5px 30px',
            }}
          >
            {/* Previous Page Button */}
            <IconButton
              onClick={() => {
                const newPage = Math.max(0, page - 1);
                const newParams = encodeQueryParams({
                  ...valuesFromParams,
                  page: newPage,
                });
                setParams(newParams);
              }}
              disabled={page === 0 || isPendingPermissionsReport}
            >
              <ChevronLeft />
            </IconButton>

            {/* Page Number Input */}
            <TextField
              value={inputPage}
              size='small'
              sx={{
                width: 50,
                '& .MuiOutlinedInput-input': {
                  padding: '4px 14px',
                },
              }}
              onChange={handlePageInputChange}
              onBlur={handlePageInputBlur}
              onKeyDown={handlePageInputKeyDown}
              disabled={isPendingPermissionsReport}
            />
            <span> / {totalPages}</span>

            {/* Next Page Button */}
            <IconButton
              onClick={() => {
                const newPage = Math.min(totalPages - 1, page + 1);
                const newParams = encodeQueryParams({
                  ...valuesFromParams,
                  page: newPage,
                });
                setParams(newParams);
              }}
              disabled={
                currentPage === totalPages || isPendingPermissionsReport
              }
            >
              <ChevronRight />
            </IconButton>

            {/* Page Size Dropdown */}
            <Select
              value={valuesFromParams.pageSize}
              onChange={handlePageSizeChange}
              size='small'
              sx={{
                '& .MuiOutlinedInput-input': {
                  padding: '4px 8px',
                },
              }}
              disabled={isPendingPermissionsReport}
            >
              {[10, 25, 50, 100].map(size => (
                <MenuItem key={size} value={size}>
                  {size}
                </MenuItem>
              ))}
            </Select>

            {/* Displaying Records Info */}
            <span>{`${startRecord} - ${endRecord} of ${totalResults} Records`}</span>
          </Box>
        )}
      </PaginationContainer>
    </ReportContainer>
  );
};

export default memo(Report);
