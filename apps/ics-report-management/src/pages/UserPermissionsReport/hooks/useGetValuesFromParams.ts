import { useMemo, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';

import type { UserPermissionsFormSchema } from '../../../schemas/report/userPermissionsReport';

import safeJsonParse from '../../../utils/safeJsonParse';
import encodeQueryParams from '../../../utils/encodeQueryParams';

const useGetValuesFromParams = () => {
  const [params, setParams] = useSearchParams({
    roles: '',
    permissions: '',
    user: '',
    statuses: '',
    pageSize: '10',
  });
  const valuesFromParams = useMemo<UserPermissionsFormSchema>(() => {
    const page = params.get('page');
    const roles = params.get('roles');
    const permissions = params.get('permissions');
    const user = params.get('user');
    const statuses = params.get('statuses') === 'true';
    const pageSizeParam = params.get('pageSize');
    const pageIndex = page ? parseInt(page, 10) : 0;
    const pageSize = pageSizeParam ? parseInt(pageSizeParam, 10) : 10;
    const decodedRoles = roles ? decodeURIComponent(roles) : '[]';
    const parsedRoles = safeJsonParse({
      string: decodedRoles,
      defaultValue: [],
    });

    const decodedPermissions = permissions
      ? decodeURIComponent(permissions)
      : '[]';
    const parsedPermissions = safeJsonParse({
      string: decodedPermissions,
      defaultValue: [],
    });
    const decodedUser = user ? decodeURIComponent(user) : '""';
    const parsedUser = safeJsonParse({ string: decodedUser, defaultValue: '' });
    const currentValues = {
      pageIndex,
      pageSize,
      permissions: parsedPermissions,
      roles: parsedRoles,
      statuses,
      user: parsedUser,
    } as unknown as UserPermissionsFormSchema;

    return currentValues;
  }, [params]);

  const updatePageIndex = useCallback(
    (newPage: number) => {
      const newParams = encodeQueryParams({
        ...valuesFromParams,
        page: newPage,
      });
      setParams(newParams);
    },
    [valuesFromParams, setParams]
  );

  const setPageSize = useCallback(
    (newPageSize: number) => {
      const newParams = encodeQueryParams({
        ...valuesFromParams,
        pageSize: newPageSize,
        page: 0,
      });
      setParams(newParams);
    },
    [valuesFromParams, setParams]
  );

  return { valuesFromParams, setParams, updatePageIndex, setPageSize };
};

export default useGetValuesFromParams;
