import { useMemo } from 'react';

import type {
  UserHistoryReportResponse,
  UserPermissionsReportResult,
} from '../../../schemas/report/userPermissionsReport';

import UserRoles, { roles } from '../../../constants/userRoles';

const useMutateDataForRender = ({
  data,
  isDataSuccess,
}: {
  data: UserHistoryReportResponse;
  isDataSuccess: boolean;
}) => {
  const pageData = useMemo(() => {
    let currentPageData = {
      page: 0,
      pageCount: 0,
      rowData: [],
      totalResults: 0,
    };

    if (isDataSuccess) {
      const {
        resultsMetadata: { pageIndex, pageSize, totalResults },
        results,
      } = data;
      const pageCount = Math.ceil(totalResults / pageSize);
      currentPageData = {
        ...currentPageData,
        page: pageIndex,
        pageCount,
      };
      if (results.length) {
        const newRowData = results?.map(
          (item: UserPermissionsReportResult) => ({
            ...item,
            permissions:
              item?.roles?.filter(
                role =>
                  !roles.includes(role as UserRoles) &&
                  role !== UserRoles.BRIDGE_APP
              ) ?? [],
            roles:
              item?.roles?.filter(
                role =>
                  roles.includes(role as UserRoles) &&
                  role !== UserRoles.BRIDGE_APP
              ) ?? [],
          })
        );
        currentPageData = {
          ...currentPageData,
          rowData: newRowData,
        };
      }
    }
    return currentPageData;
  }, [data, isDataSuccess]);

  return pageData;
};

export default useMutateDataForRender;
