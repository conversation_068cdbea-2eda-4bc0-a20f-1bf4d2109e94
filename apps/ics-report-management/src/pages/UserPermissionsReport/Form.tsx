import { memo, useCallback, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import PopupState from 'material-ui-popup-state';
import { Box, Button } from '@mui/material';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import AssessmentOutlinedIcon from '@mui/icons-material/AssessmentOutlined';

import { userPermissionsFormSchema } from '../../schemas/report/userPermissionsReport';
import BaseForm from '../../components/layout/BaseForm';
import BaseTextInput from '../../components/inputs/BaseTextInput';
import CustomAutocomplete from '../../components/inputs/CustomAutocomplete';
import encodeQueryParams from '../../utils/encodeQueryParams';
import saveCSVblob from '../../utils/saveCSVblob';
import useGetFormValues from './hooks/useGetFormValues';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import usePostUserPermissionsReportCSV from '../../services/usePostUserPermissionsReportCSV';
import { roles as roleOptions } from '../../constants/userRoles';
import PermissionsAutoComplete from '../../components/inputs/PermissionsAutoComplete';
import CircularProgressWithGradient from '../../components/inputs/CircularProgressWithGradient';

const UserPermissionsReport = ({
  isLoading = false,
  onRunReportClick,
}: {
  isLoading?: boolean;
  onRunReportClick: () => void;
}) => {
  const { valuesFromParams, setParams } = useGetValuesFromParams();

  const { control, getValues, handleSubmit, trigger } = useForm({
    resolver: zodResolver(userPermissionsFormSchema),
  });

  const getFormValues = useGetFormValues({
    getValues,
    trigger,
  });

  const {
    data: dataUserPermissionsReport,
    mutateAsync: postUserPermissionsReportCSV,
    isPending: isPendingPermissionsReportCSV,
    isSuccess: isSuccessPermissionsReportCSV,
  } = usePostUserPermissionsReportCSV(
    {},
    {
      mutationKey: ['userPermissionsReportCSV'],
    }
  );

  const handleSubmitAsReport = useCallback(
    data => {
      onRunReportClick();
      const newParams = encodeQueryParams(data);
      setParams(newParams);
    },
    [setParams]
  );

  const handleSubmitAsCSV = useCallback(async () => {
    const values = await getFormValues();
    if (!values) {
      return;
    }
    postUserPermissionsReportCSV(values);
  }, [getFormValues, postUserPermissionsReportCSV]);

  useEffect(() => {
    if (dataUserPermissionsReport && isSuccessPermissionsReportCSV) {
      saveCSVblob({
        file: dataUserPermissionsReport,
        fileName: 'user-permissions-report.csv',
      });
    }
  }, [dataUserPermissionsReport, isSuccessPermissionsReportCSV]);

  return (
    <BaseForm onSubmit={handleSubmit(handleSubmitAsReport)}>
      <Controller
        control={control}
        defaultValue={valuesFromParams.user}
        name='user'
        render={({ field }) => <BaseTextInput {...field} label='User' />}
      />
      <Controller
        control={control}
        defaultValue={valuesFromParams.roles}
        name='roles'
        render={({ field }) => (
          <CustomAutocomplete {...field} label='Roles' options={roleOptions} />
        )}
      />
      <Controller
        control={control}
        defaultValue={valuesFromParams.permissions}
        name='permissions'
        render={({ field }) => (
          <PermissionsAutoComplete {...field} label='permissions' />
        )}
      />
      <FormControlLabel
        label='Include Inactive Users'
        control={
          <Controller
            control={control}
            defaultValue={valuesFromParams.statuses}
            name='statuses'
            render={({ field: { value, ref, ...field } }) => (
              <Checkbox {...field} inputRef={ref} checked={value} />
            )}
          />
        }
        sx={{
          margin: 0,
        }}
      />
      <PopupState variant='popover' popupId='actions-popup-menu'>
        {popupState => (
          <Box display='flex' flexDirection='row' justifyContent='flex-end'>
            <Button
              variant='contained'
              onClick={() => {
                popupState.close();
                handleSubmitAsCSV();
              }}
              disabled={isPendingPermissionsReportCSV}
              sx={{ mr: '10px' }}
            >
              {isPendingPermissionsReportCSV ? (
                <Button disabled>
                  <CircularProgressWithGradient size={20} />
                  <Box sx={{ px: '10px' }}>Export CSV</Box>
                </Button>
              ) : (
                <>
                  <FileDownloadOutlinedIcon sx={{ mr: 1 }} />
                  Export CSV
                </>
              )}
            </Button>
            <Button
              variant='contained'
              onClick={() => {
                popupState.close();
                handleSubmit(handleSubmitAsReport)();
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <Button disabled>
                  <CircularProgressWithGradient size={20} />
                  <Box sx={{ px: '10px' }}>Run Report</Box>
                </Button>
              ) : (
                <>
                  <AssessmentOutlinedIcon sx={{ mr: 1 }} />
                  Run Report
                </>
              )}
            </Button>
          </Box>
        )}
      </PopupState>
    </BaseForm>
  );
};

export default memo(
  UserPermissionsReport,
  (prevProps, nextProps) => prevProps.isLoading === nextProps.isLoading
);
