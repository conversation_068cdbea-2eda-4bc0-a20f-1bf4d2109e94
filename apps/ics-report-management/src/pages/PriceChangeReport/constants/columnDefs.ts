import type { ColDef } from '@ag-grid-community/core';

import { PriceChangeReportResult } from '../../../schemas/report/priceChangeReport';
import '../style.css';

let header;

const styleDataWithDir = {
  height: '100%',
  display: 'flex',
  justifyContent: 'right',
  alignItems: 'center ',
  direction: 'rtl',
};

const styleDataWithoutDir = {
  height: '100%',
  display: 'flex',
  alignItems: 'center',
};

const columnDefs: ColDef<PriceChangeReportResult>[] = [
  {
    field: 'siteName',
    headerName: (header = 'Site Name'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'productName',
    headerName: (header = 'Product Name'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'previousPrice',
    headerName: (header = 'Previous Price'),
    headerTooltip: header,
    type: 'rightAligned',
    headerClass: 'right-align',
    cellStyle: styleDataWithDir,
  },

  {
    field: 'price',
    headerName: (header = 'Price'),
    headerTooltip: header,
    type: 'rightAligned',
    headerClass: 'right-align',
    cellStyle: styleDataWithDir,
  },

  {
    field: 'deviceId',
    headerName: (header = 'Device'),
    headerTooltip: header,
    type: 'rightAligned',
    headerClass: 'right-align',
    cellStyle: styleDataWithDir,
  },

  {
    field: 'status',
    flex: 1,
    headerName: (header = 'Status'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'errorCode',
    headerName: (header = 'Error Code'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'errorText',
    headerName: (header = 'Error Text'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'userName',
    headerName: (header = 'Username'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'receivedDate',
    headerName: (header = 'Received (Site Time)'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'scheduledDate',
    headerName: (header = 'Scheduled (Site Time)'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'appliedDate',
    headerName: (header = 'Applied (Site Time)'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'jobId',
    headerName: (header = 'ICS Job ID'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },

  {
    field: 'fpsId',
    headerName: (header = 'FPS Job ID'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },
  {
    field: 'authorizedby',
    headerName: (header = 'Authorized by'),
    headerTooltip: header,
    cellStyle: styleDataWithoutDir,
  },
];

export default columnDefs;
