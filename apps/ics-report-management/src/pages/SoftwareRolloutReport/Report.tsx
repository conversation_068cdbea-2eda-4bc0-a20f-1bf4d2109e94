import { memo, useCallback, Suspense, useState, useEffect } from 'react';
import { Box, IconButton, MenuItem, Select, TextField } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';

import ReportContainer from '../../components/layout/ReportContainer';
import AGgridContainer from '../../components/layout/AGgridContainer';
import PaginationContainer from '../../components/layout/PaginationContainer';
import Form from './Form';
import lazyWithPreload from '../../utils/lazyWithPreload';
import useFormSubmit from './hooks/useFormSubmit';
import useMutateDataForRender from './hooks/useMutateDataForRender';
import encodeQueryParams from '../../utils/encodeQueryParams';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import { PER_PAGE_SIZE } from './constants/pageContent';
import { usePageInput } from '../../components/userPageInput';

const Grid = lazyWithPreload(() => import('./Grid'));

Grid.preload();

const Report = () => {
  const { valuesFromParams, setParams } = useGetValuesFromParams();
  const { isSuccessSoftwareRolloutReport, dataSoftwareRolloutReport } =
    useFormSubmit();

  const { page, totalResults, rowData } = useMutateDataForRender({
    data: dataSoftwareRolloutReport,
    isDataSuccess: isSuccessSoftwareRolloutReport,
  });
  const [pageSize, setPageSize] = useState(PER_PAGE_SIZE);

  const totalPages = Math.ceil(totalResults / pageSize);

  const cPage = page + 1;
  const startWith = (cPage - 1) * pageSize + 1;
  const endWith = Math.min((cPage - 1) * pageSize + pageSize, totalResults);

  const setPage = useCallback(
    (_, value: number) => {
      const newPage = value - 1;
      if (newPage === page) {
        return;
      }
      const { pageIndex, ...currentParams } = {
        ...valuesFromParams,
        page: newPage,
      };
      const newParams = encodeQueryParams(currentParams);
      setParams(newParams);
    },
    [page, setParams, valuesFromParams]
  );

  const handlePageSizeChange = e => {
    const newPageSize = Number(e.target.value);
    setPageSize(newPageSize);
    const { pageIndex, ...currentParams } = {
      ...valuesFromParams,
      pageSize: newPageSize,
      page: 0,
    };
    const newParams = encodeQueryParams(currentParams);
    setParams(newParams);
  };

  // Sync pageSize with params whenever they change
  useEffect(() => {
    if (valuesFromParams.pageSize && valuesFromParams.pageSize !== pageSize) {
      setPageSize(valuesFromParams.pageSize);
    }
  }, [valuesFromParams.pageSize]);

  // Use the new usePageInput hook
  const {
    inputPage,
    handlePageInputChange,
    handlePageInputBlur,
    handlePageInputKeyDown,
  } = usePageInput({
    currentPage: cPage,
    totalPages,
    updatePageIndex: (newPageIndex: number) => {
      const { pageIndex, ...currentParams } = {
        ...valuesFromParams,
        page: newPageIndex,
      };
      const newParams = encodeQueryParams(currentParams);
      setParams(newParams);
    },
  });

  return (
    <ReportContainer>
      <Form />
      {isSuccessSoftwareRolloutReport && (
        <>
          <AGgridContainer>
            <Suspense fallback={<div />}>
              <Grid results={rowData} />
            </Suspense>
          </AGgridContainer>
          {Boolean(rowData?.length) && (
            <PaginationContainer>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  p: '5px 30px',
                }}
              >
                {/* Previous Page Button */}
                <IconButton
                  onClick={() => setPage(null, cPage - 1)}
                  disabled={cPage === 1}
                >
                  <ChevronLeft />
                </IconButton>

                {/* Page Number Input */}
                <TextField
                  value={inputPage}
                  size='small'
                  onChange={handlePageInputChange}
                  onBlur={handlePageInputBlur}
                  onKeyDown={handlePageInputKeyDown}
                  sx={{
                    width: 50,
                    '& .MuiOutlinedInput-input': {
                      padding: '4px 14px',
                    },
                  }}
                />
                <span> / {totalPages}</span>

                {/* Next Page Button */}
                <IconButton
                  onClick={() => setPage(null, cPage + 1)}
                  disabled={cPage === totalPages}
                >
                  <ChevronRight />
                </IconButton>

                {/* Page Size Dropdown */}
                <Select
                  value={pageSize}
                  onChange={handlePageSizeChange}
                  size='small'
                  sx={{
                    '& .MuiOutlinedInput-input': {
                      padding: '4px 8px',
                    },
                  }}
                >
                  {[10, 25, 50, 100].map(size => (
                    <MenuItem key={size} value={size}>
                      {size}
                    </MenuItem>
                  ))}
                </Select>

                {/* Displaying Records Info */}
                <span>{`${startWith} - ${endWith} of ${totalResults} Records`}</span>
              </Box>
            </PaginationContainer>
          )}
        </>
      )}
    </ReportContainer>
  );
};

export default memo(Report);
