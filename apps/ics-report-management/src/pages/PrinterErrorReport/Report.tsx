import { memo, useCallback, Suspense } from 'react';
import { Box, IconButton, MenuItem, Select, TextField } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';

import ReportContainer from '../../components/layout/ReportContainer';
import AGgridContainer from '../../components/layout/AGgridContainer';
import PaginationContainer from '../../components/layout/PaginationContainer';
import Form from './Form';
import lazyWithPreload from '../../utils/lazyWithPreload';
import useFormSubmit from './hooks/useFormSubmit';
import useMutateDataForRender from './hooks/useMutateDataForRender';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import { usePageInput } from '../../components/userPageInput';

const Grid = lazyWithPreload(() => import('./Grid'));

Grid.preload();

const Report = () => {
  const { valuesFromParams, updatePageIndex, setPageSize } =
    useGetValuesFromParams();

  const { isSuccessPrinterErrorReport, dataPrinterErrorReport } =
    useFormSubmit();

  const { page, totalResults, rowData } = useMutateDataForRender({
    data: dataPrinterErrorReport,
    isDataSuccess: isSuccessPrinterErrorReport,
  });

  const currentPage = page + 1;
  const totalPages = Math.ceil(totalResults / valuesFromParams.pageSize);
  const startRecord = (currentPage - 1) * valuesFromParams.pageSize + 1;
  const endRecord = Math.min(
    currentPage * valuesFromParams.pageSize,
    totalResults
  );

  // Use the same usePageInput hook logic here
  const {
    inputPage,
    handlePageInputChange,
    handlePageInputBlur,
    handlePageInputKeyDown,
  } = usePageInput({
    currentPage,
    totalPages,
    updatePageIndex,
  });

  const handlePageSizeChange = useCallback(
    event => {
      const newPageSize = event.target.value;
      setPageSize(newPageSize); // Use the setPageSize function from the hook
    },
    [setPageSize]
  );

  return (
    <ReportContainer>
      <Form />
      {isSuccessPrinterErrorReport && (
        <>
          <AGgridContainer>
            <Suspense fallback={<div />}>
              <Grid results={rowData} />
            </Suspense>
          </AGgridContainer>
          {rowData?.length > 0 && (
            <PaginationContainer>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  p: '5px 30px',
                }}
              >
                {/* Previous Page Button */}
                <IconButton
                  onClick={() => {
                    const newPage = Math.max(0, page - 1);
                    updatePageIndex(newPage);
                  }}
                  disabled={page === 0}
                >
                  <ChevronLeft />
                </IconButton>

                {/* Page Number Input */}
                <TextField
                  value={inputPage}
                  size='small'
                  sx={{
                    width: 50,
                    '& .MuiOutlinedInput-input': {
                      padding: '4px 14px',
                    },
                  }}
                  onChange={handlePageInputChange}
                  onBlur={handlePageInputBlur}
                  onKeyDown={handlePageInputKeyDown}
                />
                <span> / {totalPages}</span>

                {/* Next Page Button */}
                <IconButton
                  onClick={() => {
                    const newPage = Math.min(totalPages - 1, page + 1);
                    updatePageIndex(newPage);
                  }}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight />
                </IconButton>

                {/* Page Size Dropdown */}
                <Select
                  value={valuesFromParams.pageSize}
                  onChange={handlePageSizeChange}
                  size='small'
                  sx={{
                    '& .MuiOutlinedInput-input': {
                      padding: '4px 8px',
                    },
                  }}
                >
                  {[10, 25, 50, 100].map(size => (
                    <MenuItem key={size} value={size}>
                      {size}
                    </MenuItem>
                  ))}
                </Select>

                {/* Displaying Records Info */}
                <span>{`${startRecord} - ${endRecord} of ${totalResults} Records`}</span>
              </Box>
            </PaginationContainer>
          )}
        </>
      )}
    </ReportContainer>
  );
};

export default memo(Report);
