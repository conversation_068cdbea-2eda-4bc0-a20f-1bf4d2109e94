( function() {
  'use strict';

  angular
    .module( 'commonModule' )
    .component( 'promptBuilder2', {
      controller: PromptBuilderCtrl,
      controllerAs: 'vm',
      bindings: {
        promptSet: '<',
        deviceTypes: '<',
        close: '&',
        dismiss: '&',
        fonts: '<'
      },
      templateUrl: 'moduleMedia/components/promptBuilder2/prompt-builder.component.html'
    } );

  angular
    .module( 'mediaModule' )
    .constant( 'promptActions', { CREATE: 'create', EDIT: 'edit', DELETE: 'delete', CLONE: 'clone' } )
    .controller( 'SoftKeyModalCtrl', SoftKeyModalCtrl )
    .controller( 'DaypartModalCtrl', DaypartModalCtrl )
    .controller( 'ViewAsODMLCtrl', ViewAsODMLCtrl )
    .controller( 'PromptModalCtrl', PromptModalCtrl )
    .controller( 'PromptLanguageEditModal', PromptLanguageEditModal )
    .controller('MyController', function ($scope) {
      $scope.screenOption = 'main';
      $scope.auxResolutions = false;
      $scope.inputValue = '';
      $scope.updateInput = function () {
        if ($scope.screenOption === 'aux') {
          $scope.inputValue = 'aux-';
        } else {
          $scope.inputValue = '';
        }
      };
    });

// eslint-disable-next-line max-len
function PromptBuilderCtrl( $scope, $filter, modalPrompt, $uibModal, $stateParams, $state, $window, $document, ngToast, Fullscreen, mediaService, authService, icsServiceApiConfig, devicesService, utilsService, promptActions, colorPickerConfig,$rootScope ) {
    /*jshint validthis:true*/
    const vm = this;

    // Generate a simple short id, to keep track of elements (maybe we already have code like this somewhere)
    const generate = function( alphabet ) {
      const chars = alphabet || '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const length = 8;
      let rtn = '';
      // eslint-disable-next-line no-plusplus
      for ( let i = 0; i < length; i++ ) {
        rtn += chars.charAt( Math.floor( Math.random() * chars.length ) );
      }
      return rtn;
    };

    mediaService.getDayParts().then( function( results ) {
      vm.dayparts = results;
    } );

    vm.selectedElement = null;
    vm.showMasterProperties = true;
    vm.initPromptSet = true;
    vm.isGridShown = false;
    vm.auxResolutions = false;

    if ( $stateParams.showMeta === 'false' ) {
      vm.isRightContracted = true;
    } else {
      vm.isRightContracted = false;
    }

    vm.$onInit = onInit;
    vm.goFullscreen = goFullscreen;
    vm.updateFont = updateFont;
    vm.updateFontColor = updateFontColor;
    vm.handleCreateElement = handleCreateElement;
    vm.handleMoveTo = handleMoveTo;
    vm.handleRemoveElement = handleRemoveElement;
    vm.handleDeleteException = handleDeleteException;
    vm.handleSelectElement = handleSelectElement;
    vm.handleSelectPrompt = handleSelectPrompt;
    vm.handleUpdateElement = handleUpdateElement;
    vm.handleSaveElement = handleSaveElement;
    vm.handleAddPrompt = handleAddPrompt;
    vm.handlePromptLanguageEdit = handlePromptLanguageEdit;
    vm.handleSavePromptSet = handleSavePromptSet;
    vm.handleSavePrompts = handleSavePrompts;
    vm.changeLinkedPrompt = handleChangeLinkedPrompt;
    vm.handleResetElement = handleResetElement;
    vm.handleSoftKey = handleSoftKey;
    vm.addDayPart = addDayPart;
    vm.openAssetModal = openAssetModal;
    vm.updatePromptSetName = updatePromptSetName;
    vm.approveRejectPromptSet = approveRejectPromptSet;
    vm.viewAsODML = viewAsODML;
    vm.toggleMetadataPanel = toggleMetadataPanel;
    vm.markStateAsChanged = markStateAsChanged;
    vm.markTouchMaskAsChanged = markTouchMaskAsChanged;
    vm.handleTouchMaskSave = handleTouchMaskSave;
    vm.isSequoiaDevice = devicesService.isSequoiaDevice;
    vm.enableSoftkey = enableSoftkey;
    vm.getEditDeleteOptions = getEditDeleteOptions;
    vm.isDefaultFontSettingsAvail = false;
    vm.companyLanguages = [];
    vm.promptsetLanguages = [];
    vm.languageKeysSet = [];
    vm.groupedAssignments = [];
    vm.linkedAuxPrompt = {};
    vm.standardAuxPrompt = [];
    vm.selectedPrimaryAssignment = [];
    vm.selectedAuxAssignment = [];
    vm.profiles1= JSON.parse(localStorage.getItem('promptSetProfiles'));
    vm.enablePlaylistButton = true;

    vm.promptSetTemplates = [];
    vm.ediPromptSet = handleEditPromptSet;
    vm.deletePromptSet = handleDeletePromptSet;
    vm.clonePrompt = handleClonePrompt;
    vm.MoreActions = ['Edit prompt', 'Delete prompt','Duplicate prompt'];
    vm.DefaultActions = ['Edit prompt', 'Delete prompt', 'Duplicate prompt'];
    vm.isEdit = false;
    vm.isClone = false;
    vm.defaultPromptFlag = true;
    vm.selectSettingState = {};

    vm.actions = {
      'Edit prompt' : vm.ediPromptSet,
      'Delete prompt' : vm.deletePromptSet,
      'Duplicate prompt' : vm.clonePrompt
    }

    function handleEditPromptSet (prompt) {
      vm.isEdit = true; 
      vm.isClone = false;
      const promptState= prompt;
      openAddPromptModal(promptState);
    }

    function handleClonePrompt (prompt) {
      vm.isClone = true; 
      vm.isEdit = false;
      const promptState= prompt;
      openAddPromptModal(promptState);
    }

    function handleEditCancel () {
      vm.isEdit = false;
    }
    function handleCloneCancel () {
      vm.isClone = false;
    }

    function handleDeletePromptSet (prompt) {
      prompt.isDelete = true;
      let linkedPrompts = 0;
      if(prompt.screenOption === 'aux'){
        const auxId = prompt.assignments[0].id;
        const auxPrompts = vm.ClonedMainPrompts.filter(x=> x.auxPrompt=== auxId);
        linkedPrompts = auxPrompts.length;
      }
      const mainMsg = '<strong class="delete-prompt-name">' + prompt.code + '</strong> will be permanently deleted.';
      const linkingMsg = `</br> <strong> ${linkedPrompts} </strong> prompts will become unlinked`
      const message = linkedPrompts > 0 ? mainMsg + linkingMsg : mainMsg;
        modalPrompt( {
          title: 'Are you sure you want to delete?',
          textContent: message,
          windowClass: 'modal-prompt-delete',
          buttons: [
            { label: 'Cancel', cancel: true, class: 'btn-link btn-link-default' },
            { label: 'Delete', primary: true, class: 'btn-gray text-danger btn-wide' }
          ]
        } ).then( function() {
          mediaService.deletePromptSetPrompt( prompt.assignments[0].id )
            .then( function() {
              mediaService.getPromptSet( vm.promptSet.id ).then( ( res ) => {
                vm.selectedState = null;
                vm.assignment = null;
                resetPromptEditView( res );
              } );
              ngToast.create( 'Prompt deleted' );
            } );
        } );
      
    }

    const createDefaultFontSettings = () => {
      const defaultFontSettings = [];
      _.forEach( vm.promptsetLanguages, ( lang ) => {
        const fallbackDefault = {};
        if ( devicesService.isSequoiaDevice( vm.promptSet.deviceType ) ) {
          fallbackDefault.size = 48;
          fallbackDefault.face = {
            fontId: 'Liberation Sans',
            name: 'Liberation Sans',
            family: 'Liberation Sans',
            type: 'DEFAULT',
            supportedDevices: [ 'G7-100', 'G6-300', 'G6-400', 'G6-500' ],
            active: true
          };
        } else if ( vm.promptSet.deviceType === 'G6-200' ) {
            fallbackDefault.size = 24;
            fallbackDefault.face = {
            fontId: 'FreeSans',
            name: 'FreeSans',
            family: 'FreeSans',
            type: 'DEFAULT',
            supportedDevices: [ 'G6-200' ],
            active: true
          };
        }

        if ( lang.promptSetLanguageSupport.type === null ) {
          // eslint-disable-next-line no-param-reassign
          lang.promptSetLanguageSupport.type = fallbackDefault.face.name;
        }

        if ( lang.promptSetLanguageSupport.size === null ) {
          // eslint-disable-next-line no-param-reassign
          lang.promptSetLanguageSupport.size = fallbackDefault.size;
        }
        const font = _.find( vm.fonts, { name: lang.promptSetLanguageSupport.type } );
        const defaultItem = {
          isoCode: lang.isoCode,
          fontFace: font,
          fontSize: lang.promptSetLanguageSupport.size
        };
        defaultFontSettings.push( defaultItem );
      } );
      return _.groupBy( defaultFontSettings, 'isoCode' );
    };

    const getDefaults = () => {
      let defaults = {};
      if ( vm.companyLanguages.length === 0 && vm.languageKeysSet.length === 0 ) {
        defaults = getDefaultFontSettings();
      } else {
        defaults = createDefaultFontSettings();
      }
      return defaults;
    };

    // assets used in prompts (values )
    const promptSetAssets = [];

    let viewAsODMLTimeOut = null;

    const resetPromptEditView = async ( updatedPromptSet ) => {
      if ( !vm.promptSet ) return;

      const defaultStateIdx = 0;
      const defaultPromptIdx = 0;

      // Map and override elements in each assignment (Only if not initial load)
      if ( updatedPromptSet ) {
        const prevStates = vm.promptSet.states;
        const prevPrompts = _.flatMap( vm.promptSet.states, function( state ) {
            return state.assignments;
          } );

          // eslint-disable-next-line no-restricted-syntax
          for ( const [si, state] of updatedPromptSet.states.entries() ) {
            const existingState = prevStates.find( ( s ) => s.code === state.code );
            // eslint-disable-next-line no-restricted-syntax
            for ( const [ai, prompt] of state.assignments.entries( prompt ) ) {
                const existingPrompt = prevPrompts.find( ( p ) => p.id === prompt.id );
                // Back-Override existing element
                const parentId = prompt.parentId;
                if ( existingPrompt ) {
                    const mergedObject = {
                        ...prompt,
                        ...existingPrompt,
                        ...{ promptSetLanguageId: prompt.promptSetLanguageId },
                        ...{
                            elements: existingPrompt.elements.map( ( elem ) => ( {
                                    ...elem,
                                    stateId: parentId
                                } ) )
                        }
                    };
                    state.assignments[ ai ] = mergedObject;

                    // Update Selected Prompt
                    if ( vm.assignment && vm.assignment.id === prompt.id ) {
                        vm.assignment = state.assignments[ ai ];
                    }
                } else {
                    // This is for the new Prompts to have stateId (for UI)
                    const mergedObject = {
                        ...prompt,
                        ...{
                            elements: prompt.elements.map( ( elem ) => ( {
                                    ...elem,
                                    stateId: parentId
                                } ) )
                        }
                    };
                    state.assignments[ ai ] = mergedObject;
                }
            }

            // Replace IsChanged State
            // eslint-disable-next-line no-param-reassign
            updatedPromptSet.states[ si ] = {
                ...state,
                ...( existingState ? { isChanged: existingState.isChanged } : null )
            };

            // Update Selected State
            if ( vm.selectedState && vm.selectedState.code === state.code ) {
                vm.selectedState = updatedPromptSet.states[ si ];
            }
          }
          vm.promptSet = updatedPromptSet;
      }

      if ( vm.companyLanguages ) {
        getPromptsetLanguages( vm.promptSet );
      }

      // Sort Prompts by Language if Multi Language
      if ( vm.promptSet.lang && Object.values( vm.promptSet.lang ).length > 1 ) {
        // Get Default Prompt-Set Language
        const promptSetDefaultLanguage = Object.values( vm.promptSet.lang ).find( ( lang ) => lang.promptSetLanguageSupport.default === true );
        const defaultLanguageId = promptSetDefaultLanguage.languageSupportId;

        // Sort Prompts(assignments) by Language (Default and ISO Code)
        vm.promptSet.states = vm.promptSet.states.map( ( state ) => {
            // eslint-disable-next-line no-param-reassign
            state.assignments = _.sortBy( state.assignments, [
                function( o ) { return o.promptSetLanguageId !== defaultLanguageId; },
                function( o ) {
                    const langObject = Object.values( vm.promptSet.lang ).find( ( lang ) => lang.languageSupportId === o.promptSetLanguageId );
                    return langObject.isoCode;
                }
            ] );

            return state;
        } );
      }

      _.forEach( vm.promptSet.states, function( prompt ) {
        _.forEach( prompt.assignments, function( assignment ) {
          assignment.flatElements = flattenElements( assignment.elements ); // eslint-disable-line no-param-reassign
          addAssets( assignment.flatElements );
        } );
      } );

      vm.assignment = vm.assignment || vm.promptSet.states[ defaultStateIdx ].assignments[ defaultPromptIdx ];
      vm.assignment.flatElements = flattenElements( vm.assignment.elements );
      vm.selectedState = vm.selectedState || vm.promptSet.states[ defaultStateIdx ];
      mediaService.getSoftKeysByDeviceType( vm.promptSet.deviceType ).then( function( keys ) {
        vm.keys = keys;
        populateSoftKeys( vm.assignment.softkeys );
      } );
      vm.currentDeviceType = _.find( vm.deviceTypes, { id: vm.promptSet.deviceType } );

      vm.fontColorPickerOptions = {
        ...colorPickerConfig,
        defaultColor: vm.promptSet.fontColor
      };

      vm.estimatedSize = getTotalAssetSize( promptSetAssets );
      vm.defaultFontSettings = getDefaults();
      vm.selectedState.showAssignments = true;
      handleSelectElement( null, vm.assignment, vm.selectedState );
      flatenAndGroupAssignments( vm.promptSet.states );
      await mediaService.getPromptTemplateStates(vm.promptSet.promptTemplateId).then(function(result){
        vm.promptSetTemplates = result;
        handleSettingIcon();
      })
      getPromptSets();
    };

    function getPromptSets () {
      if (vm.auxResolutions) {
        vm.mainPrompts = vm.promptSet.states.filter(
          x => x.screenOption === 'main'
        );
        vm.auxPrompts = vm.promptSet.states.filter(
          x => x.screenOption === 'aux'
        );
        const defaultValue = { id: null, code: 'None' };
        vm.auxPromptsChoices = [defaultValue, ...vm.auxPrompts];
        vm.standardAuxPrompt = vm.auxPrompts.filter(x => x.promptType === "standard");
        vm.standardAuxPromptChoices = [defaultValue, ...vm.standardAuxPrompt];
        vm.mainPrompts.map(x =>
        {
          if(vm.auxPrompts?.length >0){
            for(let a of vm.auxPrompts){
              for(let b of a.assignments){
                if(b.id === x.auxPrompt){
                  vm.linkedAuxPrompt[x.id] = a;
                  return;
                }
                else vm.linkedAuxPrompt[x.id] = vm.auxPromptsChoices[0];
              }
            }
          }
          else 
          vm.linkedAuxPrompt[x.id] = vm.auxPromptsChoices[0];
        }
        );
      } else vm.mainPrompts = vm.promptSet.states;
      vm.ClonedMainPrompts = JSON.parse(JSON.stringify(vm.mainPrompts));
      $rootScope.promptSet = vm.promptSet;
    }

    function onInit() {
      vm.isIE = angular.element( document.querySelector( 'html' ) ).hasClass( 'ie' );

      mediaService.getCompanyLanguages( vm.promptSet.company ).then( function( companyLanguages ) {
        vm.companyLanguages = companyLanguages;
        getPromptsetLanguages( vm.promptSet );
      } );
      getifAuxResolution();
         
         authService.self().then( function( user ) {
        if ( $stateParams.readOnly || ( vm.promptSet && vm.promptSet.status !== 'DRAFT' ) ) {
          // disable controls by default
          vm.readOnlyMode = true;
          // show approve / reject buttons
          if ( vm.promptSet && vm.promptSet.isApprover ) {
            // check if a user has already approved and if that is not the current user
            if ( ( vm.promptSet.firstApprover && vm.promptSet.firstApprover.id !== user.id ) || !vm.promptSet.firstApprover ) {
              vm.approvalEnabled = true;
            } else if ( vm.promptSet.firstApprover && vm.promptSet.firstApprover.id === user.id ) {
              vm.approvalEnabled = true;
              vm.approved = true;
            }
          }
        } else if ( user.roles.indexOf( 'MEDIA_DESIGNER' ) > -1 ) {
          // designer can edit prompt sets
          vm.readOnlyMode = false;
        } else {
          // disable controls for those who do not have MEDIA_DESIGNER role
          vm.readOnlyMode = true;
        }

        vm.isDesigner = user.roles.indexOf( 'MEDIA_DESIGNER' ) > -1;
      } );

      if ( !vm.promptSet ) return;

      resetPromptEditView();
    }

    $document.on( 'click', function() {
      $scope.$apply( function() {
        vm.canEditBG = false;
      } );
    } );

    function  getifAuxResolution(){  
      if(vm.profiles1){
        const selectedProfile= vm.profiles1.filter(x=> x.id=== vm.promptSet.promptSetProfileName)
        vm.currentProfile = selectedProfile[0];
        if(selectedProfile && selectedProfile[0].auxResolutions){ 
          vm.auxResolutions = true;
        }
      }
    }
    
    function handleSettingIcon(){
      vm.promptSet.states = vm.promptSet.states.map(x => ({
        ...x,
        isEditable: !vm.promptSetTemplates.some(y => y.id === x.id)
      }));
    }

    function getPromptsetLanguages( promptSet ) {
      const promptsetLanguages = Object.values( promptSet.lang );
      if ( vm.companyLanguages.length >= 1 && promptsetLanguages.length === 0 ) {
        const companyDefault = _.find( vm.companyLanguages, ( item ) => item.default === true );
        if ( companyDefault ) {
          vm.promptSet.lang[ companyDefault.isoCode ] = {
            languageSupportId: companyDefault.languageSupportId,
            language: companyDefault.language,
            isoCode: companyDefault.isoCode,
            promptSetLanguageSupport: {
              default: companyDefault.default,
              deleted: companyDefault.deleted
            }
          };
          vm.promptsetLanguages = Object.values( vm.promptSet.lang );
        }
      } else {
        vm.promptsetLanguages = promptsetLanguages;
      }
      vm.languageKeysSet = Object.keys( vm.promptSet.lang );
      vm.defaultFontSettings = getDefaults();
    }

    // Get font settings for a promptset (temporary @see REQ-3803)
    function getDefaultFontSettings() {
      // Get Default Font Settings (Local Storage)
      try {
        const fontSettings = JSON.parse( localStorage.getItem( 'defaultFontSettings' ) );
        const defaultFontSettings = fontSettings[ vm.promptSet.id ] || {};
        vm.isDefaultFontSettingsAvail = !angular.equals( defaultFontSettings, {} );
        return defaultFontSettings;
      } catch ( err ) {
        // Reset the default settings
        localStorage.setItem( 'defaultFontSettings', JSON.stringify( {} ) );
        vm.isDefaultFontSettingsAvail = false;
        return {};
      }
    }

    // Set font settings for a promptset (temporary @see REQ-3803)
    function setDefaultFontSettings( newSettings ) {
        const fontSettings = JSON.parse( localStorage.getItem( 'defaultFontSettings' ) ) || {};
        fontSettings[ vm.promptSet.id ] = newSettings;
        localStorage.setItem( 'defaultFontSettings', JSON.stringify( fontSettings ) );
        vm.defaultFontSettings = newSettings;
        vm.isDefaultFontSettingsAvail = !angular.equals( newSettings, {} );
    }

    function enableSoftkey() {
        return ['G6-200', 'G7-100'].indexOf( vm.promptSet.deviceType ) >= 0;
    }

    function handleChangeLinkedPrompt(primaryItem, auxItem) {
      let selectedAuxAssignment = {};
      const existingPrompt = vm.mainPrompts.find(x=> x.id === primaryItem.id);
      const existingAssignment = existingPrompt.assignments.find(x => x.code === primaryItem.code);
      if(auxItem?.assignments){
        selectedAuxAssignment = auxItem.assignments.find(x => x.code === auxItem.code);
      }
      else selectedAuxAssignment = auxItem;

      if(existingAssignment.auxPrompt === selectedAuxAssignment.id){
        auxItem.code === 'None' ? primaryItem.auxPrompt = auxItem.id : primaryItem.auxPrompt = auxItem.assignments[0].id;
        primaryItem.isChanged = false;
        return;
      }
      else {
        vm.linkedAuxPrompt[primaryItem.id] = auxItem;
        vm.selectedPrimaryAssignment = [...vm.selectedPrimaryAssignment, existingAssignment];
        vm.selectedAuxAssignment = [...vm.selectedAuxAssignment, selectedAuxAssignment];
        auxItem.code === 'None' ? primaryItem.auxPrompt = auxItem.id : primaryItem.auxPrompt = auxItem.assignments[0].id;
        primaryItem.isChanged = true;
      }
    }

    function goFullscreen() {
      vm.isFullscreen = !vm.isFullscreen;
      return Fullscreen.isEnabled() ? Fullscreen.cancel() : Fullscreen.all();
    }

    function openAssetModal() {
      const modalInstance = $uibModal.open( {
        component: 'selectMediaModal',
        size: 'lg',
        resolve: {
          filters: function() {
            return _.merge( false, { type: 'IMAGE' } );
          }
        }
      } );
      modalInstance.result
        .then( function( result ) {
          if ( !result ) return;
          vm.selectedBackground.color = null;
          vm.selectedBackground.url = icsServiceApiConfig.baseUrl + '/v1/media/assets/' + result.id + '/source';
          vm.promptSet.globalBG = {
            value: result.id,
            type: 'image'
          };
        } )
        .catch( function () {} );
    }

    $scope.$watch( 'vm.promptSet.bg', function( bg, oldBG ) {
      if ( !bg || !oldBG || ( bg === oldBG ) ) return;

      const type = bg.length === 6 ? 'color' : 'image';

      mediaService.updateBackground( vm.promptSet.id, bg )
        .then( function( result ) {
          _.forEach( vm.promptSet.states, function( state ) {
            _.forEach( state.assignments, function( assignment ) {
              const promptBG = _.find( assignment.elements, { type: 'bg' } );
              if ( promptBG && !promptBG.lock ) {
                promptBG.bgType = type;
                promptBG.value = bg;
                assignment.flatElements = flattenElements( assignment.elements ); // eslint-disable-line no-param-reassign
              }
            } );
          } );
          ngToast.create( 'Default background updated' );
          vm.promptSet.version = result.version;
          vm.promptSet.modifiedBy = result.modifiedBy;
          vm.promptSet.modified = result.modified;
        } );
    } );

    function updateFont( ) {
      const that = this;

      const fontSettingModalInstance = $uibModal.open( {
          templateUrl: 'moduleMedia/components/promptBuilder2/modals/font-settings.modal.html',
          size: 'md',
          controllerAs: 'vm',
        controller: function( $uibModalInstance ) {
              // eslint-disable-next-line no-shadow
              const vm = this;

              vm.fonts = that.fonts;
              vm.controls = ['size', 'font'];
              vm.deviceType = { id: that.promptSet.deviceType };
              vm.defaultFontSettings = Object.assign( {}, that.defaultFontSettings );
              vm.element = {
                  face: vm.defaultFontSettings.fontFace,
                  size: vm.defaultFontSettings.fontSize
              };

              // Reset the selected element to prevent displaying row text like '{}'
              if ( _.isEmpty( vm.selectedFontFace ) ) {
                  vm.selectedFontFace = undefined;
              }

              vm.cancel = cancel;
              vm.done = done;
              vm.handleChange = handleChange;

              function cancel() {
                $uibModalInstance.dismiss();
              }

              function done() {
                setDefaultFontSettings( vm.defaultFontSettings );
                $uibModalInstance.close();
              }

              function handleChange( ) {
                vm.defaultFontSettings.fontFace = vm.element.face;
                vm.defaultFontSettings.fontSize = vm.element.size;
              }
        }
      } );

      fontSettingModalInstance.result.then( function() {
        const fontUpdateModalInstance = $uibModal.open( {
          templateUrl: 'moduleMedia/components/promptBuilder2/modals/update-default-font.modal.html',
          size: 'sm',
          controllerAs: 'vm',
          // eslint-disable-next-line no-shadow
          controller: function( $scope, $uibModalInstance ) {
              // eslint-disable-next-line no-shadow
              const vm = this;

              vm.title = 'Font Settings';
              vm.message = 'Do you also want to update <strong>all existing</strong> text and input assets?';

              vm.yes = yes;
              vm.no = no;

              function yes() {
                  $uibModalInstance.close();
              }

              function no() {
                  $uibModalInstance.dismiss();
              }
          }
        } );

        fontUpdateModalInstance.result.then( function() {
            // Apply to all Prompts
            vm.isFontSettingUpdating = true;

            const settings = that.defaultFontSettings;
            const query = {};

            if ( settings.fontFace ) {
                query.fontFace = settings.fontFace.fontId;
            }

            if ( settings.fontSize ) {
                query.fontSize = settings.fontSize;
            }

            mediaService.updateFont( that.promptSet.id, query, true )
                .then( function( result ) {
                    // eslint-disable-next-line no-restricted-syntax
                    for ( const state of that.promptSet.states ) {
                        // eslint-disable-next-line no-restricted-syntax
                        for ( const assignment of state.assignments ) {
                            // eslint-disable-next-line no-restricted-syntax
                            for ( const element of assignment.elements ) {
                                if ( element.type === 'text' || element.type === 'input' ) {
                                    if ( settings.fontFace ) {
                                        element.face = settings.fontFace;
                                    }
                                    if ( settings.fontSize ) {
                                        element.size = settings.fontSize;
                                    }
                                }
                            }
                            assignment.flatElements = flattenElements( assignment.elements );
                        }
                    }
                    that.promptSet.version = result.version;
                    that.promptSet.modifiedBy = result.modifiedBy;
                    that.promptSet.modified = result.modified;
                    ngToast.create( 'Font Settings updated' );
                    vm.isFontSettingUpdating = false;
                } )
                .catch( function() {
                    vm.isFontSettingUpdating = false;
                } );
        } ).catch( function() {} ); // Angular Legacy Issue, See $qProvider.errorOnUnhandledRejections
      } ).catch( function() {} );
    }

    function updateFontColor( color ) {

        vm.isFontColorUpdating = true;

        const modalInstance = $uibModal.open( {
            templateUrl: 'moduleMedia/components/promptBuilder2/modals/update-default-font.modal.html',
            size: 'sm',
            controllerAs: 'vm',
            // eslint-disable-next-line no-shadow
            controller: function( $scope, $uibModalInstance ) {

                /* jshint validthis:true */
                // eslint-disable-next-line no-shadow
                const vm = this;

                vm.title = 'Default font color';
                vm.message = 'Do you also want to update the color of <strong>all existing</strong> text and input assets?';

                vm.yes = yes;
                vm.no = no;

                function yes() {
                    $uibModalInstance.close();
                }

                function no() {
                    $uibModalInstance.dismiss();
                }

            }
        } );

        modalInstance.result.then( function() {

            mediaService.updateFont( vm.promptSet.id, { fontColor: color }, true )
                .then( function( result ) {
                    // eslint-disable-next-line no-restricted-syntax
                    for ( const state of vm.promptSet.states ) {
                        // eslint-disable-next-line no-restricted-syntax
                        for ( const assignment of state.assignments ) {
                            // eslint-disable-next-line no-restricted-syntax
                            for ( const element of assignment.elements ) {
                                if ( element.type === 'text' || element.type === 'input' ) {
                                    element.color = color;
                                }
                            }
                            assignment.flatElements = flattenElements( assignment.elements );
                        }
                    }
                    vm.promptSet.version = result.version;
                    vm.promptSet.modifiedBy = result.modifiedBy;
                    vm.promptSet.modified = result.modified;
                    ngToast.create( 'Default font color updated' );
                    vm.isFontColorUpdating = false;
                } )
                .catch( function() {
                    vm.isFontColorUpdating = false;
                } );

        }, function() {

            mediaService.updateFont( vm.promptSet.id, { fontColor: color }, false )
                .then( function( result ) {
                    vm.promptSet.version = result.version;
                    vm.promptSet.modifiedBy = result.modifiedBy;
                    vm.promptSet.modified = result.modified;
                    ngToast.create( 'Default font color updated' );
                    vm.isFontColorUpdating = false;
                } )
                .catch( function() {
                    vm.isFontColorUpdating = false;
                } );

        } );

    }

    $window.onbeforeunload = function() { // eslint-disable-line no-param-reassign, consistent-return

      if ( $state.current.name !== 'editPromptSet' ) {
          $window.onbeforeunload = null; // eslint-disable-line no-param-reassign
          return null;
      }

      const token = localStorage.getItem( 'token' );

      const isChanged = _.some( vm.promptSet.states, function( state ) {
        return state.isChanged;
      } );

      if ( token && isChanged ) {
          return true;
      }

    };

    // flatten elements so they're easier to manipulate in the components
    function flattenElements( elements ) {
      const flatElements = [];

      _.forEach( elements, function( element ) {
        if ( element.type === 'touchmask' && element.elements.length > 0 ) {
          flatElements.push( element );
          _.forEach( element.elements, function( area ) {
            flatElements.push( area );
          } );
          return;
        }
        flatElements.push( element );
      } );

      // put flags for layering

      _.forEach( flatElements, function( element, index ) {
        /* eslint-disable no-param-reassign */
        element.canMoveUp = true;
        element.canMoveDown = true;

        // First element is always the background
        if ( index === 0 ) {
          element.canMoveUp = false;
          element.canMoveDown = false;
          return;
        }

        // touchmasks cannot be moved
        if ( element.type === 'touchmask' ) {
          element.canMoveUp = false;
          element.canMoveDown = false;
        }

        // can't move below backgrounds
        // touchmasks will always have their children beneath them
        if ( flatElements[ index - 1 ].type === 'bg' ||
          flatElements[ index - 1 ].type === 'touchmask' ) {
          element.canMoveUp = false;
        }

        // last elements can't move down
        if ( !flatElements[ index + 1 ] ) {
          element.canMoveDown = false;
          // can't overlap touchmasks
          // if your part of a touchmask can't overlap video
        } else if ( flatElements[ index + 1 ].type === 'video' ||
          flatElements[ index + 1 ].type === 'touchmask' ) {
          element.canMoveDown = false;
        }
        /* eslint-enable no-param-reassign */
      } );

      return flatElements;
    }

    function getTotalAssetSize( assets ) {
      return assets.reduce( ( total, asset ) => {
        const size = Number( asset.size );
        if ( !_.isNaN( size ) ) {
          total += size; // eslint-disable-line no-param-reassign
        }

        return total;
      }, 0 );
    }

    function calculateCustomFontAssetSize( ) {
      // Remove All Custom Fonts Asset
      _.remove( promptSetAssets, ( n ) => n.type === 'font' );

      _.forEach( vm.promptSet.states, function( prompt ) {
        _.forEach( prompt.assignments, function( assignment ) {
          assignment.flatElements = flattenElements( assignment.elements ); // eslint-disable-line no-param-reassign
          assignment.flatElements.forEach( function ( element ) {
            if ( ( element.type === 'input' || element.type === 'text' ) && element.face && element.face.type === 'CUSTOM' ) {
              addAsset( { value: element.face.fontId, size: element.face.fileSize, type: 'font' } );
            }
          } );
        } );
      } );

      vm.estimatedSize = getTotalAssetSize( promptSetAssets );
    }

    function bundleSizeWarning( ) {
      if ( vm.estimatedSize >= 524288000 ) {
        modalPrompt( {
          title: 'Prompt Set bundle exceeds the limit',
          textContent: 'The estimated size of the Prompt Set bundle exceeds the limit of ' + $filter( 'fileSize' )( 524288000 ),
          buttons: [
            { label: 'Ok', primary: true, class: 'btn-link btn-wide btn-link-default' }
          ]
        } );
      }
    }

    function handleCreateElement( assignment, element, index, parent ) {
      /* eslint-disable no-param-reassign */
      element.stateId = assignment ? assignment.parentId : null;
      element.id = element.id ? element.id : generate();
      if ( index === undefined || index === null ) {
        // insert at the index before a touchmask, unless it's a video
        index = _.findIndex( assignment.elements, ['type', 'touchmask'] ) - 1;
        if ( index < 0 ) {
          index = assignment.elements.length - 1;
        }
      }

      let positionOffset = 0;
      let parentElement = assignment;
      if ( parent ) {
        parentElement = _.find( assignment.elements, function( elm, idx ) {
          positionOffset = idx + 1;
          return elm.id === parent;
        } );

        if ( !parentElement ) {
          return;
        }

        index -= positionOffset;
      }

      element.parent = parent;
      parentElement.elements = Immutable.List( parentElement.elements ).splice( index + 1, 0, element ).toJS();
      assignment.flatElements = flattenElements( assignment.elements );

      // Update prompt assets size only if element added is an image or a video
      if ( ( element.type === 'image' || element.type === 'video' ) && element.size ) {
        addAsset( { value: element.value, size: element.size, type: element.type } );
        vm.estimatedSize = getTotalAssetSize( promptSetAssets );
      }

      // marking the state as changed
      if ( element.type !== 'area' ) {
          const state = _.find( vm.promptSet.states, { id: assignment.parentId } );
          state.isChanged = true;
      } else {
          assignment.hasUnsavedElement = true;
          markTouchMaskAsChanged( vm.assignment );
      }
      /* eslint-disable no-param-reassign */
    }

    function handleMoveTo( from, to ) {
      const state = _.find( vm.promptSet.states, { id: vm.assignment.parentId } );
      state.isChanged = true;
      const element = vm.assignment.elements[ from ];
      vm.assignment.elements.splice( from, 1 );
      vm.assignment.elements.splice( to, 0, element );
      vm.assignment.flatElements = flattenElements( vm.assignment.elements );
    }

    function handleRemoveElement( assignment, element ) {
      let isTouchArea = false;
      let touchMask;
      let index;

      // get index of element from list
      index = _.findIndex( assignment.elements, { id: element.id } );

      if ( index < 0 ) {
        touchMask = _.find( assignment.elements, { type: 'touchmask' } );
        if ( touchMask ) {
          index = _.findIndex( touchMask.elements, { id: element.id } );
          if ( index > -1 ) {
            isTouchArea = true;
          }
        }
      }

      // delete item from elements
      if ( isTouchArea ) {
        touchMask.elements.splice( index, 1 );
        touchMask.isChanged = true;
        assignment.hasUnsavedElement = true;
      } else {
        if ( element.type === 'touchmask' ) {
            assignment.hasUnsavedElement = false;
        }
        assignment.elements = Immutable.List( assignment.elements ).splice( index, 1 ).toJS();
      }

      assignment.flatElements = flattenElements( assignment.elements );

      // Update prompt assets size only if element added is an image or a video
      if ( ( element.type === 'image' || element.type === 'video' ) && element.size ) {
        removeAsset( { value: element.value } );
        vm.estimatedSize = getTotalAssetSize( promptSetAssets );
      }

      // Update prompt custom font assets size.
      calculateCustomFontAssetSize();

      // marking the state as changed
      markStateAsChanged( assignment.parentId );
    }

    function handleDeleteException( index, prompt ) {
      const exceptionID = prompt.assignments[ index ].id;
      mediaService.deleteException( exceptionID )
        .then( function( result ) {
          prompt.assignments.splice( index, 1 );
          vm.promptSet.version = result.version;
          vm.promptSet.modifiedBy = result.modifiedBy;
          vm.promptSet.modified = result.modified;
          mediaService.getPromptSet( vm.promptSet.id ).then( ( res ) => {
            resetPromptEditView( res );
          } );
        } );
    }

    function handleSelectElement( element, assignment, state ) {
      vm.enablePlaylistButton = assignment?.screenOption !== 'aux';
      if ( !element && assignment && state ) {
        //selected asset is a state
        vm.selectedState = _.find( vm.promptSet.states, { id: assignment.parentId } );
        if ( vm.selectedState ) {
          vm.handleSelectPrompt( vm.selectedState.assignments[ 0 ] );
          // expand the first assignment and contract the rest
          _.forEach( vm.selectedState.assignments, function( currentAssignment, i ) {
            if ( i === 0 ) {
              currentAssignment.showAssets = true;
            } else {
              currentAssignment.showAssets = false;
            }
          } );
        }
        if ( vm.selectedElement ) {
          vm.selectedElement.selected = false;
        }
        vm.selectedElement = null;
        flatenAndGroupAssignments( vm.promptSet.states );
      } else if ( !element && assignment && !state ) {
        //selected asset is an assignment
        if ( vm.selectedElement ) {
          vm.selectedElement.selected = false;
        }
        vm.selectedElement = null;
        vm.selectedState = null;
        vm.handleSelectPrompt( assignment );
      } else if ( element ) {
        //selected asset is an element
        if ( vm.selectedElement ) {
          vm.selectedElement.selected = false;
        }
        vm.selectedElement = element;
        element.shadow = _.clone( element );
        vm.selectedElement.selected = true;
        vm.selectedState = _.find( vm.promptSet.states, { id: element.stateId } );
        let selectedAssignment;
        if ( vm.selectedState ) {
          if ( assignment && assignment.id ) {
              selectedAssignment = _.find( vm.selectedState.assignments, { id: assignment.id } );
          } else {
              selectedAssignment = _.find( vm.selectedState.assignments, { id: element.promptId } );
          }
        }
        if ( selectedAssignment ) {
          vm.handleSelectPrompt( selectedAssignment );
        }
      }
    }

    function flatenAndGroupAssignments( states ) {
      const flatAssignments = _.flatMap( states, ( state ) => state.assignments );
      const assignmentsByCode = _.groupBy( flatAssignments, 'code' );
      const assignmentsByLanguage = _.groupBy( assignmentsByCode, 'promptSetLanguageId' );
      vm.groupedAssignments = assignmentsByLanguage[ undefined ];
      getFlatenedGroups();
    }

    function getFlatenedGroups () {
      if (vm.auxResolutions) {
        vm.mainGroupedAssignments = vm.groupedAssignments.filter(x =>
          x.some(y => y.screenOption === 'main')
        );
        vm.auxGroupedAssignments = vm.groupedAssignments.filter(x =>
          x.some(y => y.screenOption === 'aux')
        );
      } else vm.mainGroupedAssignments = vm.groupedAssignments;
    }

    function handleSelectPrompt( assignment ) {
      vm.assignment = assignment;
      vm.assignment.flatElements = flattenElements( vm.assignment.elements );

      populateSoftKeys( vm.assignment.softkeys );
    }

    function populateSoftKeys( keys ) {
      const availableKeys = angular.copy( vm.keys );
      _.forEach( availableKeys, function( key ) {
        key.softkey = key.id;
        const availableKey = _.find( keys, { softkey: key.id } );
        if ( availableKey ) {
          key.keycode = availableKey.keycode;
          key.label = availableKey.label;
        }
      } );
      if ( !_.isEqual( angular.copy( vm.assignment.softkeys ), availableKeys ) ) {
        vm.assignment.softkeys = availableKeys;
      }
    }

    function handleUpdateElement( element ) {
      vm.assignment.elements = Immutable.List( vm.assignment.elements ).toJS();
      vm.assignment.flatElements = flattenElements( vm.assignment.elements );
      vm.selectedElement = element;

      // marking the state as changed
      const state = _.find( vm.promptSet.states, { id: element.stateId } );
      if ( state ) {
        state.isChanged = true;
      }

      if ( element.type === 'area' ) {
        vm.assignment.hasUnsavedElement = true;
        markTouchMaskAsChanged( vm.assignment );
      }

      calculateCustomFontAssetSize( );
    }

    function handleSaveElement( element ) {
      element.shadow = _.clone( element );
      // TODO: call the api to save this thing
    }

    function handleAddPrompt() {
      vm.isEdit = false; 
      vm.isClone = false;
      openAddPromptModal();
    }

    function handlePromptLanguageEdit() {
      openPromptLanguageEditModal();
    }

    function deleteActionKey( result ) {
      delete result.action;
    }

    function sortStates( currentStates, promptSetId ) {
      const sortOrders = [];
      const sortedStates = [];
      mediaService.getPromptSet( promptSetId ).then( ( response ) => {
        response.states.forEach( ( state ) => {
          sortOrders.push( { id: state.id, code: state.code } );
        } );

        sortOrders.forEach( ( sortOrder ) => {
          sortedStates.push( _.find( currentStates, { code: sortOrder.code } ) );
        } );
        vm.promptSet.states = sortedStates;
      } );
    }
    const pushAndSelectNewState = ( result,action ) => {
      if ( action === promptActions.CLONE){
        const previousState = vm.selectSettingState;
        const currentState = _.find( vm.promptSet.states, { code: result.code } );
        vm.selectedState = _.find( vm.promptSet.states, { code: result.code } );
        // Fix for touchmask not appearing in cloned prompts
        if (previousState && currentState && currentState.assignments && previousState.assignments) {
          // For each assignment in the current state
          _.forEach(currentState.assignments, (currentAssignment, index) => {    
            if (index < previousState.assignments.length) {
              const previousAssignment = previousState.assignments[index];
              // Find touchmask elements in previous assignment
              const touchmaskElements = _.filter(previousAssignment.elements, element => element.type === 'touchmask');
              // Add touchmask elements to current assignment if they don't exist    
              if (touchmaskElements.length > 0 && currentAssignment.elements) {
                const hasTouchmask = _.some(currentAssignment.elements, { type: 'touchmask' });
                if (!hasTouchmask) {
                  // Clone and add touchmask elements
                  _.forEach(touchmaskElements, touchmask => {
                    // Create a deep copy of the touchmask element
                    const touchmaskCopy = angular.copy(touchmask);
                    // Update IDs to match the current assignment and state
                    touchmaskCopy.promptId = currentAssignment.id;
                    touchmaskCopy.stateId = currentState.id;
                    // Update IDs in the areas and elements
                    if (touchmaskCopy.areas) {
                      _.forEach(touchmaskCopy.areas, area => {
                        area.promptId = currentAssignment.id;
                        area.stateId = currentState.id;
                      });
                    }
                    if (touchmaskCopy.elements) {
                      _.forEach(touchmaskCopy.elements, element => {
                        element.promptId = currentAssignment.id;
                        element.stateId = currentState.id;
                      });
                    }
                    // Add to elements array
                    currentAssignment.elements.push(touchmaskCopy);
                    // Do NOT add individual area elements separately - they're already in the touchmask
                  });    
                }
              }
            }
          });
        }
        vm.selectedState.showAssignments = true;
        vm.handleSelectElement( null, currentState.assignments[0], previousState);
      } else if(action === promptActions.CREATE) {
        vm.selectedState =  _.find( vm.promptSet.states, { code: result.code } );
        vm.selectedState.showAssignments = true;
        vm.handleSelectElement( null, result.assignments, vm.selectedState );
      }
    };

    function refreshStatesList( result, action ) {
      const promptSetId = result.promptData.promptSetId;
      mediaService.getPromptSet( promptSetId ).then( ( response ) => {
        const states = response.states;
        const newState = _.find( states, { code: result.promptData.code } );
        resetPromptEditView( response );
        pushAndSelectNewState( newState,action );
        if ( action === promptActions.CREATE || action === promptActions.EDIT || action === promptActions.CLONE ) {
          sortStates( vm.promptSet.states, promptSetId );
        }
      } );
    }

    const openAddPromptModal = (promptState = null) => {
      const promptModalInstance = $uibModal.open( {
        templateUrl: 'moduleMedia/components/promptBuilder2/modals/prompt-modal.html',
        controller: 'PromptModalCtrl',
        controllerAs: 'vm',
        bindToController: true,
        backdrop: 'static',
        size: 'sm',
        resolve: {
          params: () => ({
            promptSetId: vm.promptSet.id,
            deviceType: vm.promptSet.deviceType,
            auxResolutions: vm.auxResolutions,
            isEdit: vm.isEdit,
            isClone : vm.isClone,
            promptState: promptState
          }),
          saveChangedPrompts: () => saveChangedPrompts,
          handleEditCancel: () => handleEditCancel,
          handleCloneCancel: () => handleCloneCancel
        }
      });
      promptModalInstance.result
        .then( ( result ) => {
          if ( !result ) return;
          if ( result.promptData.action === promptActions.CREATE ) {
            refreshStatesList( result, promptActions.CREATE );
          }
          if ( result.promptData.action === promptActions.CLONE ) {
            refreshStatesList( result, promptActions.CLONE );
          }
          deleteActionKey( result.promptData );
        } )
        .catch( () => {} );
    };

    const openPromptLanguageEditModal = () => {
      const editLanguageModalInstance = $uibModal.open( {
        templateUrl: 'moduleMedia/components/promptBuilder2/modals/prompt-language-edit-modal.html',
        controller: PromptLanguageEditModal,
        controllerAs: 'vm',
        bindToController: true,
        size: 'md',
        resolve: {
          params: () => ( {
              companyLanguages: angular.copy( vm.companyLanguages ),
              promptsetLanguages: angular.copy( vm.promptsetLanguages ),
              promptSetId: vm.promptSet.id,
              deviceType: vm.promptSet.deviceType
            } ),
          fonts: function() {
            return vm.fonts;
          }
        }
      } );

      editLanguageModalInstance.result
        .then( () => {
          mediaService.getPromptSet( vm.promptSet.id ).then( ( response ) => {
            resetPromptEditView( response );
          } );
        } )
        .catch( () => {} );
    };

    function getEditDeleteOptions (prompt) {
      vm.selectSettingState = prompt;
      prompt.moreDetails = vm.MoreActions;
      prompt.defaultDetails = vm.DefaultActions;
    }

    function handleSavePromptSet() {
      vm.isSaving = true;
      if(vm.selectedPrimaryAssignment.length>0 && vm.selectedPrimaryAssignment.length === vm.selectedAuxAssignment.length){
        vm.selectedPrimaryAssignment.map((ele,index)=> ele.auxPrompt=vm.selectedAuxAssignment[index].id)
      }
      const changedStates = _.filter( vm.promptSet.states, function( state ) {
        return state.isChanged;
      } );

      if ( !changedStates.length ) {
        ngToast.create( 'You do not have any unsaved changes' );
        vm.isSaving = false;
        return;
      }

      bundleSizeWarning();

      saveChangedPrompts( changedStates );
    }

    function handleSavePrompts( state ) {
      // updating the cloned prompt set so that save button will no longer be there
      if(vm.selectedPrimaryAssignment.length>0 && vm.selectedPrimaryAssignment.length === vm.selectedAuxAssignment.length){
        vm.selectedPrimaryAssignment.map((ele,index)=> ele.auxPrompt=vm.selectedAuxAssignment[index].id);
      }
      return saveChangedPrompts( [ state ] );
    }

    function saveChangedPrompts( changedStates ) {
      let promptAssignments = [];
      let isUnsavedTouchmask = false;

      _.forEach( changedStates, function( state ) {
        promptAssignments = _.concat( promptAssignments, _.map( state.assignments, function( assignment ) {
          const assignmentClone = angular.copy( assignment ); //so that prompt set displayed in the UI will not change
          assignmentClone.softkeys = getAssignedKeys( angular.copy( assignment.softkeys ) );
          const touchmap = _.find( assignment.elements, { type: 'touchmask' } );
          if ( touchmap && touchmap.id && !utilsService.isUUID( touchmap.id ) ) {
              isUnsavedTouchmask = true;
          }
          assignmentClone.touchmap = touchmap && touchmap.id ? { id: touchmap.id } : null;
          assignment.error = null;
          assignmentClone.promptId = assignmentClone.id;
          _.forEach( assignmentClone.elements, ( el ) => {
            if ( typeof el.face === 'object' ) {
              el.face = el.face.family;
            }
          } );
          return assignmentClone;
        } ) );
      } );

      if ( isUnsavedTouchmask ) {
          vm.isSaving = false;
          return handlePromptSaveError( { data: { message: 'Please save touch mask first before you save the prompt set.' } }, changedStates );
      }

      return mediaService.putPromptSetPrompts( vm.promptSet.id, promptAssignments )
        .then( function( result ) {
          _.forEach( changedStates, function( state ) {
            vm.ClonedMainPrompts.forEach((x, index) => {
              if (x.id === state.id) {
                  vm.ClonedMainPrompts[index] = state;
              }
          });          
            _.forEach( state.assignments, function( assignment ) {
              _.forEach( assignment.elements, function( element ) {
                element.isError = false;
              } );
              assignment.isError = false;
            } );
            state.isChanged = false;
            state.isError = false;
          } );
          vm.isSaving = false;
          if(vm.isEdit){
            mediaService.getPromptSet( vm.promptSet.id ).then( ( res ) => {
              resetPromptEditView( res );
            } );
            ngToast.create('Prompt updated');
            vm.isEdit = false;
          }
          else {
            ngToast.create( 'Saved ' + changedStates.length + ' ' + ( changedStates.length === 1 ? 'state ' : 'states ' ) );
          }
          vm.promptSet.version = result.version;
          vm.promptSet.modifiedBy = result.modifiedBy;
          vm.promptSet.modified = result.modified;

          return true;
        } )
        .catch( function( err ) {
          vm.isSaving = false;
          handlePromptSaveError( err, changedStates );

          return false;
        } );
    }

    function handlePromptSaveError( err, changeStates ) {

      if ( err.status === 400 ) {
        _.forEach( changeStates, function( state ) {
          const assignmentErrors = _.filter( state.assignments, function( assignment ) {
            const error = _.find( err.data, { promptId: assignment.id } );
            if ( error ) {
              assignment.error = error.err;
              assignment.isError = true;
              _.forEach( assignment.elements, function( element ) {
                element.isError = element.id === error.elementId;
              } );
              return true;
            }
            assignment.isError = false;
            return false;
          } );
          state.isError = assignmentErrors.length > 0;
        } );
        if ( err.data.length > 0 ) {
          ngToast.create( err.data.length + ' invalid ' + ( err.data.length === 1 ? 'prompt' : 'prompts' ) + ' found. Please fix this and try again' );
        } else {
          ngToast.create( err.data.message );
        }
      } else {
        ngToast.create( err.data.message );
      }
    }

    function getAssignedKeys( keys ) {
      return _.remove( keys, function( key ) {
        return key.keycode;
      } );
    }

    function handleResetElement( element ) {
      element = element.shadow;
      vm.handleUpdateElement( element );
    }


    function handleSoftKey( key, stateID ) {
      const properties = { code: key.keycode };

      const modalInstance = $uibModal.open( {
        templateUrl: 'moduleMedia/components/promptBuilder2/modals/soft-key-modal.html',
        controller: 'SoftKeyModalCtrl',
        controllerAs: 'vm',
        bindToController: true,
        size: 'sm',
        resolve: {
          properties: function() {
            return properties;
          }
        }
      } );

      modalInstance.result
        .then( function( result ) {
          let changed = false;

          // key code selected and it is not the previously selected key code
          if ( result && ( key.keycode !== result.code ) ) {
            key.label = result.name;
            key.keycode = result.code;
            changed = true;

            // key code removed and there is not a key code that was previously selected
          } else if ( !result && key.keycode ) {
            key.label = null;
            key.keycode = null;
            changed = true;
          }

          // marking the state as changed
          if ( changed ) {
            const state = _.find( vm.promptSet.states, { id: stateID } );
            if ( state ) {
              state.isChanged = true;
            }
          }
        } )
        .catch( function() {} );
    }

    // eslint-disable-next-line consistent-return
    function addDayPart( state, prompt ) {
      const defaultPromptId = prompt.id;
      prompt.selectedDayparts = [];

      _.forEach( state.assignments, function( assignment ) {
        if ( assignment.dayPart && assignment.promptSetLanguageId === prompt.promptSetLanguageId ) {
          prompt.selectedDayparts.push( assignment.dayPart.id );
        }
      } );

      const dayparts = _.filter( vm.dayparts, function( daypart ) {
        return daypart.active;
      } );

      const modalInstance = $uibModal.open( {
        templateUrl: 'moduleMedia/components/promptBuilder2/modals/daypart-modal.html',
        controller: 'DaypartModalCtrl',
        controllerAs: 'vm',
        bindToController: true,
        size: 'sm',
        resolve: {
          dayparts: function() {
            return dayparts;
          },
          properties: function() {
            return {
                selectedDayparts: prompt.selectedDayparts
            };
          }
        }
      } );

      modalInstance.result
        .then( function( result ) {
          const exception = {
            type: 'exception',
            dayPart: result,
            code: null,
            promptSetLanguageId: null,
            parentId: state.id,
            showAssets: false,
            showAssetsDropdown: false,
            elements: [],
            flatElements: [],
            softkeys: []
          };
          mediaService.createException( vm.promptSet.id, exception, defaultPromptId )
            .then( function( response ) {
              exception.id = response.id;
              state.assignments.push( exception );
              const exceptionState = _.find( vm.promptSet.states, { id: exception.parentId } );
              exception.transactionState = exceptionState.transactionState;
              exception.code = exceptionState.code;
              exception.promptSetLanguageId = exceptionState.promptSetLanguageId;
              const defaultPrompt = prompt;
              // copying the touch mask from the default prompt
              if ( defaultPrompt.touchmap ) {
                exception.touchmap = angular.copy( defaultPrompt.touchmap );
              }
              // copying the elements from the default prompt
              _.forEach( defaultPrompt.elements, function( el ) {
                const element = angular.copy( el );
                element.promptId = exception.id;
                if ( element.type === 'touchmask' ) {
                  _.forEach( element.elements, function( touchMaskElement ) {
                    touchMaskElement.id = null;
                    touchMaskElement.promptId = exception.id;
                  } );
                } else {
                  element.id = null;
                }
                vm.handleCreateElement( exception, element );
              } );

              vm.promptSet.version = response.promptSet.version;
              vm.promptSet.modifiedBy = response.promptSet.modifiedBy;
              vm.promptSet.modified = response.promptSet.modified;
              mediaService.getPromptSet( vm.promptSet.id ).then( ( res ) => {
                resetPromptEditView( res );
              } );
            } );
        } )
        .catch( function() {} );
    }

    function updatePromptSetName() {
      mediaService.putPromptSet( vm.promptSet.id, { name: vm.promptSet.name, fontColor: vm.promptSet.fontColor } )
        .then( function( result ) {
          ngToast.create( 'Prompt set name updated' );
          vm.promptSet.version = result.version;
          vm.promptSet.modifiedBy = result.modifiedBy;
          vm.promptSet.modified = result.modified;
        } );
    }

    function approveRejectPromptSet( isApproved ) {
      const action = isApproved ? 'approve' : 'reject';

      if ( isApproved ) {
        openMFAModal( action ).then( function( mfa ) {
          vm.isApproving = true;
          mediaService.approvePromptSet( {
            id: vm.promptSet.id,
            mfa: mfa
          } )
          .then( function() {
            ngToast.create( 'You approved the prompt set' );
            vm.approved = true;
            vm.isApproving = false;
          } )
          .catch( function() {
            vm.isApproving = false;
          } );
        } );
      } else {
        openMFAModal( action ).then( function( mfa ) {
          vm.isRejecting = true;
          mediaService.rejectPromptSet( {
            id: vm.promptSet.id,
            mfa: mfa
          } )
          .then( function() {
            ngToast.create( 'You rejected the prompt set' );
            vm.approvalEnabled = false;
            vm.isRejecting = false;
          } )
          .catch( function() {
            vm.isRejecting = false;
          } );
        } );
      }
    }

    function viewAsODML() {

      // View as ODML will only save current state if changed.
      const waitForSavingPromptState = function( ) {

        // vm.selectedState can be empty, therefore grab promptState from assignment object.
        const selectedState = _.find( vm.promptSet.states, { id: vm.assignment.promptState } );
        if ( selectedState && selectedState.isChanged ) {
          vm.isSaving = true;
          return saveChangedPrompts( [ selectedState ] );
        }

        return Promise.resolve( true );
      };

      if ( $state.transition === null ) {
        $uibModal.open( {
            templateUrl: 'moduleMedia/components/promptBuilder2/modals/odml-preview-modal.html',
            windowTopClass: 'modal-primary',
            size: 'lg',
            controller: 'ViewAsODMLCtrl',
            controllerAs: 'vm',
            resolve: {
                prompt: function() {
                    return vm.assignment;
                },
                fonts: function() {
                    return vm.fonts;
                },
                savingPromise: function() {
                    return waitForSavingPromptState;
                }
            }
        } );
      } else {
          // wait for the state to finish before opening the ViewAsODML modal
          viewAsODMLTimeOut = setTimeout( viewAsODML, 1000 );
      }

      bundleSizeWarning();
    }

    $scope.$on( '$destroy', () => {
        clearTimeout( viewAsODMLTimeOut );
    } );

    function toggleMetadataPanel() {
      vm.isRightContracted = !vm.isRightContracted;
      if ( vm.isRightContracted ) {
        $state.go( '.', { showMeta: 'false' } );
      } else {
        $state.go( '.', { showMeta: 'true' } );
      }
    }

    function markStateAsChanged( id ) {
      const state = _.find( vm.promptSet.states, { id: id } );
      if ( state ) {
          state.isChanged = true;
      }
    }

    function markTouchMaskAsChanged( assignment ) {
        const touchMask = _.find( assignment.elements, { type: 'touchmask' } );
        if ( touchMask ) {
          touchMask.isChanged = true;
        }
    }

    function handleTouchMaskSave( assignment ) {
        assignment.hasUnsavedElement = false;
    }

    function openMFAModal( action ) {
      const modalInstance = $uibModal.open( {
        templateUrl: 'moduleMedia/modals/approve-reject.mfa.modal.html',
        windowTopClass: 'modal-primary',
        size: 'sm',
        controller: 'ApproveRejectMFAModalController',
        controllerAs: 'vm',
        resolve: {
          action: function() {
            return action;
          }
        }
      } );
      return modalInstance.result;
    }

    // adds an asset to assets array to increase estimated size
    function addAsset( newAsset ) {
      const asset = _.find( promptSetAssets, { value: newAsset.value } );
      if ( !asset ) {
        newAsset.count = 1;
        promptSetAssets.push( newAsset );
      } else {
        asset.count += 1;
      }
    }

    // remove an asset to assets array to reduce estimated size
    function removeAsset( assetToRemove ) {
      const asset = _.find( promptSetAssets, { value: assetToRemove.value } );
      if ( asset && asset.count === 1 ) {
        _.remove( promptSetAssets, ( n ) => n.value === asset.value );
      } else {
        asset.count -= 1;
      }
    }

    function addAssets( elements ) {
      elements.forEach( function ( element ) {
        // if element is an asset, add to assets set
        if ( element.type === 'image' || element.type === 'video' ) {
          addAsset( {
            type: element.type,
            value: element.value,
            size: element.size
          } );
        }

        if ( ( element.type === 'input' || element.type === 'text' ) && element.face && element.face.type === 'CUSTOM' ) {
          addAsset( { value: element.face.fontId, size: element.face.fileSize, type: 'font' } );
        }
      } );
    }
}

function PromptModalCtrl( $scope, mediaService, ngToast, promptActions, $rootScope ) {
  const vm = this;
  vm.promptSet = $rootScope.promptSet;
  vm.PromptTypes = {
    standard: { name: 'Standard', code: 1 },
    pin: { name: 'PIN', code: 3 },
    data: { name: 'Data', code: 4 },
    internal: {name: 'Internal', code: 5}
  };
    vm.PromptTypeChoices = _.cloneDeep(vm.PromptTypes);

    function handlePromptTypeUpdate(){
      if(vm.modal.promptType === 'internal') {
        if(vm.modal.screenOption === 'Auxiliary Display'){
          if(!vm.modal.code.startsWith('aux-int-'))
            vm.modal.code = 'aux-int-';  
          else return; 
        }  
        else {
          if(  vm.modal.code == null || !vm.modal.code.startsWith('int-')){
            vm.modal.code = 'int-';
          }
        }
        
      }
      else if((vm.modal.screenOption === 'Auxiliary Display')){
        if(!vm.modal.code.startsWith('aux-') || vm.modal.code.startsWith('aux-int-'))
        vm.modal.code = 'aux-';   
      }
      else if(vm.modal.code.startsWith('int-')){
        vm.modal.code = '';
      }
    }

    function updatePromptInput() {
      if ($scope.vm.modal.screenOption === 'Auxiliary Display') {
        if(vm.modal?.promptType === 'internal') $scope.vm.modal.code = 'aux-int-';
        else $scope.vm.modal.code = 'aux-';
        vm.PromptTypeChoices = _.cloneDeep(vm.PromptTypes);
      } else if(vm.modal?.promptType === 'internal'){
        $scope.vm.modal.code = 'int-'
        delete vm.PromptTypeChoices.pin;
      } 
      else {
        $scope.vm.modal.code = '';
        delete vm.PromptTypeChoices.pin;
      }

    }

    vm.updatePromptInput = updatePromptInput;
    vm.handlePromptTypeUpdate = handlePromptTypeUpdate;

  vm.modalState = {
    add: false,
    edit: false,
    delete: false,
    clone: false,
    error: false
  };

  vm.modal = {
    promptSetId: $scope.$resolve.params.promptSetId,
    deviceType: $scope.$resolve.params.deviceType,
    auxResolutions : $scope.$resolve.params.auxResolutions,
    code: null,
    promptType: null,
    description: null,
    showTooltip: false,
    isEdit: $scope.$resolve.params.isEdit,
    isClone: $scope.$resolve.params.isClone,
    promptState: $scope.$resolve.params.promptState
  };

  if(vm.modal.isEdit || vm.modal.isClone){
    if(vm.modal.promptState){
      vm.modal.isEdit ? vm.modal.code = vm.modal.promptState.code : vm.modal.isClone ? vm.modal.code = getNextCopyName(vm.modal.promptState.code) : '';
      vm.modal.description = vm.modal.promptState.description;
      vm.modal.promptType = (vm.modal.promptState.code.startsWith('aux-int-') || vm.modal.promptState.code.startsWith('int-')) ? 'internal' : vm.modal.promptState.promptType;
      if (vm.modal.promptState.screenOption === 'aux') {
        vm.modal.screenOption = 'Auxiliary Display';
      } 
    }
  }

  function getNextCopyName(name) {
    // Updated regex to handle both -Copy and -Copy-n formats
    const regex = /^(.*?)(?:-Copy(?:-(\d+))?)?$/;
    const match = name.match(regex);

    if (!match) return name + "-Copy";

    const baseName = match[1].trim();
    const copyNumber = match[2] ? parseInt(match[2], 10) : (name.endsWith('-Copy') ? 0 : -1);
    if (copyNumber === -1) {
        // name doesn't end with -Copy or -Copy-n, so add "-Copy"
        return `${baseName}-Copy`;
    } else if (copyNumber === 0) {
        // name ends with "-Copy", so next is "-Copy-1"
        return `${baseName}-Copy-1`;
    } else {
        // increment the copy number
        return `${baseName}-Copy-${copyNumber + 1}`;
    }
  }


  vm.saveChangedPrompts = $scope.$resolve.saveChangedPrompts;
  vm.handleEditCancel = $scope.$resolve.handleEditCancel;
  vm.handleCloneCancel = $scope.$resolve.handleCloneCancel;

  vm.sanitizeCodeName = () => {
    if ( vm.modal.code !== undefined ) {
      vm.modal.code = vm.modal.code.replace( /[^\w_-]/gi, '' );
    }
  };

  vm.cleanUpErrorTexts = () => {
    vm.resetErrorState();
    vm.disableCreateButton = false;
  };

  vm.updatePrompt = () => {
    const promptData = vm.modal;
    if(promptData.promptType === 'internal'){
      const validInput = checkPromptName();
      if(!validInput){
        return null;
      }
    }
    if(promptData.promptState.screenOption === 'aux'){
      const validInput = checkInput();
      if(!validInput){
        return null;
      }
    }
    
    const duplicateState = _.find( vm.promptSet.states, ( state ) => state.code.toLowerCase() === promptData.code.toLowerCase())
    if(duplicateState && duplicateState.id !== promptData.promptState.id){
      vm.modalState.add = false;
      vm.modalState.error = true;
      vm.errorText = 'Unique name required';
      return null;
    }
    else 
    {
      setTimeout(() => {
        promptData.promptState.assignments.forEach((assignment,index) => {
          assignment.code = promptData.code;
          assignment.description = promptData.description;
        });
        const changedStates = promptData.promptState;
        vm.saveChangedPrompts([changedStates])
        vm.modal.isEdit = false;
        vm.modalState.add = false;
        $scope.$close();
      }, 1000);
    }
  }

  vm.clonePrompt = () => {
    const promptData = vm.modal;

    const duplicateState = _.find(vm.promptSet.states, ( state ) => state.code.toLowerCase() === promptData.code.toLowerCase() );
    if(duplicateState === undefined){
      mediaService.clonePrompt( promptData ).then( (successResponse) => {
        ngToast.create( 'Prompt are cloned' );
        promptData.action = promptActions.CLONE;
        vm.modalState.add = false;
        $scope.$close( { promptData, successResponse } );
        vm.modal.isEdit = false;
      }, function errorCallback( errResponse ) {
        vm.modalState.add = false;
        vm.modalState.error = true;
        vm.errorText = errResponse.data !== null && typeof errResponse.data === 'object' ? errResponse.data.message : errResponse.data;
      });
    } else {
      vm.modalState.add = false;
      vm.modalState.error = true;
      vm.errorText = 'Unique name required';
    }
  }

  const checkInput = () => {
    const promptData = vm.modal;
      if ( !promptData.code.startsWith('aux-')) {
      vm.modalState.error = true;
      vm.errorText = "The name of an auxiliary prompt must start with the prefix 'aux-'.";
      vm.modalState.add= false;
      return false;
      }
      if( promptData.code.trim() === 'aux-'){
        vm.modalState.error = true;
        vm.errorText = "Code must have atleast 1 character post 'aux-'.";
        vm.modalState.add= false;
        return false;
      }
      return true;
  }

  const checkPromptName = () => {
    const promptData = vm.modal;
    if(promptData.screenOption === 'Auxiliary Display') {
      if ( !promptData.code.startsWith('aux-int-')) {
        vm.modalState.error = true;
        vm.errorText = "The name of an auxiliary internal prompt must start with the prefix 'aux-int-'.";
        vm.modalState.add= false;
        return false;
        }
        if( promptData.code.trim() === 'aux-int-'){
          vm.modalState.error = true;
          vm.errorText = "Code must have atleast 1 character post 'aux-int-'.";
          vm.modalState.add= false;
          return false;
        }
    }
    else{
      if ( !promptData.code.startsWith('int-')) {
        vm.modalState.error = true;
        vm.errorText = "The name of an internal prompt type must start with the prefix 'int-'.";
        vm.modalState.add= false;
        return false;
        }
        if( promptData.code.trim() === 'int-'){
          vm.modalState.error = true;
          vm.errorText = "Code must have atleast 1 character post 'int-'.";
          vm.modalState.add= false;
          return false;
        }
    }
      
      return true;
  }
  vm.addPrompt = () => {
    vm.modalState.add = true;
    vm.modalState.error = false;
    const promptData = vm.modal;
    if(promptData.promptType === 'internal'){
      const validInput = checkPromptName();
      if(!validInput){
        return null;
      }
    }

    if(promptData.screenOption === 'Auxiliary Display'){
      const validInput = checkInput();
      if(!validInput){
        return null;
      }
    }

    const duplicateState = _.find( vm.promptSet.states, ( state ) => state.code.toLowerCase() === promptData.code.toLowerCase() );

    if ( duplicateState === undefined ) {
      const clonedPromptData = { ...promptData }
        if (promptData.screenOption === 'Auxiliary Display') clonedPromptData.screenOption = 'aux'
        else clonedPromptData.screenOption = 'main'
        if(clonedPromptData.promptType === 'internal') clonedPromptData.promptType= 'standard';
      mediaService.postPrompt( clonedPromptData ).then( ( successResponse ) => {
        ngToast.create( 'Prompt created' );
        promptData.action = promptActions.CREATE;
        vm.modalState.add = false;
        $scope.$close( { promptData, successResponse } );
      }, function errorCallback( errResponse ) {
        vm.modalState.add = false;
        vm.modalState.error = true;
        vm.errorText = errResponse.data !== null && typeof errResponse.data === 'object' ? errResponse.data.message : errResponse.data;
      } );
    } else {
      vm.modalState.add = false;
      vm.modalState.error = true;
      vm.errorText = 'Unique name required';
    }
  };
    // Add this function to your controller
    vm.resetErrorState = () => {
      vm.modalState.error = false;
      vm.errorText = '';
    };

  vm.cancel = () => {
    $scope.$dismiss();
    if(vm.modal.isEdit) {
      vm.handleEditCancel();
    }
    if(vm.modal.isClone){
      vm.handleCloneCancel();
    }
  };
}

function PromptLanguageEditModal( $scope, mediaService, ngToast, fonts, $uibModal, devicesService ) {
  const vm = this;
  const promptSetId = $scope.$resolve.params.promptSetId;
  const deviceType = $scope.$resolve.params.deviceType;
  const existingPromptsetLanguages = angular.copy( $scope.$resolve.params.promptsetLanguages );

  const init = () => {
    vm.fonts = fonts;
    vm.modalState = {
      error: false
    };
    vm.fontNameLength = 47;
    vm.companyLanguages = $scope.$resolve.params.companyLanguages.sort( ( a, b ) => a.isoCode.localeCompare( b.isoCode, 'en-us', { ignorePunctuation: true } ) );
    vm.promptsetLanguages = $scope.$resolve.params.promptsetLanguages;
    createModalInfo();
  };

  const createModalInfo = () => {
    vm.langModalViewItems = [];
    _.forEach( vm.companyLanguages, ( companyLanguage ) => {
      const languageDetails = getTypeAndSize( companyLanguage.languageSupportId, true );
      const isAvailable = isAvailableInPromptSet( companyLanguage );
      const item = {
        id: companyLanguage.languageSupportId,
        isAvailableInPromptSet: isAvailable,
        isoCode: companyLanguage.isoCode,
        language: companyLanguage.language,
        default: companyLanguage.default,
        deleted: companyLanguage.deleted,
        type: getFontTypeByName( languageDetails.type ),
        size: languageDetails.size
      };
      vm.langModalViewItems.push( item );
      vm.handleLanguageCheck( item, item.isAvailableInPromptSet );
    } );
    getDefaultLanguage();
  };

  const getTypeAndSize = ( languageSupportId ) => {
    const fallbackDefault = {};
    if ( devicesService.isSequoiaDevice( deviceType ) ) {
      fallbackDefault.size = 48;
      fallbackDefault.face = {
        fontId: 'Liberation Sans',
        name: 'Liberation Sans',
        family: 'Liberation Sans',
        type: 'DEFAULT',
        supportedDevices: [ 'G7-100', 'G6-300','G6-400', 'G6-500' ],
        active: true
      };
    } else if ( deviceType === 'G6-200' ) {
        fallbackDefault.size = 24;
        fallbackDefault.face = {
        fontId: 'FreeSans',
        name: 'FreeSans',
        family: 'FreeSans',
        type: 'DEFAULT',
        supportedDevices: [ 'G6-200' ],
        active: true
      };
    }

    const psLanguageSupport = _.find( vm.promptsetLanguages, ( psLang ) => psLang.languageSupportId.toLowerCase() === languageSupportId.toLowerCase() );

    return {
      type: ( psLanguageSupport && psLanguageSupport.promptSetLanguageSupport.type ) || fallbackDefault.face.name,
      size: ( psLanguageSupport && psLanguageSupport.promptSetLanguageSupport.size ) || fallbackDefault.size
    };
  };

  const isAvailableInPromptSet = ( companyLanguage ) => {
    const promptSetLanguage = _.find( vm.promptsetLanguages, ( item ) => item.languageSupportId === companyLanguage.languageSupportId );
    return !!promptSetLanguage;
  };

  // eslint-disable-next-line consistent-return
  const getFontTypeByName = ( type ) => {
    const fontIndex = _.findIndex( vm.fonts, { name: type } );
    if ( fontIndex >= 1 ) {
      return vm.fonts[ fontIndex ];
    }
  };

  const getDefaultLanguage = () => {
    const existingDefaultIndex = _.findIndex( vm.promptsetLanguages, ( item ) => item.promptSetLanguageSupport.default === true );
    if ( existingDefaultIndex >= 0 ) {
      vm.defaultLanguage = {
        key: existingDefaultIndex,
        value: vm.promptsetLanguages[ existingDefaultIndex ]
      };
    }
  };

  const createDefaultLanguageList = () => {
    vm.defaultLanguageList = _.filter( vm.langModalViewItems, { isAvailableInPromptSet: true } );
  };

  const getSelectedLanguages = () => _.filter( vm.langModalViewItems, ( item ) => item.isAvailableInPromptSet === true );

  const putPromptsetLanguages = ( languages ) => {
    mediaService.putPromptsetLanguages( promptSetId, languages ).then( ( response ) => {
      $scope.$close( response );
      ngToast.create( 'Languages updated successfully' );
    } );
  };

  const isLanguagesModified = ( newLanguages ) => {
    const existingLang = _.map( _.sortBy( existingPromptsetLanguages, 'languageSupportId' ), ( item ) => item.languageSupportId );

    const updatedLang = _.map( _.sortBy( _.filter( newLanguages, ( item ) => item.promptSetLanguageSupport.deleted === false ), 'languageSupportId' ), ( item ) => item.languageSupportId );

    return _.isEqual( existingLang, updatedLang ) === false;
  };

  const confirmAndPutPromptLanguages = ( languages ) => {
    const modalInstance = $uibModal.open( {
      templateUrl: 'moduleMedia/components/promptBuilder2/modals/update-default-font.modal.html',
      size: 'sm',
      controllerAs: 'vm',
      controller: function ( $uibModalInstance ) {
        const that = this;

        that.title = 'Update promptset languages';
        that.message = `
          <p>Disabling languages will remove all existing prompts under that language. Enabling languages will add new prompts to the prompt set.</p>
          <p>Are you sure you want to continue?</p>
        `;
        that.yes = yes;
        that.no = no;

        function yes() {
          $uibModalInstance.close();
        }

        function no() {
          $uibModalInstance.dismiss();
        }
      }
    } );

    modalInstance.result.then( () => {
      putPromptsetLanguages( languages );
    } );
  };

  vm.handleLanguageCheck = ( langModalViewItem, value ) => {
    langModalViewItem.isAvailableInPromptSet = value;
    createDefaultLanguageList();
    vm.languageValidation();
  };

  vm.validateFontSize = ( $event ) => {
    const inputValue = $event.delegateTarget.value;
    $event.delegateTarget.value = parseInt( `${inputValue}`.replace( /\D/g, '' ), 10 ) || '';
  };

  vm.setNewDefault = () => {
    const newDefault = _.find( vm.langModalViewItems, { language: vm.defaultLanguage.value.language } );
    _.forEach( vm.langModalViewItems, ( item ) => {
      item.default = item.language === newDefault.language;
    } );
    vm.languageValidation();
  };

  vm.languageValidation = () => {
    // eslint-disable-next-line consistent-return
    const invalidLangs = _.filter( vm.langModalViewItems, ( item ) => {
      if ( item.isAvailableInPromptSet ) {
        return (
          item.type === null || item.type === undefined ||
          item.size === null || item.size === undefined || item.size < 1 );
      }
    } );
    const selectedLanguages = getSelectedLanguages();
    vm.modalState.error = !!( invalidLangs.length >= 1 || selectedLanguages.length === 0 );

    //one or more are checked but default lang is different
    if ( vm.defaultLanguage !== undefined && vm.modalState.error === false ) {
      const defaultChecked = _.filter( selectedLanguages, ( item ) => item.language === vm.defaultLanguage.value.language );
      vm.modalState.error = defaultChecked.length === 0;
    }
  };

  vm.onPromptLanguageSave = () => {
    vm.languageValidation();
    vm.setNewDefault();
    const languages = _.map( vm.langModalViewItems, ( item ) => ( {
        languageSupportId: item.id,
        isoCode: item.isoCode,
        language: item.language,
        promptSetLanguageSupport: {
          type: item.type === undefined ? null : item.type.name,
          size: item.size === null ? null : item.size,
          default: item.default,
          deleted: !item.isAvailableInPromptSet
        }
      } ) );

    const languagesModified = isLanguagesModified( languages );
    if ( languagesModified ) {
      confirmAndPutPromptLanguages( languages );
    } else {
      putPromptsetLanguages( languages );
    }
  };

  vm.cancel = () => {
    $scope.$dismiss();
  };

  init();
}

  function SoftKeyModalCtrl( $scope, properties, mediaService ) {
    /*jshint validthis:true*/
    const vm = this;

    vm.setKeyCode = setKeyCode;
    vm.cancel = cancel;

    mediaService.getKeyCodes().then( function( result ) {
      vm.keyCodes = result;
      if ( properties && properties.code ) {
        vm.keyCode = _.find( vm.keyCodes, { code: properties.code.toString() } );
      }
    } );

    function setKeyCode() {
      $scope.$close( vm.keyCode );
    }

    function cancel() {
      $scope.$dismiss();
    }
  }

  function DaypartModalCtrl( $scope, dayparts, properties ) {
    /*jshint validthis:true*/
    const vm = this;

    vm.dayparts = [];

    vm.cancel = cancel;
    vm.setDaypart = setDaypart;

    if ( properties && properties.selectedDayparts && properties.selectedDayparts.length > 0 ) {
      vm.dayparts = dayparts.filter( ( daypart ) => {
        const daypartId = daypart.id;
        if ( !properties.selectedDayparts.includes( daypartId ) ) {
          return true;
        }
        return false;
      } );
    } else {
      vm.dayparts = dayparts;
    }

    function setDaypart() {
      $scope.$close( vm.daypart );
    }

    function cancel() {
      $scope.$dismiss();
    }
  }

  function ViewAsODMLCtrl( $scope, prompt, mediaService, fonts, savingPromise ) {
    /*jshint validthis:true*/
    const vm = this;

    vm.$onInit = onInit;
    vm.close = close;
    vm.isSaving = true;

    function onInit() {
      const odmlElements = [];
      _.forEach( prompt.elements, function( el ) {
        let odmlEl;
        if ( ( el.type === 'bg' && el.value.length > 6 ) || el.type === 'image' ) {
          odmlEl = {
            id: el.value,
            path: 'path/to/images/' + el.value
          };
          odmlElements.push( odmlEl );
        }

        if ( el.type === 'image' ) {
          odmlEl = {
            id: el.value,
            path: 'path/to/images/' + el.value
          };
          odmlElements.push( odmlEl );
        }

        if ( el.type === 'video' ) {
          odmlEl = {
            id: el.value,
            path: 'path/to/videos/' + el.value
          };
          odmlElements.push( odmlEl );
        }

        if ( el.type === 'text' || el.type === 'input' ) {
            const uuidV4Regex = /^[A-F\d]{8}-[A-F\d]{4}-4[A-F\d]{3}-[89AB][A-F\d]{3}-[A-F\d]{12}$/i;
            if ( el.face.family && uuidV4Regex.test( el.face.family ) ) {
                odmlEl = {
                    id: el.face.family,
                    face: el.face.name,
                    path: 'path/to/fonts/' + el.face.name
                };
                odmlElements.push( odmlEl );
            }
        }
      } );

      savingPromise().then( function( status ) {
        vm.isSaving = false;

        if ( !status ) {
          $scope.$close();
          return;
        }

        mediaService.generateODML( prompt.id, odmlElements )
        .then( function( result ) {
          vm.odml = result;
        } );
      } );
    }

    function close() {
      $scope.$close();
    }
  }
} )();
