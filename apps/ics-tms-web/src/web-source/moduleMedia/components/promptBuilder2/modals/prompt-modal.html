<div class="modal-header">
    <h4 class="modal-title">{{ vm.modal.isEdit ? 'Edit Prompt' : (vm.modal.isClone ? 'Duplicate Prompt' : 'Create new prompt')}}</h4>
</div>
<form name="addPromptForm" class="modal-body">
    <div class="form-group" ng-if="vm.modal.auxResolutions && !vm.modal.isEdit && !vm.modal.isClone">
        <div>
            <label>Display</label>
            <ui-select name="screenOption" ng-model="vm.modal.screenOption" ng-init="vm.modal.screenOption = main"
                ng-change="vm.updatePromptInput(); vm.resetErrorState()" required>
                <ui-select-match> {{$select.selected}} </ui-select-match>
                <ui-select-choices repeat="type in ['Primary Display', 'Auxiliary Display']" style="width: 100%">
                    {{ type }}
                </ui-select-choices>
            </ui-select>
        </div>
    </div>

    <div class="form-group" style="position: relative;">
        <label for="code" ng-class="{'text-danger' : vm.modalState.error}">Prompt name (Case Sensitive)</label>
        <div class="input-group" style="position: relative; width: 100%;">
            <input required allow-alphanumeric-dash-underscore ng-blur="vm.sanitizeCodeName()" type="text" name="code"
                ng-model="vm.modal.code" class="form-control" placeholder="Prompt name (Case Sensitive)"
                ng-change="vm.cleanUpErrorTexts()" />

            <!-- Tooltip Icon and Message -->
            <span ng-mouseover="showTooltip = true" ng-mouseleave="showTooltip = false">
                <span ng-if="vm.modal.screenOption === 'Auxiliary Display' || vm.modal.promptType === 'internal'" class="icon-span">
                    <i class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px;"></i>
                </span>
                <div ng-show="showTooltip && vm.modal.promptType === 'internal'" class="aux-tooltip">
                    Internal prompt type must contain int-
                </div>
                <div ng-show="showTooltip && vm.modal.screenOption === 'Auxiliary Display' && vm.modal.promptType === 'internal'" class="aux-tooltip">
                    Auxiliary internal prompt type must contain aux-int-
                </div>
                <div ng-show="showTooltip && vm.modal.screenOption === 'Auxiliary Display' && vm.modal.promptType !== 'internal'" class="aux-tooltip">
                    Auxiliary prompts must contain aux-
                </div>
            </span>
        </div>
            <span class="text-danger small" ng-if="vm.modalState.error">{{vm.errorText}}</span>
        </div>
    </div>

    <div class="form-group" ng-if="!vm.modal.isEdit && !vm.modal.isClone">
        <label for="">Prompt type</label>
        <ui-select ng-model="vm.modal.promptType" ng-change="vm.handlePromptTypeUpdate(); vm.resetErrorState()" required>
            <ui-select-match>{{$select.selected.value.name}}</ui-select-match>
            <ui-select-choices repeat="promptType.key as (key, promptType) in vm.PromptTypeChoices" style="width: 100%">
                {{promptType.value.name}}
            </ui-select-choices>
        </ui-select>
    </div>

    <div class="form-group" >
        <label for="description">Description</label>
        <input required type="text" name="description" ng-model="vm.modal.description" class="form-control" placeholder="Description"/>
    </div>
</form>
<div class="modal-footer">
    <button  class="btn btn-link text-uppercase" type="button" ng-click="vm.cancel()">
        Cancel
    </button>
    <button class="btn btn-primary text-uppercase" type="submit" ng-click="vm.modal.isEdit ? vm.updatePrompt() : (vm.modal.isClone ? vm.clonePrompt() : vm.addPrompt())" ng-disabled="addPromptForm.$invalid || vm.modalState.add || vm.modalState.error">
        {{ vm.modal.isEdit ? 'Update' : (vm.modal.isClone ? 'Duplicate' : 'Create')}} 
    </button>
</div>
