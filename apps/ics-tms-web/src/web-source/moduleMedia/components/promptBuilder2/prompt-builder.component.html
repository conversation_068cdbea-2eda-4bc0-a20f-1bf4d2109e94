<!--
<div class="flex-container">
  <div>
    <h1>{{vm.promptSet.name}}</h1>
    <div ng-repeat="prompt in vm.promptSet.states">
      <h2>{{prompt.name}}</h2>
      <prompt-tree assignment="assignment" ng-repeat="assignment in prompt.assignments"
                   elements="assignment.flatElements" on-select-element="vm.handleSelectElement( element, assignment )"
                   on-add="vm.handleCreateElement( assignment, element, index, parent )"
                   on-move-to="vm.handleMoveTo( from, to, parent )"
                   on-delete="vm.handleRemoveElement( assignment, element, parent )" style="width: 300px"></prompt-tree>
    </div>
  </div>
  <div class="flex-container" style="flex: auto; flex-direction: column;">
    <prompt-controls selected-element="vm.selectedElement" on-update-element="vm.handleUpdateElement( element )"
                     assignment="vm.assignment" on-save-element="vm.handleSaveElement( element )"
                     on-reset-element="vm.handleResetElement( element )"></prompt-controls>
    <prompt-builder elements="vm.assignment.flatElements" on-select-element="vm.handleSelectElement"
                    selected-element="vm.selectedElement" style="flex: auto;"></prompt-builder>
  </div>
</div>
-->

<style rel="preload" ng-repeat="font in vm.fonts">
  @font-face {
      font-family: "{{font.fontId}}";
      src:url('{{font.sourceUrl}}')
  }
  .{{font.fontId}} {
    font-family: "{{font.fontId}}"
  }
  .default-colors-container {
    border-right: 0;
  }
  .default-colors-container .ics-prompt-builder-default-options {
    padding-left: 0;
    border-right: 0;
    border-bottom: 0;
  }
</style>
<div class="row ics-prompt-builder" ng-class="vm.promptSet.deviceType" ics-analytics-container record-text="true">

<!--left container-->
<div class="col-xs-3 left-container">
  <div class="ics-prompt-builder-left">

    <!--prompt set heading-->
    <div class="ics-prompt-set-heading">
      <input type="text" class="form-control" ng-model="vm.promptSet.name" ng-disabled="vm.readOnlyMode" ng-blur="vm.updatePromptSetName()">
    </div>
       <!--/Primary display-->
       <div>
         <div
           class="ics-primary-heading"
           ng-if="vm.auxResolutions"
         >
           <p class="primary-heading-text">Primary display</p>
           <p class="primary-heading-text linked-dropdown">Linked to</p>
         </div>
    <!--prompts tree-->
    <div class="ics-prompt-builder-prompts" ng-class="{'mainDiv': 'vm.auxResolutions' }">
      <div ng-repeat="prompt in vm.mainPrompts" class="ics-prompt-builder-state">
        <!--state-->
        <div class="ics-prompt-builder-prompt clearfix" ng-class="{'expanded': prompt.showAssignments}"
        ng-style="{ 'background-color' : !prompt.isEditable || vm.readOnlyMode ? 'lightgray' : ''}">
          <div class="icon-text-btn-group left-icon icon" ng-class="{'warn': prompt.isError}">
            <i class="fa text-grey-400" aria-hidden="true" ng-if="!prompt.secure"></i>
            <i class="fa fa-shield text-grey-400" aria-hidden="true"
               uib-tooltip="Signed prompts should be used for data entry screens and touch templates only"
               tooltip-placement="top-left"
               tooltip-append-to-body="true"
               ng-if="prompt.secure && vm.isSequoiaDevice( vm.promptSet.deviceType )"></i>
            <i class="fa fa-shield text-grey-400" aria-hidden="true"
               uib-tooltip="Signed prompts should be used for data entry screens only"
               tooltip-placement="top-left"
               tooltip-append-to-body="true"
               ng-if="prompt.secure && !vm.isSequoiaDevice( vm.promptSet.deviceType )"></i>
          </div>
          <div class="icon-text-btn-group middle-text font-bold" 
                ng-click="vm.handleSelectElement( null, prompt.assignments[0], prompt ); prompt.showAssignments = !prompt.showAssignments"
               ng-class="{'two-icons': (prompt.isChanged || prompt.hasUnsavedElement) &&(vm.dayparts.length !== prompt.assignments.length - 1), 'warn': prompt.isError}">
            <i class="fa fa-exclamation-circle" ng-if="prompt.isError"></i>
            {{::prompt.code | uppercase}}&nbsp;
            <span class="label label-grey txn-state-label">{{prompt.transactionState | capitalize}}</span>
          </div>           
               <div class="list-aux-prompts" ng-if="vm.auxResolutions">
                 <ui-select
                   id="linked-aux-prompt"
                   name="linked-aux-prompt"
                   ng-model="vm.linkedAuxPrompt[prompt.id]"
                   on-select="vm.changeLinkedPrompt(prompt, $item)"
                   search-enabled="false"
                   ng-disabled="vm.readOnlyMode || prompt.promptType ===  'pin'"
                 >
                 <ui-select-match placeholder="">{{
                  $select.selected.code
                }}</ui-select-match>
                <ui-select-choices repeat=" item in (prompt.promptType ===  'data' ? vm.standardAuxPromptChoices : vm.auxPromptsChoices)">
                  <span ng-bind="::item.code"
                        uib-tooltip="{{item.code}}" 
                        tooltip-placement="top-left"
                        data-tooltip-append-to-body="true"
                        data-tooltip-class="promptset-tooltip">
                  </span>
                   </ui-select-choices>
                 </ui-select>
               </div>
          <div ng-if="!vm.readOnlyMode && (prompt.isChanged || prompt.hasUnsavedElement)"
               class="icon-text-btn-group right-icon-btn icon"
               ng-class="{'two-icons': (prompt.isChanged || prompt.hasUnsavedElement) && (vm.dayparts.length !== prompt.assignments.length - 1)}">
            <i class="fa fa-floppy-o" aria-hidden="true" uib-tooltip="Save prompts" tooltip-placement="left"
               ng-if="prompt.isChanged"
               ng-click="vm.handleSavePrompts(prompt); $event.stopPropagation()"></i>
            <i class="fa fa-exclamation-circle" aria-hidden="true" uib-tooltip="An element inside needs saving"
               tooltip-placement="left" ng-if="prompt.hasUnsavedElement"></i>
          </div>
          <div class="btn-group gear-icon"
               uib-dropdown
               data-dropdown-append-to-body="true"
               ng-click="vm.getEditDeleteOptions(prompt)"
               data-stop-prop>
               <span uib-dropdown-toggle>
                <span class="fa gicon-settings" aria-hidden="true" ></span>
              </span>
              <ul uib-dropdown-menu role="menu" class="dropdown-menu-right" ng-if="!prompt.isEditable || vm.readOnlyMode">
                <li role="menuitem" ng-repeat="details in prompt.defaultDetails" 
                  uib-tooltip="{{ details === 'Edit prompt' || details === 'Delete prompt' ? 'Default prompts cannot be edited or deleted' : '' }}"
                  tooltip-placement="top-left"
                  data-tooltip-append-to-body="true"
                  data-tooltip-class="promptset-tooltip"
                ng-class="{'disabled': details === 'Edit prompt' || details === 'Delete prompt'}">
                  <a href="" ng-bind="details" ng-click="vm.actions[details](prompt)" ></a>
                </li>
              </ul>
              <ul uib-dropdown-menu role="menu" class="dropdown-menu-right" ng-if="prompt.isEditable && !vm.readOnlyMode">
                <li role="menuitem" ng-repeat="details in prompt.moreDetails">
                  <a href="" ng-bind="details" ng-click="vm.actions[details](prompt)"></a>
                </li>
              </ul>
          </div>
        </div>
        <!--/state-->

        <!--assignments-->
        <div uib-collapse="!prompt.showAssignments" class="ics-prompt-builder-assignments-container">
            <prompt-tree ng-if="prompt.showAssignments" assignment="assignment" ng-repeat="assignment in vm.mainGroupedAssignments[$index] track by $index"
                       promptset-languages="vm.promptsetLanguages"
                       default-font-settings="vm.defaultFontSettings"
                       language-keys-set="vm.languageKeysSet"
                       company-languages="vm.companyLanguages"
                       editable="!vm.readOnlyMode"
                       elements="assignment.flatElements"
                       default-background="vm.promptSet.bg"
                       default-font-color="vm.promptSet.fontColor"
                       on-select-element="vm.handleSelectElement( element, assignment, null )"
                       on-update-element="vm.handleUpdateElement( element )"
                       on-add="vm.handleCreateElement( assignment, element, index, parent )"
                       on-move-to="vm.handleMoveTo( from, to )"
                       on-delete="vm.handleRemoveElement( assignment, element, parent )"
                       device-type="vm.promptSet.deviceType"
                       screen-width="vm.currentDeviceType.screenWidth"
                       screen-height="vm.currentDeviceType.screenHeight"
                       on-state-change="vm.markStateAsChanged(prompt.id)"
                       on-touch-mask-change="vm.markTouchMaskAsChanged(assignment)"
                       on-touch-mask-save="vm.handleTouchMaskSave(assignment)"
                       on-delete-exception="vm.handleDeleteException($index, prompt)" style="width: 300px"
                       add-day-part="vm.addDayPart( prompt, assignment )">
          </prompt-tree>
        <!--/assignments-->
      </div>
    </div>
    </div>
         <!--/Auxiliary display-->
         <div ng-if="vm.auxResolutions">
           <div class="ics-primary-heading">
             <p class="primary-heading-text">Auxiliary display</p>
           </div>
           <!--prompts tree-->
           <div class="ics-prompt-builder-prompts" ng-class="{'auxDiv': 'vm.auxResolutions' }">
             <div ng-if="vm.auxPrompts.length < 1" class="no-prompt-display">
                No auxiliary prompts available</div>
             <div
               ng-repeat="prompt in vm.auxPrompts"
               class="ics-prompt-builder-state"
             >
               <!--state-->
               <div
                 class="ics-prompt-builder-prompt clearfix"
                 ng-class="{ expanded: prompt.showAssignments }"
                 ng-click="vm.handleSelectElement( null, prompt.assignments[0], prompt ); prompt.showAssignments = !prompt.showAssignments"
               >
                 <div
                   class="icon-text-btn-group left-icon icon"
                   ng-class="{ warn: prompt.isError }"
                 >
                   <i
                     class="fa text-grey-400"
                     aria-hidden="true"
                     ng-if="!prompt.secure"
                   ></i>
                   <i
                     class="fa fa-shield text-grey-400"
                     aria-hidden="true"
                     uib-tooltip="Signed prompts should be used for data entry screens and touch templates only"
                     tooltip-placement="top-left"
                     tooltip-append-to-body="true"
                     ng-if="
                       prompt.secure &&
                       vm.isSequoiaDevice(vm.promptSet.deviceType)
                     "
                   ></i>
                   <i
                     class="fa fa-shield text-grey-400"
                     aria-hidden="true"
                     uib-tooltip="Signed prompts should be used for data entry screens only"
                     tooltip-placement="top-left"
                     tooltip-append-to-body="true"
                     ng-if="
                       prompt.secure &&
                       !vm.isSequoiaDevice(vm.promptSet.deviceType)
                     "
                   ></i>
                 </div>
                 <div
                   class="icon-text-btn-group middle-text font-bold"
                   ng-class="{
                     'two-icons':
                       (prompt.isChanged || prompt.hasUnsavedElement) &&
                       vm.dayparts.length !== prompt.assignments.length - 1,
                     warn: prompt.isError
                   }"
                 >
                   <i
                     class="fa fa-exclamation-circle"
                     ng-if="prompt.isError"
                   ></i>
                   {{::prompt.code | uppercase}}&nbsp;
                   <span class="label label-grey txn-state-label">{{
                     prompt.transactionState | capitalize
                   }}</span>
                 </div>
                 <div
                   ng-if="!vm.readOnlyMode"
                   class="icon-text-btn-group right-icon-btn icon"
                   ng-class="{
                     'two-icons':
                       (prompt.isChanged || prompt.hasUnsavedElement) &&
                       vm.dayparts.length !== prompt.assignments.length - 1
                   }"
                 >
                   <i
                     class="fa fa-floppy-o"
                     aria-hidden="true"
                     uib-tooltip="Save prompts"
                     tooltip-placement="left"
                     ng-if="prompt.isChanged"
                     ng-click="vm.handleSavePrompts(prompt); $event.stopPropagation()"
                   ></i>
                   <i
                     class="fa fa-exclamation-circle"
                     aria-hidden="true"
                     uib-tooltip="An element inside needs saving"
                     tooltip-placement="left"
                     ng-if="prompt.hasUnsavedElement"
                   ></i>
                </div>
                <!-- start here -->
                <div class="btn-group gear-icon"
                uib-dropdown
                data-dropdown-append-to-body="true"
                ng-click="vm.getEditDeleteOptions(prompt)"
                data-stop-prop>
                <span uib-dropdown-toggle>
                 <span class="fa gicon-settings" aria-hidden="true" ng-disabled="prompt.isEditable" ng-class="{'disableSettingIcon':!prompt.isEditable || vm.readOnlyMode}"></span>
                </span>
                 <ul uib-dropdown-menu role="menu" class="dropdown-menu-right" ng-if="prompt.isEditable && !vm.readOnlyMode">
                 <li role="menuitem" ng-repeat="details in prompt.moreDetails">
                   <a href="" ng-bind="details" ng-click="vm.actions[details](prompt)"></a>
                 </li>
               </ul>
             </div>
                <!-- end here -->
               </div>
               <!--/state-->
 
               <!--assignments-->
               <div
                 uib-collapse="!prompt.showAssignments"
                 class="ics-prompt-builder-assignments-container"
               >
                 <prompt-tree
                   ng-if="prompt.showAssignments"
                   assignment="assignment"
                   ng-repeat="assignment in vm.auxGroupedAssignments[$index] track by $index"
                   promptset-languages="vm.promptsetLanguages"
                   default-font-settings="vm.defaultFontSettings"
                   language-keys-set="vm.languageKeysSet"
                   company-languages="vm.companyLanguages"
                   editable="!vm.readOnlyMode"
                   elements="assignment.flatElements"
                   default-background="vm.promptSet.bg"
                   default-font-color="vm.promptSet.fontColor"
                   on-select-element="
                     vm.handleSelectElement(element, assignment, null)
                   "
                   on-update-element="vm.handleUpdateElement(element)"
                   on-add="
                     vm.handleCreateElement(assignment, element, index, parent)
                   "
                   on-move-to="vm.handleMoveTo(from, to)"
                   on-delete="
                     vm.handleRemoveElement(assignment, element, parent)
                   "
                   device-type="vm.promptSet.deviceType"
                   screen-width="vm.currentDeviceType.screenWidth"
                   screen-height="vm.currentDeviceType.screenHeight"
                   on-state-change="vm.markStateAsChanged(prompt.id)"
                   on-touch-mask-change="vm.markTouchMaskAsChanged(assignment)"
                   on-touch-mask-save="vm.handleTouchMaskSave(assignment)"
                   on-delete-exception="vm.handleDeleteException($index, prompt)"
                   style="width: 300px"
                   add-day-part="vm.addDayPart( prompt, assignment )"
                 >
                 </prompt-tree>
                 <!--/assignments-->
               </div>
             </div>
           </div>
         </div>
         <!--prompts tree-->
       </div>
  </div>

  <!--save btn area-->
  <div ng-if="!vm.readOnlyMode" class="ics-prompt-builder-primary-btns">
    <button class="btn btn-primary btn-wide-xl text-uppercase" ng-click="vm.handleSavePromptSet()" ng-disabled="vm.isSaving">
      <i class="fa fa-spinner fa-pulse fa-fw ics-packages-loader" ng-show="vm.isSaving"></i>
      Save</button>
      <button class="btn btn-secondary btn-wide-lg text-uppercase" ng-click="vm.handleAddPrompt()" ng-disabled="false">
        New Prompt <i class="fa fa-plus" aria-hidden="true"></i>
      </button>
  </div>
  <!--/save btn area-->

  <!--approve / reject btns area-->
  <div ng-if="vm.approvalEnabled && vm.promptSet.status == 'FOR_APPROVAL'" class="ics-prompt-builder-primary-btns">
    <button class="btn btn-primary text-uppercase mr5" ng-click="vm.approveRejectPromptSet(true)"
            ng-disabled="vm.isApproving || vm.isRejecting || vm.approved">
      <i class="fa fa-spinner fa-pulse fa-fw ics-packages-loader mr5" ng-show="vm.isApproving"></i>Approve</button>
    <button class="btn btn-link btn-link-danger text-uppercase" ng-click="vm.approveRejectPromptSet(false)"
            ng-disabled="vm.isApproving || vm.isRejecting">
      <i class="fa fa-spinner fa-pulse fa-fw ics-packages-loader mr5" ng-show="vm.isRejecting"></i>Reject</button>
  </div>
  <!--/approve / reject btns area-->

</div>
<!--/left container-->

<!--right container-->
<div class="col-xs-9 right-container">
  <div class="ics-prompt-builder-right">

    <!--controls-->
    <div class="ics-prompt-builder-controls-container">
        <prompt-controls selected-element="vm.selectedElement" on-update-element="vm.handleUpdateElement( element )"
                      editable="!vm.readOnlyMode" on-touch-mask-change="vm.markTouchMaskAsChanged(vm.assignment)"
                      on-state-change="vm.markStateAsChanged(vm.assignment.parentId)"
                      assignment="vm.assignment" on-save-element="vm.handleSaveElement( element )"
                      selected-state="vm.selectedState"
                      device-type="vm.currentDeviceType"
                      on-reset-element="vm.handleResetElement( element )"
                      fonts="vm.fonts"
                      promptset-languages="vm.promptsetLanguages"
                      language-keys-set="vm.languageKeysSet"></prompt-controls>
    </div>
    <!--/controls-->

    <!--preview area-->
    <div class="ics-prompt-builder-preview-wrapper" ng-class="{'expanded': vm.isRightContracted}">

      <!--soft keys left-->
      <div class="ics-soft-key-holder g7-keys left pull-left visible" ng-class="{'hidden': !vm.enableSoftkey()}">
        <div class="ics-soft-key-wrapper" ng-repeat="softKey in vm.assignment.softkeys | filter: {side: 'LEFT'}">
          <button dynamic-soft-key
                  side="softKey.side"
                  offset="softKey.offset"
                  resolution-height="800"
                  trigger="vm.isRightContracted"
                  ng-disabled="!vm.assignment.softKeysAllowed || !vm.isDesigner || vm.readOnlyMode"
                  class="btn btn-gray active ics-soft-key"
                  ng-click="vm.handleSoftKey(softKey, vm.assignment.parentId)">
            <hr/>
            <i class="fa fa-chevron-right" aria-hidden="true"></i></button>
          <p dynamic-key-code-label
             offset="softKey.offset"
             resolution-height="800"
             class="ics-soft-key-label"
             trigger="vm.isRightContracted"
             ng-if="softKey.label">{{softKey.label}}
          </p>
        </div>
      </div>
      <!--/soft keys left-->

      <!--soft keys right-->
      <div class="ics-soft-key-holder g7-keys right pull-right visible" ng-class="{'hidden': !vm.enableSoftkey()}">
        <div class="ics-soft-key-wrapper" ng-repeat="softKey in vm.assignment.softkeys | filter: {side: 'RIGHT'}">
          <button dynamic-soft-key
                  side="softKey.side"
                  offset="softKey.offset"
                  resolution-height="800"
                  trigger="vm.isRightContracted"
                  ng-disabled="!vm.assignment.softKeysAllowed || !vm.isDesigner || vm.readOnlyMode"
                  class="btn btn-gray active ics-soft-key"
                  ng-click="vm.handleSoftKey(softKey, vm.assignment.parentId)">
            <hr/>
            <i class="fa fa-chevron-left" aria-hidden="true"></i></button>
          <p dynamic-key-code-label
             offset="softKey.offset"
             resolution-height="800"
             class="ics-soft-key-label"
             trigger="vm.isRightContracted"
             ng-if="softKey.label">{{softKey.label}}
          </p>
        </div>
      </div>
      <!--/soft keys right-->

      <!--preview-->
      <div class="ics-prompt-builder-preview-container" ng-class="vm.promptSet.deviceType">

        <prompt-builder elements="vm.assignment.flatElements" prompt-set="vm.promptSet" on-select-element="vm.handleSelectElement"
                        device-type="vm.currentDeviceType" selected-element="vm.selectedElement"
                        show-grid="vm.isGridShown"
                        show-playlist="vm.isPlaylistShown"
                        class="ics-prompt-builder" ng-class="{ 'read-only': vm.readOnlyMode }"></prompt-builder>

        <!--soft keys bottom-->
        <div class="ics-soft-key-holder g6-keys bottom visible" ng-class="{'hidden': !vm.enableSoftkey()}">
          <div class="ics-soft-key-wrapper" ng-repeat="softKey in vm.assignment.softkeys | filter: {side: 'BOTTOM'}">
            <button
              class="btn btn-gray active"
              ng-click="vm.handleSoftKey(softKey, vm.assignment.parentId)"
              ng-disabled="!vm.assignment.softKeysAllowed || !vm.isDesigner || vm.readOnlyMode">
              <i class="fa fa-window-minimize fa-rotate-90" aria-hidden="true"></i>
            </button>
            <p>{{softKey.label}}</p>
          </div>
        </div>
        <!--/soft keys bottom-->

      </div>
      <!--/preview-->


    </div>
    <!--/preview area-->

    <!--additional options / metadata-->
    <div class="ics-prompt-builder-metadata" ng-class="{'contracted': vm.isRightContracted}">

      <div class="btn-grid top">
        <button ng-if="!vm.isIE" class="btn btn-primary" ng-click="vm.goFullscreen()"
                uib-tooltip="Fullscreen mode" tooltip-placement="bottom-left">
          <i class="fa fa-arrows-alt" aria-hidden="true"></i>
        </button>
        <button class="btn btn-primary" ng-click="vm.viewAsODML()" uib-tooltip="View as ODML"
                tooltip-placement="bottom-left">
          <i class="fa fa-code" aria-hidden="true"></i>
        </button>
        <button ng-if="!vm.isGridShown" class="btn btn-primary" ng-click="vm.isGridShown = !vm.isGridShown"
                uib-tooltip="Show grid" tooltip-placement="bottom-left">
          <i class="fa fa-th" aria-hidden="true"></i>
        </button>
        <button ng-if="vm.isGridShown" class="btn btn-primary active" ng-click="vm.isGridShown = !vm.isGridShown"
                uib-tooltip="Hide grid" tooltip-placement="bottom-left">
          <i class="fa fa-th" aria-hidden="true"></i>
        </button>
        <button ng-if="!vm.isPlaylistShown && vm.enablePlaylistButton"
                class="btn btn-primary"
                ng-click="vm.isPlaylistShown = !vm.isPlaylistShown"
                uib-tooltip="Show playlist guide"
                tooltip-placement="bottom-left">
          <i class="fa fa-play-circle"></i>
        </button>
        <button ng-if="vm.isPlaylistShown && vm.enablePlaylistButton"
                class="btn btn-primary active"
                ng-click="vm.isPlaylistShown = !vm.isPlaylistShown"
                uib-tooltip="Hide playlist guide"
                tooltip-placement="bottom-left">
          <i class="fa fa-play-circle"></i>
        </button>
        <hr>
      </div>

      <div class="right-border"></div>

      <div ng-if="vm.promptSet" class="ics-prompt-builder-metadata-wrapper">

        <div class="ics-prompt-builder-meta-text">

          <p class="font-bold">Created by</p>
          <p class="mb5">{{vm.promptSet.createdBy.name}}</p>
          <p class="small text-grey-600 word-break">{{vm.promptSet.createdBy.email}}</p>
          <hr class="separator">

          <p class="font-bold">Created at</p>
          <p>{{vm.promptSet.created | amDateFormat: 'MMM D, YYYY [at] h:mm a (UTCZ)'}}</p>
          <hr class="separator">

          <div ng-if="vm.promptSet.created != vm.promptSet.modified">
            <p class="font-bold">Last modified by</p>
            <p class="mb5">{{vm.promptSet.modifiedBy.name}}</p>
            <p class="small text-grey-600 word-break">{{vm.promptSet.modifiedBy.email}}</p>
            <hr class="separator">
          </div>

          <div ng-if="vm.promptSet.created != vm.promptSet.modified">
            <p class="font-bold">Modified at</p>
            <p>{{vm.promptSet.modified | amDateFormat: 'MMM D, YYYY [at] h:mm a (UTCZ)'}}</p>
            <hr class="separator">
          </div>

          <div ng-if="vm.estimatedSize">
              <p class="label ics-estimate-label font-size-12"
                 ng-class="{ 'label-blue': vm.estimatedSize < 524288000 , 'label-danger': vm.estimatedSize >= 524288000, 'opacity0': vm.estimatedSize == 0 }">
              Estimated prompt set size: {{vm.estimatedSize | fileSize}}
            </p>
            <p class="small mt10 text-grey-500">
                Preview generation might fail if the size is greater than 500 MB
            </p>
            <hr class="separator" />
          </div>

          <p class="font-bold">Template</p>
          <p>{{vm.promptSet.template.name}}</p>
          <hr class="separator">

          <p class="font-bold">Version</p>
          <p>{{vm.promptSet.version}}</p>
          <p ng-if="(vm.promptSet.secureFingerprint || vm.promptSet.nonSecureFingerprint)" class="font-bold mt10">Fingerprint</p>
          <p ng-if="(vm.promptSet.secureFingerprint || vm.promptSet.nonSecureFingerprint)">
            <span ng-if="vm.promptSet.secureFingerprint">S:{{vm.promptSet.secureFingerprint.slice(0,8)}}</span>
            <span ng-if="vm.promptSet.nonSecureFingerprint">NS:{{vm.promptSet.nonSecureFingerprint.slice(0,8)}}</span>
          </p>
           <!-- information of dual display -->
             <div ng-if="vm.auxResolutions">
               <hr class="separator">
               <p class="font-bold">Devices</p>
               <p class="device-name"><span>Main display:</span>
                 <span>15" (external UD15)</span>
               </p>
               <p class="device-name"><span>Auxiliary display:</span>
                 <span >5" (G6-500 internal)</span>
               </p>
             </div>
             <!-- End of dual display information -->
          <hr class="separator">

          <div class="ics-prompt-builder-left default-colors-container">
            <!--prompt set master bg-->
            <p class="font-bold">Default colors</p>
            <div ng-if="!vm.readOnlyMode" class="ics-prompt-builder-default-options clearfix">
              <div class="btn btn-primary default-options-btn"
                  ng-class="{ 'no-pointer-events': vm.isFontColorUpdating }"
                  ng-disabled="vm.isFontColorUpdating">
                <span>Font Color <i ng-show="vm.isFontColorUpdating" class="fa fa-spinner fa-pulse fa-fw"></i></span>
                <div gf-color-picker ng-model="vm.promptSet.fontColor" options="vm.fontColorPickerOptions" on-change="vm.updateFontColor"></div>
              </div>
              <div class="btn btn-primary default-options-btn" ng-click="vm.canEditBG = !vm.canEditBG; $event.stopPropagation()">
                Background
                <i class="fa" aria-hidden="true"
                  ng-class="vm.canEditBG ? 'fa-chevron-circle-up' : 'fa-chevron-circle-down'"></i>
              </div>
              <background-picker ng-show="vm.canEditBG" value="vm.promptSet.bg"></background-picker>

              <div class="btn default-options-btn" ng-if="vm.companyLanguages.length === 0" ng-click="vm.updateFont()" ng-class="{ 'no-pointer-events': vm.isFontSettingUpdating, 'btn-dark' : !vm.isDefaultFontSettingsAvail, 'btn-primary' : vm.isDefaultFontSettingsAvail }" ng-disabled="vm.isFontSettingUpdating">
                <span>Font
                <i ng-show="vm.isFontSettingUpdating" class="fa fa-spinner fa-pulse fa-fw"></i>
                </span>
              </div>

            </div>
            <!--/prompt set master bg-->
            <hr class="separator">
          </div>

          <!--Language list-->
          <div ng-if="vm.companyLanguages.length !== 0">
            <p class="font-bold">Languages and default fonts</p>
            <div ng-repeat="isoCode in vm.languageKeysSet track by $index">
              <div class="row small">
                <div class="col-md-4">
                  {{vm.promptSet.lang[isoCode].language}}
                  <span ng-if="vm.promptSet.lang[isoCode].promptSetLanguageSupport.default"> (default)</span>
                </div>
                <div class="col-md-6">{{vm.promptSet.lang[isoCode].promptSetLanguageSupport.type || ''}}</div>
                <div class="col-md-2">{{vm.promptSet.lang[isoCode].promptSetLanguageSupport.size || ''}}</div>
              </div>
            </div>
            <div class="ics-prompt-builder-left default-colors-container">
              <div ng-if="!vm.readOnlyMode" class="ics-prompt-builder-default-options clearfix">
                <div class="btn btn-primary default-options-btn" ng-click="vm.handlePromptLanguageEdit()">
                  <span>Edit</span>
                </div>
              </div>
            </div>
            <hr class="separator">
          </div>
          <!--Language list-->

        </div>

        <div class="btn-grid bottom">
          <button class="btn btn-primary right-toggle-btn" ng-if="vm.isRightContracted"
                  ng-click="vm.toggleMetadataPanel()" uib-tooltip="Show metadata">
            <i class="fa fa-angle-double-left" aria-hidden="true"></i>
          </button>
          <button class="btn btn-primary right-toggle-btn" ng-if="!vm.isRightContracted"
                  ng-click="vm.toggleMetadataPanel()" uib-tooltip="Hide metadata">
            <i class="fa fa-angle-double-right" aria-hidden="true"></i>
          </button>
          <button ng-if="!vm.isIE" class="btn btn-primary" ng-click="vm.goFullscreen()" uib-tooltip="Fullscreen mode">
            <i class="fa fa-arrows-alt" aria-hidden="true"></i>
          </button>
          <button class="btn btn-primary" uib-tooltip="View as ODML" ng-click="vm.viewAsODML()">
            <i class="fa fa-code" aria-hidden="true"></i>
          </button>
          <button ng-if="!vm.isGridShown" class="btn btn-primary" ng-click="vm.isGridShown = !vm.isGridShown" uib-tooltip="Show grid">
            <i class="fa fa-th" aria-hidden="true"></i>
          </button>
          <button ng-if="vm.isGridShown" class="btn btn-primary active" ng-click="vm.isGridShown = !vm.isGridShown" uib-tooltip="Hide grid">
            <i class="fa fa-th" aria-hidden="true"></i>
          </button>
          <button ng-if="!vm.isPlaylistShown && vm.enablePlaylistButton"
                  class="btn btn-primary"
                  ng-click="vm.isPlaylistShown = !vm.isPlaylistShown"
                  uib-tooltip="Show playlist guide">
            <i class="fa fa-play-circle"></i>
          </button>
          <button ng-if="vm.isPlaylistShown && vm.enablePlaylistButton"
                  class="btn btn-primary active"
                  ng-click="vm.isPlaylistShown = !vm.isPlaylistShown"
                  uib-tooltip="Hide playlist guide">
            <i class="fa fa-play-circle"></i>
          </button>
        </div>

      </div>

    </div>
    <!--/additional options / metadata-->

  </div>
</div>
<!--/right container-->

</div>