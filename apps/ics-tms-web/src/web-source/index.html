<!DOCTYPE html>
<html lang="en" ics-prevent-drop ng-controller="AppCtrl" class="{{$state.current.data.viewClass}}">

<head>
 
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, shrink-to-fit=no">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title ng-bind="$state.current.data.titleTag + ' | ' + adminInstance.productName"></title>
  <base href="/">
  <link rel="dns-prefetch" href="//s3.amazonaws.com">
  <link rel="dns-prefetch" href="//maps.googleapis.com">
  <link rel="shortcut icon" href="assets/img/favicon/favicon.ico">
  <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <!-- build:css assets/css/ics-bundle.css -->
  <link rel="stylesheet" href="assets/css/ics-bundle.css">
  <!-- endbuild -->
  <script id="newRelic"></script>
  <!-- build:js assets/js/modernizr-ics.js -->
  <script src="assets/js/modernizr-ics.js"></script>
  <script src="assets/js/modernizr-extended-ics.js"></script>
  
  <!-- endbuild -->
  

  <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.5.0/sql-wasm.min.js" integrity="sha512-l5XgljO54rARJoeqQoY4w0sAJVFd/0GVSvFNtr9YSCSEe+M7Lg0tDw7WQg1J06Mr0/2f9M6ExdHBChwxWammKA==" crossorigin="anonymous"></script>
  <script src="/assets/js/bundle.js"></script>
</head>

<body dir="ltr">
  <div ng-cloak class="ng-cloak" ui-view></div>

  <div ng-if="::false">
    <div class="ics-file-uploader-backdrop">
      <div class="ics-file-uploader">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="25 25 50 50" aria-labelledby="indexLoading" role="img">
          <title id="indexLoading">Loading</title>
          <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="5" stroke-miterlimit="10" />
        </svg>
      </div>
    </div>
  </div>
</div>
<!--[if lt IE 10]><iframe title="Update Your Browser" class="browserFrame" src="notsupported.html"></iframe><![endif]-->
<!-- build:js assets/js/vendor.js -->
<script src="assets/js/polyfills/promise.js"></script>
<script src="assets/js/overthrow.js"></script>
<script src="assets/js/moment/moment.js"></script>
<script src="assets/js/lodash.js"></script>
<script src="assets/js/lodash-mixins.js"></script>
<script src="assets/js/tinycolor.js"></script>
<script src="assets/js/immutable.js"></script>
<script src="assets/js/jquery-3.6.0.min.js"></script>
<script src="assets/js/snap.svg-min.js"></script>
<script src="../../node_modules/angular/angular.js"></script>
<script src="assets/js/chart.js/Chart.js"></script>
<script src="assets/js/angular-chart/angular-chart.js"></script>
<script src="assets/js/angular-deferred-bootstrap.js"></script>
<script src="assets/js/angular-drag-and-drop-lists.min.js"></script>
<script src="assets/js/ui-select-master/select-grofly-custom-new.js"></script>
<script src="assets/js/filesaver.js"></script>
<script src="assets/js/rz-slider/slider.js"></script>
<script src="assets/js/moment/moment-range.js"></script>
<script src="assets/js/datetimepicker.js"></script>
<script src="assets/js/angularjs-google-maps-master/ng-map.js"></script>
<script src="assets/js/match-media.js"></script>
<script src="assets/js/scrollpoint.js"></script>
<script src="assets/js/in-viewport.js"></script>
<script src="assets/js/loading-bar.js"></script>
<script src="assets/js/angular-shims-placeholder.js"></script>
<script src="assets/js/ng-file-upload/ng-file-upload-all.js"></script>
<script src="assets/js/autocomplete.js"></script>
<script src="assets/js/angular-fullscreen.js"></script>
<script src="assets/js/angular-input-modified.js"></script>
<script src="assets/js/angular-smooth-scroll.js"></script>
<script src="assets/js/featureflags.js"></script>
<script src="assets/js/angular-avatar.js"></script>
<script src="assets/js/angular-bootstrap-toggle.js"></script>
<script src="assets/js/angular-lazy-img.js"></script>
<script src="assets/js/ng-infinite-scroll.js"></script>
<script src="assets/js/elastic.js"></script>
<script src="assets/js/analytics/angulartics.js"></script>
<script src="assets/js/analytics/angulartics-ga.js"></script>
<script src="assets/js/ics-analytics/ics-analytics.js"></script>
<!-- endbuild -->
<!-- build:js assets/js/ics-core.js -->
<script src="common/ics-core.js"></script>
<script src="common/services/authInterceptor.service.js"></script>
<script src="common/app.controller.js"></script>
<!-- @if NODE_ENV='staging' -->
<script src="common/constants/api/preview.js"></script>
<script src="common/constants/logging/staging.js"></script>
<script src="common/constants/debug/staging.js"></script>
<!-- @endif -->
<!-- @if NODE_ENV='production' -->
<script src="common/constants/api/preview.js"></script>
<script src="common/constants/logging/production.js"></script>
<script src="common/constants/debug/production.js"></script>
<!-- @endif -->
<script src="common/services/utils.service.js"></script>
<script src="common/services/auth.service.js"></script>
<script src="common/services/common.services.js"></script>
<script src="common/services/externalReferenceTypes.service.js"></script>
<script src="common/services/sites.services.js"></script>
<script src="common/services/siteGroups.services.js"></script>
<script src="common/services/software.service.js"></script>
<script src="common/services/product-type.service.js"></script>
<script src="common/services/rollouts.service.js"></script>
<script src="common/services/tags.service.js"></script>
<script src="common/services/devices.service.js"></script>
<script src="common/services/jobs.service.js"></script>
<script src="common/services/alarms.service.js"></script>
<script src="common/services/analytics.service.js"></script>
<script src="common/services/notifications.service.js"></script>
<script src="common/services/offlinePackages.service.js"></script>
<script src="common/services/playlists.service.js"></script>
<script src="common/services/users.service.js"></script>
<script src="common/services/userGroups.service.js"></script>
<script src="common/services/fileuploadrequest.service.js"></script>
<script src="common/services/rki.service.js"></script>
<script src="common/services/nameParse.service.js"></script>
<script src="common/services/company.service.js"></script>
<script src="common/services/releaseNotes.service.js"></script>
<script src="common/services/reporting.services.js"></script>
<script src="common/services/media.service.js"></script>
<script src="common/services/prompt-templates.service.js"></script>
<script src="common/services/docs.service.js"></script>
<script src="common/directives/general/general-directive.js"></script>
<script src="common/directives/navbar/navbar-directive.js"></script>
<script src="common/directives/navbarSidebar/navbar-sidebar-directive.js"></script>
<script src="common/directives/singleSelectTree/single-select-tree-directive.js"></script>
<script src="common/directives/remoteMgmtStatus/remoteMgmtStatus-directive.js"></script>
<script src="common/directives/responsivePrompt/responsive-prompt-directive.js"></script>
<script src="common/directives/lazyLoadingSingleSelectTree/lazy-loading-single-select-tree-directive.js"></script>
<script src="common/directives/loadingMask/ics-loading-mask-directive.js"></script>
<script src="common/directives/globalSearch/global-search-directive.js"></script>
<script src="common/directives/htmlPreview/html-preview-directive.js"></script>
<script src="common/directives/htmlRenderedPreview/html-rendered-preview-directive.js"></script>
<script src="common/directives/statusLabel/status-label-directive.js"></script>
<script src="common/directives/activityStream/activity-stream-directive.js"></script>
<script src="common/directives/navAside/nav-aside-directive.js"></script>
<script src="common/directives/dualListSelect/dual-list-select-directive.js"></script>
<script src="common/directives/dynamicKeyCodeLabel/dynamic-key-code-label-directive.js"></script>
<script src="common/directives/dynamicSoftKey/dynamic-soft-key-directive.js"></script>
<script src="common/directives/dynamicSoftKeyLabel/dynamic-soft-key-label-directive.js"></script>
<script src="common/directives/dateRangePicker/date-range-picker-directive.js"></script>
<script src="common/directives/uniqueValueChecks/unique-site-name-directive.js"></script>
<script src="common/directives/uniqueValueChecks/password-validator-directive.js"></script>
<script src="common/directives/uniqueValueChecks/unique-email-validator-directive.js"></script>
<script src="common/directives/footerLinks/footer-links-directive.js"></script>
<script src="common/directives/password-reset/password-reset-directive.js"></script>
<script src="common/directives/two-factor-auth/two-factor-auth-directive.js"></script>
<script src="common/directives/modalPrompt/modal-prompt-service.js"></script>
<script src="common/directives/modalPrompt/modal-prompt-controller.js"></script>
<script src="common/directives/rkiStatusLabel/rki-status-label-directive.js"></script>
<script src="common/directives/multiSiteSelectDrop/multi-site-select-directive.js"></script>
<script src="common/directives/multiSelectList/multi-select-list.module.js"></script>
<script src="common/directives/filtersMultiSelect/filters-multi-select.module.js"></script>
<script src="common/directives/siteDeviceListItems/siteListItem-directive.js"></script>
<script src="common/directives/siteDeviceListItems/deviceListItem-directive.js"></script>
<script src="common/directives/uniqueValueChecks/unique-file-name-directive.js"></script>
<script src="common/directives/pagination/pagination.directive.js"></script>
<script src="common/directives/imageFill/image-fill-directive.js"></script>
<script src="common/directives/gfColorPicker/gf-color-picker.js"></script>
<script src="common/directives/prettyRequestUrl/pretty-request-url-directive.js"></script>
<script src="common/directives/onInput/on-input-directive.js"></script>
<script src="common/directives/roundUp/round-up-directive.js"></script>
<!-- not using in MVP <script src="common/directives/advancedSearch/report-filter-directive.js"></script> -->
<script src="common/directives/userTemplate/user.template.directive.js"></script>
<script src="common/directives/angular-selectize/angular-selectize.module.js"></script>
<script src="common/filters/filters.js"></script>
<script src="moduleLogin/login.module.js"></script>
<script src="moduleLogin/login.controllers.js"></script>
<script src="moduleMigration/migration.module.js"></script>
<script src="moduleSearch/searchResults.module.js"></script>
<script src="moduleSearch/searchResults.controllers.js"></script>
<script src="moduleMedia/media.module.js"></script>
<script src="moduleMedia/media.controllers.js"></script>
<script src="moduleDevices/device-list.module.js"></script>
<script src="moduleDevices/device-list.controllers.js"></script>
<script src="moduleRemoteMgmt/components/bulk-actions-component.js"></script>
<script src="moduleSites/sites.module.js"></script>
<script src="moduleSites/sites.controllers.js"></script>
<script src="moduleSites/controllers/pull-files-controller.js"></script>
<script src="moduleSites/controllers/copy-files-controller.js"></script>
<script src="moduleSites/controllers/challenge-response-controller.js"></script>
<script src="moduleSites/controllers/device-add-modal-controller.js"></script>
<script src="moduleSites/controllers/device-move-modal-controller.js"></script>
<script src="moduleSites/controllers/schedule-modal-controller.js"></script>
<script src="moduleSites/controllers/device-swap-modal-controller.js"></script>
<script src="moduleSites/controllers/edit-cagent-file-modal-controller.js"></script>
<script src="moduleSites/controllers/upload-cagent-device-file-modal-controller.js"></script>
<script src="moduleSites/controllers/device-overview-controller.js"></script>
<script src="moduleSites/directives/site-details-form-directive.js"></script>
<script src="moduleSites/directives/store-hour-selector-directive.js"></script>
<script src="moduleSites/directives/string-to-number.js"></script>
<script src="moduleRKI/rki.module.js"></script>
<script src="moduleRKI/rki.controllers.js"></script>
<script src="moduleRKI/directives/rki-request-directive.js"></script>
<script src="moduleRemoteMgmt/remote.mgmt.module.js"></script>
<script src="moduleRemoteMgmt/remote.mgmt.controllers.js"></script>
<script src="moduleRemoteMgmt/directives/create-file-download-directive.js"></script>
<script src="moduleSettingsCompany/settings.company.module.js"></script>
<script src="moduleSettingsCompany/settings.company.controllers.js"></script>
<script src="moduleSettingsCompany/people/people.controller.js"></script>
<script src="moduleSettingsCompany/teams/teams.controllers.js"></script>
<script src="moduleSettingsCompany/alarms/alarms.controllers.js"></script>
<script src="moduleSettingsCompany/siteGroups/siteGroups.controllers.js"></script>
<script src="moduleSettingsCompany/directives/edit-add-user-directive.js"></script>
<script src="moduleSettingsCompany/directives/alarm-editor.js"></script>
<script src="moduleSettingsCompany/directives/siteGroup-form.js"></script>
<script src="moduleSettingsCompany/directives/team-form-directive.js"></script>
<script src="moduleSettingsCompany/media/media.controller.js"></script>
<script src="moduleSettingsCompany/siteTagManagement/siteTagManagement.controller.js"></script>
<script src="moduleSettingsCompany/importSites/importSites.controller.js"></script>
<script src="moduleSettingsUser/settings.user.module.js"></script>
<script src="moduleSettingsUser/settings.user.controllers.js"></script>
<script src="moduleMedia/media.module.js"></script>
<script src="moduleMedia/media.controllers.js"></script>
<script src="moduleMedia/components/assignment/assignment.component.js"></script>
<script src="moduleMedia/components/assignmentState/assignment-state.component.js"></script>
<script src="moduleMedia/components/backgroundPicker/background-picker.component.js"></script>
<script src="moduleMedia/components/library/library.component.js"></script>
<script src="moduleMedia/components/libraryLightBox/library-lightbox.component.js"></script>
<script src="moduleMedia/components/libraryAsset/library-asset.component.js"></script>
<script src="moduleMedia/components/libraryPromptSet/library-prompt-set.component.js"></script>
<script src="moduleMedia/components/promptSet/prompt-set.component.js"></script>
<script src="moduleMedia/components/search/search.component.js"></script>
<script src="moduleMedia/components/select/select-filters.component.js"></script>
<script src="moduleMedia/components/uploadMedia/media-upload-btn.component.js"></script>
<script src="moduleMedia/components/importPromptSet/prompt-set-import-btn.component.js"></script>
<script src="moduleMedia/components/selectMediaModal/select-media-modal.component.js"></script>
<script src="moduleMedia/components/promptSetCreate/prompt-set-create.component.js"></script>
<script src="moduleMedia/components/promptSetsList/prompt-sets.component.js"></script>
<script src="moduleMedia/components/promptSetsList/prompt-sets-list.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/prompt-builder.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/builder/prompt-builder.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/builder/wrapped-text.snap.js"></script>
<script src="moduleMedia/components/promptBuilder2/builder/text.snap.js"></script>
<script src="moduleMedia/components/promptBuilder2/builder/input.snap.js"></script>
<script src="moduleMedia/components/promptBuilder2/builder/area.snap.js"></script>
<script src="moduleMedia/components/promptBuilder2/controls/area/prompt-area-control.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/controls/background/prompt-bg-control.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/controls/image/prompt-image-control.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/controls/video/prompt-video-control-components.js"></script>
<script src="moduleMedia/components/promptBuilder2/controls/text/prompt-text-control.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/controls/prompt-controls.component.js"></script>
<script src="moduleMedia/components/promptBuilder2/tree/prompt-tree.component.js"></script>
<!-- <script src="modulePlaylists/playlists.module.js"></script> -->
<script src="moduleReporting/reporting.module.js"></script>
<script src="moduleReporting/reporting.controllers.js"></script>
<script src="moduleReporting/reports/api-audit/api-audit.controller.js"></script>
<script src="moduleReporting/reports/api-audit/api-audit.service.js"></script>
<script src="moduleReporting/report-directive/report-directive.js"></script>
<script src="moduleReleaseNotes/release-notes.module.js"></script>
<script src="moduleReleaseNotes/release-notes.controller.js"></script>
<script src="templates.js"></script>
<script src="common/startup/ics-bootstrap.js"></script>



<!-- inject:adminInstance:js -->
<!-- endinject -->
<script src="/assets/js/bundle.js" defer></script>
<!-- endbuild -->
<!-- inject:analytics -->
<!-- endinject -->




</body>

</html>
