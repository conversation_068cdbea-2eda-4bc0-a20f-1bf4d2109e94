import angular from 'angular'; // eslint-disable-line import/no-extraneous-dependencies
import uiRouter from '@uirouter/angularjs';
import CommonModule from '../common/common-module';
import bulkMoveModalComponent from './components/bulk-move-modal-component';

import DeviceListController from './device-list.controllers';

export default angular
    .module( 'devicesModule', [ uiRouter, CommonModule ] )
    .component( 'bulkMoveModal', bulkMoveModalComponent )
    .controller( 'DeviceListController', DeviceListController )
    .config( ( $stateProvider ) => {
        'ngInject';
    } )
 
    ;
