(function() {
    'use strict';

    angular
        .module( 'companySettingsModule' )
        .controller( 'CompanySettingsCtrl', CompanySettingsCtrl );

    /*@ngInject*/
    function CompanySettingsCtrl( $scope, $rootScope, authService ) {

        $scope.settingsFlags = angular.fromJson( $rootScope.getFeatureFlags() );

        if ( $scope.settingsFlags ) {
            // If MEDIA_MANAGEMENT or PLAYLIST MANAGEMENT is active then show Media settings
            $scope.isMediaAllowed = _.find( $scope.settingsFlags, function( flag ) {
                return ( flag.key === 'mediaMgmt' && flag.active ) ||
                    ( flag.key === 'playlistMgmt' && flag.active );
            } );
        } else {
            $scope.isMediaAllowed = false;
        }

        $scope.companySettingNavItems = [
            {
                id: 1,
                navText: 'Alarms',
                navState: 'alarmsList',
                isAllowed: authService.isAllowedAccess( 'WRITE_ALARMS' )
            },
            {
                id: 2,
                navText: 'People',
                navState: 'companySettings.people',
                isAllowed: authService.isAllowedAccess( 'WRITE_PEOPLE' )
            },
            {
                id: 3,
                navText: 'Teams',
                navState: 'companySettings.teams',
                isAllowed: authService.isAllowedAccess( 'WRITE_TEAMS' )
            },
            {
                id: 4,
                navText: 'Site Groups',
                navState: 'companySettings.siteGroups',
                isAllowed: authService.isAllowedAccess( 'WRITE_SITE_GROUPS' )
            },
            {
                id: 5,
                navText: 'Media',
                navState: 'mediaSettings',
                isAllowed: canSeeMedia()
            },
            {
                id: 6,
                navText: 'Site Tag Management',
                navState: 'companySettings.siteTagManagement',
                isAllowed: authService.isAllowedAccess('VIEW_COMPANY_SETTINGS')
            },
            {
                id: 7,
                navText: 'Import Sites',
                navState: 'companySettings.importSites',
                isAllowed: authService.isAllowedAccess('VIEW_COMPANY_SETTINGS')
            }
        ];

        // TODO: Need to update authService with correct right name.
        $scope.canUserEdit = authService.isAllowedAccess( 'WRITE_ALARMS' );

        function canSeeMedia() {
            if ( $scope.isMediaAllowed ) {
                return ( authService.isAllowedAccess( 'WRITE_MEDIA_SETTINGS' ) || authService.isAllowedAccess( 'VIEW_MEDIA_SETTINGS' ) );
            }
            return false;
        }
    }
})();
