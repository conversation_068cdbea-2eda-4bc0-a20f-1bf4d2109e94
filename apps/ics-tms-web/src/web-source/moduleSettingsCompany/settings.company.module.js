import angular from 'angular'; // eslint-disable-line import/no-extraneous-dependencies
import uiRouter from '@uirouter/angularjs';

import CommonModule from '../common/common-module';

export default angular
    .module( 'companySettingsModule', [uiRouter, CommonModule] )
    .config( ( $stateProvider ) => {
        'ngInject';

        const states = [];

        return _.forEach( states, ( state ) => {
            $stateProvider.state( state );
        } );
    } )
    .name;

/* @ngInject */
function hasPermission( permission ) {
    /* @ngInject */
    function permissionCheck( $q, authService ) {
        if ( !authService.isAllowedAccess( permission ) ) {
            return goTo404( $q );
        }
        return true;
    }

    return permissionCheck;
}

function goTo404( q ) {
    return q.reject( { status: 404 } );
}

/* @ngInject */
function alarm( $stateParams, alarmsService ) {
    return alarmsService.getAlarm( $stateParams.id );
}

/* @ngInject */
function alarmList( alarmsService ) {
    return alarmsService.getAllAlarms();
}

/* @ngInject */
function alarmTriggers( alarmsService ) {
    return alarmsService.getAllTriggers();
}

/* @ngInject */
function getSitesSummary( sitesService, authService ) {
    return authService.self().then( ( result ) => sitesService.getSitesSummary( { company: result.company.id } ) );
}

/* @ngInject */
function allTags( tagsService ) {
    return tagsService.getAllTags( false, true );
}

/* @ngInject */
function availableTags( tagsService ) {
    return tagsService.getAllTags();
}

/* @ngInject */
function companyList( companyService ) {
    return companyService.getSuppliers();
}

/* @ngInject */
function peopleFiltered( usersService ) {
    return usersService.getAllUsers().then( data=>data.results );
}

/* @ngInject */
function roles( authService ) {
    return authService.getRoles();
}

/* @ngInject */
function self( authService ) {
    return authService.self( true );
}

/* @ngInject */
function siteGroup( siteGroupService, $stateParams ) {
    return siteGroupService.getSiteGroupById( $stateParams.id );
}

/* @ngInject */
function siteGroups( siteGroupService ) {
    return siteGroupService.getAllSiteGroups();
}

/* @ngInject */
function siteList( sitesService ) {
    return sitesService.getAllSites( 'prod', -1, 0 );
}

/* @ngInject */
function user( $stateParams, usersService ) {
    return usersService.getUserById( $stateParams.id );
}

/* @ngInject */
function userGroup( userGroupService, $stateParams ) {
    return userGroupService.getUserGroupById( $stateParams.id );
}

/* @ngInject */
function userGroups( userGroupService ) {
    return userGroupService.getAllUserGroups();
}

/* @ngInject */
function canEditMedia( authService ) {
    return authService.isAllowedAccess( 'WRITE_MEDIA_SETTINGS' );
}

/* @ngInject */
function dayparts( mediaService ) {
    return mediaService.getDayParts( true );
}

/* @ngInject */
function personas( authService ) {
    return authService.getPersonas();
}
