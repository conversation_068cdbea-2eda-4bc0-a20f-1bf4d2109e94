( function () {
  'use strict';

  angular
    .module( 'commonModule' )
    .factory( 'mediaService', mediaService );

  /*@ngInject*/
  function mediaService( icsMediaApi, ErrorService, $http, Upload, moment ) {
    const baseUrl = icsMediaApi.baseUrl + '/' + icsMediaApi.version + '/media';
    const slash = '/';
    const apiUrl = {
      assets: baseUrl + '/assets',
      assetPackages: baseUrl + '/assetpackages',
      assetsPackages: baseUrl + '/assetspackages',
      promptSets: baseUrl + '/promptsets',
      packageDownload: baseUrl + '/promptsets/package/:id/download ',
      promptSet: baseUrl + '/promptset',
      prompt: baseUrl + '/prompt',
      prompts: baseUrl + '/prompts',
      promptTypes: baseUrl + '/prompttypes',
      promptTemplates: baseUrl + '/prompttemplates',
      dayParts: baseUrl + '/dayparts',
      softKeys: baseUrl + '/softkeys',
      keyCodes: baseUrl + '/keycodes',
      companyLanguages: icsMediaApi.baseUrl + '/' + icsMediaApi.version + '/companies/:companyID/languages',
      promptsetLanguages: baseUrl + '/promptsets/{id}/languages',
      promptSetProfiles: baseUrl + '/promptsets/profile',
      promptClone: baseUrl + '/prompt'
    };
    const ignoreLoadingBar = { ignoreLoadingBar: true };

    const timeRanges = [],
      dayPartTimeRange = 49,
      timeRangeInterval = 1800000, // 30 minutes
      totalMillInDay = 86399999; // Minus 1 sec

    let time,
      cachedTimeRanges;

    const convertToMoment = function ( ts ) {
      return moment( ts ).utc().format( 'LT' );
    };

    const dayPartIntervals = function () {
      _.times( dayPartTimeRange, function ( i ) {
        const timeStamp = i * timeRangeInterval;
        if ( i !== dayPartTimeRange - 1 ) {
          time = {
            id: i,
            time: convertToMoment( timeStamp ),
            milliseconds: timeStamp
          };
        } else {
          time = {
            id: i,
            time: convertToMoment( totalMillInDay ),
            milliseconds: totalMillInDay
          };
        }
        timeRanges.push( time );
      } );
      // Cache Results
      cachedTimeRanges = timeRanges;
      return timeRanges;
    };

    const fontSizes = _.range( 10, 37, 1 );

    const globals = {
      fontColor: '#000000',
      fontSizes: fontSizes,
      fontSize: fontSizes[ 4 ],
      backgroundColor: '#FFFFFF'
    };

    const service = {
      /* Assets */
      getAssets: getAssets,
      postAssets: postAssets,
      deleteAsset: deleteAsset,
      getAsset: getAsset,
      putAsset: putAsset,
      uploadInProgress: uploadInProgress,
      /* Asset Packages */
      getAssetPackages: getAssetPackages,
      postAssetPackages: postAssetPackages,
      deleteAssetPackage: deleteAssetPackage,
      getAssetPackage: getAssetPackage,
      putAssetPackage: putAssetPackage,
      getAssetsFromPackage: getAssetsFromPackage,
      /* Prompt Sets */
      getPromptSets: getPromptSets,
      postPromptSets: postPromptSets,
      deletePromptSet: deletePromptSet,
      getPromptSet: getPromptSet,
      putPromptSet: putPromptSet,
      putPromptSetPrompts: putPromptSetPrompts,
      deletePromptSetPrompt: deletePromptSetPrompt,
      clonePromptSet: clonePromptSet,
      clonePrompt : clonePrompt,
      publishPromptSet: publishPromptSet,
      getPromptTypes: getPromptTypes,
      getPromptStateGlobals: getPromptStateGlobals,
      createPreview: createPreview,
      requestApproval: requestApproval,
      approvePromptSet: approvePromptSet,
      rejectPromptSet: rejectPromptSet,
      getApprovers: getApprovers,
      getPromptSetChildren: getPromptSetChildren,
      getPromptSetProfiles: getPromptSetProfiles,
      /* Prompt */
      postPrompt: postPrompt,
      /* Prompt Templates */
      getPromptTemplates: getPromptTemplates,
      getPromptTemplateStates: getPromptTemplateStates,
      /* Dayparts */
      getDayParts: getDayParts,
      postDayParts: postDayParts,
      getDayPart: getDayPart,
      deleteDayPart: deleteDayPart,
      updateDayPart: updateDayPart,
      getDayPartIntervals: getDayPartIntervals,
      /* Soft Keys */
      getSoftKeysByDeviceType: getSoftKeysByDeviceType,
      getKeyCodes: getKeyCodes,
      /* Prompt */
      updatePrompt: updatePrompt,
      generateODML: generateODML,
      createException: createException,
      deleteException: deleteException,
      updateBackground: updateBackground,
      updateFont: updateFont,
      saveTouchMask: saveTouchMask,
      getTouchMasks: getTouchMasks,
      overrideTouchMask: overrideTouchMask,
      getTouchMaskPromptSets: getTouchMaskPromptSets,
      getPublishStatus: getPublishStatus,
      exportPromptSet: exportPromptSet,
      importPromptSet: importPromptSet,
      putS3WithSignedURL: putS3WithSignedURL,
      /* Multi Language */
      getCompanyLanguages: getCompanyLanguages,
      getPromptsetLanguages: getPromptsetLanguages,
      putPromptsetLanguages: putPromptsetLanguages,
      getPromptSetPackage: getPromptSetPackage
    };

    return service;

    ////////////

    /* Assets */
    function getAssets( {
        pageIndex = 0,
        pageSize = -1,
        order = 'uploaded',
        name = _.noop(),
        type = _.noop(),
        signed,
        width = _.noop(),
        height = _.noop(),
        uploader = _.noop(),
        minWidth = _.noop(),
        minHeight = _.noop(),
        maxWidth = _.noop(),
        maxHeight = _.noop(),
        videoExtension,
        active
    } ) {
      const url = apiUrl.assets;
      /* eslint-disable no-param-reassign */
      /* ICS-2006: Don't replace signed to undefined/noop, if signed is false */
      signed = _.isBoolean( signed ) ? signed : _.noop();
      /* eslint-enable no-param-reassign */

      const params = {
        pageIndex: pageIndex,
        pageSize: pageSize,
        order: order,
        name: name || _.noop(),
        type: type,
        signed: signed,
        width: width,
        height: height,
        uploader: uploader,
        minWidth: minWidth,
        minHeight: minHeight,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        videoExtension: videoExtension,
        active: active
      };
      return $http.get( url, { params: params, ignoreLoadingBar: true } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( function ( error ) {
          if ( error.status === 500 ) {
            ErrorService.showToast( 'Something happened. Try again.' );
            throw error;
          } else {
            ErrorService.handleError();
          }
        } );
    }

    function postAssets( file, eventHandler, errorHandler ) {
      const url = apiUrl.assets;

      const uploadProcess = Upload.upload( {
        url: url,
        data: {
          fileContent: file,
          assetName: file.name.replace(/\s+/g, '_').toString()
        },
        ignoreLoadingBar: true
      } );
      return uploadProcess.then( function( response ) {
        return response;
      }, function ( error ) {
        if ( error.status === 413 ) {
          error.message = 'File too large, must be less than 1GB'; // eslint-disable-line no-param-reassign
        } else if ( error.status === 400 ) {
          error.message = error.data.message; // eslint-disable-line no-param-reassign
        } else if ( error.status === 504 ) {
          error.message = 'Gateway Timeout'; // eslint-disable-line no-param-reassign
        }
        return errorHandler( error );
      }, function ( evt ) {
        if ( eventHandler ) {
          eventHandler( Math.min( 100, parseInt( 100.0 * evt.loaded / evt.total, 10 ) ) ); // eslint-disable-line no-mixed-operators
        }
      } );
    }

    function deleteAsset( id ) {
      const url = apiUrl.assets + slash + id;
      return $http.delete( url, { ignoreLoadingBar: true } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getAsset( id ) {
      const url = apiUrl.assets + slash + id;
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function putAsset( asset ) {
      const url = apiUrl.assets + slash + asset.id;
      return $http.put( url, asset, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function putS3WithSignedURL( url, file ) {
        return fetch( url, {
                method: 'PUT',
                body: file,
            }
        )
        .catch( ErrorService.handleError );
    }

    function uploadInProgress() {
      return Upload.isUploadInProgress();
    }

    /* Asset Packages */
    function getAssetPackages( pageIndex, pageSize, order, name ) {
      const url = apiUrl.assetPackages;
      /* eslint-disable no-param-reassign */
      pageIndex = pageIndex || 0;
      pageSize = pageSize || -1;
      order = order || 'uploaded';
      name = name || undefined;
      /* eslint-enable no-param-reassign */

      const params = {
        pageIndex: pageIndex,
        pageSize: pageSize,
        order: order,
        name: name
      };

      return $http.get( url, { params: params, ignoreLoadingBar: true } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function importPromptSet( promptSet ) {
        const url = apiUrl.promptSets + '/import';
        return $http.post( url, promptSet, ignoreLoadingBar )
            .then( function ( response ) {
                return response.data;
            } )
            .catch( ErrorService.handleError );
    }

    function postAssetPackages( assetPackage, eventHandler, errorHandler ) {
      const url = apiUrl.assetPackages;

      const uploadProcess = Upload.upload( {
        url: url,
        data: {
          fileContent: assetPackage,
          assetPackageName: assetPackage.name,
          deviceType: null
        }
      } );
      return uploadProcess.then( function( response ) {
        return response;
      }, function ( error ) {
        if ( error.status === 413 ) {
          error.message = 'File too large, must be less than 1GB'; // eslint-disable-line no-param-reassign
        } else if ( error.status === 400 ) {
          error.message = error.data.message; // eslint-disable-line no-param-reassign
        } else if ( error.status === 504 ) {
          error.message = 'Gateway Timeout'; // eslint-disable-line no-param-reassign
        }
        return errorHandler( error );
      }, function ( evt ) {
        if ( eventHandler ) {
          eventHandler( Math.min( 100, parseInt( 100.0 * evt.loaded / evt.total, 10 ) ) ); // eslint-disable-line no-mixed-operators
        }
      } );
    }

    function deleteAssetPackage( id ) {
      const url = apiUrl.assetPackages + slash + id;
      return $http.delete( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getAssetPackage( id ) {
      const url = apiUrl.assetPackages + slash + id;
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function putAssetPackage( assetPackage ) {
      const url = apiUrl.assetPackages + slash + assetPackage.id;
      return $http.put( url, assetPackage, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getAssetsFromPackage( id ) {
      const url = apiUrl.assetsPackages + slash + id + '/assets';

      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPromptSetProfiles(pageIndex, pageSize) {
      const url = apiUrl.promptSetProfiles;
      pageIndex = pageIndex || 0;
      pageSize = pageSize || 200;
      const params = {
        pageIndex,
        pageSize,
      }
      return $http.get( url, { params: params, ignoreLoadingBar: true } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }
    /* Prompt Sets */
    function getPromptSets( pageIndex, pageSize, order, q, profileType, state ) {
      const url = apiUrl.promptSets;
      /* eslint-disable no-param-reassign */
      pageIndex = pageIndex || 0;
      pageSize = pageSize || -1;
      order = order || 'modified';
      q = q || _.noop();
      profileType = profileType || _.noop();
      state = state || _.noop();
      /* eslint-enable no-param-reassign */

      const params = {
        pageIndex: pageIndex,
        pageSize: pageSize,
        order: order,
        q: q,
        promptSetProfileName: profileType,
        state: state
      };

      return $http.get( url, { params: params, ignoreLoadingBar: true } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPromptSet( promptSetId ) {
      const url = apiUrl.promptSets + slash + promptSetId;
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response;
        } )
        .catch( ErrorService.handleError );
    }

    function postPromptSets( promptSet ) {
      const url = apiUrl.promptSets;
      return $http.post( url, promptSet, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function deletePromptSet( id ) {
      const url = apiUrl.promptSets + slash + id;
      return $http.delete( url )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPromptSet( id ) {
      const url = apiUrl.promptSets + slash + id;
      let promptSetProfiles = JSON.parse(localStorage.getItem('promptSetProfiles')) || [];
          if (promptSetProfiles.length === 0) getPromptSetProfiles().then((profiles) => {
            localStorage.setItem('promptSetProfiles', JSON.stringify(profiles.results));
            promptSetProfiles = profiles.results;
          })
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          let result = response.data;
          let mainPrompts = [];
          let auxPrompts = [];
          const psp = promptSetProfiles.find(profile => profile.id === result.promptSetProfileName);
          if (psp?.auxResolutions) {
          mainPrompts = result.states.filter(x => x.screenOption === 'main');
          auxPrompts = result.states.filter(x => x.screenOption === 'aux');
          result.states = [...mainPrompts, ...auxPrompts];
          }
          return result;
        } )
        .catch( ErrorService.handleError );
    }

    function putPromptSet( id, updates ) {
      const url = apiUrl.promptSets + slash + id;
      return $http.put( url, updates, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function putPromptSetPrompts( id, promptAssignments ) {
      const url = apiUrl.promptSets + slash + id + '/prompts';
      return $http.put( url, promptAssignments, ignoreLoadingBar )
        .then( function( response ) {
          return response.data;
        } )
        .catch( ErrorService.throwError );
    }

    function deletePromptSetPrompt(id){
      const url = apiUrl.prompt + slash + id;
      return $http.delete( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response;
        } )
        .catch( ErrorService.handleError );
    }

    function clonePromptSet ( id, name, replacedAssignments ) {
      const url = apiUrl.promptSets + slash + id + '/clone';
      const promptSet = {
        name: name,
        replacedAssignments: replacedAssignments
      };
      return $http.post( url, promptSet )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function clonePrompt ( promptData ) {
      promptData.code;
      promptData.promptState.id;
      promptData.description;
      const url = apiUrl.promptClone + '/clone';
      const promptIds = promptData.promptState.assignments.map(assignment => assignment.id);
      const prompt = {
        code: promptData.code,
        description: promptData.description,
        promptSetId : promptData.promptSetId,
        promptIds : promptIds
      };
      return $http.post( url, prompt )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function publishPromptSet( promptSet ) {
      const url = apiUrl.promptSets + slash + promptSet.id + '/publish';
      return $http.post( url, promptSet, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPromptTypes( state ) {
      const url = apiUrl.promptTypes;
      state = state || undefined; // eslint-disable-line no-param-reassign

      const params = {
        state: state
      };

      return $http.get( url, { params: params }, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPromptStateGlobals() {
      return globals;
    }

    function createPreview ( id ) {
      const url = apiUrl.promptSets + slash + id + '/preview';
      return $http.post( url )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function exportPromptSet ( id ) {
        const url = apiUrl.promptSets + slash + id + '/export';
        return $http.post( url )
        .then( function ( response ) {
            return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function requestApproval ( id, approvers ) {
      const url = apiUrl.promptSets + slash + id + '/forapproval';
      return $http.post( url, { approvers: approvers } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function approvePromptSet ( params ) {
      const url = apiUrl.promptSets + slash + params.id + '/approve';
      return $http.post( url, { mfaCode: params.mfa } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function rejectPromptSet ( params ) {
      const url = apiUrl.promptSets + slash + params.id + '/reject';
      return $http.post( url, { mfaCode: params.mfa } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getApprovers ( id ) {
      const url = apiUrl.promptSets + slash + id + '/approvers';
      return $http.get( url )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    /* Prompt Templates */
    function getPromptTemplates() {
      const url = apiUrl.promptTemplates;
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPromptTemplateStates( id ) {
      const url = apiUrl.promptTemplates + slash + id + '/states';
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function postPrompt( prompt ) {
      const url = apiUrl.prompts;
      return $http.post( url, prompt, ignoreLoadingBar )
        .then( function ( response ) {
          return response;
        } )
        .catch( ErrorService.handleError );
    }

    /* Dayparts */
    function getDayParts( active ) {
      return $http.get( apiUrl.dayParts, {
        ignoreLoading: true,
        params: {
          active: active
        }
      } )
        .then( function ( response ) {
          const items = response.data;
          // If no dayparts, return response data as is
          if ( _.isEmpty( items ) ) return items;
          // if dayparts, order by start desc then name asc.
          return _.orderBy( items, ['start', 'end', 'name'], ['asc', 'asc', 'asc'] );
        } )
        .catch( ErrorService.handleError );
    }

    function postDayParts( daypart ) {
      return $http.post( apiUrl.dayParts, daypart, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getDayPart( id ) {
      const url = apiUrl.dayParts + slash + id;
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function deleteDayPart( daypart ) {
      const url = apiUrl.dayParts + slash + daypart.id;
      return $http.delete( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response;
        } )
        .catch( ErrorService.handleError );
    }

    function updateDayPart( daypart ) {
      const url = apiUrl.dayParts + slash + daypart.id;
      return $http.put( url, daypart, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getDayPartIntervals() {
      return cachedTimeRanges || dayPartIntervals();
    }

    function getSoftKeysByDeviceType ( deviceType ) {
      const url = apiUrl.softKeys + slash + deviceType;
      return $http.get( url ).then( function ( response ) {
        return response.data;
      } ).catch( ErrorService.handleError );
    }

    function getKeyCodes () {
      const url = apiUrl.keyCodes;
      return $http.get( url ).then( function ( response ) {
        return response.data;
      } ).catch( ErrorService.handleError );
    }

    function getPromptSetChildren ( id, autoPoll ) {
      const url = apiUrl.promptSets + slash + id + '/children';
      const params = {
        autoPoll: autoPoll
      };
      return $http.get( url, {
        params: params,
        ignoreLoadingBar: autoPoll
      } ).then( function ( response ) {
        return response.data;
      } ).catch( ErrorService.handleError );
    }

    /*prompts*/

    function updatePrompt ( prompt ) {
      const url = apiUrl.prompt + slash + prompt.id;
      return $http.put( url, prompt )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.throwError );
    }

    function generateODML( promptID, elements ) {
      const url = apiUrl.prompt + slash + promptID + slash + 'toODML';
      return $http.post( url, elements, { params: { beautify: true } } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function createException ( promptSetID, exception, promptId ) {
      const url = apiUrl.promptSets + slash + promptSetID + slash + 'prompts' + slash + promptId + slash + 'exception';
      return $http.post( url, exception )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function deleteException ( exceptionID ) {
      const url = apiUrl.prompt + slash + exceptionID;
      return $http.delete( url )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function updateBackground ( promptSetID, value ) {
      const url = apiUrl.promptSets + slash + promptSetID + slash + 'bg';
      return $http.put( url, {}, { params: { bg: value } } )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function updateFont( promptSetID, fontSettings, all ) {
        const { fontColor, fontFace, fontSize } = fontSettings;
        const url = `${apiUrl.promptSets}/${promptSetID}/font`;
        return $http.put( url, {}, {
            params: {
            fontColor, fontFace, fontSize, all: all
            }
        } )
        .then( function ( response ) {
            return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function saveTouchMask ( touchMask ) {
      const url = baseUrl + slash + 'touchmaps';
      return $http.post( url, touchMask )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getTouchMasks () {
      const url = baseUrl + slash + 'touchmaps?pageSize=-1';
      return $http.get( url )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getTouchMaskPromptSets( id ) {
      const url = baseUrl + slash + 'touchmaps/' + id + '/promptsets';
      return $http.get( url )
        .then( function( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function overrideTouchMask ( touchMask ) {
      const url = baseUrl + slash + 'touchmaps' + slash + touchMask.id + '/areas';
      return $http.put( url, touchMask.areas )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getPublishStatus( id ) {
      const url = apiUrl.assets + slash + id + '/pubstatus';
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function getCompanyLanguages( companyId ) {
      const url = apiUrl.companyLanguages.replace(':companyID', companyId);
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }

    function putPromptsetLanguages(promptSetId, languages) {
      const url = apiUrl.promptsetLanguages.replace('{id}', promptSetId);
      return $http.put( url, languages, ignoreLoadingBar )
        .then( function( response ) {
          return response.data;
        } )
        .catch( ErrorService.throwError );
    }
    function getPromptSetPackage(softwareId){
      const url = apiUrl.packageDownload.replace(':id',softwareId );
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
      }

    function getPromptsetLanguages( promptSetId ) {
      const url = apiUrl.promptsetLanguages.replace('{id}', promptSetId);
      return $http.get( url, ignoreLoadingBar )
        .then( function ( response ) {
          return response.data;
        } )
        .catch( ErrorService.handleError );
    }
  }
} )();
