<nav class="navbar navbar-fixed-top navbar-ics" ng-class="{
     'navbar-default':$state.includes('companySettings'),
     'navbar-inverse':!$state.includes('companySettings')
     }">
  <div class="container-fluid">
    <div class="row">

      <div class="navbar-title-container col-xs-6 col-sm-6 col-md-3">
        <button type="button" ng-click="globalMenuCollapse()" class="btn btn-circle navbar-btn pull-left">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon gicon-dehaze" aria-hidden="true"></span>
        </button>
        <div class="navbar-brand-text"
          ng-if="!($state.current.data.navTitle === 'Fuel Price Management' && isMobileView())"
          ng-bind="$state.current.data.navTitle"></div>
      </div>

      <div class="ics-navbar-global-q col-xs-12 col-md-6" ng-if="!isBankUser" ng-show="showNavSearch" ics-global-search>
      </div>

      <div class="ics-nav-right pull-right">

        <div ng-if="!isBankUser " ng-bind="buildVersion"
          style="position: absolute; right: 5px; top: 2px; font-size: 10px; color: #999; pointer-events: none;"></div>
        <ul class="nav navbar-nav navbar-right">
          <li class="visible-xs visible-sm" ng-if="!isMobileView()">
            <button type="button" ng-click="searchToggle()" class="btn btn-link navbar-btn navbar-btn-icon no-focus">
              <span class="icon gicon-search" aria-hidden="true"></span>
            </button>
          </li>
          <li ng-if="!isBankUser">
            <a class="pos-relative navbar-icon" ng-switch="noteCount>0" data-ui-sref="notifications({reload: true})"
              data-ui-sref-opts="{reload: true}" title="{{noteCount}} Notifications"
              aria-label="{{noteCount}} Notifications">
              <span ng-switch-when="true" class="icon gicon-notifications" aria-hidden="true"></span>
              <span ng-switch-when="false" class="icon gicon-notifications_none" aria-hidden="true"></span>
              <strong role="status" aria-live="polite" class="notification-gem in-wobble" ng-switch-when="true"
                ng-bind="noteCount"></strong>
            </a>
          </li>
          <li class="visible-xs visible-sm visible-md visible-lg" uib-dropdown data-is-open="status.isopen">
            <a id="user-settings-dropdown-nav" href="" role="button" aria-label="Settings" class="navbar-icon"
              data-uib-dropdown-toggle>
              <span class="icon gicon-account_circle" aria-hidden="true"></span>
            </a>
            <ul class="ics-user-settings-dropdown" uib-dropdown-menu aria-labelledby="user-settings-dropdown-nav">
              <li class="ics-user-card" data-stop-prop>
                <a ui-sref="accountSettings" aria-label="Profile" ng-click="status.isopen = false">
                  <div class="user-name text-ellipsis" ng-bind="::user.fullName"></div>
                </a>
                <div class="user-company text-ellipsis" ng-bind="::user.company.name" ng-if="!isBankUser"></div>
              </li>



              <li role="separator" class="divider" ng-if-start="isBankUser"></li>
              <li><a aria-label="Release notes">Release notes</a></li>
              <li><a aria-label="User guide">User Guide</a></li>
              <li ng-if-end style="display:none;"></li>

              <li role="separator" class="divider" ng-if-start="!isBankUser"></li>
              <li data-feature-flag="docLink"><a ng-href="/docs/index.html" target="_blank"
                  aria-label="Developers">Developers</a></li>
              <li><a ui-sref="releaseNotes" aria-label="Release notes">Release notes</a></li>
              <li><a ui-sref="licenses">Licenses</a></li>
              <li><a ng-click="downloadUserGuide()" aria-label="User guide">User Guide</a></li>
              <li ng-if-end style="display:none;"></li>
              <li role="separator" class="divider"></li>
              <li><a href="" ng-click="logout()">Log out</a></li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </div>
</nav>