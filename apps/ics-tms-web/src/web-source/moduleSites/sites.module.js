import angular from 'angular'; // eslint-disable-line import/no-extraneous-dependencies
import uiRouter from '@uirouter/angularjs';

import CommonModule from '../common/common-module';

import DeviceConfigController from './controllers/device-config.controller';
import DeviceMediaController from './controllers/device-media.controller';
import DeviceVersionsController from './controllers/device-versions.controller';
import DeviceHistoryController from './controllers/device-history.controller';
import AppConfigController from './controllers/app-config.controller';
import DeviceNetworkController from './controllers/device-network-controller';
import { terminalLocationConfigKey, terminalLocationValues } from '../common/constants/device';

export default angular
  .module( 'sitesModule', [uiRouter, CommonModule] )
  .config( ( $stateProvider, $urlRouterProvider ) => {
    'ngInject';
   // Redirect from old URL to new URL
   $urlRouterProvider.when('/sites/:id', ($match) => {
    return `/asset-management/sites/${$match.id}`;
  });
   $urlRouterProvider.when('/sites', '/asset-management/sites');

    const states = [
      {
        name: 'sites',
        parent: 'main',
        url: '/sites?size&page&order&siteStatus&siteEvents&alarms&deviceStatus&deviceOOS&showHiddenSites',
        data: {
          titleTag: 'Asset Management',
          navTitle: 'Asset Management',
          navMobileTitle: 'Sites',
          viewClass: 'view-sticky-toolbar'
        },
        templateUrl: 'moduleSites/sites.html',
        controller: 'SiteListController',
        controllerAs: 'vm',
        bindToController: true,
        resolve: {
          pagedSiteList: pagedSiteList
        }
      },

      {
        name: 'legacy-sites-add',
        url: '/legacy-add',
        parent: 'sites',
        data: {
          titleTag: 'Add Site',
          navTitle: 'Add Site',
          navMobileTitle: 'Add Site',
          viewClass: 'view-is-centered'
        },
        views: {
          '@main': {
            templateUrl: 'moduleSites/sites-add.html',
            controller: 'SitesAddController',
            controllerAs: 'vm',
            bindToController: true
          }
        }
      },
    ];

    return _.forEach( states, function ( state ) {
      $stateProvider.state( state );
    } );
  } )
  .name;

// ***
// Resolves
// ***

/* @ngInject */
function pagedSiteList( sitesService, $stateParams ) {
  const page = $stateParams.page || 0;
  const size = $stateParams.size || 20;
  const order = 'status-desc';
  const status = $stateParams.siteStatus;
  const siteEvents = $stateParams.siteEvents;
  const deviceStatus = $stateParams.deviceStatus;
  const deviceOOS = $stateParams.deviceOOS;
  const showHiddenSites = $stateParams.showHiddenSites;
  const opts = {
    tags: null,
    order,
    status,
    siteEvents,
    deviceStatus,
    showHiddenSites,
    deviceOOS,
    isCSV: false
  };
  return sitesService.getSitesList( 'prod', size, page, null, opts );
}

/* @ngInject */
function siteDetails( sitesService, $stateParams ) {
    return sitesService.getSiteById( $stateParams.id );
}

/* @ngInject */
function deviceDetails( devicesService, $stateParams ) {
  return devicesService.getDevice( $stateParams.deviceId ).then(function (result) {
    if (!result.configData[terminalLocationConfigKey]) { 
      result.configData[terminalLocationConfigKey] = {
        value: terminalLocationValues.Forecourt, 
        timestamp: new Date().toISOString()
      };
    }
    return result;
  });
}

/* @ngInject */
function deviceTypes( devicesService ) {
    return devicesService.getDeviceTypes();
}

/* @ngInject */
function availableFiles( devicesService, $stateParams ) {
  return devicesService.getUploadableFiles( $stateParams.deviceId );
}

/* @ngInject */
function deviceVersions( devicesService, $stateParams ) {
    return devicesService.getDeviceVersions( $stateParams.deviceId );
}

/* @ngInject */
function deviceStateHistory( devicesService, $stateParams ) {
    const id = $stateParams.deviceId;
    const levels = [ 'notice', 'warn', 'error', 'critical', 'fatal' ];
    const before1Hr = new Date( Date.now() - ( 1000 * 60 * 60 ) );
    const start = before1Hr.toISOString();
    return devicesService.getDeviceStateHistory( id, undefined, levels, start, undefined, undefined, 25 );
}