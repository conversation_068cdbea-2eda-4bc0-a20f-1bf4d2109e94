name: Test

on:
  workflow_call:
    inputs:
      app-prefix:
        required: true
        type: string
      package-name:
        required: true
        type: string

permissions:
  packages: read
  contents: write

jobs:
  test:
    runs-on: [self-hosted, linux]

    container:
      image: ghcr.io/invenco-cloud-systems-ics/ics-images/os-al2-generic:latest
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        XLTS_TOKEN: ${{ secrets.XLTS_TOKEN }}
        BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
        HUSKY: 0
      credentials:
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - name: Log Parameters
        run: |
          echo "App: ${{ inputs.package-name }}"
          echo "Prefix: ${{ inputs.app-prefix }}"
          echo "Branch: ${BRANCH_NAME}"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Chrome
        run: |
          # Install Google Chrome for Amazon Linux
          yum install -y wget
          # Download and install Chrome directly
          wget -O /tmp/google-chrome.rpm https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
          yum install -y /tmp/google-chrome.rpm || true
          # Install dependencies if needed
          yum install -y liberation-fonts || true

      - name: Set Chrome Binary
        run: echo "CHROME_BIN=/usr/bin/google-chrome" >> $GITHUB_ENV

      - name: Use Node.js from .nvmrc
        id: use_node_js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com/
          scope: '@invenco-cloud-systems-ics'

      - name: Set npm globals
        run: git config --global --add safe.directory '*'

      - name: Restore npm cache
        id: npm_cache
        uses: actions/cache/restore@v4
        with:
          path: ${{ inputs.app-prefix }}/node_modules
          key: ${{ runner.os }}-npm-${{ inputs.package-name }}-${{ hashFiles(format('{0}/package-lock.json', inputs.app-prefix)) }}

      - name: Install Dependencies
        if: steps.npm_cache.outputs.cache-hit != 'true'
        run: ${{ format('{0} {1} {2}', 'npm --prefix', inputs.app-prefix, 'ci --ignore-scripts') }}

      - name: Test
        run: ${{ format('{0} {1} {2}', 'npm --prefix', inputs.app-prefix, 'run test --if-present') }}
