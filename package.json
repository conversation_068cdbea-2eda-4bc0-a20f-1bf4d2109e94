{"name": "ics-next", "license": "UNLICENSED", "version": "0.0.1", "description": "ICS NEXT the new UI Framework of Frameworks.", "private": true, "scripts": {"prepare": "husky install", "start": "concurrently \"npm run start-dev \" \"npm run start-watch \"", "start-dev": "npm run clean && webpack-dev-server --mode=development --env.env=$ENV", "start-serve-invenco": "npm run clean && webpack-dev-server --mode=production --env.isLocal=true --env.target=invenco  --env.env=$ENV", "start-serve-ncr": "npm run clean && webpack-dev-server --mode=production --env.isLocal=true --env.target=ncr --env.env=$ENV", "start-tms": "lerna run watch --scope @ics/tms-web", "start-watch": "lerna run watch", "setup": "npm i --from-lock-file --legacy-peer-deps --prefer-offline && npm run bootstrap && npm run buildApps", "bootstrap": "lerna bootstrap", "format": "prettier --write --ignore-unknown './**'", "lerna": "lerna", "lerna:clean": "lerna clean", "lint": "eslint .", "lint:fix": "eslint \".\" --fix", "nx": "nx", "test": "cross-env BABEL_ENV=test jest --passWithNoTests", "clean": "rimraf target", "buildApps": "lerna run build", "build": "npm run buildApps -- --ignore @ics/config-management --ignore @ics/asset-management", "build-invenco": "npm run build && webpack --mode=production --env.target=invenco", "build-ncr": "npm run build && webpack --mode=production --env.target=ncr", "build:all": "npm run clean && npm run buildApps && webpack --mode=production --env.target=invenco && webpack --mode=production --env.target=ncr", "webpack": "webpack"}, "lint-staged": {"apps/ics-asset-management/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-asset-management run format --if-present", "npm --prefix apps/ics-asset-management run lint:fix --if-present"], "apps/ics-config-management/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-config-management run format --if-present", "npm --prefix apps/ics-config-management run lint:fix --if-present"], "apps/ics-dashboard/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-dashboard run format --if-present", "npm --prefix apps/ics-dashboard run lint:fix --if-present"], "apps/ics-fuel-price-management/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-fuel-price-management run format --if-present", "npm --prefix apps/ics-fuel-price-management run lint:fix --if-present"], "apps/ics-playlist/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-playlist run format --if-present", "npm --prefix apps/ics-playlist run lint:fix --if-present"], "apps/ics-report-management/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-report-management run format --if-present", "npm --prefix apps/ics-report-management run lint:fix --if-present", "npm --prefix apps/ics-report-management run test --if-present"], "apps/ics-tms-web-v2/{src,__test__}/**/*{.js,ts,jsx,tsx}": ["npm --prefix apps/ics-tms-web-v2 run format --if-present", "npm --prefix apps/ics-tms-web-v2 run lint:fix --if-present"], "*": ["npm run lint:fix", "npm run format"], "*.png": "git add"}, "dependencies": {"@invenco-cloud-systems-ics/rspack-webpack-config": "0.1.5", "@ng-bootstrap/ng-bootstrap": "19.0.0", "axios": "1.4.0", "qs": "6.11.2", "single-spa": "6.0.1", "single-spa-html": "1.3.0", "single-spa-layout": "2.2.0"}, "engines": {"node": "18.*", "npm": "^9", "yarn": "please-use-npm"}, "engineStrict": true, "resolutions": {"ejs": "3.1.7", "glob-parent": "5.1.2", "minimatch": "3.0.5", "node-forge": "1.3.1"}, "devDependencies": {"@babel/core": "7.19.6", "@babel/plugin-transform-runtime": "7.19.6", "@babel/preset-env": "7.19.4", "@babel/runtime": "7.20.1", "@invenco-cloud-systems-ics/eslint-config": "1.0.2", "@nrwl/nx-cloud": "18.0.0", "@types/jest": "29.2.1", "@types/systemjs": "6.1.1", "babel-eslint": "10.1.0", "concurrently": "7.5.0", "copy-webpack-plugin": "6.4.1", "cross-env": "7.0.3", "ejs": "3.1.7", "eslint": "8.41.0", "file-loader": "6.2.0", "glob": "8.0.3", "glob-parent": "5.1.2", "html-webpack-plugin": "4.5.2", "husky": "8.0.1", "jest": "29.2.2", "jest-cli": "29.2.2", "lerna": "6.0.1", "lint-staged": "13.2.3", "minimatch": "3.0.5", "node-forge": "1.3.1", "prettier": "3.0.0", "pretty-quick": "3.1.3", "rimraf": "3.0.2", "serve": "14.0.1", "typescript": "5.1.6", "webpack": "4.46.0", "webpack-cli": "3.3.12", "webpack-config-single-spa": "1.18.3", "webpack-dev-server": "3.11.3", "webpack-merge": "4.2.2"}, "useNx": true, "jest": {"testPathIgnorePatterns": ["apps", "common", "build"], "coveragePathIgnorePatterns": ["apps", "common", "build"], "modulePathIgnorePatterns": ["apps", "common", "build"]}}